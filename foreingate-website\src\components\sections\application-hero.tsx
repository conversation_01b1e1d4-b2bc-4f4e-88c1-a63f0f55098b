'use client'

import { motion } from 'framer-motion'
import Link from 'next/link'
import { ArrowRight, CheckCircle, Clock, Users, Award } from 'lucide-react'
import { Button } from '@/components/ui/button'

export function ApplicationHeroSection() {
  return (
    <section className="relative min-h-[70vh] flex items-center justify-center overflow-hidden bg-gradient-to-br from-primary/10 via-background to-secondary/10">
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-primary/10 rounded-full blur-3xl animate-pulse-slow" />
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-secondary/10 rounded-full blur-3xl animate-pulse-slow" />
      </div>

      <div className="container relative z-10 px-4 py-20">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Content */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            className="space-y-8"
          >
            {/* Badge */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2, duration: 0.6 }}
              className="inline-flex items-center space-x-2 bg-primary/10 text-primary px-4 py-2 rounded-full text-sm font-medium"
            >
              <CheckCircle className="w-4 h-4" />
              <span>98% Application Success Rate</span>
            </motion.div>

            {/* Main Heading */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4, duration: 0.8 }}
              className="space-y-4"
            >
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight">
                Start Your{' '}
                <span className="gradient-text">University Application</span>
              </h1>
              <p className="text-xl text-muted-foreground max-w-2xl">
                Take the first step towards your academic future. Our streamlined application
                process makes it easy to apply to top universities in Northern Cyprus with
                expert guidance every step of the way.
              </p>
            </motion.div>

            {/* Key Benefits */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6, duration: 0.8 }}
              className="space-y-3"
            >
              {[
                'Free application review and guidance',
                'Apply to multiple universities with one form',
                'Expert counselor assigned to your case',
                'Track your application status in real-time'
              ].map((benefit, index) => (
                <motion.div
                  key={benefit}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.8 + index * 0.1, duration: 0.5 }}
                  className="flex items-center space-x-3"
                >
                  <CheckCircle className="w-5 h-5 text-primary flex-shrink-0" />
                  <span className="text-muted-foreground">{benefit}</span>
                </motion.div>
              ))}
            </motion.div>

            {/* CTA Buttons */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1, duration: 0.8 }}
              className="flex flex-col sm:flex-row gap-4"
            >
              <Button size="lg" className="group" asChild>
                <Link href="#application-form">
                  Start Application
                  <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                </Link>
              </Button>
              <Button size="lg" variant="outline" asChild>
                <Link href="/contact">
                  Get Free Consultation
                </Link>
              </Button>
            </motion.div>

            {/* Process Timeline */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1.2, duration: 0.8 }}
              className="grid grid-cols-4 gap-4 pt-8"
            >
              {[
                { step: '1', title: 'Apply', time: '5 min' },
                { step: '2', title: 'Review', time: '24 hrs' },
                { step: '3', title: 'Submit', time: '1 week' },
                { step: '4', title: 'Accept', time: '2-4 weeks' }
              ].map((item, index) => (
                <motion.div
                  key={item.step}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 1.4 + index * 0.1, duration: 0.5 }}
                  className="text-center"
                >
                  <div className="w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center font-bold text-sm mx-auto mb-2">
                    {item.step}
                  </div>
                  <div className="text-sm font-medium">{item.title}</div>
                  <div className="text-xs text-muted-foreground">{item.time}</div>
                </motion.div>
              ))}
            </motion.div>
          </motion.div>

          {/* Application Preview Card */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="relative"
          >
            <div className="bg-background rounded-2xl p-8 shadow-xl border">
              <h3 className="text-2xl font-bold mb-6">Quick Application Preview</h3>

              <div className="space-y-6">
                {/* Application Stats */}
                <div className="grid grid-cols-3 gap-4 text-center">
                  <div>
                    <Clock className="w-6 h-6 text-primary mx-auto mb-2" />
                    <div className="text-lg font-bold">5 min</div>
                    <div className="text-xs text-muted-foreground">To complete</div>
                  </div>
                  <div>
                    <Users className="w-6 h-6 text-primary mx-auto mb-2" />
                    <div className="text-lg font-bold">25+</div>
                    <div className="text-xs text-muted-foreground">Universities</div>
                  </div>
                  <div>
                    <Award className="w-6 h-6 text-primary mx-auto mb-2" />
                    <div className="text-lg font-bold">98%</div>
                    <div className="text-xs text-muted-foreground">Success rate</div>
                  </div>
                </div>

                {/* Form Preview */}
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">Personal Information</label>
                    <div className="h-10 bg-muted rounded-lg flex items-center px-3 text-sm text-muted-foreground">
                      Full name, email, phone...
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Academic Background</label>
                    <div className="h-10 bg-muted rounded-lg flex items-center px-3 text-sm text-muted-foreground">
                      Education level, GPA, transcripts...
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Program Preferences</label>
                    <div className="h-10 bg-muted rounded-lg flex items-center px-3 text-sm text-muted-foreground">
                      Preferred programs and universities...
                    </div>
                  </div>
                </div>

                <Button className="w-full" size="lg">
                  Start Your Application
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </div>
            </div>

            {/* Floating Success Badge */}
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 1.4, duration: 0.6 }}
              className="absolute -top-4 -right-4 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-3 py-2 rounded-full text-sm font-medium"
            >
              ✓ Trusted Process
            </motion.div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}
