import { NextRequest, NextResponse } from 'next/server'
import { readFile, writeFile } from 'fs/promises'
import { existsSync } from 'fs'
import path from 'path'

const DATA_DIR = path.join(process.cwd(), 'data')
const APPLICATIONS_FILE = path.join(DATA_DIR, 'applications.json')

async function readApplications() {
  try {
    if (!existsSync(APPLICATIONS_FILE)) {
      return []
    }
    const data = await readFile(APPLICATIONS_FILE, 'utf-8')
    return JSON.parse(data)
  } catch (error) {
    console.error('Error reading applications:', error)
    return []
  }
}

async function writeApplications(applications: any[]) {
  try {
    await writeFile(APPLICATIONS_FILE, JSON.stringify(applications, null, 2))
  } catch (error) {
    console.error('Error writing applications:', error)
    throw error
  }
}

// GET individual application
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const applications = await readApplications()
    const application = applications.find((app: any) => 
      app.id === params.id || app.applicationId === params.id
    )

    if (!application) {
      return NextResponse.json(
        { success: false, error: 'Application not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: application
    })

  } catch (error) {
    console.error('Application fetch error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch application' },
      { status: 500 }
    )
  }
}

// PUT - Update application (Admin only)
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json()
    const applications = await readApplications()
    
    const applicationIndex = applications.findIndex((app: any) => 
      app.id === params.id || app.applicationId === params.id
    )

    if (applicationIndex === -1) {
      return NextResponse.json(
        { success: false, error: 'Application not found' },
        { status: 404 }
      )
    }

    // Update application
    const updatedApplication = {
      ...applications[applicationIndex],
      ...body,
      updatedAt: new Date().toISOString()
    }

    applications[applicationIndex] = updatedApplication

    await writeApplications(applications)

    // Send notification email if status changed
    if (body.status && body.status !== applications[applicationIndex].status) {
      try {
        if (process.env.RESEND_API_KEY) {
          const { Resend } = require('resend')
          const resend = new Resend(process.env.RESEND_API_KEY)

          let emailSubject = ''
          let emailContent = ''

          switch (body.status) {
            case 'UNDER_REVIEW':
              emailSubject = `Application Under Review - ${updatedApplication.applicationId}`
              emailContent = `
                <h2>Application Status Update</h2>
                <p>Dear ${updatedApplication.firstName} ${updatedApplication.lastName},</p>
                <p>Your application is now under review by our admissions team. We will contact you soon with updates.</p>
                <p><strong>Application ID:</strong> ${updatedApplication.applicationId}</p>
                <p><strong>Status:</strong> Under Review</p>
              `
              break
            case 'APPROVED':
              emailSubject = `🎉 Application Approved - ${updatedApplication.applicationId}`
              emailContent = `
                <h2>Congratulations! Your Application Has Been Approved</h2>
                <p>Dear ${updatedApplication.firstName} ${updatedApplication.lastName},</p>
                <p>We are pleased to inform you that your application has been approved!</p>
                <p><strong>Application ID:</strong> ${updatedApplication.applicationId}</p>
                <p><strong>University:</strong> ${updatedApplication.preferredUniversity}</p>
                <p><strong>Program:</strong> ${updatedApplication.firstChoiceProgram}</p>
                <p>Our team will contact you soon with next steps for enrollment.</p>
              `
              break
            case 'REJECTED':
              emailSubject = `Application Status Update - ${updatedApplication.applicationId}`
              emailContent = `
                <h2>Application Status Update</h2>
                <p>Dear ${updatedApplication.firstName} ${updatedApplication.lastName},</p>
                <p>Thank you for your interest in studying with us. After careful review, we regret to inform you that your application was not successful this time.</p>
                <p><strong>Application ID:</strong> ${updatedApplication.applicationId}</p>
                <p>We encourage you to apply again in the future.</p>
              `
              break
          }

          if (emailSubject && emailContent) {
            await resend.emails.send({
              from: 'Foreingate Admissions <<EMAIL>>',
              to: [updatedApplication.email],
              subject: emailSubject,
              html: emailContent,
            })
          }
        }
      } catch (emailError) {
        console.error('Email notification failed:', emailError)
        // Don't fail the update if email fails
      }
    }

    return NextResponse.json({
      success: true,
      data: updatedApplication,
      message: 'Application updated successfully'
    })

  } catch (error) {
    console.error('Application update error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to update application' },
      { status: 500 }
    )
  }
}

// DELETE application (Admin only)
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const applications = await readApplications()
    
    const applicationIndex = applications.findIndex((app: any) => 
      app.id === params.id || app.applicationId === params.id
    )

    if (applicationIndex === -1) {
      return NextResponse.json(
        { success: false, error: 'Application not found' },
        { status: 404 }
      )
    }

    const deletedApplication = applications[applicationIndex]
    applications.splice(applicationIndex, 1)

    await writeApplications(applications)

    return NextResponse.json({
      success: true,
      message: 'Application deleted successfully',
      data: deletedApplication
    })

  } catch (error) {
    console.error('Application deletion error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to delete application' },
      { status: 500 }
    )
  }
}
