'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { ImageIcon, GraduationCap, Building, Users, MapPin } from 'lucide-react'
import Image from 'next/image'

interface ImagePlaceholderProps {
  src?: string
  alt: string
  width?: number
  height?: number
  className?: string
  type?: 'university' | 'campus' | 'student' | 'general'
  fallbackText?: string
}

export function ImagePlaceholder({
  src,
  alt,
  width = 400,
  height = 300,
  className = '',
  type = 'general',
  fallbackText
}: ImagePlaceholderProps) {
  const [imageError, setImageError] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  const getPlaceholderIcon = () => {
    switch (type) {
      case 'university':
        return <GraduationCap className="w-12 h-12 text-gray-400" />
      case 'campus':
        return <Building className="w-12 h-12 text-gray-400" />
      case 'student':
        return <Users className="w-12 h-12 text-gray-400" />
      default:
        return <ImageIcon className="w-12 h-12 text-gray-400" />
    }
  }

  const getPlaceholderGradient = () => {
    switch (type) {
      case 'university':
        return 'from-blue-100 to-blue-200'
      case 'campus':
        return 'from-green-100 to-green-200'
      case 'student':
        return 'from-purple-100 to-purple-200'
      default:
        return 'from-gray-100 to-gray-200'
    }
  }

  const PlaceholderContent = () => (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className={`
        w-full h-full bg-gradient-to-br ${getPlaceholderGradient()}
        flex flex-col items-center justify-center
        border-2 border-dashed border-gray-300
        rounded-lg
      `}
      style={{ width, height }}
    >
      {getPlaceholderIcon()}
      <p className="text-sm text-gray-500 mt-2 text-center px-4">
        {fallbackText || alt || 'Image not available'}
      </p>
    </motion.div>
  )

  if (!src || imageError) {
    return (
      <div className={className}>
        <PlaceholderContent />
      </div>
    )
  }

  return (
    <div className={`relative ${className}`}>
      {isLoading && (
        <div className="absolute inset-0 z-10">
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className={`
              w-full h-full bg-gradient-to-br ${getPlaceholderGradient()}
              flex items-center justify-center
              rounded-lg
            `}
            style={{ width, height }}
          >
            <div className="animate-pulse">
              {getPlaceholderIcon()}
            </div>
          </motion.div>
        </div>
      )}
      
      <Image
        src={src}
        alt={alt}
        width={width}
        height={height}
        className={`rounded-lg ${isLoading ? 'opacity-0' : 'opacity-100'} transition-opacity duration-300`}
        onLoad={() => setIsLoading(false)}
        onError={() => {
          setImageError(true)
          setIsLoading(false)
        }}
      />
    </div>
  )
}

// Specific placeholder components for different use cases
export function UniversityLogo({ 
  src, 
  universityName, 
  className = '' 
}: { 
  src?: string
  universityName: string
  className?: string 
}) {
  return (
    <ImagePlaceholder
      src={src}
      alt={`${universityName} logo`}
      width={120}
      height={80}
      type="university"
      className={className}
      fallbackText={universityName}
    />
  )
}

export function CampusImage({ 
  src, 
  campusName, 
  className = '' 
}: { 
  src?: string
  campusName: string
  className?: string 
}) {
  return (
    <ImagePlaceholder
      src={src}
      alt={`${campusName} campus`}
      width={600}
      height={400}
      type="campus"
      className={className}
      fallbackText={`${campusName} Campus`}
    />
  )
}

export function StudentPhoto({ 
  src, 
  studentName, 
  className = '' 
}: { 
  src?: string
  studentName: string
  className?: string 
}) {
  return (
    <ImagePlaceholder
      src={src}
      alt={`${studentName} photo`}
      width={150}
      height={150}
      type="student"
      className={className}
      fallbackText={studentName}
    />
  )
}

export function HeroImage({ 
  src, 
  title, 
  className = '' 
}: { 
  src?: string
  title: string
  className?: string 
}) {
  return (
    <ImagePlaceholder
      src={src}
      alt={title}
      width={1200}
      height={600}
      type="general"
      className={className}
      fallbackText={title}
    />
  )
}
