'use client'

import { motion } from 'framer-motion'
import { useInView } from 'react-intersection-observer'
import { Calendar, Users, Award, Globe } from 'lucide-react'

const milestones = [
  {
    year: '2015',
    title: 'Company Founded',
    description: 'Foreingate Group was established with a vision to bridge the gap between international students and quality education in Northern Cyprus.',
    icon: Calendar
  },
  {
    year: '2017',
    title: 'First 1,000 Students',
    description: 'Successfully helped our first 1,000 students secure admissions to top universities in Northern Cyprus.',
    icon: Users
  },
  {
    year: '2019',
    title: 'Excellence Award',
    description: 'Received the "Excellence in Student Services" award from the Northern Cyprus Education Ministry.',
    icon: Award
  },
  {
    year: '2023',
    title: 'Global Expansion',
    description: 'Expanded our services to 50+ countries, helping students from around the world achieve their academic dreams.',
    icon: Globe
  }
]

export function CompanyStorySection() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1
  })

  return (
    <section ref={ref} className="section-padding">
      <div className="container">
        {/* Hero Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
          className="text-center max-w-4xl mx-auto mb-16"
        >
          <h1 className="text-4xl md:text-5xl font-bold mb-6">
            Our Story: Empowering Dreams Through{' '}
            <span className="gradient-text">Education</span>
          </h1>
          <p className="text-xl text-muted-foreground leading-relaxed">
            For nearly a decade, Foreingate Group has been the trusted bridge connecting 
            ambitious students with world-class education opportunities in Northern Cyprus. 
            Our journey began with a simple belief: every student deserves access to quality 
            education, regardless of their background or location.
          </p>
        </motion.div>

        {/* Main Story Content */}
        <div className="grid lg:grid-cols-2 gap-12 items-center mb-20">
          {/* Story Text */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={inView ? { opacity: 1, x: 0 } : {}}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="space-y-6"
          >
            <h2 className="text-3xl font-bold">From Vision to Reality</h2>
            <div className="space-y-4 text-muted-foreground leading-relaxed">
              <p>
                Founded in 2015 by a group of education enthusiasts who recognized the 
                untapped potential of Northern Cyprus as an educational hub, Foreingate Group 
                started as a small consultancy with big dreams.
              </p>
              <p>
                Our founders, having experienced the challenges of studying abroad firsthand, 
                understood the complexities students face when navigating international education 
                systems. They envisioned a comprehensive support system that would eliminate 
                barriers and make quality education accessible to all.
              </p>
              <p>
                Today, we stand proud as the leading educational consultancy in the region, 
                having successfully guided over 5,000 students from 50+ countries toward 
                their academic and career goals. Our success is measured not just in numbers, 
                but in the transformed lives and bright futures we've helped create.
              </p>
            </div>
          </motion.div>

          {/* Story Image */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={inView ? { opacity: 1, x: 0 } : {}}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="relative"
          >
            <div className="rounded-2xl overflow-hidden shadow-2xl">
              <img
                src="/images/about/company-story.jpg"
                alt="Foreingate Group team and students"
                className="w-full h-[400px] object-cover"
                onError={(e) => {
                  e.currentTarget.src = "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='600' height='400' viewBox='0 0 600 400'%3E%3Crect width='600' height='400' fill='%23f3f4f6'/%3E%3Ctext x='50%25' y='50%25' dominant-baseline='middle' text-anchor='middle' font-family='Arial, sans-serif' font-size='24' fill='%236b7280'%3EForeingate Group Story%3C/text%3E%3C/svg%3E"
                }}
              />
            </div>
            
            {/* Floating Stats */}
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={inView ? { opacity: 1, scale: 1 } : {}}
              transition={{ duration: 0.6, delay: 0.8 }}
              className="absolute -bottom-6 -left-6 bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg border"
            >
              <div className="text-center">
                <div className="text-3xl font-bold text-primary mb-1">5,000+</div>
                <div className="text-sm text-muted-foreground">Students Helped</div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={inView ? { opacity: 1, scale: 1 } : {}}
              transition={{ duration: 0.6, delay: 1 }}
              className="absolute -top-6 -right-6 bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg border"
            >
              <div className="text-center">
                <div className="text-3xl font-bold text-primary mb-1">50+</div>
                <div className="text-sm text-muted-foreground">Countries Served</div>
              </div>
            </motion.div>
          </motion.div>
        </div>

        {/* Timeline */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="max-w-4xl mx-auto"
        >
          <h2 className="text-3xl font-bold text-center mb-12">Our Journey</h2>
          
          <div className="relative">
            {/* Timeline Line */}
            <div className="absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-gradient-to-b from-primary to-secondary rounded-full" />
            
            {/* Timeline Items */}
            <div className="space-y-12">
              {milestones.map((milestone, index) => (
                <motion.div
                  key={milestone.year}
                  initial={{ opacity: 0, y: 30 }}
                  animate={inView ? { opacity: 1, y: 0 } : {}}
                  transition={{ duration: 0.6, delay: 0.8 + index * 0.2 }}
                  className={`flex items-center ${
                    index % 2 === 0 ? 'flex-row' : 'flex-row-reverse'
                  }`}
                >
                  {/* Content */}
                  <div className={`w-5/12 ${index % 2 === 0 ? 'text-right pr-8' : 'text-left pl-8'}`}>
                    <div className="bg-background border rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow">
                      <div className="flex items-center mb-3 justify-center">
                        <milestone.icon className="w-6 h-6 text-primary" />
                      </div>
                      <h3 className="text-xl font-semibold mb-2">{milestone.title}</h3>
                      <p className="text-muted-foreground text-sm leading-relaxed">
                        {milestone.description}
                      </p>
                    </div>
                  </div>
                  
                  {/* Year Badge */}
                  <div className="w-2/12 flex justify-center">
                    <div className="w-16 h-16 bg-primary text-primary-foreground rounded-full flex items-center justify-center font-bold text-sm shadow-lg">
                      {milestone.year}
                    </div>
                  </div>
                  
                  {/* Spacer */}
                  <div className="w-5/12" />
                </motion.div>
              ))}
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
