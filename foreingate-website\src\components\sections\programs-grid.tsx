'use client'

import { motion } from 'framer-motion'
import Link from 'next/link'
import { useInView } from 'react-intersection-observer'
import { Clock, Globe, GraduationCap, ArrowRight, DollarSign, MapPin } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { usePrograms } from '@/hooks/use-api'
import { formatCurrency } from '@/lib/utils'

export function ProgramsGridSection() {
  const { data: programs, loading } = usePrograms()
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1
  })

  if (loading) {
    return (
      <section className="section-padding">
        <div className="container">
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="bg-muted rounded-xl h-80" />
              </div>
            ))}
          </div>
        </div>
      </section>
    )
  }

  return (
    <section ref={ref} className="section-padding" id="programs">
      <div className="container">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
          className="text-center max-w-3xl mx-auto mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Explore Academic{' '}
            <span className="gradient-text">Programs</span>
          </h2>
          <p className="text-xl text-muted-foreground">
            Discover programs that align with your career goals and academic interests
          </p>
        </motion.div>

        {/* Programs Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {programs?.map((program, index) => (
            <motion.div
              key={program.id}
              initial={{ opacity: 0, y: 30 }}
              animate={inView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              className="group"
            >
              <Link href={`/programs/${program.slug}`}>
                <div className="bg-background rounded-xl overflow-hidden shadow-sm border hover:shadow-lg transition-all duration-300 group-hover:border-primary/20 h-full">
                  {/* Program Header */}
                  <div className="p-6 border-b">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex-1">
                        <h3 className="text-xl font-semibold mb-2 group-hover:text-primary transition-colors line-clamp-2">
                          {program.name}
                        </h3>
                        <div className="flex items-center text-sm text-muted-foreground mb-2">
                          <GraduationCap className="w-4 h-4 mr-1" />
                          <span>{program.degree} in {program.field}</span>
                        </div>
                      </div>
                      <div className="bg-primary/10 text-primary px-2 py-1 rounded-full text-xs font-medium">
                        {program.degree}
                      </div>
                    </div>

                    <p className="text-muted-foreground text-sm line-clamp-2 mb-4">
                      {program.description}
                    </p>

                    {/* Program Details */}
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div className="flex items-center">
                        <Clock className="w-4 h-4 text-primary mr-2" />
                        <span>{program.duration}</span>
                      </div>
                      <div className="flex items-center">
                        <Globe className="w-4 h-4 text-primary mr-2" />
                        <span>{program.language}</span>
                      </div>
                    </div>
                  </div>

                  {/* Program Content */}
                  <div className="p-6">
                    {/* Key Courses */}
                    <div className="mb-4">
                      <h4 className="font-medium mb-2 text-sm">Key Courses:</h4>
                      <div className="flex flex-wrap gap-1">
                        {program.courses.slice(0, 3).map((course) => (
                          <span
                            key={course}
                            className="text-xs bg-muted px-2 py-1 rounded-full"
                          >
                            {course}
                          </span>
                        ))}
                        {program.courses.length > 3 && (
                          <span className="text-xs text-primary font-medium">
                            +{program.courses.length - 3} more
                          </span>
                        )}
                      </div>
                    </div>

                    {/* Career Opportunities */}
                    <div className="mb-4">
                      <h4 className="font-medium mb-2 text-sm">Career Paths:</h4>
                      <ul className="space-y-1">
                        {program.careerOpportunities.slice(0, 2).map((career) => (
                          <li key={career} className="text-xs text-muted-foreground flex items-center">
                            <div className="w-1 h-1 bg-primary rounded-full mr-2" />
                            {career}
                          </li>
                        ))}
                        {program.careerOpportunities.length > 2 && (
                          <li className="text-xs text-primary font-medium">
                            +{program.careerOpportunities.length - 2} more opportunities
                          </li>
                        )}
                      </ul>
                    </div>

                    {/* Tuition and Action */}
                    <div className="flex items-center justify-between pt-4 border-t">
                      <div>
                        <span className="text-sm text-muted-foreground">Tuition</span>
                        <div className="font-semibold text-primary">
                          {formatCurrency(program.tuition, program.currency)}/year
                        </div>
                      </div>
                      <ArrowRight className="w-4 h-4 text-muted-foreground group-hover:text-primary group-hover:translate-x-1 transition-all" />
                    </div>
                  </div>
                </div>
              </Link>
            </motion.div>
          ))}
        </div>

        {/* Load More / CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="text-center bg-gradient-to-r from-primary/5 to-secondary/5 rounded-2xl p-8 md:p-12"
        >
          <h3 className="text-2xl md:text-3xl font-bold mb-4">
            Can't Find Your Ideal Program?
          </h3>
          <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
            Our education counselors can help you discover programs that match your specific
            interests and career goals. Get personalized recommendations based on your background.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" asChild>
              <Link href="/contact">
                Get Program Recommendations
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
            <Button size="lg" variant="outline" asChild>
              <Link href="/universities">
                Browse Universities
              </Link>
            </Button>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
