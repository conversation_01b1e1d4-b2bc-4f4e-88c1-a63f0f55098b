"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"848affbdbcef\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXE5pZGhhbFxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxmb3JlaW5nYXRlX2dyb3VwZVxcZm9yZWluZ2F0ZS13ZWJzaXRlXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI4NDhhZmZiZGJjZWZcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/smart-chatbot.tsx":
/*!*********************************************!*\
  !*** ./src/components/ui/smart-chatbot.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SmartChatbot: () => (/* binding */ SmartChatbot)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/../node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Bot_Copy_MessageCircle_Minimize2_RefreshCw_Send_Sparkles_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Copy,MessageCircle,Minimize2,RefreshCw,Send,Sparkles,ThumbsDown,ThumbsUp,X!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Copy_MessageCircle_Minimize2_RefreshCw_Send_Sparkles_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Copy,MessageCircle,Minimize2,RefreshCw,Send,Sparkles,ThumbsDown,ThumbsUp,X!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Copy_MessageCircle_Minimize2_RefreshCw_Send_Sparkles_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Copy,MessageCircle,Minimize2,RefreshCw,Send,Sparkles,ThumbsDown,ThumbsUp,X!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Copy_MessageCircle_Minimize2_RefreshCw_Send_Sparkles_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Copy,MessageCircle,Minimize2,RefreshCw,Send,Sparkles,ThumbsDown,ThumbsUp,X!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/minimize-2.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Copy_MessageCircle_Minimize2_RefreshCw_Send_Sparkles_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Copy,MessageCircle,Minimize2,RefreshCw,Send,Sparkles,ThumbsDown,ThumbsUp,X!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Copy_MessageCircle_Minimize2_RefreshCw_Send_Sparkles_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Copy,MessageCircle,Minimize2,RefreshCw,Send,Sparkles,ThumbsDown,ThumbsUp,X!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Copy_MessageCircle_Minimize2_RefreshCw_Send_Sparkles_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Copy,MessageCircle,Minimize2,RefreshCw,Send,Sparkles,ThumbsDown,ThumbsUp,X!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/thumbs-up.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Copy_MessageCircle_Minimize2_RefreshCw_Send_Sparkles_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Copy,MessageCircle,Minimize2,RefreshCw,Send,Sparkles,ThumbsDown,ThumbsUp,X!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/thumbs-down.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Copy_MessageCircle_Minimize2_RefreshCw_Send_Sparkles_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Copy,MessageCircle,Minimize2,RefreshCw,Send,Sparkles,ThumbsDown,ThumbsUp,X!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Copy_MessageCircle_Minimize2_RefreshCw_Send_Sparkles_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Copy,MessageCircle,Minimize2,RefreshCw,Send,Sparkles,ThumbsDown,ThumbsUp,X!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* __next_internal_client_entry_do_not_use__ SmartChatbot auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction SmartChatbot(param) {\n    let { className = '' } = param;\n    _s();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMinimized, setIsMinimized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [inputValue, setInputValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [sessionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"SmartChatbot.useState\": ()=>Math.random().toString(36).substr(2, 9)\n    }[\"SmartChatbot.useState\"]);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Debug function to test click\n    const handleChatButtonClick = ()=>{\n        console.log('Chat button clicked - opening chat!');\n        setIsOpen(true);\n    };\n    // Auto-scroll to bottom when new messages arrive\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SmartChatbot.useEffect\": ()=>{\n            var _messagesEndRef_current;\n            (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n                behavior: 'smooth'\n            });\n        }\n    }[\"SmartChatbot.useEffect\"], [\n        messages\n    ]);\n    // Focus input when chat opens\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SmartChatbot.useEffect\": ()=>{\n            if (isOpen && !isMinimized) {\n                setTimeout({\n                    \"SmartChatbot.useEffect\": ()=>{\n                        var _inputRef_current;\n                        return (_inputRef_current = inputRef.current) === null || _inputRef_current === void 0 ? void 0 : _inputRef_current.focus();\n                    }\n                }[\"SmartChatbot.useEffect\"], 100);\n            }\n        }\n    }[\"SmartChatbot.useEffect\"], [\n        isOpen,\n        isMinimized\n    ]);\n    // Initialize with welcome message\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SmartChatbot.useEffect\": ()=>{\n            if (isOpen && messages.length === 0) {\n                const welcomeMessage = {\n                    id: 'welcome',\n                    type: 'bot',\n                    content: \"\\uD83C\\uDF1F **Hello! I'm your Foreingate AI Assistant!**\\n\\nI can help you with:\\n• University information and programs\\n• Admission requirements and process\\n• Costs, scholarships, and financial aid\\n• Visa support and documentation\\n• Accommodation options\\n• Life in Northern Cyprus\\n• Any other questions about studying abroad!\\n\\nWhat would you like to know? \\uD83C\\uDF93\",\n                    timestamp: new Date(),\n                    suggestions: [\n                        \"Tell me about universities\",\n                        \"What are the costs?\",\n                        \"How do I apply?\",\n                        \"What about scholarships?\"\n                    ]\n                };\n                setMessages([\n                    welcomeMessage\n                ]);\n            }\n        }\n    }[\"SmartChatbot.useEffect\"], [\n        isOpen,\n        messages.length\n    ]);\n    const sendMessage = async (message)=>{\n        if (!message.trim() || isLoading) return;\n        const userMessage = {\n            id: Date.now().toString(),\n            type: 'user',\n            content: message,\n            timestamp: new Date()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        setInputValue('');\n        setIsLoading(true);\n        try {\n            const response = await fetch('/api/chatbot', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    message,\n                    sessionId\n                })\n            });\n            const data = await response.json();\n            if (data.success) {\n                const botMessage = {\n                    id: (Date.now() + 1).toString(),\n                    type: 'bot',\n                    content: data.response,\n                    timestamp: new Date(),\n                    category: data.category,\n                    confidence: data.confidence,\n                    suggestions: data.suggestions,\n                    data: data.data\n                };\n                setMessages((prev)=>[\n                        ...prev,\n                        botMessage\n                    ]);\n            } else {\n                throw new Error(data.error || 'Failed to get response');\n            }\n        } catch (error) {\n            console.error('Chat error:', error);\n            const errorMessage = {\n                id: (Date.now() + 1).toString(),\n                type: 'bot',\n                content: \"❌ Sorry, I encountered an error. Please try again or contact our support team at:\\n\\n\\uD83D\\uDCE7 **Email:** <EMAIL>\\n\\uD83D\\uDCF1 **WhatsApp:** +90 ************\\n\\uD83D\\uDCDE **Phone:** +90 ************\\n\\nOur human advisors are always ready to help! \\uD83E\\uDD1D\",\n                timestamp: new Date()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    errorMessage\n                ]);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleSuggestionClick = (suggestion)=>{\n        sendMessage(suggestion);\n    };\n    const copyMessage = (content)=>{\n        navigator.clipboard.writeText(content);\n    // Could add a toast notification here\n    };\n    const formatMessage = (content)=>{\n        // Convert markdown-like formatting to HTML\n        return content.replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>').replace(/\\*(.*?)\\*/g, '<em>$1</em>').replace(/•/g, '•').replace(/\\n/g, '<br>');\n    };\n    if (!isOpen) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed bottom-6 right-6 z-[9999] \".concat(className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 rounded-full bg-gradient-to-r from-blue-600 to-purple-600 animate-ping opacity-20 pointer-events-none\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                    lineNumber: 172,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                    initial: {\n                        scale: 0\n                    },\n                    animate: {\n                        scale: 1\n                    },\n                    transition: {\n                        delay: 1,\n                        type: \"spring\",\n                        stiffness: 260,\n                        damping: 20\n                    },\n                    className: \"relative z-10\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        onClick: handleChatButtonClick,\n                        className: \"h-16 w-16 rounded-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 relative cursor-pointer\",\n                        size: \"icon\",\n                        type: \"button\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Copy_MessageCircle_Minimize2_RefreshCw_Send_Sparkles_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-7 w-7\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute -top-1 -right-1 h-4 w-4 bg-green-500 rounded-full animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                    lineNumber: 174,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n            lineNumber: 170,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed bottom-6 right-6 z-[9999] \".concat(className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n            initial: {\n                opacity: 0,\n                scale: 0.8,\n                y: 20\n            },\n            animate: {\n                opacity: 1,\n                scale: 1,\n                y: 0,\n                height: isMinimized ? 60 : 600,\n                width: isMinimized ? 300 : 400\n            },\n            exit: {\n                opacity: 0,\n                scale: 0.8,\n                y: 20\n            },\n            transition: {\n                duration: 0.3\n            },\n            className: \"bg-white dark:bg-gray-800 rounded-2xl shadow-2xl border border-gray-200 dark:border-gray-700 overflow-hidden relative z-10\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-blue-600 to-purple-600 p-4 text-white\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Copy_MessageCircle_Minimize2_RefreshCw_Send_Sparkles_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"w-8 h-8\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Copy_MessageCircle_Minimize2_RefreshCw_Send_Sparkles_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-3 h-3 absolute -top-1 -right-1 text-yellow-300\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold text-lg\",\n                                                children: \"Foreingate AI\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs opacity-90\",\n                                                children: \"Smart Education Assistant\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: ()=>setIsMinimized(!isMinimized),\n                                        className: \"h-8 w-8 p-0 text-white hover:bg-white/20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Copy_MessageCircle_Minimize2_RefreshCw_Send_Sparkles_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: ()=>setIsOpen(false),\n                                        className: \"h-8 w-8 p-0 text-white hover:bg-white/20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Copy_MessageCircle_Minimize2_RefreshCw_Send_Sparkles_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                    lineNumber: 210,\n                    columnNumber: 9\n                }, this),\n                !isMinimized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-96 overflow-y-auto p-4 space-y-4 bg-gray-50 dark:bg-gray-900\",\n                            children: [\n                                messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex \".concat(message.type === 'user' ? 'justify-end' : 'justify-start'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"max-w-[85%] rounded-2xl px-4 py-3 \".concat(message.type === 'user' ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white' : 'bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 shadow-md border'),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start space-x-2\",\n                                                children: [\n                                                    message.type === 'bot' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Copy_MessageCircle_Minimize2_RefreshCw_Send_Sparkles_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"w-5 h-5 mt-1 text-blue-600 flex-shrink-0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                                        lineNumber: 261,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm leading-relaxed\",\n                                                                dangerouslySetInnerHTML: {\n                                                                    __html: formatMessage(message.content)\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                                                lineNumber: 264,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            message.type === 'bot' && message.confidence && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mt-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                        variant: \"outline\",\n                                                                        className: \"text-xs\",\n                                                                        children: message.category\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                                                        lineNumber: 272,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                        variant: \"outline\",\n                                                                        className: \"text-xs\",\n                                                                        children: [\n                                                                            Math.round(message.confidence),\n                                                                            \"% confident\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                                                        lineNumber: 275,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                                                lineNumber: 271,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            message.suggestions && message.suggestions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-3 space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-600 dark:text-gray-400\",\n                                                                        children: \"Quick questions:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                                                        lineNumber: 284,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex flex-wrap gap-2\",\n                                                                        children: message.suggestions.map((suggestion, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                                variant: \"outline\",\n                                                                                size: \"sm\",\n                                                                                onClick: ()=>handleSuggestionClick(suggestion),\n                                                                                className: \"text-xs h-7 px-2 hover:bg-blue-50 hover:border-blue-300\",\n                                                                                children: suggestion\n                                                                            }, index, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                                                                lineNumber: 287,\n                                                                                columnNumber: 33\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                                                        lineNumber: 285,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                                                lineNumber: 283,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            message.type === 'bot' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mt-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        size: \"sm\",\n                                                                        onClick: ()=>copyMessage(message.content),\n                                                                        className: \"h-6 w-6 p-0 text-gray-500 hover:text-gray-700\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Copy_MessageCircle_Minimize2_RefreshCw_Send_Sparkles_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                            className: \"h-3 w-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                                                            lineNumber: 310,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                                                        lineNumber: 304,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        size: \"sm\",\n                                                                        className: \"h-6 w-6 p-0 text-gray-500 hover:text-green-600\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Copy_MessageCircle_Minimize2_RefreshCw_Send_Sparkles_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                            className: \"h-3 w-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                                                            lineNumber: 317,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                                                        lineNumber: 312,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        size: \"sm\",\n                                                                        className: \"h-6 w-6 p-0 text-gray-500 hover:text-red-600\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Copy_MessageCircle_Minimize2_RefreshCw_Send_Sparkles_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                            className: \"h-3 w-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                                                            lineNumber: 324,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                                                        lineNumber: 319,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                                                lineNumber: 303,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                                        lineNumber: 263,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, message.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 17\n                                    }, this)),\n                                isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-start\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white dark:bg-gray-800 rounded-2xl px-4 py-3 shadow-md border\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Copy_MessageCircle_Minimize2_RefreshCw_Send_Sparkles_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-5 h-5 text-blue-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-blue-600 rounded-full animate-bounce\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                                            lineNumber: 340,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-blue-600 rounded-full animate-bounce\",\n                                                            style: {\n                                                                animationDelay: '0.1s'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                                            lineNumber: 341,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-blue-600 rounded-full animate-bounce\",\n                                                            style: {\n                                                                animationDelay: '0.2s'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                                            lineNumber: 342,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                                    lineNumber: 339,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                            lineNumber: 337,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                    lineNumber: 335,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    ref: messagesEndRef\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                    lineNumber: 349,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                            lineNumber: 246,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 border-t bg-white dark:bg-gray-800\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                            ref: inputRef,\n                                            value: inputValue,\n                                            onChange: (e)=>setInputValue(e.target.value),\n                                            onKeyPress: (e)=>e.key === 'Enter' && sendMessage(inputValue),\n                                            placeholder: \"Ask me anything about studying abroad...\",\n                                            disabled: isLoading,\n                                            className: \"flex-1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                            lineNumber: 355,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            onClick: ()=>sendMessage(inputValue),\n                                            disabled: isLoading || !inputValue.trim(),\n                                            className: \"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700\",\n                                            size: \"icon\",\n                                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Copy_MessageCircle_Minimize2_RefreshCw_Send_Sparkles_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"h-4 w-4 animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                                lineNumber: 371,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Copy_MessageCircle_Minimize2_RefreshCw_Send_Sparkles_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                                lineNumber: 373,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                            lineNumber: 364,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                    lineNumber: 354,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500 mt-2 text-center\",\n                                    children: \"Powered by Foreingate AI • Always here to help! \\uD83C\\uDF93\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                    lineNumber: 377,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                            lineNumber: 353,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n            lineNumber: 196,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n        lineNumber: 195,\n        columnNumber: 5\n    }, this);\n}\n_s(SmartChatbot, \"Eu1ISuiZBhpjVDECGUEX5BB5ejM=\");\n_c = SmartChatbot;\nvar _c;\n$RefreshReg$(_c, \"SmartChatbot\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/smart-chatbot.tsx\n"));

/***/ })

});