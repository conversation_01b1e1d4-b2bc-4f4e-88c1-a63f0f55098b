'use client'

import { motion } from 'framer-motion'
import { useState } from 'react'
import { useInView } from 'react-intersection-observer'
import { Search, Filter, MapPin, DollarSign, GraduationCap, Globe } from 'lucide-react'
import { Button } from '@/components/ui/button'

const locations = ['All Locations', 'Nicosia', 'Famagusta', 'Kyrenia', 'Morphou']
const tuitionRanges = [
  { label: 'All Ranges', min: 0, max: 50000 },
  { label: '$3,000 - $5,000', min: 3000, max: 5000 },
  { label: '$5,000 - $8,000', min: 5000, max: 8000 },
  { label: '$8,000 - $12,000', min: 8000, max: 12000 },
  { label: '$12,000+', min: 12000, max: 50000 }
]
const programTypes = ['All Programs', 'Engineering', 'Business', 'Medicine', 'Arts', 'Sciences', 'Law']
const languages = ['All Languages', 'English', 'Turkish', 'Both']

export function UniversityFiltersSection() {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedLocation, setSelectedLocation] = useState('All Locations')
  const [selectedTuition, setSelectedTuition] = useState('All Ranges')
  const [selectedProgram, setSelectedProgram] = useState('All Programs')
  const [selectedLanguage, setSelectedLanguage] = useState('All Languages')
  const [showFilters, setShowFilters] = useState(false)

  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1
  })

  const handleSearch = () => {
    // This would typically trigger a search/filter action
    console.log('Searching with filters:', {
      searchTerm,
      selectedLocation,
      selectedTuition,
      selectedProgram,
      selectedLanguage
    })
  }

  const clearFilters = () => {
    setSearchTerm('')
    setSelectedLocation('All Locations')
    setSelectedTuition('All Ranges')
    setSelectedProgram('All Programs')
    setSelectedLanguage('All Languages')
  }

  return (
    <section ref={ref} className="section-padding bg-muted/30">
      <div className="container">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
          className="max-w-4xl mx-auto"
        >
          {/* Search Bar */}
          <div className="bg-background rounded-xl p-6 shadow-sm border mb-6">
            <div className="flex flex-col lg:flex-row gap-4">
              {/* Search Input */}
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search universities by name..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                />
              </div>

              {/* Filter Toggle */}
              <Button
                variant="outline"
                onClick={() => setShowFilters(!showFilters)}
                className="lg:w-auto"
              >
                <Filter className="w-4 h-4 mr-2" />
                Filters
              </Button>

              {/* Search Button */}
              <Button onClick={handleSearch} className="lg:w-auto">
                Search Universities
              </Button>
            </div>

            {/* Advanced Filters */}
            {showFilters && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.3 }}
                className="mt-6 pt-6 border-t grid md:grid-cols-2 lg:grid-cols-4 gap-4"
              >
                {/* Location Filter */}
                <div>
                  <label className="block text-sm font-medium mb-2">
                    <MapPin className="w-4 h-4 inline mr-1" />
                    Location
                  </label>
                  <select
                    value={selectedLocation}
                    onChange={(e) => setSelectedLocation(e.target.value)}
                    className="w-full p-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                  >
                    {locations.map((location) => (
                      <option key={location} value={location}>
                        {location}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Tuition Filter */}
                <div>
                  <label className="block text-sm font-medium mb-2">
                    <DollarSign className="w-4 h-4 inline mr-1" />
                    Tuition Range
                  </label>
                  <select
                    value={selectedTuition}
                    onChange={(e) => setSelectedTuition(e.target.value)}
                    className="w-full p-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                  >
                    {tuitionRanges.map((range) => (
                      <option key={range.label} value={range.label}>
                        {range.label}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Program Filter */}
                <div>
                  <label className="block text-sm font-medium mb-2">
                    <GraduationCap className="w-4 h-4 inline mr-1" />
                    Program Type
                  </label>
                  <select
                    value={selectedProgram}
                    onChange={(e) => setSelectedProgram(e.target.value)}
                    className="w-full p-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                  >
                    {programTypes.map((program) => (
                      <option key={program} value={program}>
                        {program}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Language Filter */}
                <div>
                  <label className="block text-sm font-medium mb-2">
                    <Globe className="w-4 h-4 inline mr-1" />
                    Language
                  </label>
                  <select
                    value={selectedLanguage}
                    onChange={(e) => setSelectedLanguage(e.target.value)}
                    className="w-full p-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                  >
                    {languages.map((language) => (
                      <option key={language} value={language}>
                        {language}
                      </option>
                    ))}
                  </select>
                </div>
              </motion.div>
            )}

            {/* Clear Filters */}
            {showFilters && (
              <div className="mt-4 flex justify-end">
                <Button variant="ghost" onClick={clearFilters} size="sm">
                  Clear All Filters
                </Button>
              </div>
            )}
          </div>

          {/* Quick Filter Tags */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={inView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="flex flex-wrap gap-2 justify-center"
          >
            <span className="text-sm text-muted-foreground mr-2">Popular filters:</span>
            {['Engineering Programs', 'Under $5,000', 'English Taught', 'Nicosia'].map((tag) => (
              <button
                key={tag}
                className="px-3 py-1 bg-primary/10 text-primary rounded-full text-sm hover:bg-primary/20 transition-colors"
              >
                {tag}
              </button>
            ))}
          </motion.div>
        </motion.div>
      </div>
    </section>
  )
}
