[{"id": "contact001", "inquiryId": "FG-INQ-001", "createdAt": "2025-07-07T13:02:47.401Z", "updatedAt": "2025-07-08T13:02:47.401Z", "name": "<PERSON>", "email": "<EMAIL>", "phone": "****** 123 4567", "subject": "Information about Engineering Programs", "message": "I'm interested in learning more about your engineering programs and admission requirements.", "preferredContact": "email", "interestedServices": "[\"University Admissions\",\"Academic Support\"]", "status": "RESPONDED", "assignedAgent": "<PERSON>", "responseNotes": "Sent detailed program information", "respondedAt": "2025-07-08T13:02:47.401Z"}, {"id": "contact002", "inquiryId": "FG-INQ-002", "createdAt": "2025-07-09T13:02:47.401Z", "updatedAt": "2025-07-09T13:02:47.401Z", "name": "<PERSON>", "email": "<EMAIL>", "phone": "+82 10 1234 5678", "subject": "Visa Support Services", "message": "I need help with student visa application process for Northern Cyprus.", "preferredContact": "phone", "interestedServices": "[\"Visa Support\",\"Document Translation\"]", "status": "IN_PROGRESS", "assignedAgent": "<PERSON>", "responseNotes": "Scheduled consultation call", "respondedAt": null}]