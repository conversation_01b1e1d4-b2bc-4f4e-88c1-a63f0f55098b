[{"id": "contact001", "inquiryId": "FG-INQ-001", "createdAt": "2025-07-07T13:02:47.401Z", "updatedAt": "2025-07-08T13:02:47.401Z", "name": "<PERSON>", "email": "<EMAIL>", "phone": "****** 123 4567", "subject": "Information about Engineering Programs", "message": "I'm interested in learning more about your engineering programs and admission requirements.", "preferredContact": "email", "interestedServices": "[\"University Admissions\",\"Academic Support\"]", "status": "RESPONDED", "assignedAgent": "<PERSON>", "responseNotes": "Sent detailed program information", "respondedAt": "2025-07-08T13:02:47.401Z"}, {"id": "contact002", "inquiryId": "FG-INQ-002", "createdAt": "2025-07-09T13:02:47.401Z", "updatedAt": "2025-07-09T13:02:47.401Z", "name": "<PERSON>", "email": "<EMAIL>", "phone": "+82 10 1234 5678", "subject": "Visa Support Services", "message": "I need help with student visa application process for Northern Cyprus.", "preferredContact": "phone", "interestedServices": "[\"Visa Support\",\"Document Translation\"]", "status": "IN_PROGRESS", "assignedAgent": "<PERSON>", "responseNotes": "Scheduled consultation call", "respondedAt": null}, {"id": "tlfnv3rz7", "inquiryId": "FG-1752242115095-FFKDJLLTF", "createdAt": "2025-07-11T13:55:15.095Z", "updatedAt": "2025-07-11T13:55:15.095Z", "name": "Admin Test User", "email": "<EMAIL>", "phone": null, "subject": "Testing Admin Contact System", "message": "This is a test message to verify the admin contact system is working properly.", "preferredContact": "email", "interestedServices": "[\"University Admissions\",\"Visa Support\"]", "status": "NEW", "assignedAgent": null, "responseNotes": null, "respondedAt": null}, {"id": "8yn21aek3", "inquiryId": "FG-1752242335071-OV317DB4Z", "createdAt": "2025-07-11T13:58:55.071Z", "updatedAt": "2025-07-11T13:58:55.071Z", "name": "nidhal", "email": "<EMAIL>", "phone": "+216 93569167", "subject": "hhh", "message": "hi just test", "preferredContact": "email", "interestedServices": "[\"Visa Support\",\"University Admissions\"]", "status": "NEW", "assignedAgent": null, "responseNotes": null, "respondedAt": null}, {"id": "0voxqd0gp", "inquiryId": "FG-1752243673795-BHZ5X2TZK", "createdAt": "2025-07-11T14:21:13.795Z", "updatedAt": "2025-07-11T14:21:13.795Z", "name": "omar", "email": "<EMAIL>", "phone": "+216 93569167", "subject": "test", "message": "just test", "preferredContact": "whatsapp", "interestedServices": "[\"Document Translation\",\"Student Housing\"]", "status": "NEW", "assignedAgent": null, "responseNotes": null, "respondedAt": null}, {"id": "phhs0cbpq", "inquiryId": "FG-1752244938378-1GS8088W9", "createdAt": "2025-07-11T14:42:18.378Z", "updatedAt": "2025-07-11T14:42:18.379Z", "name": "Security Test User", "email": "<EMAIL>", "phone": null, "subject": "Testing HTTPS Security API", "message": "This is a comprehensive test of the secure HTTPS API endpoint with all security features enabled.", "preferredContact": "email", "interestedServices": "[]", "status": "NEW", "assignedAgent": null, "responseNotes": null, "respondedAt": null}]