import { notFound } from 'next/navigation'
import { UniversityDetailHero } from '@/components/sections/university-detail-hero'
import { UniversityDetailContent } from '@/components/sections/university-detail-content'
import { UniversityProgramsSection } from '@/components/sections/university-programs'
import { UniversityGallerySection } from '@/components/sections/university-gallery'
import { WhatsAppWidget } from '@/components/ui/whatsapp-widget'
import { mockUniversities } from '@/lib/mock-data'

interface UniversityPageProps {
  params: {
    slug: string
  }
}

export async function generateMetadata({ params }: UniversityPageProps) {
  const university = mockUniversities.find(u => u.slug === params.slug)
  
  if (!university) {
    return {
      title: 'University Not Found - Foreingate Group'
    }
  }

  return {
    title: `${university.name} - Foreingate Group`,
    description: university.description,
    keywords: [university.name, 'Northern Cyprus', 'university', 'study abroad'],
  }
}

export default function UniversityPage({ params }: UniversityPageProps) {
  const university = mockUniversities.find(u => u.slug === params.slug)

  if (!university) {
    notFound()
  }

  return (
    <>
      <UniversityDetailHero university={university} />
      <UniversityDetailContent university={university} />
      <UniversityProgramsSection university={university} />
      <UniversityGallerySection university={university} />
      
      {/* WhatsApp Widget */}
      <WhatsAppWidget 
        phoneNumber="905392123456"
        message={`Hello! I'm interested in learning more about ${university.name}.`}
      />
    </>
  )
}

// Generate static params for all universities
export async function generateStaticParams() {
  return mockUniversities.map((university) => ({
    slug: university.slug,
  }))
}
