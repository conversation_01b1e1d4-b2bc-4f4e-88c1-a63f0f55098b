/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/about/page",{

/***/ "(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/circle-check-big.js":
/*!***********************************************************************!*\
  !*** ../node_modules/lucide-react/dist/esm/icons/circle-check-big.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ CircleCheckBig)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M21.801 10A10 10 0 1 1 17 3.335\",\n            key: \"yps3ct\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m9 11 3 3L22 4\",\n            key: \"1pflzl\"\n        }\n    ]\n];\nconst CircleCheckBig = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"circle-check-big\", __iconNode);\n //# sourceMappingURL=circle-check-big.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/circle-check-big.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/headphones.js":
/*!*****************************************************************!*\
  !*** ../node_modules/lucide-react/dist/esm/icons/headphones.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Headphones)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M3 14h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-7a9 9 0 0 1 18 0v7a2 2 0 0 1-2 2h-1a2 2 0 0 1-2-2v-3a2 2 0 0 1 2-2h3\",\n            key: \"1xhozi\"\n        }\n    ]\n];\nconst Headphones = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"headphones\", __iconNode);\n //# sourceMappingURL=headphones.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/headphones.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/linkedin.js":
/*!***************************************************************!*\
  !*** ../node_modules/lucide-react/dist/esm/icons/linkedin.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Linkedin)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z\",\n            key: \"c2jq9f\"\n        }\n    ],\n    [\n        \"rect\",\n        {\n            width: \"4\",\n            height: \"12\",\n            x: \"2\",\n            y: \"9\",\n            key: \"mk3on5\"\n        }\n    ],\n    [\n        \"circle\",\n        {\n            cx: \"4\",\n            cy: \"4\",\n            r: \"2\",\n            key: \"bt5ra8\"\n        }\n    ]\n];\nconst Linkedin = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"linkedin\", __iconNode);\n //# sourceMappingURL=linkedin.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/linkedin.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/mail.js":
/*!***********************************************************!*\
  !*** ../node_modules/lucide-react/dist/esm/icons/mail.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Mail)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7\",\n            key: \"132q7q\"\n        }\n    ],\n    [\n        \"rect\",\n        {\n            x: \"2\",\n            y: \"4\",\n            width: \"20\",\n            height: \"16\",\n            rx: \"2\",\n            key: \"izxlao\"\n        }\n    ]\n];\nconst Mail = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"mail\", __iconNode);\n //# sourceMappingURL=mail.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/mail.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/phone.js":
/*!************************************************************!*\
  !*** ../node_modules/lucide-react/dist/esm/icons/phone.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Phone)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384\",\n            key: \"9njp5v\"\n        }\n    ]\n];\nconst Phone = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"phone\", __iconNode);\n //# sourceMappingURL=phone.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/phone.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/shield.js":
/*!*************************************************************!*\
  !*** ../node_modules/lucide-react/dist/esm/icons/shield.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Shield)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z\",\n            key: \"oel41y\"\n        }\n    ]\n];\nconst Shield = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"shield\", __iconNode);\n //# sourceMappingURL=shield.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/shield.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/trending-up.js":
/*!******************************************************************!*\
  !*** ../node_modules/lucide-react/dist/esm/icons/trending-up.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ TrendingUp)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M16 7h6v6\",\n            key: \"box55l\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m22 7-8.5 8.5-5-5L2 17\",\n            key: \"1t1m79\"\n        }\n    ]\n];\nconst TrendingUp = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"trending-up\", __iconNode);\n //# sourceMappingURL=trending-up.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/trending-up.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5Ccompany-story.tsx%22%2C%22ids%22%3A%5B%22CompanyStorySection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5Cteam-section.tsx%22%2C%22ids%22%3A%5B%22TeamSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5Cvision-mission.tsx%22%2C%22ids%22%3A%5B%22VisionMissionSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5Cwhy-choose-us.tsx%22%2C%22ids%22%3A%5B%22WhyChooseUsSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Cwhatsapp-widget.tsx%22%2C%22ids%22%3A%5B%22WhatsAppWidget%22%5D%7D&server=false!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5Ccompany-story.tsx%22%2C%22ids%22%3A%5B%22CompanyStorySection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5Cteam-section.tsx%22%2C%22ids%22%3A%5B%22TeamSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5Cvision-mission.tsx%22%2C%22ids%22%3A%5B%22VisionMissionSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5Cwhy-choose-us.tsx%22%2C%22ids%22%3A%5B%22WhyChooseUsSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Cwhatsapp-widget.tsx%22%2C%22ids%22%3A%5B%22WhatsAppWidget%22%5D%7D&server=false! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/sections/company-story.tsx */ \"(app-pages-browser)/./src/components/sections/company-story.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/sections/team-section.tsx */ \"(app-pages-browser)/./src/components/sections/team-section.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/sections/vision-mission.tsx */ \"(app-pages-browser)/./src/components/sections/vision-mission.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/sections/why-choose-us.tsx */ \"(app-pages-browser)/./src/components/sections/why-choose-us.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/whatsapp-widget.tsx */ \"(app-pages-browser)/./src/components/ui/whatsapp-widget.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDTmlkaGFsJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q2ZvcmVpbmdhdGVfZ3JvdXBlJTVDJTVDZm9yZWluZ2F0ZS13ZWJzaXRlJTVDJTVDc3JjJTVDJTVDY29tcG9uZW50cyU1QyU1Q3NlY3Rpb25zJTVDJTVDY29tcGFueS1zdG9yeS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJDb21wYW55U3RvcnlTZWN0aW9uJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q05pZGhhbCU1QyU1Q0RvY3VtZW50cyU1QyU1Q2F1Z21lbnQtcHJvamVjdHMlNUMlNUNmb3JlaW5nYXRlX2dyb3VwZSU1QyU1Q2ZvcmVpbmdhdGUtd2Vic2l0ZSU1QyU1Q3NyYyU1QyU1Q2NvbXBvbmVudHMlNUMlNUNzZWN0aW9ucyU1QyU1Q3RlYW0tc2VjdGlvbi50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJUZWFtU2VjdGlvbiUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNOaWRoYWwlNUMlNUNEb2N1bWVudHMlNUMlNUNhdWdtZW50LXByb2plY3RzJTVDJTVDZm9yZWluZ2F0ZV9ncm91cGUlNUMlNUNmb3JlaW5nYXRlLXdlYnNpdGUlNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDc2VjdGlvbnMlNUMlNUN2aXNpb24tbWlzc2lvbi50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJWaXNpb25NaXNzaW9uU2VjdGlvbiUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNOaWRoYWwlNUMlNUNEb2N1bWVudHMlNUMlNUNhdWdtZW50LXByb2plY3RzJTVDJTVDZm9yZWluZ2F0ZV9ncm91cGUlNUMlNUNmb3JlaW5nYXRlLXdlYnNpdGUlNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDc2VjdGlvbnMlNUMlNUN3aHktY2hvb3NlLXVzLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMldoeUNob29zZVVzU2VjdGlvbiUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNOaWRoYWwlNUMlNUNEb2N1bWVudHMlNUMlNUNhdWdtZW50LXByb2plY3RzJTVDJTVDZm9yZWluZ2F0ZV9ncm91cGUlNUMlNUNmb3JlaW5nYXRlLXdlYnNpdGUlNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDdWklNUMlNUN3aGF0c2FwcC13aWRnZXQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyV2hhdHNBcHBXaWRnZXQlMjIlNUQlN0Qmc2VydmVyPWZhbHNlISIsIm1hcHBpbmdzIjoiQUFBQSxnTkFBaU47QUFDak47QUFDQSw4TUFBd007QUFDeE07QUFDQSxrTkFBbU47QUFDbk47QUFDQSxnTkFBZ047QUFDaE47QUFDQSx3TUFBd00iLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIkNvbXBhbnlTdG9yeVNlY3Rpb25cIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxOaWRoYWxcXFxcRG9jdW1lbnRzXFxcXGF1Z21lbnQtcHJvamVjdHNcXFxcZm9yZWluZ2F0ZV9ncm91cGVcXFxcZm9yZWluZ2F0ZS13ZWJzaXRlXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXHNlY3Rpb25zXFxcXGNvbXBhbnktc3RvcnkudHN4XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJUZWFtU2VjdGlvblwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXE5pZGhhbFxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxmb3JlaW5nYXRlX2dyb3VwZVxcXFxmb3JlaW5nYXRlLXdlYnNpdGVcXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxcc2VjdGlvbnNcXFxcdGVhbS1zZWN0aW9uLnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiVmlzaW9uTWlzc2lvblNlY3Rpb25cIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxOaWRoYWxcXFxcRG9jdW1lbnRzXFxcXGF1Z21lbnQtcHJvamVjdHNcXFxcZm9yZWluZ2F0ZV9ncm91cGVcXFxcZm9yZWluZ2F0ZS13ZWJzaXRlXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXHNlY3Rpb25zXFxcXHZpc2lvbi1taXNzaW9uLnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiV2h5Q2hvb3NlVXNTZWN0aW9uXCJdICovIFwiQzpcXFxcVXNlcnNcXFxcTmlkaGFsXFxcXERvY3VtZW50c1xcXFxhdWdtZW50LXByb2plY3RzXFxcXGZvcmVpbmdhdGVfZ3JvdXBlXFxcXGZvcmVpbmdhdGUtd2Vic2l0ZVxcXFxzcmNcXFxcY29tcG9uZW50c1xcXFxzZWN0aW9uc1xcXFx3aHktY2hvb3NlLXVzLnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiV2hhdHNBcHBXaWRnZXRcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxOaWRoYWxcXFxcRG9jdW1lbnRzXFxcXGF1Z21lbnQtcHJvamVjdHNcXFxcZm9yZWluZ2F0ZV9ncm91cGVcXFxcZm9yZWluZ2F0ZS13ZWJzaXRlXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXHVpXFxcXHdoYXRzYXBwLXdpZGdldC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5Ccompany-story.tsx%22%2C%22ids%22%3A%5B%22CompanyStorySection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5Cteam-section.tsx%22%2C%22ids%22%3A%5B%22TeamSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5Cvision-mission.tsx%22%2C%22ids%22%3A%5B%22VisionMissionSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5Cwhy-choose-us.tsx%22%2C%22ids%22%3A%5B%22WhyChooseUsSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Cwhatsapp-widget.tsx%22%2C%22ids%22%3A%5B%22WhatsAppWidget%22%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/sections/team-section.tsx":
/*!**************************************************!*\
  !*** ./src/components/sections/team-section.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TeamSection: () => (/* binding */ TeamSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/../node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var react_intersection_observer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-intersection-observer */ \"(app-pages-browser)/../node_modules/react-intersection-observer/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Linkedin_Mail_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Linkedin,Mail,Phone!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Linkedin_Mail_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Linkedin,Mail,Phone!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Linkedin_Mail_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Linkedin,Mail,Phone!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/linkedin.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* __next_internal_client_entry_do_not_use__ TeamSection auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n// Extended mock team data for the About page\nconst extendedTeamMembers = [\n    {\n        id: '1',\n        name: 'Dr. Michael Thompson',\n        position: 'CEO & Founder',\n        image: '/images/team/michael.jpg',\n        bio: 'With over 15 years of experience in international education, Dr. Thompson founded Foreingate Group to help students achieve their academic dreams. He holds a PhD in Educational Leadership and has personally guided thousands of students.',\n        email: '<EMAIL>',\n        phone: '+90 ************',\n        linkedin: 'https://linkedin.com/in/michael-thompson',\n        languages: [\n            'English',\n            'Turkish',\n            'Arabic'\n        ],\n        specializations: [\n            'University Admissions',\n            'Educational Consulting',\n            'International Relations'\n        ]\n    },\n    {\n        id: '2',\n        name: 'Sarah Johnson',\n        position: 'Director of Student Services',\n        image: '/images/team/sarah.jpg',\n        bio: 'Sarah brings 12 years of experience in student counseling and has helped over 2,000 students navigate their educational journey. She specializes in personalized guidance and career counseling.',\n        email: '<EMAIL>',\n        phone: '+90 ************',\n        linkedin: 'https://linkedin.com/in/sarah-johnson',\n        languages: [\n            'English',\n            'French',\n            'Spanish'\n        ],\n        specializations: [\n            'Student Counseling',\n            'Career Guidance',\n            'Program Selection'\n        ]\n    },\n    {\n        id: '3',\n        name: 'Ahmed Al-Rashid',\n        position: 'Visa & Immigration Specialist',\n        image: '/images/team/ahmed.jpg',\n        bio: 'Ahmed is our visa expert with 10 years of experience in immigration law and procedures. He has achieved a 98% visa success rate and specializes in complex immigration cases.',\n        email: '<EMAIL>',\n        phone: '+90 ************',\n        linkedin: 'https://linkedin.com/in/ahmed-alrashid',\n        languages: [\n            'Arabic',\n            'English',\n            'Turkish'\n        ],\n        specializations: [\n            'Visa Processing',\n            'Immigration Law',\n            'Document Verification'\n        ]\n    },\n    {\n        id: '4',\n        name: 'Elena Rodriguez',\n        position: 'Scholarship Coordinator',\n        image: '/images/team/elena.jpg',\n        bio: 'Elena has secured over $2 million in scholarships for our students. With her background in financial aid and scholarship programs, she helps make education affordable for everyone.',\n        email: '<EMAIL>',\n        phone: '+90 ************',\n        linkedin: 'https://linkedin.com/in/elena-rodriguez',\n        languages: [\n            'Spanish',\n            'English',\n            'Portuguese'\n        ],\n        specializations: [\n            'Scholarship Applications',\n            'Financial Aid',\n            'Grant Writing'\n        ]\n    },\n    {\n        id: '5',\n        name: 'David Chen',\n        position: 'Technology & Innovation Lead',\n        image: '/images/team/david.jpg',\n        bio: 'David leads our digital transformation initiatives and develops innovative solutions to enhance the student experience. He has a background in EdTech and software development.',\n        email: '<EMAIL>',\n        phone: '+90 ************',\n        linkedin: 'https://linkedin.com/in/david-chen',\n        languages: [\n            'English',\n            'Mandarin',\n            'Japanese'\n        ],\n        specializations: [\n            'EdTech Solutions',\n            'Digital Innovation',\n            'Student Platforms'\n        ]\n    },\n    {\n        id: '6',\n        name: 'Fatima Hassan',\n        position: 'Regional Manager - Middle East',\n        image: '/images/team/fatima.jpg',\n        bio: 'Fatima oversees our operations in the Middle East region and has deep understanding of the educational needs of students from this region. She has 8 years of experience in international education.',\n        email: '<EMAIL>',\n        phone: '+90 ************',\n        linkedin: 'https://linkedin.com/in/fatima-hassan',\n        languages: [\n            'Arabic',\n            'English',\n            'French'\n        ],\n        specializations: [\n            'Regional Operations',\n            'Cultural Adaptation',\n            'Student Support'\n        ]\n    }\n];\nfunction TeamSection() {\n    _s();\n    const [ref, inView] = (0,react_intersection_observer__WEBPACK_IMPORTED_MODULE_2__.useInView)({\n        triggerOnce: true,\n        threshold: 0.1\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        ref: ref,\n        className: \"section-padding bg-muted/30\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    animate: inView ? {\n                        opacity: 1,\n                        y: 0\n                    } : {},\n                    transition: {\n                        duration: 0.8\n                    },\n                    className: \"text-center max-w-3xl mx-auto mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold mb-4\",\n                            children: [\n                                \"Meet Our\",\n                                ' ',\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"gradient-text\",\n                                    children: \"Expert Team\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\team-section.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\team-section.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-muted-foreground\",\n                            children: \"Dedicated professionals committed to your educational success, bringing decades of combined experience in international education\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\team-section.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\team-section.tsx\",\n                    lineNumber: 95,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                    children: extendedTeamMembers.map((member, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            animate: inView ? {\n                                opacity: 1,\n                                y: 0\n                            } : {},\n                            transition: {\n                                duration: 0.6,\n                                delay: index * 0.1\n                            },\n                            className: \"bg-background rounded-xl overflow-hidden shadow-sm border hover:shadow-lg transition-all duration-300 group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative h-64 overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: member.image,\n                                            alt: member.name,\n                                            className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\",\n                                            onError: (e)=>{\n                                                e.currentTarget.src = \"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='300' height='300' viewBox='0 0 300 300'%3E%3Crect width='300' height='300' fill='%23f3f4f6'/%3E%3Ctext x='50%25' y='50%25' dominant-baseline='middle' text-anchor='middle' font-family='Arial, sans-serif' font-size='60' fill='%236b7280'%3E\".concat(member.name.charAt(0), \"%3C/text%3E%3C/svg%3E\");\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\team-section.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\team-section.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\team-section.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold mb-1\",\n                                            children: member.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\team-section.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-primary font-medium mb-3\",\n                                            children: member.position\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\team-section.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-muted-foreground text-sm leading-relaxed mb-4\",\n                                            children: member.bio\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\team-section.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-sm font-medium mb-2\",\n                                                    children: \"Languages:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\team-section.tsx\",\n                                                    lineNumber: 143,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-wrap gap-1\",\n                                                    children: member.languages.map((language)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs bg-muted px-2 py-1 rounded-full\",\n                                                            children: language\n                                                        }, language, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\team-section.tsx\",\n                                                            lineNumber: 146,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\team-section.tsx\",\n                                                    lineNumber: 144,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\team-section.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-sm font-medium mb-2\",\n                                                    children: \"Specializations:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\team-section.tsx\",\n                                                    lineNumber: 158,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-wrap gap-1\",\n                                                    children: member.specializations.map((spec)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs bg-primary/10 text-primary px-2 py-1 rounded-full\",\n                                                            children: spec\n                                                        }, spec, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\team-section.tsx\",\n                                                            lineNumber: 161,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\team-section.tsx\",\n                                                    lineNumber: 159,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\team-section.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 pt-4 border-t\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"mailto:\".concat(member.email),\n                                                    className: \"text-muted-foreground hover:text-primary transition-colors\",\n                                                    title: \"Email\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Linkedin_Mail_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\team-section.tsx\",\n                                                        lineNumber: 178,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\team-section.tsx\",\n                                                    lineNumber: 173,\n                                                    columnNumber: 19\n                                                }, this),\n                                                member.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"tel:\".concat(member.phone),\n                                                    className: \"text-muted-foreground hover:text-primary transition-colors\",\n                                                    title: \"Phone\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Linkedin_Mail_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\team-section.tsx\",\n                                                        lineNumber: 186,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\team-section.tsx\",\n                                                    lineNumber: 181,\n                                                    columnNumber: 21\n                                                }, this),\n                                                member.linkedin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: member.linkedin,\n                                                    target: \"_blank\",\n                                                    rel: \"noopener noreferrer\",\n                                                    className: \"text-muted-foreground hover:text-primary transition-colors\",\n                                                    title: \"LinkedIn\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Linkedin_Mail_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\team-section.tsx\",\n                                                        lineNumber: 197,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\team-section.tsx\",\n                                                    lineNumber: 190,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\team-section.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\team-section.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, member.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\team-section.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\team-section.tsx\",\n                    lineNumber: 111,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    animate: inView ? {\n                        opacity: 1,\n                        y: 0\n                    } : {},\n                    transition: {\n                        duration: 0.8,\n                        delay: 0.6\n                    },\n                    className: \"mt-16 bg-gradient-to-r from-primary/5 to-secondary/5 rounded-2xl p-8 md:p-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-2xl md:text-3xl font-bold mb-4\",\n                                    children: \"Our Team's Collective Impact\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\team-section.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-muted-foreground max-w-2xl mx-auto\",\n                                    children: \"Together, our team brings decades of experience and a passion for student success\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\team-section.tsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\team-section.tsx\",\n                            lineNumber: 213,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 md:grid-cols-4 gap-6 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl font-bold text-primary mb-2\",\n                                            children: \"80+\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\team-section.tsx\",\n                                            lineNumber: 222,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: \"Years Combined Experience\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\team-section.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\team-section.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl font-bold text-primary mb-2\",\n                                            children: \"15+\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\team-section.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: \"Languages Spoken\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\team-section.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\team-section.tsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl font-bold text-primary mb-2\",\n                                            children: \"25+\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\team-section.tsx\",\n                                            lineNumber: 230,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: \"Countries Represented\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\team-section.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\team-section.tsx\",\n                                    lineNumber: 229,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl font-bold text-primary mb-2\",\n                                            children: \"100%\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\team-section.tsx\",\n                                            lineNumber: 234,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: \"Dedicated to Your Success\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\team-section.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\team-section.tsx\",\n                                    lineNumber: 233,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\team-section.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\team-section.tsx\",\n                    lineNumber: 207,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    animate: inView ? {\n                        opacity: 1,\n                        y: 0\n                    } : {},\n                    transition: {\n                        duration: 0.8,\n                        delay: 0.8\n                    },\n                    className: \"text-center mt-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-2xl font-bold mb-4\",\n                            children: \"Ready to Work with Our Team?\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\team-section.tsx\",\n                            lineNumber: 247,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground mb-6 max-w-2xl mx-auto\",\n                            children: \"Schedule a free consultation with one of our experts and take the first step toward your educational goals.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\team-section.tsx\",\n                            lineNumber: 248,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                            size: \"lg\",\n                            asChild: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"/contact\",\n                                children: \"Schedule Free Consultation\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\team-section.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\team-section.tsx\",\n                            lineNumber: 251,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\team-section.tsx\",\n                    lineNumber: 241,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\team-section.tsx\",\n            lineNumber: 93,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\team-section.tsx\",\n        lineNumber: 92,\n        columnNumber: 5\n    }, this);\n}\n_s(TeamSection, \"GpcLnEGLCRT/LcXgsVwPMCbjDPg=\", false, function() {\n    return [\n        react_intersection_observer__WEBPACK_IMPORTED_MODULE_2__.useInView\n    ];\n});\n_c = TeamSection;\nvar _c;\n$RefreshReg$(_c, \"TeamSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/team-section.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/sections/why-choose-us.tsx":
/*!***************************************************!*\
  !*** ./src/components/sections/why-choose-us.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WhyChooseUsSection: () => (/* binding */ WhyChooseUsSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/../node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var react_intersection_observer__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-intersection-observer */ \"(app-pages-browser)/../node_modules/react-intersection-observer/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_Globe_HeadphonesIcon_Shield_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,Globe,HeadphonesIcon,Shield,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_Globe_HeadphonesIcon_Shield_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,Globe,HeadphonesIcon,Shield,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_Globe_HeadphonesIcon_Shield_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,Globe,HeadphonesIcon,Shield,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_Globe_HeadphonesIcon_Shield_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,Globe,HeadphonesIcon,Shield,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/headphones.js\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_Globe_HeadphonesIcon_Shield_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,Globe,HeadphonesIcon,Shield,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_Globe_HeadphonesIcon_Shield_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,Globe,HeadphonesIcon,Shield,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_Globe_HeadphonesIcon_Shield_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,Globe,HeadphonesIcon,Shield,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* __next_internal_client_entry_do_not_use__ WhyChooseUsSection auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst reasons = [\n    {\n        icon: _barrel_optimize_names_Award_CheckCircle_Globe_HeadphonesIcon_Shield_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n        title: 'Trusted Expertise',\n        description: 'Nearly a decade of experience in international education consulting with a proven track record of success.',\n        stats: '98% Success Rate'\n    },\n    {\n        icon: _barrel_optimize_names_Award_CheckCircle_Globe_HeadphonesIcon_Shield_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        title: 'Personalized Service',\n        description: 'One-on-one guidance tailored to your unique academic goals, background, and aspirations.',\n        stats: '1:1 Counseling'\n    },\n    {\n        icon: _barrel_optimize_names_Award_CheckCircle_Globe_HeadphonesIcon_Shield_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        title: 'Global Network',\n        description: 'Strong partnerships with 25+ universities and connections across 50+ countries worldwide.',\n        stats: '25+ Partner Universities'\n    },\n    {\n        icon: _barrel_optimize_names_Award_CheckCircle_Globe_HeadphonesIcon_Shield_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        title: '24/7 Support',\n        description: 'Round-the-clock assistance throughout your entire educational journey, from application to graduation.',\n        stats: 'Always Available'\n    },\n    {\n        icon: _barrel_optimize_names_Award_CheckCircle_Globe_HeadphonesIcon_Shield_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        title: 'Scholarship Assistance',\n        description: 'Expert guidance in securing scholarships and financial aid to make education more affordable.',\n        stats: '$2M+ Scholarships Secured'\n    },\n    {\n        icon: _barrel_optimize_names_Award_CheckCircle_Globe_HeadphonesIcon_Shield_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        title: 'Career Guidance',\n        description: 'Comprehensive career counseling to help you choose programs that align with your professional goals.',\n        stats: '90% Career Success'\n    }\n];\nconst achievements = [\n    {\n        number: '5,000+',\n        label: 'Students Successfully Placed'\n    },\n    {\n        number: '25+',\n        label: 'Partner Universities'\n    },\n    {\n        number: '50+',\n        label: 'Countries Served'\n    },\n    {\n        number: '98%',\n        label: 'Visa Success Rate'\n    },\n    {\n        number: '$2M+',\n        label: 'Scholarships Secured'\n    },\n    {\n        number: '24/7',\n        label: 'Student Support'\n    }\n];\nfunction WhyChooseUsSection() {\n    _s();\n    const [ref, inView] = (0,react_intersection_observer__WEBPACK_IMPORTED_MODULE_7__.useInView)({\n        triggerOnce: true,\n        threshold: 0.1\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        ref: ref,\n        className: \"section-padding\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    animate: inView ? {\n                        opacity: 1,\n                        y: 0\n                    } : {},\n                    transition: {\n                        duration: 0.8\n                    },\n                    className: \"text-center max-w-3xl mx-auto mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold mb-4\",\n                            children: [\n                                \"Why Choose\",\n                                ' ',\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"gradient-text\",\n                                    children: \"Foreingate Group\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\why-choose-us.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\why-choose-us.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-muted-foreground\",\n                            children: \"Discover what sets us apart and makes us the preferred choice for thousands of students worldwide\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\why-choose-us.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\why-choose-us.tsx\",\n                    lineNumber: 74,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16\",\n                    children: reasons.map((reason, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            animate: inView ? {\n                                opacity: 1,\n                                y: 0\n                            } : {},\n                            transition: {\n                                duration: 0.6,\n                                delay: index * 0.1\n                            },\n                            className: \"bg-background rounded-xl p-6 shadow-sm border hover:shadow-lg transition-all duration-300 hover:border-primary/20 group\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0 group-hover:bg-primary/20 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(reason.icon, {\n                                            className: \"w-6 h-6 text-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\why-choose-us.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\why-choose-us.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold mb-2 group-hover:text-primary transition-colors\",\n                                                children: reason.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\why-choose-us.tsx\",\n                                                lineNumber: 104,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-muted-foreground text-sm leading-relaxed mb-3\",\n                                                children: reason.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\why-choose-us.tsx\",\n                                                lineNumber: 107,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"inline-flex items-center text-xs font-medium text-primary bg-primary/10 px-2 py-1 rounded-full\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_Globe_HeadphonesIcon_Shield_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"w-3 h-3 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\why-choose-us.tsx\",\n                                                        lineNumber: 111,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    reason.stats\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\why-choose-us.tsx\",\n                                                lineNumber: 110,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\why-choose-us.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\why-choose-us.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 15\n                            }, this)\n                        }, reason.title, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\why-choose-us.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\why-choose-us.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    animate: inView ? {\n                        opacity: 1,\n                        y: 0\n                    } : {},\n                    transition: {\n                        duration: 0.8,\n                        delay: 0.6\n                    },\n                    className: \"bg-gradient-to-r from-primary/5 to-secondary/5 rounded-2xl p-8 md:p-12 mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-2xl md:text-3xl font-bold mb-4\",\n                                    children: \"Our Achievements Speak for Themselves\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\why-choose-us.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-muted-foreground max-w-2xl mx-auto\",\n                                    children: \"Numbers that reflect our commitment to excellence and student success\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\why-choose-us.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\why-choose-us.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6\",\n                            children: achievements.map((achievement, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        scale: 0.8\n                                    },\n                                    animate: inView ? {\n                                        opacity: 1,\n                                        scale: 1\n                                    } : {},\n                                    transition: {\n                                        duration: 0.5,\n                                        delay: 0.8 + index * 0.1\n                                    },\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl md:text-3xl font-bold text-primary mb-2\",\n                                            children: achievement.number\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\why-choose-us.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: achievement.label\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\why-choose-us.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, achievement.label, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\why-choose-us.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\why-choose-us.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\why-choose-us.tsx\",\n                    lineNumber: 121,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    animate: inView ? {\n                        opacity: 1,\n                        y: 0\n                    } : {},\n                    transition: {\n                        duration: 0.8,\n                        delay: 0.8\n                    },\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-2xl md:text-3xl font-bold mb-6\",\n                            children: \"Our Proven Process\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\why-choose-us.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground max-w-3xl mx-auto mb-8\",\n                            children: \"We've refined our approach over years of experience to ensure the smoothest possible journey for our students\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\why-choose-us.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-4 gap-6\",\n                            children: [\n                                {\n                                    step: '01',\n                                    title: 'Consultation',\n                                    desc: 'Free initial assessment of your goals and profile'\n                                },\n                                {\n                                    step: '02',\n                                    title: 'Planning',\n                                    desc: 'Customized roadmap with university and program selection'\n                                },\n                                {\n                                    step: '03',\n                                    title: 'Application',\n                                    desc: 'Complete application support and document preparation'\n                                },\n                                {\n                                    step: '04',\n                                    title: 'Success',\n                                    desc: 'Visa assistance and pre-departure orientation'\n                                }\n                            ].map((process, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: inView ? {\n                                        opacity: 1,\n                                        y: 0\n                                    } : {},\n                                    transition: {\n                                        duration: 0.5,\n                                        delay: 1 + index * 0.1\n                                    },\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-primary text-primary-foreground rounded-full flex items-center justify-center font-bold text-lg mx-auto mb-4\",\n                                            children: process.step\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\why-choose-us.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold mb-2\",\n                                            children: process.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\why-choose-us.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: process.desc\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\why-choose-us.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 17\n                                        }, this),\n                                        index < 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"hidden md:block absolute top-8 left-full w-full h-0.5 bg-gradient-to-r from-primary to-transparent -translate-y-1/2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\why-choose-us.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, process.step, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\why-choose-us.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\why-choose-us.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\why-choose-us.tsx\",\n                    lineNumber: 155,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\why-choose-us.tsx\",\n            lineNumber: 72,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\why-choose-us.tsx\",\n        lineNumber: 71,\n        columnNumber: 5\n    }, this);\n}\n_s(WhyChooseUsSection, \"GpcLnEGLCRT/LcXgsVwPMCbjDPg=\", false, function() {\n    return [\n        react_intersection_observer__WEBPACK_IMPORTED_MODULE_7__.useInView\n    ];\n});\n_c = WhyChooseUsSection;\nvar _c;\n$RefreshReg$(_c, \"WhyChooseUsSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/why-choose-us.tsx\n"));

/***/ })

});