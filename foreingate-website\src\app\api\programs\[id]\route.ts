import { NextRequest, NextResponse } from 'next/server'
import { readFile } from 'fs/promises'
import { existsSync } from 'fs'
import path from 'path'

const DATA_DIR = path.join(process.cwd(), 'data')
const PROGRAMS_FILE = path.join(DATA_DIR, 'programs.json')

async function readPrograms() {
  try {
    if (!existsSync(PROGRAMS_FILE)) {
      return []
    }
    const data = await readFile(PROGRAMS_FILE, 'utf-8')
    return JSON.parse(data)
  } catch (error) {
    console.error('Error reading programs:', error)
    return []
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const programs = await readPrograms()
    const program = programs.find((prog: any) => 
      prog.id === params.id || prog.slug === params.id
    )

    if (!program) {
      return NextResponse.json(
        { success: false, error: 'Program not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: program
    })

  } catch (error) {
    console.error('Program API error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch program' },
      { status: 500 }
    )
  }
}
