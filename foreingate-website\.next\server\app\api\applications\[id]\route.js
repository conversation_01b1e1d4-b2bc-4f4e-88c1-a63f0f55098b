/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/applications/[id]/route";
exports.ids = ["app/api/applications/[id]/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fapplications%2F%5Bid%5D%2Froute&page=%2Fapi%2Fapplications%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fapplications%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fapplications%2F%5Bid%5D%2Froute&page=%2Fapi%2Fapplications%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fapplications%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Nidhal_Documents_augment_projects_foreingate_groupe_foreingate_website_src_app_api_applications_id_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/applications/[id]/route.ts */ \"(rsc)/./src/app/api/applications/[id]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/applications/[id]/route\",\n        pathname: \"/api/applications/[id]\",\n        filename: \"route\",\n        bundlePath: \"app/api/applications/[id]/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\api\\\\applications\\\\[id]\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Nidhal_Documents_augment_projects_foreingate_groupe_foreingate_website_src_app_api_applications_id_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fapplications%2F%5Bid%5D%2Froute&page=%2Fapi%2Fapplications%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fapplications%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/applications/[id]/route.ts":
/*!************************************************!*\
  !*** ./src/app/api/applications/[id]/route.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fs/promises */ \"fs/promises\");\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fs_promises__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nconst DATA_DIR = path__WEBPACK_IMPORTED_MODULE_3___default().join(process.cwd(), 'data');\nconst APPLICATIONS_FILE = path__WEBPACK_IMPORTED_MODULE_3___default().join(DATA_DIR, 'applications.json');\nasync function readApplications() {\n    try {\n        if (!(0,fs__WEBPACK_IMPORTED_MODULE_2__.existsSync)(APPLICATIONS_FILE)) {\n            return [];\n        }\n        const data = await (0,fs_promises__WEBPACK_IMPORTED_MODULE_1__.readFile)(APPLICATIONS_FILE, 'utf-8');\n        return JSON.parse(data);\n    } catch (error) {\n        console.error('Error reading applications:', error);\n        return [];\n    }\n}\nasync function writeApplications(applications) {\n    try {\n        await (0,fs_promises__WEBPACK_IMPORTED_MODULE_1__.writeFile)(APPLICATIONS_FILE, JSON.stringify(applications, null, 2));\n    } catch (error) {\n        console.error('Error writing applications:', error);\n        throw error;\n    }\n}\n// GET individual application\nasync function GET(request, { params }) {\n    try {\n        const applications = await readApplications();\n        const application = applications.find((app)=>app.id === params.id || app.applicationId === params.id);\n        if (!application) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Application not found'\n            }, {\n                status: 404\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: application\n        });\n    } catch (error) {\n        console.error('Application fetch error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Failed to fetch application'\n        }, {\n            status: 500\n        });\n    }\n}\n// PUT - Update application (Admin only)\nasync function PUT(request, { params }) {\n    try {\n        const body = await request.json();\n        const applications = await readApplications();\n        const applicationIndex = applications.findIndex((app)=>app.id === params.id || app.applicationId === params.id);\n        if (applicationIndex === -1) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Application not found'\n            }, {\n                status: 404\n            });\n        }\n        // Update application\n        const updatedApplication = {\n            ...applications[applicationIndex],\n            ...body,\n            updatedAt: new Date().toISOString()\n        };\n        applications[applicationIndex] = updatedApplication;\n        await writeApplications(applications);\n        // Send notification email if status changed\n        if (body.status && body.status !== applications[applicationIndex].status) {\n            try {\n                if (process.env.RESEND_API_KEY) {\n                    const { Resend } = __webpack_require__(/*! resend */ \"(rsc)/../node_modules/resend/dist/index.js\");\n                    const resend = new Resend(process.env.RESEND_API_KEY);\n                    let emailSubject = '';\n                    let emailContent = '';\n                    switch(body.status){\n                        case 'UNDER_REVIEW':\n                            emailSubject = `Application Under Review - ${updatedApplication.applicationId}`;\n                            emailContent = `\n                <h2>Application Status Update</h2>\n                <p>Dear ${updatedApplication.firstName} ${updatedApplication.lastName},</p>\n                <p>Your application is now under review by our admissions team. We will contact you soon with updates.</p>\n                <p><strong>Application ID:</strong> ${updatedApplication.applicationId}</p>\n                <p><strong>Status:</strong> Under Review</p>\n              `;\n                            break;\n                        case 'APPROVED':\n                            emailSubject = `🎉 Application Approved - ${updatedApplication.applicationId}`;\n                            emailContent = `\n                <h2>Congratulations! Your Application Has Been Approved</h2>\n                <p>Dear ${updatedApplication.firstName} ${updatedApplication.lastName},</p>\n                <p>We are pleased to inform you that your application has been approved!</p>\n                <p><strong>Application ID:</strong> ${updatedApplication.applicationId}</p>\n                <p><strong>University:</strong> ${updatedApplication.preferredUniversity}</p>\n                <p><strong>Program:</strong> ${updatedApplication.firstChoiceProgram}</p>\n                <p>Our team will contact you soon with next steps for enrollment.</p>\n              `;\n                            break;\n                        case 'REJECTED':\n                            emailSubject = `Application Status Update - ${updatedApplication.applicationId}`;\n                            emailContent = `\n                <h2>Application Status Update</h2>\n                <p>Dear ${updatedApplication.firstName} ${updatedApplication.lastName},</p>\n                <p>Thank you for your interest in studying with us. After careful review, we regret to inform you that your application was not successful this time.</p>\n                <p><strong>Application ID:</strong> ${updatedApplication.applicationId}</p>\n                <p>We encourage you to apply again in the future.</p>\n              `;\n                            break;\n                    }\n                    if (emailSubject && emailContent) {\n                        await resend.emails.send({\n                            from: 'Foreingate Admissions <<EMAIL>>',\n                            to: [\n                                updatedApplication.email\n                            ],\n                            subject: emailSubject,\n                            html: emailContent\n                        });\n                    }\n                }\n            } catch (emailError) {\n                console.error('Email notification failed:', emailError);\n            // Don't fail the update if email fails\n            }\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: updatedApplication,\n            message: 'Application updated successfully'\n        });\n    } catch (error) {\n        console.error('Application update error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Failed to update application'\n        }, {\n            status: 500\n        });\n    }\n}\n// DELETE application (Admin only)\nasync function DELETE(request, { params }) {\n    try {\n        const applications = await readApplications();\n        const applicationIndex = applications.findIndex((app)=>app.id === params.id || app.applicationId === params.id);\n        if (applicationIndex === -1) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Application not found'\n            }, {\n                status: 404\n            });\n        }\n        const deletedApplication = applications[applicationIndex];\n        applications.splice(applicationIndex, 1);\n        await writeApplications(applications);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'Application deleted successfully',\n            data: deletedApplication\n        });\n    } catch (error) {\n        console.error('Application deletion error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Failed to delete application'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/applications/[id]/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "async_hooks":
/*!******************************!*\
  !*** external "async_hooks" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("async_hooks");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "fs/promises":
/*!******************************!*\
  !*** external "fs/promises" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("fs/promises");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "prettier/plugins/html":
/*!****************************************!*\
  !*** external "prettier/plugins/html" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = import("prettier/plugins/html");;

/***/ }),

/***/ "prettier/standalone":
/*!**************************************!*\
  !*** external "prettier/standalone" ***!
  \**************************************/
/***/ ((module) => {

"use strict";
module.exports = import("prettier/standalone");;

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/resend"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fapplications%2F%5Bid%5D%2Froute&page=%2Fapi%2Fapplications%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fapplications%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();