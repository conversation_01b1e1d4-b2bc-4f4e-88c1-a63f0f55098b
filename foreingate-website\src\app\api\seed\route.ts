import { NextRequest, NextResponse } from 'next/server'
import { writeFile, mkdir } from 'fs/promises'
import { existsSync } from 'fs'
import path from 'path'

const DATA_DIR = path.join(process.cwd(), 'data')

// Ensure data directory exists
async function ensureDataDir() {
  if (!existsSync(DATA_DIR)) {
    await mkdir(DATA_DIR, { recursive: true })
  }
}

export async function POST(request: NextRequest) {
  try {
    await ensureDataDir()

    // Sample applications data
    const sampleApplications = [
      {
        id: "app001",
        applicationId: "FG-2024-SAMPLE001",
        createdAt: new Date(Date.now() - 86400000 * 5).toISOString(), // 5 days ago
        updatedAt: new Date(Date.now() - 86400000 * 5).toISOString(),
        status: "PENDING",
        firstName: "Ahmed",
        lastName: "Hassan",
        email: "<EMAIL>",
        phone: "+90 ************",
        dateOfBirth: "2000-05-15",
        nationality: "Egyptian",
        passportNumber: "A12345678",
        highSchoolName: "Cairo International School",
        graduationYear: "2022",
        gpa: "3.8/4.0",
        englishProficiency: "IELTS 7.0",
        preferredUniversity: "Eastern Mediterranean University",
        firstChoiceProgram: "Computer Engineering",
        secondChoiceProgram: "Software Engineering",
        intakeYear: "2024",
        intakeSemester: "Fall",
        assignedCounselor: null,
        notes: null
      },
      {
        id: "app002",
        applicationId: "FG-2024-SAMPLE002",
        createdAt: new Date(Date.now() - 86400000 * 3).toISOString(), // 3 days ago
        updatedAt: new Date(Date.now() - 86400000 * 2).toISOString(),
        status: "UNDER_REVIEW",
        firstName: "Maria",
        lastName: "Rodriguez",
        email: "<EMAIL>",
        phone: "+**************",
        dateOfBirth: "1999-12-03",
        nationality: "Spanish",
        passportNumber: "ESP987654",
        highSchoolName: "Instituto Cervantes",
        graduationYear: "2021",
        gpa: "8.5/10",
        englishProficiency: "TOEFL 95",
        preferredUniversity: "Near East University",
        firstChoiceProgram: "Business Administration",
        secondChoiceProgram: "International Relations",
        intakeYear: "2024",
        intakeSemester: "Spring",
        assignedCounselor: "Sarah Johnson",
        notes: "Documents received, processing admission"
      },
      {
        id: "app003",
        applicationId: "FG-2024-SAMPLE003",
        createdAt: new Date(Date.now() - 86400000 * 1).toISOString(), // 1 day ago
        updatedAt: new Date(Date.now() - 86400000 * 1).toISOString(),
        status: "PENDING",
        firstName: "David",
        lastName: "Chen",
        email: "<EMAIL>",
        phone: "+86 138 0013 8000",
        dateOfBirth: "2001-08-22",
        nationality: "Chinese",
        passportNumber: "G12345678",
        highSchoolName: "Beijing International School",
        graduationYear: "2023",
        gpa: "3.9/4.0",
        englishProficiency: "IELTS 7.5",
        preferredUniversity: "Cyprus International University",
        firstChoiceProgram: "Medicine",
        secondChoiceProgram: "Dentistry",
        intakeYear: "2024",
        intakeSemester: "Fall",
        assignedCounselor: null,
        notes: null
      },
      {
        id: "app004",
        applicationId: "FG-2024-SAMPLE004",
        createdAt: new Date(Date.now() - 86400000 * 7).toISOString(), // 1 week ago
        updatedAt: new Date(Date.now() - 86400000 * 1).toISOString(),
        status: "APPROVED",
        firstName: "Fatima",
        lastName: "Al-Zahra",
        email: "<EMAIL>",
        phone: "+971 50 123 4567",
        dateOfBirth: "2000-03-10",
        nationality: "UAE",
        passportNumber: "UAE123456",
        highSchoolName: "Dubai International Academy",
        graduationYear: "2022",
        gpa: "3.7/4.0",
        englishProficiency: "IELTS 6.5",
        preferredUniversity: "University of Kyrenia",
        firstChoiceProgram: "Architecture",
        secondChoiceProgram: "Interior Design",
        intakeYear: "2024",
        intakeSemester: "Fall",
        assignedCounselor: "Michael Thompson",
        notes: "Approved for admission, visa process started"
      },
      {
        id: "app005",
        applicationId: "FG-2024-SAMPLE005",
        createdAt: new Date(Date.now() - 86400000 * 2).toISOString(), // 2 days ago
        updatedAt: new Date(Date.now() - 86400000 * 2).toISOString(),
        status: "DOCUMENTS_REQUESTED",
        firstName: "James",
        lastName: "Wilson",
        email: "<EMAIL>",
        phone: "+44 7700 900123",
        dateOfBirth: "1998-11-18",
        nationality: "British",
        passportNumber: "GBR789012",
        highSchoolName: "London Grammar School",
        graduationYear: "2020",
        gpa: "A-levels: AAB",
        englishProficiency: "Native",
        preferredUniversity: "Eastern Mediterranean University",
        firstChoiceProgram: "Psychology",
        secondChoiceProgram: "Sociology",
        intakeYear: "2024",
        intakeSemester: "Spring",
        assignedCounselor: "Emma Davis",
        notes: "Additional transcripts requested"
      }
    ]

    // Sample newsletter subscribers
    const sampleNewsletterSubscribers = [
      {
        id: "news001",
        email: "<EMAIL>",
        subscribedAt: new Date(Date.now() - 86400000 * 10).toISOString(),
        isActive: true,
        source: "website"
      },
      {
        id: "news002",
        email: "<EMAIL>",
        subscribedAt: new Date(Date.now() - 86400000 * 8).toISOString(),
        isActive: true,
        source: "website"
      },
      {
        id: "news003",
        email: "<EMAIL>",
        subscribedAt: new Date(Date.now() - 86400000 * 6).toISOString(),
        isActive: true,
        source: "website"
      },
      {
        id: "news004",
        email: "<EMAIL>",
        subscribedAt: new Date(Date.now() - 86400000 * 4).toISOString(),
        isActive: true,
        source: "website"
      },
      {
        id: "news005",
        email: "<EMAIL>",
        subscribedAt: new Date(Date.now() - 86400000 * 2).toISOString(),
        isActive: true,
        source: "website"
      }
    ]

    // Sample contact inquiries
    const sampleContactInquiries = [
      {
        id: "contact001",
        inquiryId: "FG-INQ-001",
        createdAt: new Date(Date.now() - 86400000 * 4).toISOString(),
        updatedAt: new Date(Date.now() - 86400000 * 3).toISOString(),
        name: "Sarah Johnson",
        email: "<EMAIL>",
        phone: "****** 123 4567",
        subject: "Information about Engineering Programs",
        message: "I'm interested in learning more about your engineering programs and admission requirements.",
        preferredContact: "email",
        interestedServices: JSON.stringify(["University Admissions", "Academic Support"]),
        status: "RESPONDED",
        assignedAgent: "Mike Chen",
        responseNotes: "Sent detailed program information",
        respondedAt: new Date(Date.now() - 86400000 * 3).toISOString()
      },
      {
        id: "contact002",
        inquiryId: "FG-INQ-002",
        createdAt: new Date(Date.now() - 86400000 * 2).toISOString(),
        updatedAt: new Date(Date.now() - 86400000 * 2).toISOString(),
        name: "Robert Kim",
        email: "<EMAIL>",
        phone: "+82 10 1234 5678",
        subject: "Visa Support Services",
        message: "I need help with student visa application process for Northern Cyprus.",
        preferredContact: "phone",
        interestedServices: JSON.stringify(["Visa Support", "Document Translation"]),
        status: "IN_PROGRESS",
        assignedAgent: "Lisa Wang",
        responseNotes: "Scheduled consultation call",
        respondedAt: null
      }
    ]

    // Write sample data to files
    await writeFile(
      path.join(DATA_DIR, 'applications.json'),
      JSON.stringify(sampleApplications, null, 2)
    )

    await writeFile(
      path.join(DATA_DIR, 'newsletter.json'),
      JSON.stringify(sampleNewsletterSubscribers, null, 2)
    )

    await writeFile(
      path.join(DATA_DIR, 'contact-inquiries.json'),
      JSON.stringify(sampleContactInquiries, null, 2)
    )

    return NextResponse.json({
      success: true,
      message: 'Sample data seeded successfully!',
      data: {
        applications: sampleApplications.length,
        newsletter: sampleNewsletterSubscribers.length,
        contacts: sampleContactInquiries.length
      }
    })

  } catch (error) {
    console.error('Seeding error:', error)
    return NextResponse.json(
      { error: 'Failed to seed sample data' },
      { status: 500 }
    )
  }
}
