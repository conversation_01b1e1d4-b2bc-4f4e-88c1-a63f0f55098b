'use client'

import { motion } from 'framer-motion'
import Link from 'next/link'
import { useInView } from 'react-intersection-observer'
import { MapPin, Users, Star, ArrowRight, GraduationCap } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { useUniversities } from '@/hooks/use-api'
import { formatCurrency } from '@/lib/utils'

export function UniversitiesGridSection() {
  const { data: universities, loading } = useUniversities()
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1
  })

  if (loading) {
    return (
      <section className="section-padding">
        <div className="container">
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="bg-muted rounded-xl h-80" />
              </div>
            ))}
          </div>
        </div>
      </section>
    )
  }

  return (
    <section ref={ref} className="section-padding" id="universities">
      <div className="container">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
          className="text-center max-w-3xl mx-auto mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Explore Our Partner{' '}
            <span className="gradient-text">Universities</span>
          </h2>
          <p className="text-xl text-muted-foreground">
            Choose from top-ranked universities offering internationally recognized degrees
          </p>
        </motion.div>

        {/* Universities Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {universities?.map((university, index) => (
            <motion.div
              key={university.id}
              initial={{ opacity: 0, y: 30 }}
              animate={inView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              className="group"
            >
              <Link href={`/universities/${university.slug}`}>
                <div className="bg-background rounded-xl overflow-hidden shadow-sm border hover:shadow-lg transition-all duration-300 group-hover:border-primary/20">
                  {/* University Image */}
                  <div className="relative h-48 overflow-hidden">
                    <img
                      src={university.images[0]}
                      alt={university.name}
                      className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                      onError={(e) => {
                        e.currentTarget.src = `data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='400' height='200' viewBox='0 0 400 200'%3E%3Crect width='400' height='200' fill='%23f3f4f6'/%3E%3Ctext x='50%25' y='50%25' dominant-baseline='middle' text-anchor='middle' font-family='Arial, sans-serif' font-size='16' fill='%236b7280'%3E${university.name}%3C/text%3E%3C/svg%3E`
                      }}
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />

                    {/* University Logo */}
                    <div className="absolute top-4 left-4 w-12 h-12 bg-white rounded-lg p-2 shadow-sm">
                      <img
                        src={university.logo}
                        alt={`${university.name} logo`}
                        className="w-full h-full object-contain"
                        onError={(e) => {
                          e.currentTarget.src = `data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='48' height='48' viewBox='0 0 48 48'%3E%3Crect width='48' height='48' fill='%23f3f4f6'/%3E%3Ctext x='50%25' y='50%25' dominant-baseline='middle' text-anchor='middle' font-family='Arial, sans-serif' font-size='20' fill='%236b7280'%3E${university.name.charAt(0)}%3C/text%3E%3C/svg%3E`
                        }}
                      />
                    </div>

                    {/* Established Badge */}
                    <div className="absolute top-4 right-4 bg-primary text-primary-foreground px-2 py-1 rounded-full text-xs font-medium">
                      Est. {university.establishedYear}
                    </div>
                  </div>

                  {/* Content */}
                  <div className="p-6">
                    <h3 className="text-xl font-semibold mb-2 group-hover:text-primary transition-colors line-clamp-2">
                      {university.name}
                    </h3>

                    <div className="flex items-center text-muted-foreground mb-3">
                      <MapPin className="w-4 h-4 mr-1" />
                      <span className="text-sm">{university.location}</span>
                    </div>

                    <p className="text-muted-foreground text-sm mb-4 line-clamp-2">
                      {university.description}
                    </p>

                    {/* Stats */}
                    <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
                      <div className="flex items-center">
                        <Users className="w-4 h-4 text-primary mr-2" />
                        <span>{university.studentCount.toLocaleString()} Students</span>
                      </div>
                      <div className="flex items-center">
                        <GraduationCap className="w-4 h-4 text-primary mr-2" />
                        <span>{university.internationalStudents.toLocaleString()} International</span>
                      </div>
                    </div>

                    {/* Tuition */}
                    <div className="flex items-center justify-between">
                      <div>
                        <span className="text-sm text-muted-foreground">Tuition from</span>
                        <div className="font-semibold text-primary">
                          {formatCurrency(university.tuitionFrom, university.currency)}/year
                        </div>
                      </div>
                      <ArrowRight className="w-4 h-4 text-muted-foreground group-hover:text-primary group-hover:translate-x-1 transition-all" />
                    </div>
                  </div>
                </div>
              </Link>
            </motion.div>
          ))}
        </div>

        {/* CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="text-center bg-gradient-to-r from-primary/5 to-secondary/5 rounded-2xl p-8 md:p-12"
        >
          <h3 className="text-2xl md:text-3xl font-bold mb-4">
            Need Help Choosing the Right University?
          </h3>
          <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
            Our expert counselors can help you find the perfect university based on your
            academic goals, budget, and career aspirations.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" asChild>
              <Link href="/contact">
                Get Free Consultation
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
            <Button size="lg" variant="outline" asChild>
              <Link href="/apply">
                Start Application
              </Link>
            </Button>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
