"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"86511ef62350\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXE5pZGhhbFxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxmb3JlaW5nYXRlX2dyb3VwZVxcZm9yZWluZ2F0ZS13ZWJzaXRlXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI4NjUxMWVmNjIzNTBcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/hooks/use-translation.ts":
/*!**************************************!*\
  !*** ./src/hooks/use-translation.ts ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TranslationContext: () => (/* binding */ TranslationContext),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   formatRelativeTime: () => (/* binding */ formatRelativeTime),\n/* harmony export */   getNestedTranslation: () => (/* binding */ getNestedTranslation),\n/* harmony export */   getTranslations: () => (/* binding */ getTranslations),\n/* harmony export */   pluralize: () => (/* binding */ pluralize),\n/* harmony export */   translateWithInterpolation: () => (/* binding */ translateWithInterpolation),\n/* harmony export */   useTranslation: () => (/* binding */ useTranslation)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_i18n__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/i18n */ \"(app-pages-browser)/./src/lib/i18n.ts\");\n/* harmony import */ var _locales_en__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/locales/en */ \"(app-pages-browser)/./src/locales/en.ts\");\n/* harmony import */ var _locales_tr__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/locales/tr */ \"(app-pages-browser)/./src/locales/tr.ts\");\n/* harmony import */ var _locales_ar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/locales/ar */ \"(app-pages-browser)/./src/locales/ar.ts\");\n/* harmony import */ var _locales_fr__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/locales/fr */ \"(app-pages-browser)/./src/locales/fr.ts\");\n/* __next_internal_client_entry_do_not_use__ TranslationContext,useTranslation,getTranslations,getNestedTranslation,translateWithInterpolation,pluralize,formatDate,formatNumber,formatCurrency,formatRelativeTime auto */ \n\n// Import all translations\n\n\n\n\n// Translation map\nconst translations = {\n    en: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    tr: _locales_tr__WEBPACK_IMPORTED_MODULE_3__.tr,\n    ar: _locales_ar__WEBPACK_IMPORTED_MODULE_4__.ar,\n    fr: _locales_fr__WEBPACK_IMPORTED_MODULE_5__.fr,\n    // Add more languages as they are created\n    es: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    de: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ru: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    zh: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ja: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ko: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    pt: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    it: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    nl: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    sv: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    no: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    da: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    fi: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    pl: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    cs: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    hu: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ro: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    bg: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    hr: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    sk: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    sl: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    et: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    lv: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    lt: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    mt: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    cy: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ga: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    is: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    mk: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    sq: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    sr: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    bs: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    me: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    uk: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    be: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    kk: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ky: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    uz: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    tg: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    tm: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    mn: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ka: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    hy: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    az: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    fa: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ur: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    hi: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    bn: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ta: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    te: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ml: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    kn: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    gu: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    pa: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    or: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    as: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ne: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    si: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    my: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    th: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    lo: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    km: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    vi: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    id: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ms: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    tl: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    haw: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    mi: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    sm: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    to: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    fj: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    sw: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    am: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ti: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    om: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    so: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    rw: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    rn: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    lg: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ak: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    tw: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    yo: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ig: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ha: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ff: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    wo: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    sn: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    zu: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    xh: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    af: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    st: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    tn: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ss: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ve: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ts: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    nr: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    he: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    yi: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    jv: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    su: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    mad: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ban: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    bug: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    mak: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    min: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ace: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    bjn: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    bbc: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    nij: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    rej: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    sas: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    tet: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en\n};\nconst TranslationContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(undefined);\nfunction useTranslation() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(TranslationContext);\n    if (!context) {\n        throw new Error('useTranslation must be used within a TranslationProvider');\n    }\n    return context;\n}\n// Helper function to get translations for a specific locale\nfunction getTranslations(locale) {\n    return translations[locale] || translations[_lib_i18n__WEBPACK_IMPORTED_MODULE_1__.defaultLocale];\n}\n// Helper function to get nested translation value\nfunction getNestedTranslation(translations, key) {\n    const keys = key.split('.');\n    let value = translations;\n    for (const k of keys){\n        if (value && typeof value === 'object' && k in value) {\n            value = value[k];\n        } else {\n            return key // Return the key if translation not found\n            ;\n        }\n    }\n    return typeof value === 'string' ? value : key;\n}\n// Translation function with interpolation support\nfunction translateWithInterpolation(template) {\n    let values = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    return template.replace(/\\{\\{(\\w+)\\}\\}/g, (match, key)=>{\n        var _values_key;\n        return ((_values_key = values[key]) === null || _values_key === void 0 ? void 0 : _values_key.toString()) || match;\n    });\n}\n// Pluralization helper\nfunction pluralize(count, singular, plural) {\n    if (count === 1) {\n        return singular;\n    }\n    return plural || \"\".concat(singular, \"s\");\n}\n// Date formatting helper\nfunction formatDate(date, locale, options) {\n    try {\n        return new Intl.DateTimeFormat(locale, options).format(date);\n    } catch (error) {\n        // Fallback to English if locale is not supported\n        return new Intl.DateTimeFormat('en', options).format(date);\n    }\n}\n// Number formatting helper\nfunction formatNumber(number, locale, options) {\n    try {\n        return new Intl.NumberFormat(locale, options).format(number);\n    } catch (error) {\n        // Fallback to English if locale is not supported\n        return new Intl.NumberFormat('en', options).format(number);\n    }\n}\n// Currency formatting helper\nfunction formatCurrency(amount, locale) {\n    let currency = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 'USD';\n    try {\n        return new Intl.NumberFormat(locale, {\n            style: 'currency',\n            currency\n        }).format(amount);\n    } catch (error) {\n        // Fallback to English if locale is not supported\n        return new Intl.NumberFormat('en', {\n            style: 'currency',\n            currency\n        }).format(amount);\n    }\n}\n// Relative time formatting helper\nfunction formatRelativeTime(date, locale) {\n    try {\n        const rtf = new Intl.RelativeTimeFormat(locale, {\n            numeric: 'auto'\n        });\n        const now = new Date();\n        const diffInSeconds = Math.floor((date.getTime() - now.getTime()) / 1000);\n        if (Math.abs(diffInSeconds) < 60) {\n            return rtf.format(diffInSeconds, 'second');\n        }\n        const diffInMinutes = Math.floor(diffInSeconds / 60);\n        if (Math.abs(diffInMinutes) < 60) {\n            return rtf.format(diffInMinutes, 'minute');\n        }\n        const diffInHours = Math.floor(diffInMinutes / 60);\n        if (Math.abs(diffInHours) < 24) {\n            return rtf.format(diffInHours, 'hour');\n        }\n        const diffInDays = Math.floor(diffInHours / 24);\n        return rtf.format(diffInDays, 'day');\n    } catch (error) {\n        // Fallback to simple date formatting\n        return formatDate(date, locale);\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/use-translation.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/locales/fr.ts":
/*!***************************!*\
  !*** ./src/locales/fr.ts ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fr: () => (/* binding */ fr)\n/* harmony export */ });\nconst fr = {\n    nav: {\n        home: 'Accueil',\n        about: 'À propos',\n        services: 'Services',\n        universities: 'Universités',\n        programs: 'Programmes',\n        blog: 'Blog',\n        contact: 'Contact',\n        applyNow: 'Postuler',\n        getStarted: 'Commencer',\n        language: 'Langue'\n    },\n    hero: {\n        title: 'Votre Porte d\\'Entrée vers l\\'Éducation de Classe Mondiale à Chypre du Nord',\n        subtitle: 'Étudiez à l\\'Étranger en Toute Confiance',\n        description: 'Conseils d\\'experts pour les étudiants internationaux recherchant une éducation de qualité dans les meilleures universités de Chypre du Nord. De la candidature à la remise des diplômes, nous sommes avec vous à chaque étape.',\n        ctaPrimary: 'Commencez Votre Voyage',\n        ctaSecondary: 'Explorer les Universités',\n        trustBadge: 'Approuvé par plus de 10 000 étudiants dans le monde',\n        studentsServed: 'Étudiants Servis',\n        successRate: 'Taux de Réussite',\n        yearsExperience: 'Années d\\'Expérience'\n    },\n    about: {\n        title: 'À propos du Groupe Foreingate',\n        subtitle: 'Votre Partenaire Éducatif de Confiance',\n        description: 'Nous sommes un cabinet de conseil en éducation de premier plan spécialisé dans l\\'aide aux étudiants internationaux pour réaliser leurs rêves académiques à Chypre du Nord.',\n        mission: 'Fournir des conseils éducatifs complets et des services de soutien qui permettent aux étudiants de réussir dans leur parcours académique international.',\n        vision: 'Être le pont le plus fiable reliant les étudiants du monde entier aux opportunités d\\'éducation de qualité à Chypre du Nord.',\n        values: 'Excellence, Intégrité, Innovation et Réussite Étudiante',\n        whyChooseUs: 'Pourquoi Nous Choisir',\n        experience: '15+ Années d\\'Expérience',\n        expertise: 'Conseils d\\'Experts',\n        support: 'Support 24/7',\n        success: '98% Taux de Réussite'\n    },\n    services: {\n        title: 'Nos Services',\n        subtitle: 'Support Complet pour Votre Parcours Éducatif',\n        universitySelection: 'Sélection d\\'Université',\n        universitySelectionDesc: 'Conseils d\\'experts pour choisir la bonne université et le bon programme en fonction de vos objectifs et préférences.',\n        admissionGuidance: 'Conseils d\\'Admission',\n        admissionGuidanceDesc: 'Support complet tout au long du processus de candidature, de la préparation des documents à la soumission.',\n        visaSupport: 'Support Visa',\n        visaSupportDesc: 'Assistance professionnelle pour les demandes de visa et les procédures d\\'immigration.',\n        accommodationHelp: 'Aide au Logement',\n        accommodationHelpDesc: 'Trouvez des options de logement appropriées près de votre université avec nos services d\\'hébergement.',\n        scholarshipAssistance: 'Aide aux Bourses',\n        scholarshipAssistanceDesc: 'Identifiez et postulez pour des bourses et des opportunités d\\'aide financière.',\n        ongoingSupport: 'Support Continu',\n        ongoingSupportDesc: 'Support continu tout au long de vos études, de l\\'arrivée à la remise des diplômes.'\n    },\n    universities: {\n        title: 'Universités Partenaires',\n        subtitle: 'Universités de Premier Rang à Chypre du Nord',\n        emu: 'Université de Méditerranée Orientale',\n        neu: 'Université du Proche-Orient',\n        ciu: 'Université Internationale de Chypre',\n        programs: 'Programmes',\n        students: 'Étudiants',\n        established: 'Établie',\n        accreditation: 'Accréditation',\n        tuitionFrom: 'Frais de scolarité à partir de',\n        learnMore: 'En Savoir Plus',\n        applyNow: 'Postuler'\n    },\n    programs: {\n        title: 'Programmes d\\'Études',\n        subtitle: 'Programmes Académiques Diversifiés pour Correspondre à Vos Intérêts',\n        engineering: 'Ingénierie',\n        medicine: 'Médecine',\n        business: 'Commerce',\n        arts: 'Arts et Sciences Humaines',\n        sciences: 'Sciences',\n        law: 'Droit',\n        architecture: 'Architecture',\n        education: 'Éducation',\n        duration: 'Durée',\n        language: 'Langue',\n        degree: 'Diplôme',\n        bachelor: 'Licence',\n        master: 'Master',\n        doctorate: 'Doctorat'\n    },\n    testimonials: {\n        title: 'Histoires de Réussite d\\'Étudiants',\n        subtitle: 'Écoutez Nos Étudiants Réussis',\n        readMore: 'Lire Plus',\n        showLess: 'Afficher Moins',\n        verified: 'Étudiant Vérifié',\n        graduate: 'Diplômé',\n        currentStudent: 'Étudiant Actuel'\n    },\n    contact: {\n        title: 'Contactez-Nous',\n        subtitle: 'Entrez en Contact avec Nos Experts en Éducation',\n        getInTouch: 'Entrer en Contact',\n        name: 'Nom Complet',\n        email: 'Adresse E-mail',\n        phone: 'Numéro de Téléphone',\n        message: 'Message',\n        subject: 'Sujet',\n        send: 'Envoyer le Message',\n        sending: 'Envoi en cours...',\n        sent: 'Message Envoyé avec Succès !',\n        error: 'Échec de l\\'envoi du message. Veuillez réessayer.',\n        required: 'Ce champ est requis',\n        invalidEmail: 'Veuillez entrer une adresse e-mail valide',\n        office: 'Heures de Bureau',\n        hours: 'Lundi - Vendredi : 9h00 - 18h00',\n        emergency: 'Support d\\'Urgence 24/7 Disponible'\n    },\n    footer: {\n        description: 'Votre partenaire de confiance pour l\\'éducation internationale à Chypre du Nord. Conseils d\\'experts de la candidature à la remise des diplômes.',\n        quickLinks: 'Liens Rapides',\n        services: 'Services',\n        contact: 'Informations de Contact',\n        followUs: 'Suivez-Nous',\n        newsletter: 'Newsletter',\n        newsletterDesc: 'Abonnez-vous pour recevoir les dernières mises à jour sur les universités, programmes et bourses.',\n        subscribe: 'S\\'abonner',\n        subscribing: 'Abonnement en cours...',\n        subscribed: 'Abonné avec Succès !',\n        privacy: 'Politique de Confidentialité',\n        terms: 'Conditions de Service',\n        cookies: 'Politique des Cookies',\n        sitemap: 'Plan du Site',\n        allRightsReserved: 'Tous droits réservés.'\n    },\n    common: {\n        loading: 'Chargement...',\n        error: 'Erreur',\n        success: 'Succès',\n        warning: 'Avertissement',\n        info: 'Information',\n        close: 'Fermer',\n        cancel: 'Annuler',\n        confirm: 'Confirmer',\n        save: 'Sauvegarder',\n        edit: 'Modifier',\n        delete: 'Supprimer',\n        search: 'Rechercher',\n        filter: 'Filtrer',\n        sort: 'Trier',\n        next: 'Suivant',\n        previous: 'Précédent',\n        page: 'Page',\n        of: 'de',\n        showing: 'Affichage',\n        results: 'résultats',\n        noResults: 'Aucun résultat trouvé',\n        tryAgain: 'Réessayer',\n        learnMore: 'En Savoir Plus',\n        readMore: 'Lire Plus',\n        showMore: 'Afficher Plus',\n        showLess: 'Afficher Moins',\n        viewAll: 'Voir Tout',\n        backToTop: 'Retour en Haut'\n    },\n    chatbot: {\n        title: 'Assistant Éducatif',\n        placeholder: 'Demandez-moi à propos des universités, programmes, coûts...',\n        send: 'Envoyer',\n        thinking: 'Réflexion...',\n        error: 'Désolé, j\\'ai rencontré une erreur. Veuillez réessayer.',\n        retry: 'Réessayer',\n        clear: 'Effacer la Discussion',\n        minimize: 'Réduire',\n        maximize: 'Agrandir',\n        close: 'Fermer',\n        greeting: 'Bonjour ! Je suis ici pour vous aider dans votre parcours éducatif. Que souhaitez-vous savoir ?',\n        suggestions: 'Questions Suggérées',\n        typing: 'Frappe...',\n        offline: 'Hors ligne',\n        online: 'En ligne'\n    },\n    forms: {\n        firstName: 'Prénom',\n        lastName: 'Nom de Famille',\n        fullName: 'Nom Complet',\n        email: 'Adresse E-mail',\n        phone: 'Numéro de Téléphone',\n        country: 'Pays',\n        city: 'Ville',\n        address: 'Adresse',\n        zipCode: 'Code Postal',\n        dateOfBirth: 'Date de Naissance',\n        gender: 'Genre',\n        male: 'Homme',\n        female: 'Femme',\n        other: 'Autre',\n        preferNotToSay: 'Préfère ne pas dire',\n        nationality: 'Nationalité',\n        passportNumber: 'Numéro de Passeport',\n        education: 'Niveau d\\'Éducation',\n        highSchool: 'Lycée',\n        bachelor: 'Licence',\n        master: 'Master',\n        doctorate: 'Doctorat',\n        workExperience: 'Expérience Professionnelle',\n        englishLevel: 'Niveau d\\'Anglais',\n        beginner: 'Débutant',\n        intermediate: 'Intermédiaire',\n        advanced: 'Avancé',\n        native: 'Langue Maternelle',\n        interestedProgram: 'Programme d\\'Intérêt',\n        interestedUniversity: 'Université d\\'Intérêt',\n        startDate: 'Date de Début Préférée',\n        additionalInfo: 'Informations Supplémentaires',\n        agreeTerms: 'J\\'accepte les Conditions de Service',\n        agreePrivacy: 'J\\'accepte la Politique de Confidentialité',\n        agreeMarketing: 'J\\'accepte de recevoir des communications marketing',\n        submit: 'Soumettre',\n        submitting: 'Soumission...',\n        submitted: 'Soumis avec Succès !',\n        required: 'Champ requis',\n        invalid: 'Format invalide',\n        tooShort: 'Trop court',\n        tooLong: 'Trop long',\n        passwordMismatch: 'Les mots de passe ne correspondent pas'\n    },\n    costs: {\n        title: 'Coûts et Bourses',\n        subtitle: 'Éducation Abordable avec Options de Soutien Financier',\n        tuitionFees: 'Frais de Scolarité',\n        livingCosts: 'Coûts de la Vie',\n        totalCost: 'Coût Total',\n        scholarships: 'Bourses',\n        financialAid: 'Aide Financière',\n        paymentPlans: 'Plans de Paiement',\n        currency: 'USD',\n        perYear: 'par an',\n        perMonth: 'par mois',\n        accommodation: 'Hébergement',\n        food: 'Nourriture et Repas',\n        transportation: 'Transport',\n        books: 'Livres et Fournitures',\n        personal: 'Dépenses Personnelles',\n        insurance: 'Assurance Santé',\n        visa: 'Visa et Immigration',\n        other: 'Autres Dépenses',\n        meritScholarship: 'Bourse au Mérite',\n        needBasedAid: 'Aide Basée sur les Besoins',\n        earlyBird: 'Remise Inscription Précoce',\n        siblingDiscount: 'Remise Fratrie',\n        calculate: 'Calculer les Coûts',\n        getQuote: 'Obtenir un Devis'\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/locales/fr.ts\n"));

/***/ })

});