/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/universities/page",{

/***/ "(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/graduation-cap.js":
/*!*********************************************************************!*\
  !*** ../node_modules/lucide-react/dist/esm/icons/graduation-cap.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ GraduationCap)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M21.42 10.922a1 1 0 0 0-.019-1.838L12.83 5.18a2 2 0 0 0-1.66 0L2.6 9.08a1 1 0 0 0 0 1.832l8.57 3.908a2 2 0 0 0 1.66 0z\",\n            key: \"j76jl0\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M22 10v6\",\n            key: \"1lu8f3\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M6 12.5V16a6 3 0 0 0 12 0v-3.5\",\n            key: \"1r8lef\"\n        }\n    ]\n];\nconst GraduationCap = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"graduation-cap\", __iconNode);\n //# sourceMappingURL=graduation-cap.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/graduation-cap.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/map-pin.js":
/*!**************************************************************!*\
  !*** ../node_modules/lucide-react/dist/esm/icons/map-pin.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ MapPin)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0\",\n            key: \"1r0f0z\"\n        }\n    ],\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"10\",\n            r: \"3\",\n            key: \"ilqhr7\"\n        }\n    ]\n];\nconst MapPin = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"map-pin\", __iconNode);\n //# sourceMappingURL=map-pin.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/map-pin.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/users.js":
/*!************************************************************!*\
  !*** ../node_modules/lucide-react/dist/esm/icons/users.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Users)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2\",\n            key: \"1yyitq\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M16 3.128a4 4 0 0 1 0 7.744\",\n            key: \"16gr8j\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M22 21v-2a4 4 0 0 0-3-3.87\",\n            key: \"kshegd\"\n        }\n    ],\n    [\n        \"circle\",\n        {\n            cx: \"9\",\n            cy: \"7\",\n            r: \"4\",\n            key: \"nufk8\"\n        }\n    ]\n];\nconst Users = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"users\", __iconNode);\n //# sourceMappingURL=users.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2ljb25zL3VzZXJzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUdPLE1BQU0sVUFBdUI7SUFDbEM7UUFBQyxNQUFRO1FBQUE7WUFBRSxHQUFHLENBQTZDO1lBQUEsS0FBSztRQUFBLENBQVU7S0FBQTtJQUMxRTtRQUFDLE1BQVE7UUFBQTtZQUFFLEdBQUcsQ0FBK0I7WUFBQSxLQUFLO1FBQUEsQ0FBVTtLQUFBO0lBQzVEO1FBQUMsTUFBUTtRQUFBO1lBQUUsR0FBRyxDQUE4QjtZQUFBLEtBQUs7UUFBQSxDQUFVO0tBQUE7SUFDM0Q7UUFBQyxDQUFVO1FBQUE7WUFBRSxDQUFJLE9BQUs7WUFBQSxFQUFJO1lBQUssQ0FBRztZQUFLLENBQUs7UUFBUztLQUFBO0NBQ3ZEO0FBYU0sWUFBUSxrRUFBaUIsVUFBUyxDQUFVIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXE5pZGhhbFxcRG9jdW1lbnRzXFxzcmNcXGljb25zXFx1c2Vycy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY3JlYXRlTHVjaWRlSWNvbiBmcm9tICcuLi9jcmVhdGVMdWNpZGVJY29uJztcbmltcG9ydCB7IEljb25Ob2RlIH0gZnJvbSAnLi4vdHlwZXMnO1xuXG5leHBvcnQgY29uc3QgX19pY29uTm9kZTogSWNvbk5vZGUgPSBbXG4gIFsncGF0aCcsIHsgZDogJ00xNiAyMXYtMmE0IDQgMCAwIDAtNC00SDZhNCA0IDAgMCAwLTQgNHYyJywga2V5OiAnMXl5aXRxJyB9XSxcbiAgWydwYXRoJywgeyBkOiAnTTE2IDMuMTI4YTQgNCAwIDAgMSAwIDcuNzQ0Jywga2V5OiAnMTZncjhqJyB9XSxcbiAgWydwYXRoJywgeyBkOiAnTTIyIDIxdi0yYTQgNCAwIDAgMC0zLTMuODcnLCBrZXk6ICdrc2hlZ2QnIH1dLFxuICBbJ2NpcmNsZScsIHsgY3g6ICc5JywgY3k6ICc3JywgcjogJzQnLCBrZXk6ICdudWZrOCcgfV0sXG5dO1xuXG4vKipcbiAqIEBjb21wb25lbnQgQG5hbWUgVXNlcnNcbiAqIEBkZXNjcmlwdGlvbiBMdWNpZGUgU1ZHIGljb24gY29tcG9uZW50LCByZW5kZXJzIFNWRyBFbGVtZW50IHdpdGggY2hpbGRyZW4uXG4gKlxuICogQHByZXZpZXcgIVtpbWddKGRhdGE6aW1hZ2Uvc3ZnK3htbDtiYXNlNjQsUEhOMlp5QWdlRzFzYm5NOUltaDBkSEE2THk5M2QzY3Vkek11YjNKbkx6SXdNREF2YzNabklnb2dJSGRwWkhSb1BTSXlOQ0lLSUNCb1pXbG5hSFE5SWpJMElnb2dJSFpwWlhkQ2IzZzlJakFnTUNBeU5DQXlOQ0lLSUNCbWFXeHNQU0p1YjI1bElnb2dJSE4wY205clpUMGlJekF3TUNJZ2MzUjViR1U5SW1KaFkydG5jbTkxYm1RdFkyOXNiM0k2SUNObVptWTdJR0p2Y21SbGNpMXlZV1JwZFhNNklESndlQ0lLSUNCemRISnZhMlV0ZDJsa2RHZzlJaklpQ2lBZ2MzUnliMnRsTFd4cGJtVmpZWEE5SW5KdmRXNWtJZ29nSUhOMGNtOXJaUzFzYVc1bGFtOXBiajBpY205MWJtUWlDajRLSUNBOGNHRjBhQ0JrUFNKTk1UWWdNakYyTFRKaE5DQTBJREFnTUNBd0xUUXRORWcyWVRRZ05DQXdJREFnTUMwMElEUjJNaUlnTHo0S0lDQThjR0YwYUNCa1BTSk5NVFlnTXk0eE1qaGhOQ0EwSURBZ01DQXhJREFnTnk0M05EUWlJQzgrQ2lBZ1BIQmhkR2dnWkQwaVRUSXlJREl4ZGkweVlUUWdOQ0F3SURBZ01DMHpMVE11T0RjaUlDOCtDaUFnUEdOcGNtTnNaU0JqZUQwaU9TSWdZM2s5SWpjaUlISTlJalFpSUM4K0Nqd3ZjM1puUGdvPSkgLSBodHRwczovL2x1Y2lkZS5kZXYvaWNvbnMvdXNlcnNcbiAqIEBzZWUgaHR0cHM6Ly9sdWNpZGUuZGV2L2d1aWRlL3BhY2thZ2VzL2x1Y2lkZS1yZWFjdCAtIERvY3VtZW50YXRpb25cbiAqXG4gKiBAcGFyYW0ge09iamVjdH0gcHJvcHMgLSBMdWNpZGUgaWNvbnMgcHJvcHMgYW5kIGFueSB2YWxpZCBTVkcgYXR0cmlidXRlXG4gKiBAcmV0dXJucyB7SlNYLkVsZW1lbnR9IEpTWCBFbGVtZW50XG4gKlxuICovXG5jb25zdCBVc2VycyA9IGNyZWF0ZUx1Y2lkZUljb24oJ3VzZXJzJywgX19pY29uTm9kZSk7XG5cbmV4cG9ydCBkZWZhdWx0IFVzZXJzO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/users.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5Cplaceholder-section.tsx%22%2C%22ids%22%3A%5B%22PlaceholderSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5Cuniversities-grid.tsx%22%2C%22ids%22%3A%5B%22UniversitiesGridSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5Cuniversities-hero.tsx%22%2C%22ids%22%3A%5B%22UniversitiesHeroSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Cwhatsapp-widget.tsx%22%2C%22ids%22%3A%5B%22WhatsAppWidget%22%5D%7D&server=false!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5Cplaceholder-section.tsx%22%2C%22ids%22%3A%5B%22PlaceholderSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5Cuniversities-grid.tsx%22%2C%22ids%22%3A%5B%22UniversitiesGridSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5Cuniversities-hero.tsx%22%2C%22ids%22%3A%5B%22UniversitiesHeroSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Cwhatsapp-widget.tsx%22%2C%22ids%22%3A%5B%22WhatsAppWidget%22%5D%7D&server=false! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/sections/placeholder-section.tsx */ \"(app-pages-browser)/./src/components/sections/placeholder-section.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/sections/universities-grid.tsx */ \"(app-pages-browser)/./src/components/sections/universities-grid.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/sections/universities-hero.tsx */ \"(app-pages-browser)/./src/components/sections/universities-hero.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/whatsapp-widget.tsx */ \"(app-pages-browser)/./src/components/ui/whatsapp-widget.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5Cplaceholder-section.tsx%22%2C%22ids%22%3A%5B%22PlaceholderSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5Cuniversities-grid.tsx%22%2C%22ids%22%3A%5B%22UniversitiesGridSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5Cuniversities-hero.tsx%22%2C%22ids%22%3A%5B%22UniversitiesHeroSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Cwhatsapp-widget.tsx%22%2C%22ids%22%3A%5B%22WhatsAppWidget%22%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/sections/universities-grid.tsx":
/*!*******************************************************!*\
  !*** ./src/components/sections/universities-grid.tsx ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UniversitiesGridSection: () => (/* binding */ UniversitiesGridSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/../node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_intersection_observer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-intersection-observer */ \"(app-pages-browser)/../node_modules/react-intersection-observer/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_GraduationCap_MapPin_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,GraduationCap,MapPin,Users!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_GraduationCap_MapPin_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,GraduationCap,MapPin,Users!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_GraduationCap_MapPin_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,GraduationCap,MapPin,Users!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_GraduationCap_MapPin_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,GraduationCap,MapPin,Users!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _hooks_use_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/use-api */ \"(app-pages-browser)/./src/hooks/use-api.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ UniversitiesGridSection auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction UniversitiesGridSection() {\n    _s();\n    const { data: universities, loading } = (0,_hooks_use_api__WEBPACK_IMPORTED_MODULE_3__.useUniversities)();\n    const [ref, inView] = (0,react_intersection_observer__WEBPACK_IMPORTED_MODULE_5__.useInView)({\n        triggerOnce: true,\n        threshold: 0.1\n    });\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n            className: \"section-padding\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                    children: [\n                        ...Array(6)\n                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-pulse\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-muted rounded-xl h-80\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\universities-grid.tsx\",\n                                lineNumber: 25,\n                                columnNumber: 17\n                            }, this)\n                        }, i, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\universities-grid.tsx\",\n                            lineNumber: 24,\n                            columnNumber: 15\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\universities-grid.tsx\",\n                    lineNumber: 22,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\universities-grid.tsx\",\n                lineNumber: 21,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\universities-grid.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        ref: ref,\n        className: \"section-padding\",\n        id: \"universities\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    animate: inView ? {\n                        opacity: 1,\n                        y: 0\n                    } : {},\n                    transition: {\n                        duration: 0.8\n                    },\n                    className: \"text-center max-w-3xl mx-auto mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold mb-4\",\n                            children: [\n                                \"Explore Our Partner\",\n                                ' ',\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"gradient-text\",\n                                    children: \"Universities\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\universities-grid.tsx\",\n                                    lineNumber: 46,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\universities-grid.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-muted-foreground\",\n                            children: \"Choose from top-ranked universities offering internationally recognized degrees\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\universities-grid.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\universities-grid.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12\",\n                    children: universities === null || universities === void 0 ? void 0 : universities.map((university, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            animate: inView ? {\n                                opacity: 1,\n                                y: 0\n                            } : {},\n                            transition: {\n                                duration: 0.6,\n                                delay: index * 0.1\n                            },\n                            className: \"group\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/universities/\".concat(university.slug),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-background rounded-xl overflow-hidden shadow-sm border hover:shadow-lg transition-all duration-300 group-hover:border-primary/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative h-48 overflow-hidden\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: university.images[0],\n                                                    alt: university.name,\n                                                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\",\n                                                    onError: (e)=>{\n                                                        e.currentTarget.src = \"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='400' height='200' viewBox='0 0 400 200'%3E%3Crect width='400' height='200' fill='%23f3f4f6'/%3E%3Ctext x='50%25' y='50%25' dominant-baseline='middle' text-anchor='middle' font-family='Arial, sans-serif' font-size='16' fill='%236b7280'%3E\".concat(university.name, \"%3C/text%3E%3C/svg%3E\");\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\universities-grid.tsx\",\n                                                    lineNumber: 67,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\universities-grid.tsx\",\n                                                    lineNumber: 75,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute top-4 left-4 w-12 h-12 bg-white rounded-lg p-2 shadow-sm\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: university.logo,\n                                                        alt: \"\".concat(university.name, \" logo\"),\n                                                        className: \"w-full h-full object-contain\",\n                                                        onError: (e)=>{\n                                                            e.currentTarget.src = \"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='48' height='48' viewBox='0 0 48 48'%3E%3Crect width='48' height='48' fill='%23f3f4f6'/%3E%3Ctext x='50%25' y='50%25' dominant-baseline='middle' text-anchor='middle' font-family='Arial, sans-serif' font-size='20' fill='%236b7280'%3E\".concat(university.name.charAt(0), \"%3C/text%3E%3C/svg%3E\");\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\universities-grid.tsx\",\n                                                        lineNumber: 79,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\universities-grid.tsx\",\n                                                    lineNumber: 78,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute top-4 right-4 bg-primary text-primary-foreground px-2 py-1 rounded-full text-xs font-medium\",\n                                                    children: [\n                                                        \"Est. \",\n                                                        university.establishedYear\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\universities-grid.tsx\",\n                                                    lineNumber: 90,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\universities-grid.tsx\",\n                                            lineNumber: 66,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-semibold mb-2 group-hover:text-primary transition-colors line-clamp-2\",\n                                                    children: university.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\universities-grid.tsx\",\n                                                    lineNumber: 97,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center text-muted-foreground mb-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_GraduationCap_MapPin_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"w-4 h-4 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\universities-grid.tsx\",\n                                                            lineNumber: 102,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm\",\n                                                            children: university.location\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\universities-grid.tsx\",\n                                                            lineNumber: 103,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\universities-grid.tsx\",\n                                                    lineNumber: 101,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-muted-foreground text-sm mb-4 line-clamp-2\",\n                                                    children: university.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\universities-grid.tsx\",\n                                                    lineNumber: 106,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 gap-4 mb-4 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_GraduationCap_MapPin_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    className: \"w-4 h-4 text-primary mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\universities-grid.tsx\",\n                                                                    lineNumber: 113,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        university.studentCount.toLocaleString(),\n                                                                        \" Students\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\universities-grid.tsx\",\n                                                                    lineNumber: 114,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\universities-grid.tsx\",\n                                                            lineNumber: 112,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_GraduationCap_MapPin_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                    className: \"w-4 h-4 text-primary mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\universities-grid.tsx\",\n                                                                    lineNumber: 117,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        university.internationalStudents.toLocaleString(),\n                                                                        \" International\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\universities-grid.tsx\",\n                                                                    lineNumber: 118,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\universities-grid.tsx\",\n                                                            lineNumber: 116,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\universities-grid.tsx\",\n                                                    lineNumber: 111,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"Tuition from\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\universities-grid.tsx\",\n                                                                    lineNumber: 125,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-semibold text-primary\",\n                                                                    children: [\n                                                                        (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatCurrency)(university.tuitionFrom, university.currency),\n                                                                        \"/year\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\universities-grid.tsx\",\n                                                                    lineNumber: 126,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\universities-grid.tsx\",\n                                                            lineNumber: 124,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_GraduationCap_MapPin_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"w-4 h-4 text-muted-foreground group-hover:text-primary group-hover:translate-x-1 transition-all\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\universities-grid.tsx\",\n                                                            lineNumber: 130,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\universities-grid.tsx\",\n                                                    lineNumber: 123,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\universities-grid.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\universities-grid.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\universities-grid.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 15\n                            }, this)\n                        }, university.id, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\universities-grid.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\universities-grid.tsx\",\n                    lineNumber: 54,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    animate: inView ? {\n                        opacity: 1,\n                        y: 0\n                    } : {},\n                    transition: {\n                        duration: 0.8,\n                        delay: 0.6\n                    },\n                    className: \"text-center bg-gradient-to-r from-primary/5 to-secondary/5 rounded-2xl p-8 md:p-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-2xl md:text-3xl font-bold mb-4\",\n                            children: \"Need Help Choosing the Right University?\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\universities-grid.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground mb-6 max-w-2xl mx-auto\",\n                            children: \"Our expert counselors can help you find the perfect university based on your academic goals, budget, and career aspirations.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\universities-grid.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    size: \"lg\",\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/contact\",\n                                        children: [\n                                            \"Get Free Consultation\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_GraduationCap_MapPin_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"ml-2 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\universities-grid.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\universities-grid.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\universities-grid.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    size: \"lg\",\n                                    variant: \"outline\",\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/apply\",\n                                        children: \"Start Application\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\universities-grid.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\universities-grid.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\universities-grid.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\universities-grid.tsx\",\n                    lineNumber: 140,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\universities-grid.tsx\",\n            lineNumber: 36,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\universities-grid.tsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, this);\n}\n_s(UniversitiesGridSection, \"UhqPWdFAyJjxF3fYdNQsUjszuNw=\", false, function() {\n    return [\n        _hooks_use_api__WEBPACK_IMPORTED_MODULE_3__.useUniversities,\n        react_intersection_observer__WEBPACK_IMPORTED_MODULE_5__.useInView\n    ];\n});\n_c = UniversitiesGridSection;\nvar _c;\n$RefreshReg$(_c, \"UniversitiesGridSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/universities-grid.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/hooks/use-api.ts":
/*!******************************!*\
  !*** ./src/hooks/use-api.ts ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useBlogPost: () => (/* binding */ useBlogPost),\n/* harmony export */   useBlogPostBySlug: () => (/* binding */ useBlogPostBySlug),\n/* harmony export */   useBlogPosts: () => (/* binding */ useBlogPosts),\n/* harmony export */   useFAQs: () => (/* binding */ useFAQs),\n/* harmony export */   useProgram: () => (/* binding */ useProgram),\n/* harmony export */   useProgramBySlug: () => (/* binding */ useProgramBySlug),\n/* harmony export */   usePrograms: () => (/* binding */ usePrograms),\n/* harmony export */   useService: () => (/* binding */ useService),\n/* harmony export */   useServiceBySlug: () => (/* binding */ useServiceBySlug),\n/* harmony export */   useServices: () => (/* binding */ useServices),\n/* harmony export */   useTeamMembers: () => (/* binding */ useTeamMembers),\n/* harmony export */   useTestimonials: () => (/* binding */ useTestimonials),\n/* harmony export */   useUniversities: () => (/* binding */ useUniversities),\n/* harmony export */   useUniversity: () => (/* binding */ useUniversity),\n/* harmony export */   useUniversityBySlug: () => (/* binding */ useUniversityBySlug)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_mock_data__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/mock-data */ \"(app-pages-browser)/./src/lib/mock-data.ts\");\n/* __next_internal_client_entry_do_not_use__ useUniversities,useUniversity,useUniversityBySlug,usePrograms,useProgram,useProgramBySlug,useServices,useService,useServiceBySlug,useTestimonials,useBlogPosts,useBlogPost,useBlogPostBySlug,useTeamMembers,useFAQs auto */ \n\n// Generic hook for API calls with loading and error states\nfunction useApiCall(apiCall) {\n    let dependencies = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : [];\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useApiCall.useEffect\": ()=>{\n            const fetchData1 = {\n                \"useApiCall.useEffect.fetchData\": async ()=>{\n                    try {\n                        setLoading(true);\n                        setError(null);\n                        const response = await apiCall();\n                        if (response.success) {\n                            setData(response.data);\n                        } else {\n                            setError(response.error || 'An error occurred');\n                        }\n                    } catch (err) {\n                        setError(err instanceof Error ? err.message : 'An error occurred');\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"useApiCall.useEffect.fetchData\"];\n            fetchData1();\n        }\n    }[\"useApiCall.useEffect\"], dependencies);\n    return {\n        data,\n        loading,\n        error,\n        refetch: ()=>fetchData()\n    };\n}\n// Universities hooks\nfunction useUniversities(filters) {\n    // For development, return mock data\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(_lib_mock_data__WEBPACK_IMPORTED_MODULE_1__.mockUniversities);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    return {\n        data,\n        loading,\n        error,\n        refetch: ()=>{}\n    };\n}\nfunction useUniversity(id) {\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(_lib_mock_data__WEBPACK_IMPORTED_MODULE_1__.mockUniversities.find({\n        \"useUniversity.useState\": (u)=>u.id === id\n    }[\"useUniversity.useState\"]) || null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    return {\n        data,\n        loading,\n        error,\n        refetch: ()=>{}\n    };\n}\nfunction useUniversityBySlug(slug) {\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(_lib_mock_data__WEBPACK_IMPORTED_MODULE_1__.mockUniversities.find({\n        \"useUniversityBySlug.useState\": (u)=>u.slug === slug\n    }[\"useUniversityBySlug.useState\"]) || null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    return {\n        data,\n        loading,\n        error,\n        refetch: ()=>{}\n    };\n}\n// Programs hooks\nfunction usePrograms(filters) {\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(_lib_mock_data__WEBPACK_IMPORTED_MODULE_1__.mockPrograms);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    return {\n        data,\n        loading,\n        error,\n        refetch: ()=>{}\n    };\n}\nfunction useProgram(id) {\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(_lib_mock_data__WEBPACK_IMPORTED_MODULE_1__.mockPrograms.find({\n        \"useProgram.useState\": (p)=>p.id === id\n    }[\"useProgram.useState\"]) || null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    return {\n        data,\n        loading,\n        error,\n        refetch: ()=>{}\n    };\n}\nfunction useProgramBySlug(slug) {\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(_lib_mock_data__WEBPACK_IMPORTED_MODULE_1__.mockPrograms.find({\n        \"useProgramBySlug.useState\": (p)=>p.slug === slug\n    }[\"useProgramBySlug.useState\"]) || null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    return {\n        data,\n        loading,\n        error,\n        refetch: ()=>{}\n    };\n}\n// Services hooks\nfunction useServices() {\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(_lib_mock_data__WEBPACK_IMPORTED_MODULE_1__.mockServices);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    return {\n        data,\n        loading,\n        error,\n        refetch: ()=>{}\n    };\n}\nfunction useService(id) {\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(_lib_mock_data__WEBPACK_IMPORTED_MODULE_1__.mockServices.find({\n        \"useService.useState\": (s)=>s.id === id\n    }[\"useService.useState\"]) || null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    return {\n        data,\n        loading,\n        error,\n        refetch: ()=>{}\n    };\n}\nfunction useServiceBySlug(slug) {\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(_lib_mock_data__WEBPACK_IMPORTED_MODULE_1__.mockServices.find({\n        \"useServiceBySlug.useState\": (s)=>s.slug === slug\n    }[\"useServiceBySlug.useState\"]) || null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    return {\n        data,\n        loading,\n        error,\n        refetch: ()=>{}\n    };\n}\n// Testimonials hooks\nfunction useTestimonials() {\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(_lib_mock_data__WEBPACK_IMPORTED_MODULE_1__.mockTestimonials);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    return {\n        data,\n        loading,\n        error,\n        refetch: ()=>{}\n    };\n}\n// Blog hooks\nfunction useBlogPosts() {\n    let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1, limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10;\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(_lib_mock_data__WEBPACK_IMPORTED_MODULE_1__.mockBlogPosts);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    return {\n        data,\n        loading,\n        error,\n        refetch: ()=>{}\n    };\n}\nfunction useBlogPost(id) {\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(_lib_mock_data__WEBPACK_IMPORTED_MODULE_1__.mockBlogPosts.find({\n        \"useBlogPost.useState\": (p)=>p.id === id\n    }[\"useBlogPost.useState\"]) || null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    return {\n        data,\n        loading,\n        error,\n        refetch: ()=>{}\n    };\n}\nfunction useBlogPostBySlug(slug) {\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(_lib_mock_data__WEBPACK_IMPORTED_MODULE_1__.mockBlogPosts.find({\n        \"useBlogPostBySlug.useState\": (p)=>p.slug === slug\n    }[\"useBlogPostBySlug.useState\"]) || null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    return {\n        data,\n        loading,\n        error,\n        refetch: ()=>{}\n    };\n}\n// Team hooks\nfunction useTeamMembers() {\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(_lib_mock_data__WEBPACK_IMPORTED_MODULE_1__.mockTeamMembers);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    return {\n        data,\n        loading,\n        error,\n        refetch: ()=>{}\n    };\n}\n// FAQ hooks\nfunction useFAQs(category) {\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(category ? _lib_mock_data__WEBPACK_IMPORTED_MODULE_1__.mockFAQs.filter({\n        \"useFAQs.useState\": (faq)=>faq.category === category\n    }[\"useFAQs.useState\"]) : _lib_mock_data__WEBPACK_IMPORTED_MODULE_1__.mockFAQs);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    return {\n        data,\n        loading,\n        error,\n        refetch: ()=>{}\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/use-api.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/mock-data.ts":
/*!******************************!*\
  !*** ./src/lib/mock-data.ts ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mockBlogPosts: () => (/* binding */ mockBlogPosts),\n/* harmony export */   mockFAQs: () => (/* binding */ mockFAQs),\n/* harmony export */   mockPrograms: () => (/* binding */ mockPrograms),\n/* harmony export */   mockServices: () => (/* binding */ mockServices),\n/* harmony export */   mockTeamMembers: () => (/* binding */ mockTeamMembers),\n/* harmony export */   mockTestimonials: () => (/* binding */ mockTestimonials),\n/* harmony export */   mockUniversities: () => (/* binding */ mockUniversities)\n/* harmony export */ });\nconst mockUniversities = [\n    {\n        id: '1',\n        name: 'Eastern Mediterranean University',\n        slug: 'eastern-mediterranean-university',\n        logo: '/images/universities/emu-logo.png',\n        location: 'Famagusta, Northern Cyprus',\n        tuitionFrom: 3500,\n        currency: 'USD',\n        description: 'Eastern Mediterranean University (EMU) is a leading higher education institution in Northern Cyprus, offering world-class education with international recognition.',\n        images: [\n            '/images/universities/emu-campus-1.jpg',\n            '/images/universities/emu-campus-2.jpg',\n            '/images/universities/emu-campus-3.jpg'\n        ],\n        programs: [],\n        requirements: [\n            'High school diploma or equivalent',\n            'English proficiency test (IELTS 6.0 or TOEFL 79)',\n            'Academic transcripts',\n            'Passport copy',\n            'Health insurance'\n        ],\n        facilities: [\n            'Modern laboratories',\n            'Library with 200,000+ books',\n            'Sports facilities',\n            'Student dormitories',\n            'Medical center',\n            'Cafeterias and restaurants'\n        ],\n        accreditations: [\n            'YÖK (Council of Higher Education, Turkey)',\n            'YÖDAK (Higher Education Planning, Evaluation, Accreditation and Coordination Council)',\n            'Various international accreditations'\n        ],\n        establishedYear: 1979,\n        studentCount: 20000,\n        internationalStudents: 15000,\n        campusSize: '2,500 acres',\n        website: 'https://www.emu.edu.tr',\n        contactEmail: '<EMAIL>',\n        contactPhone: '+90 ************'\n    },\n    {\n        id: '2',\n        name: 'Near East University',\n        slug: 'near-east-university',\n        logo: '/images/universities/neu-logo.png',\n        location: 'Nicosia, Northern Cyprus',\n        tuitionFrom: 4000,\n        currency: 'USD',\n        description: 'Near East University is one of the most prestigious universities in Northern Cyprus, known for its innovative programs and state-of-the-art facilities.',\n        images: [\n            '/images/universities/neu-campus-1.jpg',\n            '/images/universities/neu-campus-2.jpg'\n        ],\n        programs: [],\n        requirements: [\n            'High school diploma',\n            'English proficiency (IELTS 6.0 or equivalent)',\n            'Academic records',\n            'Valid passport',\n            'Medical report'\n        ],\n        facilities: [\n            'Advanced research centers',\n            'Digital library',\n            'Olympic-size swimming pool',\n            'Modern dormitories',\n            'Hospital and medical facilities',\n            'Multiple dining options'\n        ],\n        accreditations: [\n            'YÖK accreditation',\n            'YÖDAK recognition',\n            'International quality assurance'\n        ],\n        establishedYear: 1988,\n        studentCount: 25000,\n        internationalStudents: 18000,\n        campusSize: '3,000 acres',\n        website: 'https://www.neu.edu.tr',\n        contactEmail: '<EMAIL>',\n        contactPhone: '+90 ************'\n    }\n];\nconst mockPrograms = [\n    {\n        id: '1',\n        name: 'Computer Engineering',\n        slug: 'computer-engineering',\n        universityId: '1',\n        degree: 'Bachelor',\n        field: 'Engineering',\n        duration: '4 years',\n        language: 'English',\n        tuition: 4500,\n        currency: 'USD',\n        description: 'Comprehensive computer engineering program covering software development, hardware design, and emerging technologies.',\n        requirements: [\n            'High school diploma with mathematics and physics',\n            'IELTS 6.0 or TOEFL 79',\n            'SAT or equivalent entrance exam'\n        ],\n        courses: [\n            'Programming Fundamentals',\n            'Data Structures and Algorithms',\n            'Computer Architecture',\n            'Software Engineering',\n            'Database Systems',\n            'Artificial Intelligence'\n        ],\n        careerOpportunities: [\n            'Software Developer',\n            'Systems Analyst',\n            'IT Consultant',\n            'Data Scientist',\n            'Cybersecurity Specialist'\n        ],\n        applicationDeadline: '2024-07-15',\n        startDate: '2024-09-15'\n    },\n    {\n        id: '2',\n        name: 'Business Administration',\n        slug: 'business-administration',\n        universityId: '2',\n        degree: 'Bachelor',\n        field: 'Business',\n        duration: '4 years',\n        language: 'English',\n        tuition: 3800,\n        currency: 'USD',\n        description: 'Dynamic business administration program preparing students for leadership roles in the global business environment.',\n        requirements: [\n            'High school diploma',\n            'English proficiency test',\n            'Personal statement'\n        ],\n        courses: [\n            'Principles of Management',\n            'Marketing Management',\n            'Financial Accounting',\n            'International Business',\n            'Strategic Management',\n            'Entrepreneurship'\n        ],\n        careerOpportunities: [\n            'Business Manager',\n            'Marketing Specialist',\n            'Financial Analyst',\n            'Consultant',\n            'Entrepreneur'\n        ],\n        applicationDeadline: '2024-07-20',\n        startDate: '2024-09-15'\n    }\n];\nconst mockServices = [\n    {\n        id: '1',\n        title: 'University Admissions',\n        slug: 'university-admissions',\n        description: 'Complete assistance with university applications, document preparation, and admission guidance.',\n        image: '/images/services/admissions.jpg',\n        icon: 'GraduationCap',\n        benefits: [\n            'Expert guidance on university selection',\n            'Application document preparation',\n            'Interview preparation',\n            'Scholarship application assistance',\n            'Follow-up with universities'\n        ],\n        process: [\n            'Initial consultation and assessment',\n            'University and program selection',\n            'Document preparation and review',\n            'Application submission',\n            'Follow-up and acceptance'\n        ],\n        pricing: {\n            basic: 500,\n            premium: 1000,\n            currency: 'USD'\n        },\n        featured: true\n    },\n    {\n        id: '2',\n        title: 'Visa Support',\n        slug: 'visa-support',\n        description: 'Professional visa application assistance and documentation support for student visas.',\n        image: '/images/services/visa.jpg',\n        icon: 'FileText',\n        benefits: [\n            'Visa application guidance',\n            'Document checklist and preparation',\n            'Embassy appointment scheduling',\n            'Interview preparation',\n            'Status tracking and updates'\n        ],\n        process: [\n            'Visa requirements assessment',\n            'Document collection and verification',\n            'Application form completion',\n            'Embassy submission',\n            'Follow-up until approval'\n        ],\n        pricing: {\n            basic: 300,\n            premium: 600,\n            currency: 'USD'\n        },\n        featured: true\n    }\n];\nconst mockTestimonials = [\n    {\n        id: '1',\n        name: 'Ahmed Hassan',\n        country: 'Egypt',\n        university: 'Eastern Mediterranean University',\n        program: 'Computer Engineering',\n        image: '/images/testimonials/ahmed.jpg',\n        content: 'Foreingate Group made my dream of studying in Northern Cyprus a reality. Their support throughout the application process was exceptional, and I couldn\\'t be happier with my choice.',\n        rating: 5,\n        year: 2023,\n        videoUrl: 'https://youtube.com/watch?v=example1'\n    },\n    {\n        id: '2',\n        name: 'Fatima Al-Zahra',\n        country: 'Jordan',\n        university: 'Near East University',\n        program: 'Business Administration',\n        image: '/images/testimonials/fatima.jpg',\n        content: 'The team at Foreingate was incredibly helpful from start to finish. They guided me through every step and helped me secure a scholarship. I highly recommend their services.',\n        rating: 5,\n        year: 2023\n    }\n];\nconst mockBlogPosts = [\n    {\n        id: '1',\n        title: 'Top 10 Universities in Northern Cyprus for International Students',\n        slug: 'top-universities-northern-cyprus',\n        excerpt: 'Discover the best universities in Northern Cyprus that offer world-class education and excellent opportunities for international students.',\n        content: 'Full blog content here...',\n        image: '/images/blog/universities-guide.jpg',\n        author: {\n            name: 'Sarah Johnson',\n            avatar: '/images/team/sarah.jpg',\n            bio: 'Education Consultant with 10+ years of experience'\n        },\n        category: 'Education',\n        tags: [\n            'Universities',\n            'Northern Cyprus',\n            'Study Abroad'\n        ],\n        publishedAt: '2024-01-15',\n        readTime: 8,\n        featured: true\n    }\n];\nconst mockTeamMembers = [\n    {\n        id: '1',\n        name: 'Dr. Michael Thompson',\n        position: 'CEO & Founder',\n        image: '/images/team/michael.jpg',\n        bio: 'With over 15 years of experience in international education, Dr. Thompson founded Foreingate Group to help students achieve their academic dreams.',\n        email: '<EMAIL>',\n        phone: '+90 ************',\n        linkedin: 'https://linkedin.com/in/michael-thompson',\n        languages: [\n            'English',\n            'Turkish',\n            'Arabic'\n        ],\n        specializations: [\n            'University Admissions',\n            'Educational Consulting',\n            'International Relations'\n        ]\n    }\n];\nconst mockFAQs = [\n    {\n        id: '1',\n        question: 'What are the admission requirements for universities in Northern Cyprus?',\n        answer: 'Generally, you need a high school diploma, English proficiency test results (IELTS 6.0 or TOEFL 79), academic transcripts, passport copy, and health insurance. Specific requirements may vary by university and program.',\n        category: 'Admission',\n        order: 1\n    },\n    {\n        id: '2',\n        question: 'How long does the visa application process take?',\n        answer: 'The student visa process typically takes 2-4 weeks after submitting all required documents. We recommend starting the process at least 6-8 weeks before your intended travel date.',\n        category: 'Visa',\n        order: 2\n    }\n];\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/mock-data.ts\n"));

/***/ })

});