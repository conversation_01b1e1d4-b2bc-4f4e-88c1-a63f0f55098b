import { NextRequest, NextResponse } from 'next/server'
import { writeFile, readFile, mkdir } from 'fs/promises'
import { existsSync } from 'fs'
import path from 'path'

const DATA_DIR = path.join(process.cwd(), 'data')
const NEWSLETTER_FILE = path.join(DATA_DIR, 'newsletter.json')

// Ensure data directory exists
async function ensureDataDir() {
  if (!existsSync(DATA_DIR)) {
    await mkdir(DATA_DIR, { recursive: true })
  }
}

// Read newsletter subscribers from file
async function readSubscribers() {
  try {
    await ensureDataDir()
    if (!existsSync(NEWSLETTER_FILE)) {
      return []
    }
    const data = await readFile(NEWSLETTER_FILE, 'utf-8')
    return JSON.parse(data)
  } catch (error) {
    console.error('Error reading newsletter subscribers:', error)
    return []
  }
}

// Write newsletter subscribers to file
async function writeSubscribers(subscribers: any[]) {
  try {
    await ensureDataDir()
    await writeFile(NEWSLETTER_FILE, JSON.stringify(subscribers, null, 2))
  } catch (error) {
    console.error('Error writing newsletter subscribers:', error)
    throw error
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { email } = body

    // Validate email
    if (!email) {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      )
    }

    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Please enter a valid email address' },
        { status: 400 }
      )
    }

    // Read existing subscribers
    const subscribers = await readSubscribers()
    
    // Check if email already exists
    const existingSubscriber = subscribers.find((sub: any) => sub.email === email)
    if (existingSubscriber) {
      return NextResponse.json(
        { error: 'This email is already subscribed to our newsletter' },
        { status: 409 }
      )
    }

    // Create new subscriber
    const subscriber = {
      id: Math.random().toString(36).substr(2, 9),
      email,
      subscribedAt: new Date().toISOString(),
      isActive: true,
      source: 'website'
    }

    // Add new subscriber
    subscribers.push(subscriber)
    
    // Write back to file
    await writeSubscribers(subscribers)

    // Send welcome email (if email service is configured)
    try {
      if (process.env.RESEND_API_KEY) {
        const { Resend } = require('resend')
        const resend = new Resend(process.env.RESEND_API_KEY)

        const welcomeEmailHtml = `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center;">
              <h1 style="color: white; margin: 0;">Welcome to Foreingate Group!</h1>
              <p style="color: white; opacity: 0.9; margin: 10px 0 0 0;">Thank you for subscribing to our newsletter</p>
            </div>
            
            <div style="padding: 30px; background: #f8f9fa;">
              <p style="color: #333; font-size: 16px; line-height: 1.6;">Hello!</p>
              
              <p style="color: #555; line-height: 1.6;">
                Welcome to the Foreingate Group community! You've successfully subscribed to our newsletter and will now receive:
              </p>

              <div style="background: #e8f5e8; padding: 20px; border-radius: 8px; border-left: 4px solid #4caf50; margin: 20px 0;">
                <h3 style="color: #2e7d32; margin: 0 0 15px 0;">📧 What you'll receive:</h3>
                <ul style="color: #555; margin: 0; padding-left: 20px;">
                  <li>Latest university admission updates</li>
                  <li>Scholarship opportunities and deadlines</li>
                  <li>Student success stories and tips</li>
                  <li>Educational insights and guides</li>
                  <li>Exclusive offers and early access to services</li>
                </ul>
              </div>

              <div style="background: #fff3e0; padding: 20px; border-radius: 8px; border-left: 4px solid #ff9800; margin: 20px 0;">
                <h3 style="color: #f57c00; margin: 0 0 10px 0;">🎓 Ready to start your journey?</h3>
                <p style="color: #555; margin: 0;">
                  If you're ready to apply to universities in Northern Cyprus, our expert team is here to help. 
                  Contact us for a free consultation!
                </p>
              </div>

              <div style="text-align: center; margin: 30px 0;">
                <a href="${process.env.NEXT_PUBLIC_APP_URL}/contact" 
                   style="background: #667eea; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: bold; display: inline-block;">
                  Get Free Consultation
                </a>
              </div>

              <p style="color: #555; line-height: 1.6;">
                Thank you for choosing Foreingate Group as your educational partner. We're excited to help you achieve your academic dreams!
              </p>

              <p style="color: #555; line-height: 1.6;">
                Best regards,<br>
                <strong>The Foreingate Group Team</strong><br>
                <em>Your Gateway to International Education</em>
              </p>
            </div>

            <div style="background: #333; padding: 20px; text-align: center;">
              <p style="color: #ccc; margin: 0; font-size: 14px;">
                © 2024 Foreingate Group. All rights reserved.<br>
                You can unsubscribe at any time by replying to this email.
              </p>
            </div>
          </div>
        `

        await resend.emails.send({
          from: 'Foreingate Group <<EMAIL>>',
          to: [email],
          subject: 'Welcome to Foreingate Group Newsletter!',
          html: welcomeEmailHtml,
        })
      }
    } catch (emailError) {
      console.error('Welcome email sending failed:', emailError)
      // Don't fail the subscription if email fails
    }

    return NextResponse.json({
      success: true,
      message: 'Successfully subscribed to newsletter!',
      subscriberId: subscriber.id
    })

  } catch (error) {
    console.error('Newsletter subscription error:', error)
    return NextResponse.json(
      { error: 'Failed to subscribe. Please try again.' },
      { status: 500 }
    )
  }
}

// GET endpoint to retrieve subscribers count (for admin use)
export async function GET(request: NextRequest) {
  try {
    const subscribers = await readSubscribers()
    const activeSubscribers = subscribers.filter((sub: any) => sub.isActive)
    
    return NextResponse.json({
      success: true,
      totalSubscribers: subscribers.length,
      activeSubscribers: activeSubscribers.length,
      latestSubscriptions: subscribers.slice(-5).reverse() // Last 5 subscribers
    })
  } catch (error) {
    console.error('Error fetching newsletter stats:', error)
    return NextResponse.json(
      { error: 'Failed to fetch newsletter statistics' },
      { status: 500 }
    )
  }
}
