'use client'

import { useState, useEffect, useRef } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Search, X, Clock, TrendingUp, BookOpen, GraduationCap, DollarSign } from 'lucide-react'
import { Input } from './input'
import { But<PERSON> } from './button'
import { Badge } from './badge'

interface SearchResult {
  id: string
  title: string
  description: string
  category: 'university' | 'program' | 'service' | 'faq' | 'cost' | 'general'
  url: string
  relevance: number
}

interface SearchProps {
  placeholder?: string
  className?: string
  onSearch?: (query: string) => void
  showSuggestions?: boolean
}

export function SearchBar({ 
  placeholder = "Search universities, programs, costs...",
  className = "",
  onSearch,
  showSuggestions = true
}: SearchProps) {
  const [query, setQuery] = useState('')
  const [isOpen, setIsOpen] = useState(false)
  const [results, setResults] = useState<SearchResult[]>([])
  const [recentSearches, setRecentSearches] = useState<string[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const searchRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  // Mock search data - in production, this would come from an API
  const searchData: SearchResult[] = [
    {
      id: '1',
      title: 'Eastern Mediterranean University (EMU)',
      description: 'Leading university in Famagusta with 20,000+ students',
      category: 'university',
      url: '/universities/emu',
      relevance: 0.95
    },
    {
      id: '2',
      title: 'Computer Engineering Program',
      description: 'Bachelor\'s degree in Computer Engineering at EMU',
      category: 'program',
      url: '/programs/computer-engineering',
      relevance: 0.90
    },
    {
      id: '3',
      title: 'Tuition Fees and Costs',
      description: 'Complete breakdown of study costs in Northern Cyprus',
      category: 'cost',
      url: '/costs',
      relevance: 0.85
    },
    {
      id: '4',
      title: 'University Admission Process',
      description: 'Step-by-step guide to applying for universities',
      category: 'service',
      url: '/services/admissions',
      relevance: 0.80
    },
    {
      id: '5',
      title: 'Scholarship Opportunities',
      description: 'Merit scholarships and financial aid options',
      category: 'cost',
      url: '/scholarships',
      relevance: 0.75
    }
  ]

  const popularSearches = [
    'EMU Computer Engineering',
    'NEU Medicine Program',
    'Tuition fees',
    'Scholarship requirements',
    'Visa process',
    'Accommodation costs'
  ]

  useEffect(() => {
    // Load recent searches from localStorage
    const saved = localStorage.getItem('recentSearches')
    if (saved) {
      setRecentSearches(JSON.parse(saved))
    }
  }, [])

  useEffect(() => {
    // Close search when clicking outside
    function handleClickOutside(event: MouseEvent) {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const performSearch = async (searchQuery: string) => {
    if (!searchQuery.trim()) {
      setResults([])
      return
    }

    setIsLoading(true)
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300))
    
    // Simple search implementation
    const filtered = searchData.filter(item =>
      item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.description.toLowerCase().includes(searchQuery.toLowerCase())
    ).sort((a, b) => b.relevance - a.relevance)

    setResults(filtered)
    setIsLoading(false)
  }

  const handleSearch = (searchQuery: string) => {
    setQuery(searchQuery)
    performSearch(searchQuery)
    
    if (onSearch) {
      onSearch(searchQuery)
    }

    // Add to recent searches
    if (searchQuery.trim() && !recentSearches.includes(searchQuery)) {
      const updated = [searchQuery, ...recentSearches.slice(0, 4)]
      setRecentSearches(updated)
      localStorage.setItem('recentSearches', JSON.stringify(updated))
    }
  }

  const handleInputChange = (value: string) => {
    setQuery(value)
    if (value.trim()) {
      performSearch(value)
      setIsOpen(true)
    } else {
      setResults([])
      setIsOpen(false)
    }
  }

  const clearSearch = () => {
    setQuery('')
    setResults([])
    setIsOpen(false)
    inputRef.current?.focus()
  }

  const getCategoryIcon = (category: SearchResult['category']) => {
    switch (category) {
      case 'university':
        return <GraduationCap className="w-4 h-4" />
      case 'program':
        return <BookOpen className="w-4 h-4" />
      case 'cost':
        return <DollarSign className="w-4 h-4" />
      default:
        return <Search className="w-4 h-4" />
    }
  }

  const getCategoryColor = (category: SearchResult['category']) => {
    switch (category) {
      case 'university':
        return 'bg-blue-100 text-blue-800'
      case 'program':
        return 'bg-green-100 text-green-800'
      case 'cost':
        return 'bg-yellow-100 text-yellow-800'
      case 'service':
        return 'bg-purple-100 text-purple-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div ref={searchRef} className={`relative ${className}`}>
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
        <Input
          ref={inputRef}
          type="text"
          placeholder={placeholder}
          value={query}
          onChange={(e) => handleInputChange(e.target.value)}
          onFocus={() => setIsOpen(true)}
          onKeyDown={(e) => {
            if (e.key === 'Enter') {
              handleSearch(query)
            }
            if (e.key === 'Escape') {
              setIsOpen(false)
            }
          }}
          className="pl-10 pr-10"
        />
        {query && (
          <Button
            variant="ghost"
            size="sm"
            onClick={clearSearch}
            className="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
          >
            <X className="w-4 h-4" />
          </Button>
        )}
      </div>

      <AnimatePresence>
        {isOpen && showSuggestions && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="absolute top-full left-0 right-0 mt-2 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50 max-h-96 overflow-y-auto"
          >
            {isLoading ? (
              <div className="p-4 text-center">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
                <p className="text-sm text-gray-500 mt-2">Searching...</p>
              </div>
            ) : results.length > 0 ? (
              <div className="py-2">
                <div className="px-4 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wide">
                  Search Results
                </div>
                {results.map((result) => (
                  <motion.a
                    key={result.id}
                    href={result.url}
                    className="block px-4 py-3 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                    whileHover={{ x: 4 }}
                  >
                    <div className="flex items-start space-x-3">
                      <div className="flex-shrink-0 mt-1">
                        {getCategoryIcon(result.category)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 mb-1">
                          <p className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                            {result.title}
                          </p>
                          <Badge variant="outline" className={`text-xs ${getCategoryColor(result.category)}`}>
                            {result.category}
                          </Badge>
                        </div>
                        <p className="text-sm text-gray-500 dark:text-gray-400 line-clamp-2">
                          {result.description}
                        </p>
                      </div>
                    </div>
                  </motion.a>
                ))}
              </div>
            ) : query ? (
              <div className="p-4 text-center">
                <p className="text-sm text-gray-500">No results found for "{query}"</p>
                <p className="text-xs text-gray-400 mt-1">Try different keywords or browse our services</p>
              </div>
            ) : (
              <div className="py-2">
                {recentSearches.length > 0 && (
                  <div className="mb-4">
                    <div className="px-4 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wide flex items-center">
                      <Clock className="w-3 h-3 mr-1" />
                      Recent Searches
                    </div>
                    {recentSearches.map((search, index) => (
                      <button
                        key={index}
                        onClick={() => handleSearch(search)}
                        className="block w-full text-left px-4 py-2 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                      >
                        <span className="text-sm text-gray-700 dark:text-gray-300">{search}</span>
                      </button>
                    ))}
                  </div>
                )}
                
                <div>
                  <div className="px-4 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wide flex items-center">
                    <TrendingUp className="w-3 h-3 mr-1" />
                    Popular Searches
                  </div>
                  {popularSearches.map((search, index) => (
                    <button
                      key={index}
                      onClick={() => handleSearch(search)}
                      className="block w-full text-left px-4 py-2 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                    >
                      <span className="text-sm text-gray-700 dark:text-gray-300">{search}</span>
                    </button>
                  ))}
                </div>
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

// Compact search for header
export function HeaderSearch() {
  const [isExpanded, setIsExpanded] = useState(false)

  return (
    <div className="relative">
      {isExpanded ? (
        <motion.div
          initial={{ width: 0 }}
          animate={{ width: 300 }}
          exit={{ width: 0 }}
          className="overflow-hidden"
        >
          <SearchBar
            placeholder="Search..."
            className="w-full"
            onSearch={() => setIsExpanded(false)}
          />
        </motion.div>
      ) : (
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsExpanded(true)}
          className="p-2"
        >
          <Search className="w-5 h-5" />
        </Button>
      )}
    </div>
  )
}
