'use client'

import { createContext, useContext } from 'react'
import { Locale, defaultLocale } from '@/lib/i18n'
import { Translations } from '@/lib/translations'

// Import all translations
import { en } from '@/locales/en'
import { tr } from '@/locales/tr'
import { ar } from '@/locales/ar'
import { fr } from '@/locales/fr'
import { es } from '@/locales/es'

// Translation map
const translations: Record<Locale, Translations> = {
  en,
  tr,
  ar,
  fr,
  es,
  // Add more languages as they are created
  es: en,
  de: en,
  ru: en,
  zh: en,
  ja: en,
  ko: en,
  pt: en,
  it: en,
  nl: en,
  sv: en,
  no: en,
  da: en,
  fi: en,
  pl: en,
  cs: en,
  hu: en,
  ro: en,
  bg: en,
  hr: en,
  sk: en,
  sl: en,
  et: en,
  lv: en,
  lt: en,
  mt: en,
  cy: en,
  ga: en,
  is: en,
  mk: en,
  sq: en,
  sr: en,
  bs: en,
  me: en,
  uk: en,
  be: en,
  kk: en,
  ky: en,
  uz: en,
  tg: en,
  tm: en,
  mn: en,
  ka: en,
  hy: en,
  az: en,
  fa: en,
  ur: en,
  hi: en,
  bn: en,
  ta: en,
  te: en,
  ml: en,
  kn: en,
  gu: en,
  pa: en,
  or: en,
  as: en,
  ne: en,
  si: en,
  my: en,
  th: en,
  lo: en,
  km: en,
  vi: en,
  id: en,
  ms: en,
  tl: en,
  haw: en,
  mi: en,
  sm: en,
  to: en,
  fj: en,
  sw: en,
  am: en,
  ti: en,
  om: en,
  so: en,
  rw: en,
  rn: en,
  lg: en,
  ak: en,
  tw: en,
  yo: en,
  ig: en,
  ha: en,
  ff: en,
  wo: en,
  sn: en,
  zu: en,
  xh: en,
  af: en,
  st: en,
  tn: en,
  ss: en,
  ve: en,
  ts: en,
  nr: en,
  he: en,
  yi: en,
  jv: en,
  su: en,
  mad: en,
  ban: en,
  bug: en,
  mak: en,
  min: en,
  ace: en,
  bjn: en,
  bbc: en,
  nij: en,
  rej: en,
  sas: en,
  tet: en,
}

interface TranslationContextType {
  locale: Locale
  setLocale: (locale: Locale) => void
  t: Translations
  isRTL: boolean
}

export const TranslationContext = createContext<TranslationContextType | undefined>(undefined)

export function useTranslation() {
  const context = useContext(TranslationContext)
  if (!context) {
    throw new Error('useTranslation must be used within a TranslationProvider')
  }
  return context
}

// Helper function to get translations for a specific locale
export function getTranslations(locale: Locale): Translations {
  return translations[locale] || translations[defaultLocale]
}

// Helper function to get nested translation value
export function getNestedTranslation(
  translations: Translations,
  key: string
): string {
  const keys = key.split('.')
  let value: any = translations
  
  for (const k of keys) {
    if (value && typeof value === 'object' && k in value) {
      value = value[k]
    } else {
      return key // Return the key if translation not found
    }
  }
  
  return typeof value === 'string' ? value : key
}

// Translation function with interpolation support
export function translateWithInterpolation(
  template: string,
  values: Record<string, string | number> = {}
): string {
  return template.replace(/\{\{(\w+)\}\}/g, (match, key) => {
    return values[key]?.toString() || match
  })
}

// Pluralization helper
export function pluralize(
  count: number,
  singular: string,
  plural?: string
): string {
  if (count === 1) {
    return singular
  }
  return plural || `${singular}s`
}

// Date formatting helper
export function formatDate(
  date: Date,
  locale: Locale,
  options?: Intl.DateTimeFormatOptions
): string {
  try {
    return new Intl.DateTimeFormat(locale, options).format(date)
  } catch (error) {
    // Fallback to English if locale is not supported
    return new Intl.DateTimeFormat('en', options).format(date)
  }
}

// Number formatting helper
export function formatNumber(
  number: number,
  locale: Locale,
  options?: Intl.NumberFormatOptions
): string {
  try {
    return new Intl.NumberFormat(locale, options).format(number)
  } catch (error) {
    // Fallback to English if locale is not supported
    return new Intl.NumberFormat('en', options).format(number)
  }
}

// Currency formatting helper
export function formatCurrency(
  amount: number,
  locale: Locale,
  currency: string = 'USD'
): string {
  try {
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency
    }).format(amount)
  } catch (error) {
    // Fallback to English if locale is not supported
    return new Intl.NumberFormat('en', {
      style: 'currency',
      currency
    }).format(amount)
  }
}

// Relative time formatting helper
export function formatRelativeTime(
  date: Date,
  locale: Locale
): string {
  try {
    const rtf = new Intl.RelativeTimeFormat(locale, { numeric: 'auto' })
    const now = new Date()
    const diffInSeconds = Math.floor((date.getTime() - now.getTime()) / 1000)
    
    if (Math.abs(diffInSeconds) < 60) {
      return rtf.format(diffInSeconds, 'second')
    }
    
    const diffInMinutes = Math.floor(diffInSeconds / 60)
    if (Math.abs(diffInMinutes) < 60) {
      return rtf.format(diffInMinutes, 'minute')
    }
    
    const diffInHours = Math.floor(diffInMinutes / 60)
    if (Math.abs(diffInHours) < 24) {
      return rtf.format(diffInHours, 'hour')
    }
    
    const diffInDays = Math.floor(diffInHours / 24)
    return rtf.format(diffInDays, 'day')
  } catch (error) {
    // Fallback to simple date formatting
    return formatDate(date, locale)
  }
}
