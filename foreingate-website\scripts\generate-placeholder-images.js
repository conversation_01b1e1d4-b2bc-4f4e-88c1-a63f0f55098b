const fs = require('fs');
const path = require('path');

// Create directories if they don't exist
const createDir = (dir) => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
};

// Generate a simple SVG placeholder
const generateSVGPlaceholder = (width, height, text, bgColor = '#f3f4f6', textColor = '#6b7280') => {
  return `<svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
    <rect width="100%" height="100%" fill="${bgColor}"/>
    <text x="50%" y="50%" font-family="Arial, sans-serif" font-size="16" fill="${textColor}" text-anchor="middle" dy=".3em">${text}</text>
  </svg>`;
};

// Create placeholder images
const createPlaceholders = () => {
  const publicDir = path.join(__dirname, '..', 'public');
  const imagesDir = path.join(publicDir, 'images');
  
  // Create directories
  createDir(path.join(imagesDir, 'hero'));
  createDir(path.join(imagesDir, 'universities'));
  createDir(path.join(imagesDir, 'testimonials'));

  // Hero images
  const heroSVG = generateSVGPlaceholder(1200, 600, 'Students Campus', '#dbeafe', '#1e40af');
  fs.writeFileSync(path.join(imagesDir, 'hero', 'students-campus.svg'), heroSVG);

  // University logos and campus images
  const universities = [
    { code: 'emu', name: 'Eastern Mediterranean University' },
    { code: 'neu', name: 'Near East University' },
    { code: 'ciu', name: 'Cyprus International University' }
  ];

  universities.forEach(uni => {
    // Logo
    const logoSVG = generateSVGPlaceholder(200, 100, uni.name, '#f0f9ff', '#0369a1');
    fs.writeFileSync(path.join(imagesDir, 'universities', `${uni.code}-logo.svg`), logoSVG);
    
    // Campus image
    const campusSVG = generateSVGPlaceholder(600, 400, `${uni.name} Campus`, '#ecfdf5', '#059669');
    fs.writeFileSync(path.join(imagesDir, 'universities', `${uni.code}-campus-1.svg`), campusSVG);
  });

  // Testimonial photos
  const testimonials = [
    'ahmed-hassan',
    'maria-rodriguez', 
    'david-chen',
    'fatima-alzahra',
    'james-wilson',
    'priya-sharma',
    'omar-khalil',
    'elena-popovic'
  ];

  testimonials.forEach(name => {
    const photoSVG = generateSVGPlaceholder(150, 150, name.split('-').map(n => n.charAt(0).toUpperCase() + n.slice(1)).join(' '), '#fef3c7', '#d97706');
    fs.writeFileSync(path.join(imagesDir, 'testimonials', `${name}.svg`), photoSVG);
  });

  console.log('✅ Placeholder images generated successfully!');
  console.log('📁 Generated files:');
  console.log('   - Hero image: students-campus.svg');
  console.log('   - University logos: emu-logo.svg, neu-logo.svg, ciu-logo.svg');
  console.log('   - Campus images: *-campus-1.svg');
  console.log('   - Testimonial photos: 8 student photos');
};

// Run the script
createPlaceholders();
