"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/faq/page",{

/***/ "(app-pages-browser)/./src/components/sections/faq-categories.tsx":
/*!****************************************************!*\
  !*** ./src/components/sections/faq-categories.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FAQCategoriesSection: () => (/* binding */ FAQCategoriesSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/../node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var react_intersection_observer__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-intersection-observer */ \"(app-pages-browser)/../node_modules/react-intersection-observer/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronDown_ChevronUp_DollarSign_FileText_Globe_HelpCircle_Home_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronDown,ChevronUp,DollarSign,FileText,Globe,HelpCircle,Home!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronDown_ChevronUp_DollarSign_FileText_Globe_HelpCircle_Home_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronDown,ChevronUp,DollarSign,FileText,Globe,HelpCircle,Home!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronDown_ChevronUp_DollarSign_FileText_Globe_HelpCircle_Home_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronDown,ChevronUp,DollarSign,FileText,Globe,HelpCircle,Home!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronDown_ChevronUp_DollarSign_FileText_Globe_HelpCircle_Home_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronDown,ChevronUp,DollarSign,FileText,Globe,HelpCircle,Home!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronDown_ChevronUp_DollarSign_FileText_Globe_HelpCircle_Home_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronDown,ChevronUp,DollarSign,FileText,Globe,HelpCircle,Home!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/circle-question-mark.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronDown_ChevronUp_DollarSign_FileText_Globe_HelpCircle_Home_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronDown,ChevronUp,DollarSign,FileText,Globe,HelpCircle,Home!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronDown_ChevronUp_DollarSign_FileText_Globe_HelpCircle_Home_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronDown,ChevronUp,DollarSign,FileText,Globe,HelpCircle,Home!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronDown_ChevronUp_DollarSign_FileText_Globe_HelpCircle_Home_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronDown,ChevronUp,DollarSign,FileText,Globe,HelpCircle,Home!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* __next_internal_client_entry_do_not_use__ FAQCategoriesSection auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst faqData = {\n    admissions: {\n        icon: _barrel_optimize_names_BookOpen_ChevronDown_ChevronUp_DollarSign_FileText_Globe_HelpCircle_Home_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        title: 'Admissions & Applications',\n        questions: [\n            {\n                q: 'What are the admission requirements for international students?',\n                a: 'International students need a high school diploma or equivalent, English proficiency test scores (IELTS/TOEFL), passport copy, and academic transcripts. Specific requirements may vary by program and university.'\n            },\n            {\n                q: 'When is the application deadline?',\n                a: 'Most universities have multiple intake periods: Fall (September), Spring (February), and Summer (June). Application deadlines are typically 2-3 months before each intake period.'\n            },\n            {\n                q: 'Can I apply to multiple universities?',\n                a: 'Yes, you can apply to multiple universities through our platform. We recommend applying to 3-5 universities to increase your chances of acceptance.'\n            },\n            {\n                q: 'How long does the application process take?',\n                a: 'The complete application process typically takes 4-6 weeks from submission to final acceptance, including document verification and university review.'\n            }\n        ]\n    },\n    visa: {\n        icon: _barrel_optimize_names_BookOpen_ChevronDown_ChevronUp_DollarSign_FileText_Globe_HelpCircle_Home_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        title: 'Visa & Documentation',\n        questions: [\n            {\n                q: 'Do I need a visa to study in Northern Cyprus?',\n                a: 'Most international students need a student visa. EU citizens may have different requirements. We provide complete visa support and guidance throughout the process.'\n            },\n            {\n                q: 'What documents are required for a student visa?',\n                a: 'Required documents include acceptance letter, passport, financial proof, health insurance, medical certificate, and police clearance certificate.'\n            },\n            {\n                q: 'How long does visa processing take?',\n                a: 'Student visa processing typically takes 2-4 weeks after submitting all required documents. We recommend applying at least 6 weeks before your intended travel date.'\n            }\n        ]\n    },\n    housing: {\n        icon: _barrel_optimize_names_BookOpen_ChevronDown_ChevronUp_DollarSign_FileText_Globe_HelpCircle_Home_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        title: 'Housing & Accommodation',\n        questions: [\n            {\n                q: 'What housing options are available for students?',\n                a: 'Students can choose from university dormitories, private apartments, shared housing, or homestay programs. We help you find the best option based on your budget and preferences.'\n            },\n            {\n                q: 'How much does student accommodation cost?',\n                a: 'Housing costs range from $200-600 per month depending on the type and location. University dormitories are typically the most affordable option.'\n            },\n            {\n                q: 'Is accommodation guaranteed for international students?',\n                a: 'Most universities guarantee accommodation for first-year international students. We recommend applying for housing early to secure your preferred option.'\n            }\n        ]\n    },\n    costs: {\n        icon: _barrel_optimize_names_BookOpen_ChevronDown_ChevronUp_DollarSign_FileText_Globe_HelpCircle_Home_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        title: 'Costs & Financial Aid',\n        questions: [\n            {\n                q: 'What are the tuition fees for international students?',\n                a: 'Tuition fees range from $3,500 to $12,000 per year depending on the program and university. Engineering and medical programs typically have higher fees.'\n            },\n            {\n                q: 'Are scholarships available for international students?',\n                a: 'Yes, many universities offer merit-based scholarships ranging from 25% to 100% tuition coverage. We help you identify and apply for available scholarships.'\n            },\n            {\n                q: 'What are the living costs in Northern Cyprus?',\n                a: 'Monthly living costs range from $400-800 including accommodation, food, transportation, and personal expenses. Northern Cyprus is generally more affordable than many European countries.'\n            }\n        ]\n    },\n    general: {\n        icon: _barrel_optimize_names_BookOpen_ChevronDown_ChevronUp_DollarSign_FileText_Globe_HelpCircle_Home_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        title: 'General Information',\n        questions: [\n            {\n                q: 'What language are courses taught in?',\n                a: 'Most programs are taught in English, with some programs available in Turkish. All universities require English proficiency for English-taught programs.'\n            },\n            {\n                q: 'Is Northern Cyprus safe for international students?',\n                a: 'Yes, Northern Cyprus is considered very safe with low crime rates. Universities provide additional security measures and support for international students.'\n            },\n            {\n                q: 'Can I work while studying?',\n                a: 'International students can work part-time (up to 20 hours per week) with proper permits. Many students find opportunities in tutoring, hospitality, or campus jobs.'\n            }\n        ]\n    },\n    support: {\n        icon: _barrel_optimize_names_BookOpen_ChevronDown_ChevronUp_DollarSign_FileText_Globe_HelpCircle_Home_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        title: 'Student Support Services',\n        questions: [\n            {\n                q: 'What support services do you provide?',\n                a: 'We provide comprehensive support including application assistance, visa guidance, accommodation help, airport pickup, academic support, and ongoing counseling throughout your studies.'\n            },\n            {\n                q: 'Do you provide support after arrival?',\n                a: 'Yes, our support continues after your arrival with orientation programs, academic counseling, career guidance, and assistance with any challenges you may face.'\n            },\n            {\n                q: 'How can I contact support if I need help?',\n                a: 'You can reach our support team 24/7 through phone, email, WhatsApp, or our online portal. We also have local representatives in Northern Cyprus for immediate assistance.'\n            }\n        ]\n    }\n};\nfunction FAQCategoriesSection() {\n    _s();\n    const [activeCategory, setActiveCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('admissions');\n    const [expandedQuestion, setExpandedQuestion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [ref, inView] = (0,react_intersection_observer__WEBPACK_IMPORTED_MODULE_8__.useInView)({\n        triggerOnce: true,\n        threshold: 0.1\n    });\n    const toggleQuestion = (index)=>{\n        setExpandedQuestion(expandedQuestion === index ? null : index);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        ref: ref,\n        className: \"section-padding bg-muted/30\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    animate: inView ? {\n                        opacity: 1,\n                        y: 0\n                    } : {},\n                    transition: {\n                        duration: 0.8\n                    },\n                    className: \"text-center max-w-3xl mx-auto mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold mb-4\",\n                            children: [\n                                \"Browse Questions by\",\n                                ' ',\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"gradient-text\",\n                                    children: \"Category\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-categories.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-categories.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-muted-foreground\",\n                            children: \"Find detailed answers organized by topic to help you make informed decisions\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-categories.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-categories.tsx\",\n                    lineNumber: 141,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl mx-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid lg:grid-cols-4 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    x: -50\n                                },\n                                animate: inView ? {\n                                    opacity: 1,\n                                    x: 0\n                                } : {},\n                                transition: {\n                                    duration: 0.8,\n                                    delay: 0.2\n                                },\n                                className: \"lg:col-span-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-background rounded-xl p-6 shadow-sm border sticky top-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold mb-4\",\n                                            children: \"Categories\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-categories.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                            className: \"space-y-2\",\n                                            children: Object.entries(faqData).map((param)=>{\n                                                let [key, category] = param;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setActiveCategory(key),\n                                                    className: \"w-full flex items-center space-x-3 p-3 rounded-lg text-left transition-all duration-200 \".concat(activeCategory === key ? 'bg-primary text-primary-foreground' : 'hover:bg-muted'),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(category.icon, {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-categories.tsx\",\n                                                            lineNumber: 178,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium text-sm\",\n                                                                    children: category.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-categories.tsx\",\n                                                                    lineNumber: 180,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs \".concat(activeCategory === key ? 'text-primary-foreground/80' : 'text-muted-foreground'),\n                                                                    children: [\n                                                                        category.questions.length,\n                                                                        \" questions\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-categories.tsx\",\n                                                                    lineNumber: 181,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-categories.tsx\",\n                                                            lineNumber: 179,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, key, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-categories.tsx\",\n                                                    lineNumber: 169,\n                                                    columnNumber: 21\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-categories.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-categories.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-categories.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    x: 50\n                                },\n                                animate: inView ? {\n                                    opacity: 1,\n                                    x: 0\n                                } : {},\n                                transition: {\n                                    duration: 0.8,\n                                    delay: 0.4\n                                },\n                                className: \"lg:col-span-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-background rounded-xl p-8 shadow-sm border\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 mb-8\",\n                                            children: [\n                                                /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(faqData[activeCategory].icon, {\n                                                    className: \"w-8 h-8 text-primary\"\n                                                }),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-2xl font-bold\",\n                                                            children: faqData[activeCategory].title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-categories.tsx\",\n                                                            lineNumber: 207,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-muted-foreground\",\n                                                            children: [\n                                                                faqData[activeCategory].questions.length,\n                                                                \" frequently asked questions\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-categories.tsx\",\n                                                            lineNumber: 210,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-categories.tsx\",\n                                                    lineNumber: 206,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-categories.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: faqData[activeCategory].questions.map((faq, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                                    initial: {\n                                                        opacity: 0,\n                                                        y: 20\n                                                    },\n                                                    animate: {\n                                                        opacity: 1,\n                                                        y: 0\n                                                    },\n                                                    transition: {\n                                                        duration: 0.5,\n                                                        delay: index * 0.1\n                                                    },\n                                                    className: \"border rounded-lg overflow-hidden\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>toggleQuestion(index),\n                                                            className: \"w-full flex items-center justify-between p-4 text-left hover:bg-muted/50 transition-colors\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium pr-4\",\n                                                                    children: faq.q\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-categories.tsx\",\n                                                                    lineNumber: 230,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                expandedQuestion === index ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronDown_ChevronUp_DollarSign_FileText_Globe_HelpCircle_Home_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    className: \"w-5 h-5 text-primary flex-shrink-0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-categories.tsx\",\n                                                                    lineNumber: 232,\n                                                                    columnNumber: 27\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronDown_ChevronUp_DollarSign_FileText_Globe_HelpCircle_Home_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                    className: \"w-5 h-5 text-muted-foreground flex-shrink-0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-categories.tsx\",\n                                                                    lineNumber: 234,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-categories.tsx\",\n                                                            lineNumber: 226,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        expandedQuestion === index && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                                            initial: {\n                                                                opacity: 0,\n                                                                height: 0\n                                                            },\n                                                            animate: {\n                                                                opacity: 1,\n                                                                height: 'auto'\n                                                            },\n                                                            exit: {\n                                                                opacity: 0,\n                                                                height: 0\n                                                            },\n                                                            transition: {\n                                                                duration: 0.3\n                                                            },\n                                                            className: \"px-4 pb-4 border-t bg-muted/20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-muted-foreground pt-4\",\n                                                                children: faq.a\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-categories.tsx\",\n                                                                lineNumber: 246,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-categories.tsx\",\n                                                            lineNumber: 239,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-categories.tsx\",\n                                                    lineNumber: 219,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-categories.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-categories.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-categories.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-categories.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-categories.tsx\",\n                    lineNumber: 156,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-categories.tsx\",\n            lineNumber: 139,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-categories.tsx\",\n        lineNumber: 138,\n        columnNumber: 5\n    }, this);\n}\n_s(FAQCategoriesSection, \"MI9fDkQyImkL+bS/13vw1qjMqjc=\", false, function() {\n    return [\n        react_intersection_observer__WEBPACK_IMPORTED_MODULE_8__.useInView\n    ];\n});\n_c = FAQCategoriesSection;\nvar _c;\n$RefreshReg$(_c, \"FAQCategoriesSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/faq-categories.tsx\n"));

/***/ })

});