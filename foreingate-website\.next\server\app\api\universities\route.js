/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/universities/route";
exports.ids = ["app/api/universities/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Funiversities%2Froute&page=%2Fapi%2Funiversities%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Funiversities%2Froute.ts&appDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Funiversities%2Froute&page=%2Fapi%2Funiversities%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Funiversities%2Froute.ts&appDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Nidhal_Documents_augment_projects_foreingate_groupe_foreingate_website_src_app_api_universities_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/universities/route.ts */ \"(rsc)/./src/app/api/universities/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/universities/route\",\n        pathname: \"/api/universities\",\n        filename: \"route\",\n        bundlePath: \"app/api/universities/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\api\\\\universities\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Nidhal_Documents_augment_projects_foreingate_groupe_foreingate_website_src_app_api_universities_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZhcGklMkZ1bml2ZXJzaXRpZXMlMkZyb3V0ZSZwYWdlPSUyRmFwaSUyRnVuaXZlcnNpdGllcyUyRnJvdXRlJmFwcFBhdGhzPSZwYWdlUGF0aD1wcml2YXRlLW5leHQtYXBwLWRpciUyRmFwaSUyRnVuaXZlcnNpdGllcyUyRnJvdXRlLnRzJmFwcERpcj1DJTNBJTVDVXNlcnMlNUNOaWRoYWwlNUNEb2N1bWVudHMlNUNhdWdtZW50LXByb2plY3RzJTVDZm9yZWluZ2F0ZV9ncm91cGUlNUNmb3JlaW5nYXRlLXdlYnNpdGUlNUNzcmMlNUNhcHAmcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyZyb290RGlyPUMlM0ElNUNVc2VycyU1Q05pZGhhbCU1Q0RvY3VtZW50cyU1Q2F1Z21lbnQtcHJvamVjdHMlNUNmb3JlaW5nYXRlX2dyb3VwZSU1Q2ZvcmVpbmdhdGUtd2Vic2l0ZSZpc0Rldj10cnVlJnRzY29uZmlnUGF0aD10c2NvbmZpZy5qc29uJmJhc2VQYXRoPSZhc3NldFByZWZpeD0mbmV4dENvbmZpZ091dHB1dD0mcHJlZmVycmVkUmVnaW9uPSZtaWRkbGV3YXJlQ29uZmlnPWUzMCUzRCEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFBK0Y7QUFDdkM7QUFDcUI7QUFDNEU7QUFDeko7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLHlHQUFtQjtBQUMzQztBQUNBLGNBQWMsa0VBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLFlBQVk7QUFDWixDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0EsUUFBUSxzREFBc0Q7QUFDOUQ7QUFDQSxXQUFXLDRFQUFXO0FBQ3RCO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDMEY7O0FBRTFGIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQXBwUm91dGVSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLW1vZHVsZXMvYXBwLXJvdXRlL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUta2luZFwiO1xuaW1wb3J0IHsgcGF0Y2hGZXRjaCBhcyBfcGF0Y2hGZXRjaCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2xpYi9wYXRjaC1mZXRjaFwiO1xuaW1wb3J0ICogYXMgdXNlcmxhbmQgZnJvbSBcIkM6XFxcXFVzZXJzXFxcXE5pZGhhbFxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxmb3JlaW5nYXRlX2dyb3VwZVxcXFxmb3JlaW5nYXRlLXdlYnNpdGVcXFxcc3JjXFxcXGFwcFxcXFxhcGlcXFxcdW5pdmVyc2l0aWVzXFxcXHJvdXRlLnRzXCI7XG4vLyBXZSBpbmplY3QgdGhlIG5leHRDb25maWdPdXRwdXQgaGVyZSBzbyB0aGF0IHdlIGNhbiB1c2UgdGhlbSBpbiB0aGUgcm91dGVcbi8vIG1vZHVsZS5cbmNvbnN0IG5leHRDb25maWdPdXRwdXQgPSBcIlwiXG5jb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBBcHBSb3V0ZVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5BUFBfUk9VVEUsXG4gICAgICAgIHBhZ2U6IFwiL2FwaS91bml2ZXJzaXRpZXMvcm91dGVcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL2FwaS91bml2ZXJzaXRpZXNcIixcbiAgICAgICAgZmlsZW5hbWU6IFwicm91dGVcIixcbiAgICAgICAgYnVuZGxlUGF0aDogXCJhcHAvYXBpL3VuaXZlcnNpdGllcy9yb3V0ZVwiXG4gICAgfSxcbiAgICByZXNvbHZlZFBhZ2VQYXRoOiBcIkM6XFxcXFVzZXJzXFxcXE5pZGhhbFxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxmb3JlaW5nYXRlX2dyb3VwZVxcXFxmb3JlaW5nYXRlLXdlYnNpdGVcXFxcc3JjXFxcXGFwcFxcXFxhcGlcXFxcdW5pdmVyc2l0aWVzXFxcXHJvdXRlLnRzXCIsXG4gICAgbmV4dENvbmZpZ091dHB1dCxcbiAgICB1c2VybGFuZFxufSk7XG4vLyBQdWxsIG91dCB0aGUgZXhwb3J0cyB0aGF0IHdlIG5lZWQgdG8gZXhwb3NlIGZyb20gdGhlIG1vZHVsZS4gVGhpcyBzaG91bGRcbi8vIGJlIGVsaW1pbmF0ZWQgd2hlbiB3ZSd2ZSBtb3ZlZCB0aGUgb3RoZXIgcm91dGVzIHRvIHRoZSBuZXcgZm9ybWF0LiBUaGVzZVxuLy8gYXJlIHVzZWQgdG8gaG9vayBpbnRvIHRoZSByb3V0ZS5cbmNvbnN0IHsgd29ya0FzeW5jU3RvcmFnZSwgd29ya1VuaXRBc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzIH0gPSByb3V0ZU1vZHVsZTtcbmZ1bmN0aW9uIHBhdGNoRmV0Y2goKSB7XG4gICAgcmV0dXJuIF9wYXRjaEZldGNoKHtcbiAgICAgICAgd29ya0FzeW5jU3RvcmFnZSxcbiAgICAgICAgd29ya1VuaXRBc3luY1N0b3JhZ2VcbiAgICB9KTtcbn1cbmV4cG9ydCB7IHJvdXRlTW9kdWxlLCB3b3JrQXN5bmNTdG9yYWdlLCB3b3JrVW5pdEFzeW5jU3RvcmFnZSwgc2VydmVySG9va3MsIHBhdGNoRmV0Y2gsICB9O1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hcHAtcm91dGUuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Funiversities%2Froute&page=%2Fapi%2Funiversities%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Funiversities%2Froute.ts&appDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/universities/route.ts":
/*!*******************************************!*\
  !*** ./src/app/api/universities/route.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fs/promises */ \"fs/promises\");\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fs_promises__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nconst DATA_DIR = path__WEBPACK_IMPORTED_MODULE_3___default().join(process.cwd(), 'data');\nconst UNIVERSITIES_FILE = path__WEBPACK_IMPORTED_MODULE_3___default().join(DATA_DIR, 'universities.json');\n// Ensure data directory exists\nasync function ensureDataDir() {\n    if (!(0,fs__WEBPACK_IMPORTED_MODULE_2__.existsSync)(DATA_DIR)) {\n        await (0,fs_promises__WEBPACK_IMPORTED_MODULE_1__.mkdir)(DATA_DIR, {\n            recursive: true\n        });\n    }\n}\n// Read universities from file\nasync function readUniversities() {\n    try {\n        await ensureDataDir();\n        if (!(0,fs__WEBPACK_IMPORTED_MODULE_2__.existsSync)(UNIVERSITIES_FILE)) {\n            // Create initial data if file doesn't exist\n            const initialData = [\n                {\n                    id: '1',\n                    name: 'Eastern Mediterranean University',\n                    slug: 'eastern-mediterranean-university',\n                    logo: '/images/universities/emu-logo.png',\n                    location: 'Famagusta, Northern Cyprus',\n                    tuitionFrom: 3500,\n                    currency: 'USD',\n                    description: 'Eastern Mediterranean University (EMU) is a leading higher education institution in Northern Cyprus, offering world-class education with international recognition.',\n                    images: [\n                        '/images/universities/emu-campus-1.jpg',\n                        '/images/universities/emu-campus-2.jpg',\n                        '/images/universities/emu-campus-3.jpg'\n                    ],\n                    establishedYear: 1979,\n                    studentCount: 20000,\n                    internationalStudents: 15000,\n                    accreditations: [\n                        'YÖK',\n                        'YÖDAK',\n                        'ABET'\n                    ],\n                    rankings: {\n                        national: 1,\n                        regional: 5\n                    },\n                    programs: [],\n                    requirements: [\n                        'High school diploma or equivalent',\n                        'English proficiency test (IELTS 6.0 or TOEFL 79)',\n                        'Academic transcripts',\n                        'Passport copy',\n                        'Health insurance'\n                    ],\n                    facilities: [\n                        'Modern laboratories',\n                        'Library with 200,000+ books',\n                        'Sports facilities',\n                        'Student dormitories',\n                        'Medical center'\n                    ],\n                    createdAt: new Date().toISOString(),\n                    updatedAt: new Date().toISOString()\n                },\n                {\n                    id: '2',\n                    name: 'Near East University',\n                    slug: 'near-east-university',\n                    logo: '/images/universities/neu-logo.png',\n                    location: 'Nicosia, Northern Cyprus',\n                    tuitionFrom: 4000,\n                    currency: 'USD',\n                    description: 'Near East University is a comprehensive university offering innovative programs with state-of-the-art facilities and international partnerships.',\n                    images: [\n                        '/images/universities/neu-campus-1.jpg',\n                        '/images/universities/neu-campus-2.jpg'\n                    ],\n                    establishedYear: 1988,\n                    studentCount: 25000,\n                    internationalStudents: 18000,\n                    accreditations: [\n                        'YÖK',\n                        'YÖDAK'\n                    ],\n                    rankings: {\n                        national: 2,\n                        regional: 8\n                    },\n                    programs: [],\n                    requirements: [\n                        'High school diploma or equivalent',\n                        'English proficiency test (IELTS 6.0 or TOEFL 79)',\n                        'Academic transcripts',\n                        'Passport copy'\n                    ],\n                    facilities: [\n                        'Hospital and medical school',\n                        'Engineering laboratories',\n                        'Business incubator',\n                        'Sports complex',\n                        'Student housing'\n                    ],\n                    createdAt: new Date().toISOString(),\n                    updatedAt: new Date().toISOString()\n                },\n                {\n                    id: '3',\n                    name: 'Cyprus International University',\n                    slug: 'cyprus-international-university',\n                    logo: '/images/universities/ciu-logo.png',\n                    location: 'Nicosia, Northern Cyprus',\n                    tuitionFrom: 3800,\n                    currency: 'USD',\n                    description: 'Cyprus International University provides quality education with a focus on international standards and multicultural learning environment.',\n                    images: [\n                        '/images/universities/ciu-campus-1.jpg',\n                        '/images/universities/ciu-campus-2.jpg'\n                    ],\n                    establishedYear: 1997,\n                    studentCount: 15000,\n                    internationalStudents: 12000,\n                    accreditations: [\n                        'YÖK',\n                        'YÖDAK'\n                    ],\n                    rankings: {\n                        national: 3,\n                        regional: 12\n                    },\n                    programs: [],\n                    requirements: [\n                        'High school diploma or equivalent',\n                        'English proficiency test (IELTS 6.0 or TOEFL 79)',\n                        'Academic transcripts',\n                        'Passport copy'\n                    ],\n                    facilities: [\n                        'Modern classrooms',\n                        'Computer laboratories',\n                        'Library',\n                        'Student center',\n                        'Cafeteria'\n                    ],\n                    createdAt: new Date().toISOString(),\n                    updatedAt: new Date().toISOString()\n                }\n            ];\n            await (0,fs_promises__WEBPACK_IMPORTED_MODULE_1__.writeFile)(UNIVERSITIES_FILE, JSON.stringify(initialData, null, 2));\n            return initialData;\n        }\n        const data = await (0,fs_promises__WEBPACK_IMPORTED_MODULE_1__.readFile)(UNIVERSITIES_FILE, 'utf-8');\n        return JSON.parse(data);\n    } catch (error) {\n        console.error('Error reading universities:', error);\n        return [];\n    }\n}\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const location = searchParams.get('location');\n        const minTuition = searchParams.get('minTuition');\n        const maxTuition = searchParams.get('maxTuition');\n        const search = searchParams.get('search');\n        let universities = await readUniversities();\n        // Apply filters\n        if (location && location !== 'All Locations') {\n            universities = universities.filter((uni)=>uni.location.toLowerCase().includes(location.toLowerCase()));\n        }\n        if (minTuition) {\n            universities = universities.filter((uni)=>uni.tuitionFrom >= parseInt(minTuition));\n        }\n        if (maxTuition) {\n            universities = universities.filter((uni)=>uni.tuitionFrom <= parseInt(maxTuition));\n        }\n        if (search) {\n            universities = universities.filter((uni)=>uni.name.toLowerCase().includes(search.toLowerCase()) || uni.description.toLowerCase().includes(search.toLowerCase()));\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: universities,\n            total: universities.length\n        });\n    } catch (error) {\n        console.error('Universities API error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Failed to fetch universities'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/universities/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "fs/promises":
/*!******************************!*\
  !*** external "fs/promises" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("fs/promises");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Funiversities%2Froute&page=%2Fapi%2Funiversities%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Funiversities%2Froute.ts&appDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();