'use client'

import { motion } from 'framer-motion'
import { useInView } from 'react-intersection-observer'
import { Star, GraduationCap, MapPin, Quote } from 'lucide-react'
import { useTestimonials } from '@/hooks/use-api'

export function TestimonialsGridSection() {
  const { data: testimonials, loading } = useTestimonials()
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1
  })

  if (loading) {
    return (
      <section className="section-padding bg-muted/30">
        <div className="container">
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="bg-muted rounded-xl h-80" />
              </div>
            ))}
          </div>
        </div>
      </section>
    )
  }

  return (
    <section ref={ref} className="section-padding bg-muted/30" id="testimonials">
      <div className="container">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
          className="text-center max-w-3xl mx-auto mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Student Success{' '}
            <span className="gradient-text">Stories</span>
          </h2>
          <p className="text-xl text-muted-foreground">
            Read authentic testimonials from students who achieved their dreams with our guidance
          </p>
        </motion.div>

        {/* Testimonials Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {testimonials?.map((testimonial, index) => (
            <motion.div
              key={testimonial.id}
              initial={{ opacity: 0, y: 30 }}
              animate={inView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              className="group"
            >
              <div className="bg-background rounded-xl p-6 shadow-sm border hover:shadow-lg transition-all duration-300 h-full flex flex-col">
                {/* Quote Icon */}
                <div className="flex items-start justify-between mb-4">
                  <Quote className="w-8 h-8 text-primary/20 flex-shrink-0" />
                  <div className="flex items-center">
                    {[...Array(5)].map((_, i) => (
                      <Star
                        key={i}
                        className={`w-4 h-4 ${
                          i < testimonial.rating
                            ? 'text-yellow-400 fill-current'
                            : 'text-gray-300'
                        }`}
                      />
                    ))}
                  </div>
                </div>

                {/* Testimonial Content */}
                <blockquote className="text-muted-foreground mb-6 flex-grow leading-relaxed">
                  "{testimonial.content}"
                </blockquote>

                {/* Student Info */}
                <div className="flex items-center space-x-4 pt-4 border-t">
                  <div className="relative">
                    <img
                      src={testimonial.image?.replace(/\.(jpg|jpeg|png)$/, '.svg') || testimonial.image}
                      alt={testimonial.name}
                      className="w-12 h-12 rounded-full object-cover"
                      onError={(e) => {
                        e.currentTarget.src = `data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='48' height='48' viewBox='0 0 48 48'%3E%3Ccircle cx='24' cy='24' r='24' fill='%23f3f4f6'/%3E%3Ctext x='50%25' y='50%25' dominant-baseline='middle' text-anchor='middle' font-family='Arial, sans-serif' font-size='18' fill='%236b7280'%3E${testimonial.name.charAt(0)}%3C/text%3E%3C/svg%3E`
                      }}
                    />
                    <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-background" />
                  </div>

                  <div className="flex-1 min-w-0">
                    <h4 className="font-semibold text-foreground truncate">
                      {testimonial.name}
                    </h4>
                    <div className="flex items-center text-sm text-muted-foreground">
                      <GraduationCap className="w-3 h-3 mr-1" />
                      <span className="truncate">{testimonial.program}</span>
                    </div>
                    <div className="flex items-center text-xs text-muted-foreground mt-1">
                      <MapPin className="w-3 h-3 mr-1" />
                      <span>{testimonial.university}</span>
                    </div>
                  </div>
                </div>

                {/* Graduation Year */}
                <div className="mt-3 text-center">
                  <span className="inline-flex items-center bg-primary/10 text-primary px-2 py-1 rounded-full text-xs font-medium">
                    Class of {testimonial.graduationYear}
                  </span>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Stats Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="bg-gradient-to-r from-primary/5 to-secondary/5 rounded-2xl p-8 md:p-12 text-center"
        >
          <h3 className="text-2xl md:text-3xl font-bold mb-6">
            Join Thousands of Successful Students
          </h3>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-8">
            {[
              { number: '5,000+', label: 'Students Helped', icon: GraduationCap },
              { number: '4.9/5', label: 'Average Rating', icon: Star },
              { number: '98%', label: 'Success Rate', icon: Quote },
              { number: '50+', label: 'Countries', icon: MapPin }
            ].map((stat, index) => (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={inView ? { opacity: 1, scale: 1 } : {}}
                transition={{ delay: 0.8 + index * 0.1, duration: 0.5 }}
                className="text-center"
              >
                <stat.icon className="w-8 h-8 text-primary mx-auto mb-3" />
                <div className="text-2xl md:text-3xl font-bold text-foreground mb-1">
                  {stat.number}
                </div>
                <div className="text-sm text-muted-foreground">{stat.label}</div>
              </motion.div>
            ))}
          </div>

          <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
            Our students come from over 50 countries and have achieved remarkable success
            in their academic and professional careers. Join our community of achievers today.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="bg-primary text-primary-foreground px-6 py-3 rounded-lg font-medium hover:bg-primary/90 transition-colors"
            >
              Start Your Journey
            </motion.button>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="border border-primary text-primary px-6 py-3 rounded-lg font-medium hover:bg-primary/10 transition-colors"
            >
              Read More Stories
            </motion.button>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
