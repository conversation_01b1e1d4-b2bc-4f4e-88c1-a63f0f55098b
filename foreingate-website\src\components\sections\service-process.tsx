'use client'

import { motion } from 'framer-motion'
import { useInView } from 'react-intersection-observer'
import { 
  MessageSquare, 
  FileSearch, 
  Send, 
  CheckCircle, 
  Users, 
  Clock,
  Shield,
  Award
} from 'lucide-react'

const processSteps = [
  {
    step: '01',
    icon: MessageSquare,
    title: 'Initial Consultation',
    description: 'Free consultation to understand your goals, assess your profile, and recommend the best path forward.',
    duration: '30-60 minutes',
    deliverables: ['Profile assessment', 'Goal clarification', 'Service recommendations']
  },
  {
    step: '02',
    icon: FileSearch,
    title: 'Planning & Preparation',
    description: 'Detailed planning phase where we create your personalized roadmap and prepare all necessary documentation.',
    duration: '1-2 weeks',
    deliverables: ['Customized action plan', 'Document checklist', 'Timeline creation']
  },
  {
    step: '03',
    icon: Send,
    title: 'Implementation',
    description: 'Active execution of your plan with regular updates and support throughout the entire process.',
    duration: '2-8 weeks',
    deliverables: ['Application submissions', 'Regular progress updates', 'Issue resolution']
  },
  {
    step: '04',
    icon: CheckCircle,
    title: 'Success & Follow-up',
    description: 'Celebration of your success and ongoing support to ensure smooth transition to your new academic journey.',
    duration: 'Ongoing',
    deliverables: ['Success confirmation', 'Pre-departure support', 'Ongoing assistance']
  }
]

const guarantees = [
  {
    icon: Shield,
    title: 'Success Guarantee',
    description: 'We guarantee admission to at least one of your preferred universities or your money back.'
  },
  {
    icon: Clock,
    title: 'Timely Delivery',
    description: 'All services delivered within agreed timelines with regular progress updates.'
  },
  {
    icon: Users,
    title: 'Expert Support',
    description: 'Dedicated counselor assigned to your case with years of experience in international education.'
  },
  {
    icon: Award,
    title: 'Quality Assurance',
    description: 'All documents and applications reviewed by senior counselors before submission.'
  }
]

export function ServiceProcessSection() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1
  })

  return (
    <section ref={ref} className="section-padding bg-muted/30">
      <div className="container">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
          className="text-center max-w-3xl mx-auto mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Our Proven{' '}
            <span className="gradient-text">Service Process</span>
          </h2>
          <p className="text-xl text-muted-foreground">
            A systematic approach refined over years of experience to ensure your success
          </p>
        </motion.div>

        {/* Process Steps */}
        <div className="max-w-4xl mx-auto mb-16">
          <div className="relative">
            {/* Process Line */}
            <div className="hidden md:block absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-gradient-to-b from-primary via-primary to-secondary rounded-full" />
            
            {/* Process Items */}
            <div className="space-y-12">
              {processSteps.map((step, index) => (
                <motion.div
                  key={step.step}
                  initial={{ opacity: 0, y: 30 }}
                  animate={inView ? { opacity: 1, y: 0 } : {}}
                  transition={{ duration: 0.6, delay: index * 0.2 }}
                  className={`flex items-center ${
                    index % 2 === 0 ? 'md:flex-row' : 'md:flex-row-reverse'
                  } flex-col md:gap-8`}
                >
                  {/* Content */}
                  <div className={`w-full md:w-5/12 ${
                    index % 2 === 0 ? 'md:text-right md:pr-8' : 'md:text-left md:pl-8'
                  } text-center md:text-left mb-6 md:mb-0`}>
                    <div className="bg-background border rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow">
                      <div className="flex items-center justify-center md:justify-start mb-4">
                        <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mr-3">
                          <step.icon className="w-6 h-6 text-primary" />
                        </div>
                        <div className="text-left">
                          <h3 className="text-xl font-semibold">{step.title}</h3>
                          <p className="text-sm text-primary font-medium">{step.duration}</p>
                        </div>
                      </div>
                      <p className="text-muted-foreground mb-4 leading-relaxed">
                        {step.description}
                      </p>
                      <div>
                        <h4 className="font-medium mb-2 text-sm">Key Deliverables:</h4>
                        <ul className="space-y-1">
                          {step.deliverables.map((deliverable) => (
                            <li key={deliverable} className="flex items-center text-sm text-muted-foreground">
                              <CheckCircle className="w-3 h-3 text-primary mr-2 flex-shrink-0" />
                              {deliverable}
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </div>
                  
                  {/* Step Number */}
                  <div className="w-full md:w-2/12 flex justify-center mb-6 md:mb-0">
                    <div className="w-16 h-16 bg-primary text-primary-foreground rounded-full flex items-center justify-center font-bold text-lg shadow-lg relative z-10">
                      {step.step}
                    </div>
                  </div>
                  
                  {/* Spacer */}
                  <div className="hidden md:block w-5/12" />
                </motion.div>
              ))}
            </div>
          </div>
        </div>

        {/* Service Guarantees */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 0.8 }}
          className="bg-gradient-to-r from-primary/5 to-secondary/5 rounded-2xl p-8 md:p-12"
        >
          <div className="text-center mb-8">
            <h3 className="text-2xl md:text-3xl font-bold mb-4">Our Service Guarantees</h3>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              We stand behind our services with concrete guarantees that give you peace of mind
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {guarantees.map((guarantee, index) => (
              <motion.div
                key={guarantee.title}
                initial={{ opacity: 0, y: 20 }}
                animate={inView ? { opacity: 1, y: 0 } : {}}
                transition={{ duration: 0.5, delay: 1 + index * 0.1 }}
                className="text-center"
              >
                <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <guarantee.icon className="w-8 h-8 text-primary" />
                </div>
                <h4 className="font-semibold mb-2">{guarantee.title}</h4>
                <p className="text-sm text-muted-foreground leading-relaxed">
                  {guarantee.description}
                </p>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Process Stats */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 1 }}
          className="mt-16 text-center"
        >
          <h3 className="text-2xl font-bold mb-8">Process Performance</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            <div>
              <div className="text-3xl font-bold text-primary mb-2">98%</div>
              <div className="text-sm text-muted-foreground">Success Rate</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-primary mb-2">7</div>
              <div className="text-sm text-muted-foreground">Days Average</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-primary mb-2">24/7</div>
              <div className="text-sm text-muted-foreground">Support Available</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-primary mb-2">100%</div>
              <div className="text-sm text-muted-foreground">Satisfaction Rate</div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
