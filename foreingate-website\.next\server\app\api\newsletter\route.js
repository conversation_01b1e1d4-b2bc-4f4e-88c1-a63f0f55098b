/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/newsletter/route";
exports.ids = ["app/api/newsletter/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fnewsletter%2Froute&page=%2Fapi%2Fnewsletter%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnewsletter%2Froute.ts&appDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fnewsletter%2Froute&page=%2Fapi%2Fnewsletter%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnewsletter%2Froute.ts&appDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Nidhal_Documents_augment_projects_foreingate_groupe_foreingate_website_src_app_api_newsletter_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/newsletter/route.ts */ \"(rsc)/./src/app/api/newsletter/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/newsletter/route\",\n        pathname: \"/api/newsletter\",\n        filename: \"route\",\n        bundlePath: \"app/api/newsletter/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\api\\\\newsletter\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Nidhal_Documents_augment_projects_foreingate_groupe_foreingate_website_src_app_api_newsletter_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fnewsletter%2Froute&page=%2Fapi%2Fnewsletter%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnewsletter%2Froute.ts&appDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/newsletter/route.ts":
/*!*****************************************!*\
  !*** ./src/app/api/newsletter/route.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fs/promises */ \"fs/promises\");\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fs_promises__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nconst DATA_DIR = path__WEBPACK_IMPORTED_MODULE_3___default().join(process.cwd(), 'data');\nconst NEWSLETTER_FILE = path__WEBPACK_IMPORTED_MODULE_3___default().join(DATA_DIR, 'newsletter.json');\n// Ensure data directory exists\nasync function ensureDataDir() {\n    if (!(0,fs__WEBPACK_IMPORTED_MODULE_2__.existsSync)(DATA_DIR)) {\n        await (0,fs_promises__WEBPACK_IMPORTED_MODULE_1__.mkdir)(DATA_DIR, {\n            recursive: true\n        });\n    }\n}\n// Read newsletter subscribers from file\nasync function readSubscribers() {\n    try {\n        await ensureDataDir();\n        if (!(0,fs__WEBPACK_IMPORTED_MODULE_2__.existsSync)(NEWSLETTER_FILE)) {\n            return [];\n        }\n        const data = await (0,fs_promises__WEBPACK_IMPORTED_MODULE_1__.readFile)(NEWSLETTER_FILE, 'utf-8');\n        return JSON.parse(data);\n    } catch (error) {\n        console.error('Error reading newsletter subscribers:', error);\n        return [];\n    }\n}\n// Write newsletter subscribers to file\nasync function writeSubscribers(subscribers) {\n    try {\n        await ensureDataDir();\n        await (0,fs_promises__WEBPACK_IMPORTED_MODULE_1__.writeFile)(NEWSLETTER_FILE, JSON.stringify(subscribers, null, 2));\n    } catch (error) {\n        console.error('Error writing newsletter subscribers:', error);\n        throw error;\n    }\n}\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { email } = body;\n        // Validate email\n        if (!email) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Email is required'\n            }, {\n                status: 400\n            });\n        }\n        // Basic email validation\n        const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n        if (!emailRegex.test(email)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Please enter a valid email address'\n            }, {\n                status: 400\n            });\n        }\n        // Read existing subscribers\n        const subscribers = await readSubscribers();\n        // Check if email already exists\n        const existingSubscriber = subscribers.find((sub)=>sub.email === email);\n        if (existingSubscriber) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'This email is already subscribed to our newsletter'\n            }, {\n                status: 409\n            });\n        }\n        // Create new subscriber\n        const subscriber = {\n            id: Math.random().toString(36).substr(2, 9),\n            email,\n            subscribedAt: new Date().toISOString(),\n            isActive: true,\n            source: 'website'\n        };\n        // Add new subscriber\n        subscribers.push(subscriber);\n        // Write back to file\n        await writeSubscribers(subscribers);\n        // Send welcome email (if email service is configured)\n        try {\n            if (process.env.RESEND_API_KEY) {\n                const { Resend } = __webpack_require__(/*! resend */ \"(rsc)/../node_modules/resend/dist/index.js\");\n                const resend = new Resend(process.env.RESEND_API_KEY);\n                const welcomeEmailHtml = `\n          <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n            <div style=\"background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center;\">\n              <h1 style=\"color: white; margin: 0;\">Welcome to Foreingate Group!</h1>\n              <p style=\"color: white; opacity: 0.9; margin: 10px 0 0 0;\">Thank you for subscribing to our newsletter</p>\n            </div>\n            \n            <div style=\"padding: 30px; background: #f8f9fa;\">\n              <p style=\"color: #333; font-size: 16px; line-height: 1.6;\">Hello!</p>\n              \n              <p style=\"color: #555; line-height: 1.6;\">\n                Welcome to the Foreingate Group community! You've successfully subscribed to our newsletter and will now receive:\n              </p>\n\n              <div style=\"background: #e8f5e8; padding: 20px; border-radius: 8px; border-left: 4px solid #4caf50; margin: 20px 0;\">\n                <h3 style=\"color: #2e7d32; margin: 0 0 15px 0;\">📧 What you'll receive:</h3>\n                <ul style=\"color: #555; margin: 0; padding-left: 20px;\">\n                  <li>Latest university admission updates</li>\n                  <li>Scholarship opportunities and deadlines</li>\n                  <li>Student success stories and tips</li>\n                  <li>Educational insights and guides</li>\n                  <li>Exclusive offers and early access to services</li>\n                </ul>\n              </div>\n\n              <div style=\"background: #fff3e0; padding: 20px; border-radius: 8px; border-left: 4px solid #ff9800; margin: 20px 0;\">\n                <h3 style=\"color: #f57c00; margin: 0 0 10px 0;\">🎓 Ready to start your journey?</h3>\n                <p style=\"color: #555; margin: 0;\">\n                  If you're ready to apply to universities in Northern Cyprus, our expert team is here to help. \n                  Contact us for a free consultation!\n                </p>\n              </div>\n\n              <div style=\"text-align: center; margin: 30px 0;\">\n                <a href=\"${\"http://localhost:3001\"}/contact\" \n                   style=\"background: #667eea; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: bold; display: inline-block;\">\n                  Get Free Consultation\n                </a>\n              </div>\n\n              <p style=\"color: #555; line-height: 1.6;\">\n                Thank you for choosing Foreingate Group as your educational partner. We're excited to help you achieve your academic dreams!\n              </p>\n\n              <p style=\"color: #555; line-height: 1.6;\">\n                Best regards,<br>\n                <strong>The Foreingate Group Team</strong><br>\n                <em>Your Gateway to International Education</em>\n              </p>\n            </div>\n\n            <div style=\"background: #333; padding: 20px; text-align: center;\">\n              <p style=\"color: #ccc; margin: 0; font-size: 14px;\">\n                © 2024 Foreingate Group. All rights reserved.<br>\n                You can unsubscribe at any time by replying to this email.\n              </p>\n            </div>\n          </div>\n        `;\n                await resend.emails.send({\n                    from: 'Foreingate Group <<EMAIL>>',\n                    to: [\n                        email\n                    ],\n                    subject: 'Welcome to Foreingate Group Newsletter!',\n                    html: welcomeEmailHtml\n                });\n            }\n        } catch (emailError) {\n            console.error('Welcome email sending failed:', emailError);\n        // Don't fail the subscription if email fails\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'Successfully subscribed to newsletter!',\n            subscriberId: subscriber.id\n        });\n    } catch (error) {\n        console.error('Newsletter subscription error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to subscribe. Please try again.'\n        }, {\n            status: 500\n        });\n    }\n}\n// GET endpoint to retrieve subscribers count (for admin use)\nasync function GET(request) {\n    try {\n        const subscribers = await readSubscribers();\n        const activeSubscribers = subscribers.filter((sub)=>sub.isActive);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            totalSubscribers: subscribers.length,\n            activeSubscribers: activeSubscribers.length,\n            latestSubscriptions: subscribers.slice(-5).reverse() // Last 5 subscribers\n        });\n    } catch (error) {\n        console.error('Error fetching newsletter stats:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to fetch newsletter statistics'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/newsletter/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "async_hooks":
/*!******************************!*\
  !*** external "async_hooks" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("async_hooks");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "fs/promises":
/*!******************************!*\
  !*** external "fs/promises" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("fs/promises");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "prettier/plugins/html":
/*!****************************************!*\
  !*** external "prettier/plugins/html" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = import("prettier/plugins/html");;

/***/ }),

/***/ "prettier/standalone":
/*!**************************************!*\
  !*** external "prettier/standalone" ***!
  \**************************************/
/***/ ((module) => {

"use strict";
module.exports = import("prettier/standalone");;

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/resend"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fnewsletter%2Froute&page=%2Fapi%2Fnewsletter%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnewsletter%2Froute.ts&appDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();