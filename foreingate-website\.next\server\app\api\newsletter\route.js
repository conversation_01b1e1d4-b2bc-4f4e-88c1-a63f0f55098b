/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/newsletter/route";
exports.ids = ["app/api/newsletter/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fnewsletter%2Froute&page=%2Fapi%2Fnewsletter%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnewsletter%2Froute.ts&appDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fnewsletter%2Froute&page=%2Fapi%2Fnewsletter%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnewsletter%2Froute.ts&appDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Nidhal_Documents_augment_projects_foreingate_groupe_foreingate_website_src_app_api_newsletter_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/newsletter/route.ts */ \"(rsc)/./src/app/api/newsletter/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/newsletter/route\",\n        pathname: \"/api/newsletter\",\n        filename: \"route\",\n        bundlePath: \"app/api/newsletter/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\api\\\\newsletter\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Nidhal_Documents_augment_projects_foreingate_groupe_foreingate_website_src_app_api_newsletter_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fnewsletter%2Froute&page=%2Fapi%2Fnewsletter%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnewsletter%2Froute.ts&appDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/newsletter/route.ts":
/*!*****************************************!*\
  !*** ./src/app/api/newsletter/route.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fs/promises */ \"fs/promises\");\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fs_promises__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nconst DATA_DIR = path__WEBPACK_IMPORTED_MODULE_3___default().join(process.cwd(), 'data');\nconst NEWSLETTER_FILE = path__WEBPACK_IMPORTED_MODULE_3___default().join(DATA_DIR, 'newsletter.json');\n// Ensure data directory exists\nasync function ensureDataDir() {\n    if (!(0,fs__WEBPACK_IMPORTED_MODULE_2__.existsSync)(DATA_DIR)) {\n        await (0,fs_promises__WEBPACK_IMPORTED_MODULE_1__.mkdir)(DATA_DIR, {\n            recursive: true\n        });\n    }\n}\n// Read newsletter subscribers from file\nasync function readSubscribers() {\n    try {\n        await ensureDataDir();\n        if (!(0,fs__WEBPACK_IMPORTED_MODULE_2__.existsSync)(NEWSLETTER_FILE)) {\n            return [];\n        }\n        const data = await (0,fs_promises__WEBPACK_IMPORTED_MODULE_1__.readFile)(NEWSLETTER_FILE, 'utf-8');\n        return JSON.parse(data);\n    } catch (error) {\n        console.error('Error reading newsletter subscribers:', error);\n        return [];\n    }\n}\n// Write newsletter subscribers to file\nasync function writeSubscribers(subscribers) {\n    try {\n        await ensureDataDir();\n        await (0,fs_promises__WEBPACK_IMPORTED_MODULE_1__.writeFile)(NEWSLETTER_FILE, JSON.stringify(subscribers, null, 2));\n    } catch (error) {\n        console.error('Error writing newsletter subscribers:', error);\n        throw error;\n    }\n}\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { email } = body;\n        // Validate email\n        if (!email) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Email is required'\n            }, {\n                status: 400\n            });\n        }\n        // Basic email validation\n        const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n        if (!emailRegex.test(email)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Please enter a valid email address'\n            }, {\n                status: 400\n            });\n        }\n        // Read existing subscribers\n        const subscribers = await readSubscribers();\n        // Check if email already exists\n        const existingSubscriber = subscribers.find((sub)=>sub.email === email);\n        if (existingSubscriber) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'This email is already subscribed to our newsletter'\n            }, {\n                status: 409\n            });\n        }\n        // Create new subscriber\n        const subscriber = {\n            id: Math.random().toString(36).substr(2, 9),\n            email,\n            subscribedAt: new Date().toISOString(),\n            isActive: true,\n            source: 'website'\n        };\n        // Add new subscriber\n        subscribers.push(subscriber);\n        // Write back to file\n        await writeSubscribers(subscribers);\n        // Send welcome email (if email service is configured)\n        try {\n            if (process.env.RESEND_API_KEY) {\n                const { Resend } = __webpack_require__(/*! resend */ \"(rsc)/../node_modules/resend/dist/index.js\");\n                const resend = new Resend(process.env.RESEND_API_KEY);\n                const welcomeEmailHtml = `\n          <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n            <div style=\"background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center;\">\n              <h1 style=\"color: white; margin: 0;\">Welcome to Foreingate Group!</h1>\n              <p style=\"color: white; opacity: 0.9; margin: 10px 0 0 0;\">Thank you for subscribing to our newsletter</p>\n            </div>\n            \n            <div style=\"padding: 30px; background: #f8f9fa;\">\n              <p style=\"color: #333; font-size: 16px; line-height: 1.6;\">Hello!</p>\n              \n              <p style=\"color: #555; line-height: 1.6;\">\n                Welcome to the Foreingate Group community! You've successfully subscribed to our newsletter and will now receive:\n              </p>\n\n              <div style=\"background: #e8f5e8; padding: 20px; border-radius: 8px; border-left: 4px solid #4caf50; margin: 20px 0;\">\n                <h3 style=\"color: #2e7d32; margin: 0 0 15px 0;\">📧 What you'll receive:</h3>\n                <ul style=\"color: #555; margin: 0; padding-left: 20px;\">\n                  <li>Latest university admission updates</li>\n                  <li>Scholarship opportunities and deadlines</li>\n                  <li>Student success stories and tips</li>\n                  <li>Educational insights and guides</li>\n                  <li>Exclusive offers and early access to services</li>\n                </ul>\n              </div>\n\n              <div style=\"background: #fff3e0; padding: 20px; border-radius: 8px; border-left: 4px solid #ff9800; margin: 20px 0;\">\n                <h3 style=\"color: #f57c00; margin: 0 0 10px 0;\">🎓 Ready to start your journey?</h3>\n                <p style=\"color: #555; margin: 0;\">\n                  If you're ready to apply to universities in Northern Cyprus, our expert team is here to help. \n                  Contact us for a free consultation!\n                </p>\n              </div>\n\n              <div style=\"text-align: center; margin: 30px 0;\">\n                <a href=\"${\"http://localhost:3001\"}/contact\" \n                   style=\"background: #667eea; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: bold; display: inline-block;\">\n                  Get Free Consultation\n                </a>\n              </div>\n\n              <p style=\"color: #555; line-height: 1.6;\">\n                Thank you for choosing Foreingate Group as your educational partner. We're excited to help you achieve your academic dreams!\n              </p>\n\n              <p style=\"color: #555; line-height: 1.6;\">\n                Best regards,<br>\n                <strong>The Foreingate Group Team</strong><br>\n                <em>Your Gateway to International Education</em>\n              </p>\n            </div>\n\n            <div style=\"background: #333; padding: 20px; text-align: center;\">\n              <p style=\"color: #ccc; margin: 0; font-size: 14px;\">\n                © 2024 Foreingate Group. All rights reserved.<br>\n                You can unsubscribe at any time by replying to this email.\n              </p>\n            </div>\n          </div>\n        `;\n                await resend.emails.send({\n                    from: 'Foreingate Group <<EMAIL>>',\n                    to: [\n                        email\n                    ],\n                    subject: 'Welcome to Foreingate Group Newsletter!',\n                    html: welcomeEmailHtml\n                });\n            }\n        } catch (emailError) {\n            console.error('Welcome email sending failed:', emailError);\n        // Don't fail the subscription if email fails\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'Successfully subscribed to newsletter!',\n            subscriberId: subscriber.id\n        });\n    } catch (error) {\n        console.error('Newsletter subscription error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to subscribe. Please try again.'\n        }, {\n            status: 500\n        });\n    }\n}\n// GET endpoint to retrieve subscribers (for admin use)\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const status = searchParams.get('status') // 'active', 'inactive', 'all'\n        ;\n        const search = searchParams.get('search');\n        const page = parseInt(searchParams.get('page') || '1');\n        const limit = parseInt(searchParams.get('limit') || '10');\n        const sortBy = searchParams.get('sortBy') || 'subscribedAt';\n        const sortOrder = searchParams.get('sortOrder') || 'desc';\n        let subscribers = await readSubscribers();\n        // Apply filters\n        if (status && status !== 'all') {\n            const isActive = status === 'active';\n            subscribers = subscribers.filter((sub)=>sub.isActive === isActive);\n        }\n        if (search) {\n            subscribers = subscribers.filter((sub)=>sub.email.toLowerCase().includes(search.toLowerCase()));\n        }\n        // Sort subscribers\n        subscribers.sort((a, b)=>{\n            let aValue = a[sortBy];\n            let bValue = b[sortBy];\n            if (sortBy === 'subscribedAt') {\n                aValue = new Date(aValue).getTime();\n                bValue = new Date(bValue).getTime();\n            }\n            if (sortOrder === 'desc') {\n                return bValue > aValue ? 1 : -1;\n            } else {\n                return aValue > bValue ? 1 : -1;\n            }\n        });\n        // Pagination\n        const total = subscribers.length;\n        const startIndex = (page - 1) * limit;\n        const endIndex = startIndex + limit;\n        const paginatedSubscribers = subscribers.slice(startIndex, endIndex);\n        // Calculate statistics\n        const stats = {\n            total: subscribers.length,\n            active: subscribers.filter((sub)=>sub.isActive).length,\n            inactive: subscribers.filter((sub)=>!sub.isActive).length,\n            thisWeek: subscribers.filter((sub)=>{\n                const subDate = new Date(sub.subscribedAt);\n                const weekAgo = new Date();\n                weekAgo.setDate(weekAgo.getDate() - 7);\n                return subDate > weekAgo;\n            }).length,\n            thisMonth: subscribers.filter((sub)=>{\n                const subDate = new Date(sub.subscribedAt);\n                const monthAgo = new Date();\n                monthAgo.setMonth(monthAgo.getMonth() - 1);\n                return subDate > monthAgo;\n            }).length\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: paginatedSubscribers,\n            pagination: {\n                page,\n                limit,\n                total,\n                totalPages: Math.ceil(total / limit),\n                hasNext: endIndex < total,\n                hasPrev: page > 1\n            },\n            stats\n        });\n    } catch (error) {\n        console.error('Error fetching newsletter subscribers:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to fetch newsletter subscribers'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9uZXdzbGV0dGVyL3JvdXRlLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUF1RDtBQUNDO0FBQ3pCO0FBQ1I7QUFFdkIsTUFBTU0sV0FBV0QsZ0RBQVMsQ0FBQ0csUUFBUUMsR0FBRyxJQUFJO0FBQzFDLE1BQU1DLGtCQUFrQkwsZ0RBQVMsQ0FBQ0MsVUFBVTtBQUU1QywrQkFBK0I7QUFDL0IsZUFBZUs7SUFDYixJQUFJLENBQUNQLDhDQUFVQSxDQUFDRSxXQUFXO1FBQ3pCLE1BQU1ILGtEQUFLQSxDQUFDRyxVQUFVO1lBQUVNLFdBQVc7UUFBSztJQUMxQztBQUNGO0FBRUEsd0NBQXdDO0FBQ3hDLGVBQWVDO0lBQ2IsSUFBSTtRQUNGLE1BQU1GO1FBQ04sSUFBSSxDQUFDUCw4Q0FBVUEsQ0FBQ00sa0JBQWtCO1lBQ2hDLE9BQU8sRUFBRTtRQUNYO1FBQ0EsTUFBTUksT0FBTyxNQUFNWixxREFBUUEsQ0FBQ1EsaUJBQWlCO1FBQzdDLE9BQU9LLEtBQUtDLEtBQUssQ0FBQ0Y7SUFDcEIsRUFBRSxPQUFPRyxPQUFPO1FBQ2RDLFFBQVFELEtBQUssQ0FBQyx5Q0FBeUNBO1FBQ3ZELE9BQU8sRUFBRTtJQUNYO0FBQ0Y7QUFFQSx1Q0FBdUM7QUFDdkMsZUFBZUUsaUJBQWlCQyxXQUFrQjtJQUNoRCxJQUFJO1FBQ0YsTUFBTVQ7UUFDTixNQUFNVixzREFBU0EsQ0FBQ1MsaUJBQWlCSyxLQUFLTSxTQUFTLENBQUNELGFBQWEsTUFBTTtJQUNyRSxFQUFFLE9BQU9ILE9BQU87UUFDZEMsUUFBUUQsS0FBSyxDQUFDLHlDQUF5Q0E7UUFDdkQsTUFBTUE7SUFDUjtBQUNGO0FBRU8sZUFBZUssS0FBS0MsT0FBb0I7SUFDN0MsSUFBSTtRQUNGLE1BQU1DLE9BQU8sTUFBTUQsUUFBUUUsSUFBSTtRQUMvQixNQUFNLEVBQUVDLEtBQUssRUFBRSxHQUFHRjtRQUVsQixpQkFBaUI7UUFDakIsSUFBSSxDQUFDRSxPQUFPO1lBQ1YsT0FBTzFCLHFEQUFZQSxDQUFDeUIsSUFBSSxDQUN0QjtnQkFBRVIsT0FBTztZQUFvQixHQUM3QjtnQkFBRVUsUUFBUTtZQUFJO1FBRWxCO1FBRUEseUJBQXlCO1FBQ3pCLE1BQU1DLGFBQWE7UUFDbkIsSUFBSSxDQUFDQSxXQUFXQyxJQUFJLENBQUNILFFBQVE7WUFDM0IsT0FBTzFCLHFEQUFZQSxDQUFDeUIsSUFBSSxDQUN0QjtnQkFBRVIsT0FBTztZQUFxQyxHQUM5QztnQkFBRVUsUUFBUTtZQUFJO1FBRWxCO1FBRUEsNEJBQTRCO1FBQzVCLE1BQU1QLGNBQWMsTUFBTVA7UUFFMUIsZ0NBQWdDO1FBQ2hDLE1BQU1pQixxQkFBcUJWLFlBQVlXLElBQUksQ0FBQyxDQUFDQyxNQUFhQSxJQUFJTixLQUFLLEtBQUtBO1FBQ3hFLElBQUlJLG9CQUFvQjtZQUN0QixPQUFPOUIscURBQVlBLENBQUN5QixJQUFJLENBQ3RCO2dCQUFFUixPQUFPO1lBQXFELEdBQzlEO2dCQUFFVSxRQUFRO1lBQUk7UUFFbEI7UUFFQSx3QkFBd0I7UUFDeEIsTUFBTU0sYUFBYTtZQUNqQkMsSUFBSUMsS0FBS0MsTUFBTSxHQUFHQyxRQUFRLENBQUMsSUFBSUMsTUFBTSxDQUFDLEdBQUc7WUFDekNaO1lBQ0FhLGNBQWMsSUFBSUMsT0FBT0MsV0FBVztZQUNwQ0MsVUFBVTtZQUNWQyxRQUFRO1FBQ1Y7UUFFQSxxQkFBcUI7UUFDckJ2QixZQUFZd0IsSUFBSSxDQUFDWDtRQUVqQixxQkFBcUI7UUFDckIsTUFBTWQsaUJBQWlCQztRQUV2QixzREFBc0Q7UUFDdEQsSUFBSTtZQUNGLElBQUlaLFFBQVFxQyxHQUFHLENBQUNDLGNBQWMsRUFBRTtnQkFDOUIsTUFBTSxFQUFFQyxNQUFNLEVBQUUsR0FBR0MsbUJBQU9BLENBQUMsMERBQVE7Z0JBQ25DLE1BQU1DLFNBQVMsSUFBSUYsT0FBT3ZDLFFBQVFxQyxHQUFHLENBQUNDLGNBQWM7Z0JBRXBELE1BQU1JLG1CQUFtQixDQUFDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3lCQWtDVCxFQUFFMUMsdUJBQStCLENBQUM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztRQXdCbkQsQ0FBQztnQkFFRCxNQUFNeUMsT0FBT0csTUFBTSxDQUFDQyxJQUFJLENBQUM7b0JBQ3ZCQyxNQUFNO29CQUNOQyxJQUFJO3dCQUFDN0I7cUJBQU07b0JBQ1g4QixTQUFTO29CQUNUQyxNQUFNUDtnQkFDUjtZQUNGO1FBQ0YsRUFBRSxPQUFPUSxZQUFZO1lBQ25CeEMsUUFBUUQsS0FBSyxDQUFDLGlDQUFpQ3lDO1FBQy9DLDZDQUE2QztRQUMvQztRQUVBLE9BQU8xRCxxREFBWUEsQ0FBQ3lCLElBQUksQ0FBQztZQUN2QmtDLFNBQVM7WUFDVEMsU0FBUztZQUNUQyxjQUFjNUIsV0FBV0MsRUFBRTtRQUM3QjtJQUVGLEVBQUUsT0FBT2pCLE9BQU87UUFDZEMsUUFBUUQsS0FBSyxDQUFDLGtDQUFrQ0E7UUFDaEQsT0FBT2pCLHFEQUFZQSxDQUFDeUIsSUFBSSxDQUN0QjtZQUFFUixPQUFPO1FBQXlDLEdBQ2xEO1lBQUVVLFFBQVE7UUFBSTtJQUVsQjtBQUNGO0FBRUEsdURBQXVEO0FBQ2hELGVBQWVtQyxJQUFJdkMsT0FBb0I7SUFDNUMsSUFBSTtRQUNGLE1BQU0sRUFBRXdDLFlBQVksRUFBRSxHQUFHLElBQUlDLElBQUl6QyxRQUFRMEMsR0FBRztRQUM1QyxNQUFNdEMsU0FBU29DLGFBQWFHLEdBQUcsQ0FBQyxVQUFVLDhCQUE4Qjs7UUFDeEUsTUFBTUMsU0FBU0osYUFBYUcsR0FBRyxDQUFDO1FBQ2hDLE1BQU1FLE9BQU9DLFNBQVNOLGFBQWFHLEdBQUcsQ0FBQyxXQUFXO1FBQ2xELE1BQU1JLFFBQVFELFNBQVNOLGFBQWFHLEdBQUcsQ0FBQyxZQUFZO1FBQ3BELE1BQU1LLFNBQVNSLGFBQWFHLEdBQUcsQ0FBQyxhQUFhO1FBQzdDLE1BQU1NLFlBQVlULGFBQWFHLEdBQUcsQ0FBQyxnQkFBZ0I7UUFFbkQsSUFBSTlDLGNBQWMsTUFBTVA7UUFFeEIsZ0JBQWdCO1FBQ2hCLElBQUljLFVBQVVBLFdBQVcsT0FBTztZQUM5QixNQUFNZSxXQUFXZixXQUFXO1lBQzVCUCxjQUFjQSxZQUFZcUQsTUFBTSxDQUFDLENBQUN6QyxNQUFhQSxJQUFJVSxRQUFRLEtBQUtBO1FBQ2xFO1FBRUEsSUFBSXlCLFFBQVE7WUFDVi9DLGNBQWNBLFlBQVlxRCxNQUFNLENBQUMsQ0FBQ3pDLE1BQ2hDQSxJQUFJTixLQUFLLENBQUNnRCxXQUFXLEdBQUdDLFFBQVEsQ0FBQ1IsT0FBT08sV0FBVztRQUV2RDtRQUVBLG1CQUFtQjtRQUNuQnRELFlBQVl3RCxJQUFJLENBQUMsQ0FBQ0MsR0FBUUM7WUFDeEIsSUFBSUMsU0FBU0YsQ0FBQyxDQUFDTixPQUFPO1lBQ3RCLElBQUlTLFNBQVNGLENBQUMsQ0FBQ1AsT0FBTztZQUV0QixJQUFJQSxXQUFXLGdCQUFnQjtnQkFDN0JRLFNBQVMsSUFBSXZDLEtBQUt1QyxRQUFRRSxPQUFPO2dCQUNqQ0QsU0FBUyxJQUFJeEMsS0FBS3dDLFFBQVFDLE9BQU87WUFDbkM7WUFFQSxJQUFJVCxjQUFjLFFBQVE7Z0JBQ3hCLE9BQU9RLFNBQVNELFNBQVMsSUFBSSxDQUFDO1lBQ2hDLE9BQU87Z0JBQ0wsT0FBT0EsU0FBU0MsU0FBUyxJQUFJLENBQUM7WUFDaEM7UUFDRjtRQUVBLGFBQWE7UUFDYixNQUFNRSxRQUFROUQsWUFBWStELE1BQU07UUFDaEMsTUFBTUMsYUFBYSxDQUFDaEIsT0FBTyxLQUFLRTtRQUNoQyxNQUFNZSxXQUFXRCxhQUFhZDtRQUM5QixNQUFNZ0IsdUJBQXVCbEUsWUFBWW1FLEtBQUssQ0FBQ0gsWUFBWUM7UUFFM0QsdUJBQXVCO1FBQ3ZCLE1BQU1HLFFBQVE7WUFDWk4sT0FBTzlELFlBQVkrRCxNQUFNO1lBQ3pCTSxRQUFRckUsWUFBWXFELE1BQU0sQ0FBQyxDQUFDekMsTUFBYUEsSUFBSVUsUUFBUSxFQUFFeUMsTUFBTTtZQUM3RE8sVUFBVXRFLFlBQVlxRCxNQUFNLENBQUMsQ0FBQ3pDLE1BQWEsQ0FBQ0EsSUFBSVUsUUFBUSxFQUFFeUMsTUFBTTtZQUNoRVEsVUFBVXZFLFlBQVlxRCxNQUFNLENBQUMsQ0FBQ3pDO2dCQUM1QixNQUFNNEQsVUFBVSxJQUFJcEQsS0FBS1IsSUFBSU8sWUFBWTtnQkFDekMsTUFBTXNELFVBQVUsSUFBSXJEO2dCQUNwQnFELFFBQVFDLE9BQU8sQ0FBQ0QsUUFBUUUsT0FBTyxLQUFLO2dCQUNwQyxPQUFPSCxVQUFVQztZQUNuQixHQUFHVixNQUFNO1lBQ1RhLFdBQVc1RSxZQUFZcUQsTUFBTSxDQUFDLENBQUN6QztnQkFDN0IsTUFBTTRELFVBQVUsSUFBSXBELEtBQUtSLElBQUlPLFlBQVk7Z0JBQ3pDLE1BQU0wRCxXQUFXLElBQUl6RDtnQkFDckJ5RCxTQUFTQyxRQUFRLENBQUNELFNBQVNFLFFBQVEsS0FBSztnQkFDeEMsT0FBT1AsVUFBVUs7WUFDbkIsR0FBR2QsTUFBTTtRQUNYO1FBRUEsT0FBT25GLHFEQUFZQSxDQUFDeUIsSUFBSSxDQUFDO1lBQ3ZCa0MsU0FBUztZQUNUN0MsTUFBTXdFO1lBQ05jLFlBQVk7Z0JBQ1ZoQztnQkFDQUU7Z0JBQ0FZO2dCQUNBbUIsWUFBWWxFLEtBQUttRSxJQUFJLENBQUNwQixRQUFRWjtnQkFDOUJpQyxTQUFTbEIsV0FBV0g7Z0JBQ3BCc0IsU0FBU3BDLE9BQU87WUFDbEI7WUFDQW9CO1FBQ0Y7SUFDRixFQUFFLE9BQU92RSxPQUFPO1FBQ2RDLFFBQVFELEtBQUssQ0FBQywwQ0FBMENBO1FBQ3hELE9BQU9qQixxREFBWUEsQ0FBQ3lCLElBQUksQ0FDdEI7WUFBRVIsT0FBTztRQUF5QyxHQUNsRDtZQUFFVSxRQUFRO1FBQUk7SUFFbEI7QUFDRiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxOaWRoYWxcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcZm9yZWluZ2F0ZV9ncm91cGVcXGZvcmVpbmdhdGUtd2Vic2l0ZVxcc3JjXFxhcHBcXGFwaVxcbmV3c2xldHRlclxccm91dGUudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgTmV4dFJlcXVlc3QsIE5leHRSZXNwb25zZSB9IGZyb20gJ25leHQvc2VydmVyJ1xuaW1wb3J0IHsgd3JpdGVGaWxlLCByZWFkRmlsZSwgbWtkaXIgfSBmcm9tICdmcy9wcm9taXNlcydcbmltcG9ydCB7IGV4aXN0c1N5bmMgfSBmcm9tICdmcydcbmltcG9ydCBwYXRoIGZyb20gJ3BhdGgnXG5cbmNvbnN0IERBVEFfRElSID0gcGF0aC5qb2luKHByb2Nlc3MuY3dkKCksICdkYXRhJylcbmNvbnN0IE5FV1NMRVRURVJfRklMRSA9IHBhdGguam9pbihEQVRBX0RJUiwgJ25ld3NsZXR0ZXIuanNvbicpXG5cbi8vIEVuc3VyZSBkYXRhIGRpcmVjdG9yeSBleGlzdHNcbmFzeW5jIGZ1bmN0aW9uIGVuc3VyZURhdGFEaXIoKSB7XG4gIGlmICghZXhpc3RzU3luYyhEQVRBX0RJUikpIHtcbiAgICBhd2FpdCBta2RpcihEQVRBX0RJUiwgeyByZWN1cnNpdmU6IHRydWUgfSlcbiAgfVxufVxuXG4vLyBSZWFkIG5ld3NsZXR0ZXIgc3Vic2NyaWJlcnMgZnJvbSBmaWxlXG5hc3luYyBmdW5jdGlvbiByZWFkU3Vic2NyaWJlcnMoKSB7XG4gIHRyeSB7XG4gICAgYXdhaXQgZW5zdXJlRGF0YURpcigpXG4gICAgaWYgKCFleGlzdHNTeW5jKE5FV1NMRVRURVJfRklMRSkpIHtcbiAgICAgIHJldHVybiBbXVxuICAgIH1cbiAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVhZEZpbGUoTkVXU0xFVFRFUl9GSUxFLCAndXRmLTgnKVxuICAgIHJldHVybiBKU09OLnBhcnNlKGRhdGEpXG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRXJyb3IgcmVhZGluZyBuZXdzbGV0dGVyIHN1YnNjcmliZXJzOicsIGVycm9yKVxuICAgIHJldHVybiBbXVxuICB9XG59XG5cbi8vIFdyaXRlIG5ld3NsZXR0ZXIgc3Vic2NyaWJlcnMgdG8gZmlsZVxuYXN5bmMgZnVuY3Rpb24gd3JpdGVTdWJzY3JpYmVycyhzdWJzY3JpYmVyczogYW55W10pIHtcbiAgdHJ5IHtcbiAgICBhd2FpdCBlbnN1cmVEYXRhRGlyKClcbiAgICBhd2FpdCB3cml0ZUZpbGUoTkVXU0xFVFRFUl9GSUxFLCBKU09OLnN0cmluZ2lmeShzdWJzY3JpYmVycywgbnVsbCwgMikpXG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRXJyb3Igd3JpdGluZyBuZXdzbGV0dGVyIHN1YnNjcmliZXJzOicsIGVycm9yKVxuICAgIHRocm93IGVycm9yXG4gIH1cbn1cblxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIFBPU1QocmVxdWVzdDogTmV4dFJlcXVlc3QpIHtcbiAgdHJ5IHtcbiAgICBjb25zdCBib2R5ID0gYXdhaXQgcmVxdWVzdC5qc29uKClcbiAgICBjb25zdCB7IGVtYWlsIH0gPSBib2R5XG5cbiAgICAvLyBWYWxpZGF0ZSBlbWFpbFxuICAgIGlmICghZW1haWwpIHtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgICAgeyBlcnJvcjogJ0VtYWlsIGlzIHJlcXVpcmVkJyB9LFxuICAgICAgICB7IHN0YXR1czogNDAwIH1cbiAgICAgIClcbiAgICB9XG5cbiAgICAvLyBCYXNpYyBlbWFpbCB2YWxpZGF0aW9uXG4gICAgY29uc3QgZW1haWxSZWdleCA9IC9eW15cXHNAXStAW15cXHNAXStcXC5bXlxcc0BdKyQvXG4gICAgaWYgKCFlbWFpbFJlZ2V4LnRlc3QoZW1haWwpKSB7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICAgIHsgZXJyb3I6ICdQbGVhc2UgZW50ZXIgYSB2YWxpZCBlbWFpbCBhZGRyZXNzJyB9LFxuICAgICAgICB7IHN0YXR1czogNDAwIH1cbiAgICAgIClcbiAgICB9XG5cbiAgICAvLyBSZWFkIGV4aXN0aW5nIHN1YnNjcmliZXJzXG4gICAgY29uc3Qgc3Vic2NyaWJlcnMgPSBhd2FpdCByZWFkU3Vic2NyaWJlcnMoKVxuICAgIFxuICAgIC8vIENoZWNrIGlmIGVtYWlsIGFscmVhZHkgZXhpc3RzXG4gICAgY29uc3QgZXhpc3RpbmdTdWJzY3JpYmVyID0gc3Vic2NyaWJlcnMuZmluZCgoc3ViOiBhbnkpID0+IHN1Yi5lbWFpbCA9PT0gZW1haWwpXG4gICAgaWYgKGV4aXN0aW5nU3Vic2NyaWJlcikge1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgICB7IGVycm9yOiAnVGhpcyBlbWFpbCBpcyBhbHJlYWR5IHN1YnNjcmliZWQgdG8gb3VyIG5ld3NsZXR0ZXInIH0sXG4gICAgICAgIHsgc3RhdHVzOiA0MDkgfVxuICAgICAgKVxuICAgIH1cblxuICAgIC8vIENyZWF0ZSBuZXcgc3Vic2NyaWJlclxuICAgIGNvbnN0IHN1YnNjcmliZXIgPSB7XG4gICAgICBpZDogTWF0aC5yYW5kb20oKS50b1N0cmluZygzNikuc3Vic3RyKDIsIDkpLFxuICAgICAgZW1haWwsXG4gICAgICBzdWJzY3JpYmVkQXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgIGlzQWN0aXZlOiB0cnVlLFxuICAgICAgc291cmNlOiAnd2Vic2l0ZSdcbiAgICB9XG5cbiAgICAvLyBBZGQgbmV3IHN1YnNjcmliZXJcbiAgICBzdWJzY3JpYmVycy5wdXNoKHN1YnNjcmliZXIpXG4gICAgXG4gICAgLy8gV3JpdGUgYmFjayB0byBmaWxlXG4gICAgYXdhaXQgd3JpdGVTdWJzY3JpYmVycyhzdWJzY3JpYmVycylcblxuICAgIC8vIFNlbmQgd2VsY29tZSBlbWFpbCAoaWYgZW1haWwgc2VydmljZSBpcyBjb25maWd1cmVkKVxuICAgIHRyeSB7XG4gICAgICBpZiAocHJvY2Vzcy5lbnYuUkVTRU5EX0FQSV9LRVkpIHtcbiAgICAgICAgY29uc3QgeyBSZXNlbmQgfSA9IHJlcXVpcmUoJ3Jlc2VuZCcpXG4gICAgICAgIGNvbnN0IHJlc2VuZCA9IG5ldyBSZXNlbmQocHJvY2Vzcy5lbnYuUkVTRU5EX0FQSV9LRVkpXG5cbiAgICAgICAgY29uc3Qgd2VsY29tZUVtYWlsSHRtbCA9IGBcbiAgICAgICAgICA8ZGl2IHN0eWxlPVwiZm9udC1mYW1pbHk6IEFyaWFsLCBzYW5zLXNlcmlmOyBtYXgtd2lkdGg6IDYwMHB4OyBtYXJnaW46IDAgYXV0bztcIj5cbiAgICAgICAgICAgIDxkaXYgc3R5bGU9XCJiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjNjY3ZWVhIDAlLCAjNzY0YmEyIDEwMCUpOyBwYWRkaW5nOiAzMHB4OyB0ZXh0LWFsaWduOiBjZW50ZXI7XCI+XG4gICAgICAgICAgICAgIDxoMSBzdHlsZT1cImNvbG9yOiB3aGl0ZTsgbWFyZ2luOiAwO1wiPldlbGNvbWUgdG8gRm9yZWluZ2F0ZSBHcm91cCE8L2gxPlxuICAgICAgICAgICAgICA8cCBzdHlsZT1cImNvbG9yOiB3aGl0ZTsgb3BhY2l0eTogMC45OyBtYXJnaW46IDEwcHggMCAwIDA7XCI+VGhhbmsgeW91IGZvciBzdWJzY3JpYmluZyB0byBvdXIgbmV3c2xldHRlcjwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgXG4gICAgICAgICAgICA8ZGl2IHN0eWxlPVwicGFkZGluZzogMzBweDsgYmFja2dyb3VuZDogI2Y4ZjlmYTtcIj5cbiAgICAgICAgICAgICAgPHAgc3R5bGU9XCJjb2xvcjogIzMzMzsgZm9udC1zaXplOiAxNnB4OyBsaW5lLWhlaWdodDogMS42O1wiPkhlbGxvITwvcD5cbiAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgIDxwIHN0eWxlPVwiY29sb3I6ICM1NTU7IGxpbmUtaGVpZ2h0OiAxLjY7XCI+XG4gICAgICAgICAgICAgICAgV2VsY29tZSB0byB0aGUgRm9yZWluZ2F0ZSBHcm91cCBjb21tdW5pdHkhIFlvdSd2ZSBzdWNjZXNzZnVsbHkgc3Vic2NyaWJlZCB0byBvdXIgbmV3c2xldHRlciBhbmQgd2lsbCBub3cgcmVjZWl2ZTpcbiAgICAgICAgICAgICAgPC9wPlxuXG4gICAgICAgICAgICAgIDxkaXYgc3R5bGU9XCJiYWNrZ3JvdW5kOiAjZThmNWU4OyBwYWRkaW5nOiAyMHB4OyBib3JkZXItcmFkaXVzOiA4cHg7IGJvcmRlci1sZWZ0OiA0cHggc29saWQgIzRjYWY1MDsgbWFyZ2luOiAyMHB4IDA7XCI+XG4gICAgICAgICAgICAgICAgPGgzIHN0eWxlPVwiY29sb3I6ICMyZTdkMzI7IG1hcmdpbjogMCAwIDE1cHggMDtcIj7wn5OnIFdoYXQgeW91J2xsIHJlY2VpdmU6PC9oMz5cbiAgICAgICAgICAgICAgICA8dWwgc3R5bGU9XCJjb2xvcjogIzU1NTsgbWFyZ2luOiAwOyBwYWRkaW5nLWxlZnQ6IDIwcHg7XCI+XG4gICAgICAgICAgICAgICAgICA8bGk+TGF0ZXN0IHVuaXZlcnNpdHkgYWRtaXNzaW9uIHVwZGF0ZXM8L2xpPlxuICAgICAgICAgICAgICAgICAgPGxpPlNjaG9sYXJzaGlwIG9wcG9ydHVuaXRpZXMgYW5kIGRlYWRsaW5lczwvbGk+XG4gICAgICAgICAgICAgICAgICA8bGk+U3R1ZGVudCBzdWNjZXNzIHN0b3JpZXMgYW5kIHRpcHM8L2xpPlxuICAgICAgICAgICAgICAgICAgPGxpPkVkdWNhdGlvbmFsIGluc2lnaHRzIGFuZCBndWlkZXM8L2xpPlxuICAgICAgICAgICAgICAgICAgPGxpPkV4Y2x1c2l2ZSBvZmZlcnMgYW5kIGVhcmx5IGFjY2VzcyB0byBzZXJ2aWNlczwvbGk+XG4gICAgICAgICAgICAgICAgPC91bD5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgPGRpdiBzdHlsZT1cImJhY2tncm91bmQ6ICNmZmYzZTA7IHBhZGRpbmc6IDIwcHg7IGJvcmRlci1yYWRpdXM6IDhweDsgYm9yZGVyLWxlZnQ6IDRweCBzb2xpZCAjZmY5ODAwOyBtYXJnaW46IDIwcHggMDtcIj5cbiAgICAgICAgICAgICAgICA8aDMgc3R5bGU9XCJjb2xvcjogI2Y1N2MwMDsgbWFyZ2luOiAwIDAgMTBweCAwO1wiPvCfjpMgUmVhZHkgdG8gc3RhcnQgeW91ciBqb3VybmV5PzwvaDM+XG4gICAgICAgICAgICAgICAgPHAgc3R5bGU9XCJjb2xvcjogIzU1NTsgbWFyZ2luOiAwO1wiPlxuICAgICAgICAgICAgICAgICAgSWYgeW91J3JlIHJlYWR5IHRvIGFwcGx5IHRvIHVuaXZlcnNpdGllcyBpbiBOb3J0aGVybiBDeXBydXMsIG91ciBleHBlcnQgdGVhbSBpcyBoZXJlIHRvIGhlbHAuIFxuICAgICAgICAgICAgICAgICAgQ29udGFjdCB1cyBmb3IgYSBmcmVlIGNvbnN1bHRhdGlvbiFcbiAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIDxkaXYgc3R5bGU9XCJ0ZXh0LWFsaWduOiBjZW50ZXI7IG1hcmdpbjogMzBweCAwO1wiPlxuICAgICAgICAgICAgICAgIDxhIGhyZWY9XCIke3Byb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0FQUF9VUkx9L2NvbnRhY3RcIiBcbiAgICAgICAgICAgICAgICAgICBzdHlsZT1cImJhY2tncm91bmQ6ICM2NjdlZWE7IGNvbG9yOiB3aGl0ZTsgcGFkZGluZzogMTVweCAzMHB4OyB0ZXh0LWRlY29yYXRpb246IG5vbmU7IGJvcmRlci1yYWRpdXM6IDhweDsgZm9udC13ZWlnaHQ6IGJvbGQ7IGRpc3BsYXk6IGlubGluZS1ibG9jaztcIj5cbiAgICAgICAgICAgICAgICAgIEdldCBGcmVlIENvbnN1bHRhdGlvblxuICAgICAgICAgICAgICAgIDwvYT5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgPHAgc3R5bGU9XCJjb2xvcjogIzU1NTsgbGluZS1oZWlnaHQ6IDEuNjtcIj5cbiAgICAgICAgICAgICAgICBUaGFuayB5b3UgZm9yIGNob29zaW5nIEZvcmVpbmdhdGUgR3JvdXAgYXMgeW91ciBlZHVjYXRpb25hbCBwYXJ0bmVyLiBXZSdyZSBleGNpdGVkIHRvIGhlbHAgeW91IGFjaGlldmUgeW91ciBhY2FkZW1pYyBkcmVhbXMhXG4gICAgICAgICAgICAgIDwvcD5cblxuICAgICAgICAgICAgICA8cCBzdHlsZT1cImNvbG9yOiAjNTU1OyBsaW5lLWhlaWdodDogMS42O1wiPlxuICAgICAgICAgICAgICAgIEJlc3QgcmVnYXJkcyw8YnI+XG4gICAgICAgICAgICAgICAgPHN0cm9uZz5UaGUgRm9yZWluZ2F0ZSBHcm91cCBUZWFtPC9zdHJvbmc+PGJyPlxuICAgICAgICAgICAgICAgIDxlbT5Zb3VyIEdhdGV3YXkgdG8gSW50ZXJuYXRpb25hbCBFZHVjYXRpb248L2VtPlxuICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgPGRpdiBzdHlsZT1cImJhY2tncm91bmQ6ICMzMzM7IHBhZGRpbmc6IDIwcHg7IHRleHQtYWxpZ246IGNlbnRlcjtcIj5cbiAgICAgICAgICAgICAgPHAgc3R5bGU9XCJjb2xvcjogI2NjYzsgbWFyZ2luOiAwOyBmb250LXNpemU6IDE0cHg7XCI+XG4gICAgICAgICAgICAgICAgwqkgMjAyNCBGb3JlaW5nYXRlIEdyb3VwLiBBbGwgcmlnaHRzIHJlc2VydmVkLjxicj5cbiAgICAgICAgICAgICAgICBZb3UgY2FuIHVuc3Vic2NyaWJlIGF0IGFueSB0aW1lIGJ5IHJlcGx5aW5nIHRvIHRoaXMgZW1haWwuXG4gICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICBgXG5cbiAgICAgICAgYXdhaXQgcmVzZW5kLmVtYWlscy5zZW5kKHtcbiAgICAgICAgICBmcm9tOiAnRm9yZWluZ2F0ZSBHcm91cCA8bmV3c2xldHRlckBmb3JlaW5nYXRlLmNvbT4nLFxuICAgICAgICAgIHRvOiBbZW1haWxdLFxuICAgICAgICAgIHN1YmplY3Q6ICdXZWxjb21lIHRvIEZvcmVpbmdhdGUgR3JvdXAgTmV3c2xldHRlciEnLFxuICAgICAgICAgIGh0bWw6IHdlbGNvbWVFbWFpbEh0bWwsXG4gICAgICAgIH0pXG4gICAgICB9XG4gICAgfSBjYXRjaCAoZW1haWxFcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignV2VsY29tZSBlbWFpbCBzZW5kaW5nIGZhaWxlZDonLCBlbWFpbEVycm9yKVxuICAgICAgLy8gRG9uJ3QgZmFpbCB0aGUgc3Vic2NyaXB0aW9uIGlmIGVtYWlsIGZhaWxzXG4gICAgfVxuXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHtcbiAgICAgIHN1Y2Nlc3M6IHRydWUsXG4gICAgICBtZXNzYWdlOiAnU3VjY2Vzc2Z1bGx5IHN1YnNjcmliZWQgdG8gbmV3c2xldHRlciEnLFxuICAgICAgc3Vic2NyaWJlcklkOiBzdWJzY3JpYmVyLmlkXG4gICAgfSlcblxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ05ld3NsZXR0ZXIgc3Vic2NyaXB0aW9uIGVycm9yOicsIGVycm9yKVxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgIHsgZXJyb3I6ICdGYWlsZWQgdG8gc3Vic2NyaWJlLiBQbGVhc2UgdHJ5IGFnYWluLicgfSxcbiAgICAgIHsgc3RhdHVzOiA1MDAgfVxuICAgIClcbiAgfVxufVxuXG4vLyBHRVQgZW5kcG9pbnQgdG8gcmV0cmlldmUgc3Vic2NyaWJlcnMgKGZvciBhZG1pbiB1c2UpXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gR0VUKHJlcXVlc3Q6IE5leHRSZXF1ZXN0KSB7XG4gIHRyeSB7XG4gICAgY29uc3QgeyBzZWFyY2hQYXJhbXMgfSA9IG5ldyBVUkwocmVxdWVzdC51cmwpXG4gICAgY29uc3Qgc3RhdHVzID0gc2VhcmNoUGFyYW1zLmdldCgnc3RhdHVzJykgLy8gJ2FjdGl2ZScsICdpbmFjdGl2ZScsICdhbGwnXG4gICAgY29uc3Qgc2VhcmNoID0gc2VhcmNoUGFyYW1zLmdldCgnc2VhcmNoJylcbiAgICBjb25zdCBwYWdlID0gcGFyc2VJbnQoc2VhcmNoUGFyYW1zLmdldCgncGFnZScpIHx8ICcxJylcbiAgICBjb25zdCBsaW1pdCA9IHBhcnNlSW50KHNlYXJjaFBhcmFtcy5nZXQoJ2xpbWl0JykgfHwgJzEwJylcbiAgICBjb25zdCBzb3J0QnkgPSBzZWFyY2hQYXJhbXMuZ2V0KCdzb3J0QnknKSB8fCAnc3Vic2NyaWJlZEF0J1xuICAgIGNvbnN0IHNvcnRPcmRlciA9IHNlYXJjaFBhcmFtcy5nZXQoJ3NvcnRPcmRlcicpIHx8ICdkZXNjJ1xuXG4gICAgbGV0IHN1YnNjcmliZXJzID0gYXdhaXQgcmVhZFN1YnNjcmliZXJzKClcblxuICAgIC8vIEFwcGx5IGZpbHRlcnNcbiAgICBpZiAoc3RhdHVzICYmIHN0YXR1cyAhPT0gJ2FsbCcpIHtcbiAgICAgIGNvbnN0IGlzQWN0aXZlID0gc3RhdHVzID09PSAnYWN0aXZlJ1xuICAgICAgc3Vic2NyaWJlcnMgPSBzdWJzY3JpYmVycy5maWx0ZXIoKHN1YjogYW55KSA9PiBzdWIuaXNBY3RpdmUgPT09IGlzQWN0aXZlKVxuICAgIH1cblxuICAgIGlmIChzZWFyY2gpIHtcbiAgICAgIHN1YnNjcmliZXJzID0gc3Vic2NyaWJlcnMuZmlsdGVyKChzdWI6IGFueSkgPT5cbiAgICAgICAgc3ViLmVtYWlsLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoc2VhcmNoLnRvTG93ZXJDYXNlKCkpXG4gICAgICApXG4gICAgfVxuXG4gICAgLy8gU29ydCBzdWJzY3JpYmVyc1xuICAgIHN1YnNjcmliZXJzLnNvcnQoKGE6IGFueSwgYjogYW55KSA9PiB7XG4gICAgICBsZXQgYVZhbHVlID0gYVtzb3J0QnldXG4gICAgICBsZXQgYlZhbHVlID0gYltzb3J0QnldXG5cbiAgICAgIGlmIChzb3J0QnkgPT09ICdzdWJzY3JpYmVkQXQnKSB7XG4gICAgICAgIGFWYWx1ZSA9IG5ldyBEYXRlKGFWYWx1ZSkuZ2V0VGltZSgpXG4gICAgICAgIGJWYWx1ZSA9IG5ldyBEYXRlKGJWYWx1ZSkuZ2V0VGltZSgpXG4gICAgICB9XG5cbiAgICAgIGlmIChzb3J0T3JkZXIgPT09ICdkZXNjJykge1xuICAgICAgICByZXR1cm4gYlZhbHVlID4gYVZhbHVlID8gMSA6IC0xXG4gICAgICB9IGVsc2Uge1xuICAgICAgICByZXR1cm4gYVZhbHVlID4gYlZhbHVlID8gMSA6IC0xXG4gICAgICB9XG4gICAgfSlcblxuICAgIC8vIFBhZ2luYXRpb25cbiAgICBjb25zdCB0b3RhbCA9IHN1YnNjcmliZXJzLmxlbmd0aFxuICAgIGNvbnN0IHN0YXJ0SW5kZXggPSAocGFnZSAtIDEpICogbGltaXRcbiAgICBjb25zdCBlbmRJbmRleCA9IHN0YXJ0SW5kZXggKyBsaW1pdFxuICAgIGNvbnN0IHBhZ2luYXRlZFN1YnNjcmliZXJzID0gc3Vic2NyaWJlcnMuc2xpY2Uoc3RhcnRJbmRleCwgZW5kSW5kZXgpXG5cbiAgICAvLyBDYWxjdWxhdGUgc3RhdGlzdGljc1xuICAgIGNvbnN0IHN0YXRzID0ge1xuICAgICAgdG90YWw6IHN1YnNjcmliZXJzLmxlbmd0aCxcbiAgICAgIGFjdGl2ZTogc3Vic2NyaWJlcnMuZmlsdGVyKChzdWI6IGFueSkgPT4gc3ViLmlzQWN0aXZlKS5sZW5ndGgsXG4gICAgICBpbmFjdGl2ZTogc3Vic2NyaWJlcnMuZmlsdGVyKChzdWI6IGFueSkgPT4gIXN1Yi5pc0FjdGl2ZSkubGVuZ3RoLFxuICAgICAgdGhpc1dlZWs6IHN1YnNjcmliZXJzLmZpbHRlcigoc3ViOiBhbnkpID0+IHtcbiAgICAgICAgY29uc3Qgc3ViRGF0ZSA9IG5ldyBEYXRlKHN1Yi5zdWJzY3JpYmVkQXQpXG4gICAgICAgIGNvbnN0IHdlZWtBZ28gPSBuZXcgRGF0ZSgpXG4gICAgICAgIHdlZWtBZ28uc2V0RGF0ZSh3ZWVrQWdvLmdldERhdGUoKSAtIDcpXG4gICAgICAgIHJldHVybiBzdWJEYXRlID4gd2Vla0Fnb1xuICAgICAgfSkubGVuZ3RoLFxuICAgICAgdGhpc01vbnRoOiBzdWJzY3JpYmVycy5maWx0ZXIoKHN1YjogYW55KSA9PiB7XG4gICAgICAgIGNvbnN0IHN1YkRhdGUgPSBuZXcgRGF0ZShzdWIuc3Vic2NyaWJlZEF0KVxuICAgICAgICBjb25zdCBtb250aEFnbyA9IG5ldyBEYXRlKClcbiAgICAgICAgbW9udGhBZ28uc2V0TW9udGgobW9udGhBZ28uZ2V0TW9udGgoKSAtIDEpXG4gICAgICAgIHJldHVybiBzdWJEYXRlID4gbW9udGhBZ29cbiAgICAgIH0pLmxlbmd0aFxuICAgIH1cblxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7XG4gICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgZGF0YTogcGFnaW5hdGVkU3Vic2NyaWJlcnMsXG4gICAgICBwYWdpbmF0aW9uOiB7XG4gICAgICAgIHBhZ2UsXG4gICAgICAgIGxpbWl0LFxuICAgICAgICB0b3RhbCxcbiAgICAgICAgdG90YWxQYWdlczogTWF0aC5jZWlsKHRvdGFsIC8gbGltaXQpLFxuICAgICAgICBoYXNOZXh0OiBlbmRJbmRleCA8IHRvdGFsLFxuICAgICAgICBoYXNQcmV2OiBwYWdlID4gMVxuICAgICAgfSxcbiAgICAgIHN0YXRzXG4gICAgfSlcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvciBmZXRjaGluZyBuZXdzbGV0dGVyIHN1YnNjcmliZXJzOicsIGVycm9yKVxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgIHsgZXJyb3I6ICdGYWlsZWQgdG8gZmV0Y2ggbmV3c2xldHRlciBzdWJzY3JpYmVycycgfSxcbiAgICAgIHsgc3RhdHVzOiA1MDAgfVxuICAgIClcbiAgfVxufVxuIl0sIm5hbWVzIjpbIk5leHRSZXNwb25zZSIsIndyaXRlRmlsZSIsInJlYWRGaWxlIiwibWtkaXIiLCJleGlzdHNTeW5jIiwicGF0aCIsIkRBVEFfRElSIiwiam9pbiIsInByb2Nlc3MiLCJjd2QiLCJORVdTTEVUVEVSX0ZJTEUiLCJlbnN1cmVEYXRhRGlyIiwicmVjdXJzaXZlIiwicmVhZFN1YnNjcmliZXJzIiwiZGF0YSIsIkpTT04iLCJwYXJzZSIsImVycm9yIiwiY29uc29sZSIsIndyaXRlU3Vic2NyaWJlcnMiLCJzdWJzY3JpYmVycyIsInN0cmluZ2lmeSIsIlBPU1QiLCJyZXF1ZXN0IiwiYm9keSIsImpzb24iLCJlbWFpbCIsInN0YXR1cyIsImVtYWlsUmVnZXgiLCJ0ZXN0IiwiZXhpc3RpbmdTdWJzY3JpYmVyIiwiZmluZCIsInN1YiIsInN1YnNjcmliZXIiLCJpZCIsIk1hdGgiLCJyYW5kb20iLCJ0b1N0cmluZyIsInN1YnN0ciIsInN1YnNjcmliZWRBdCIsIkRhdGUiLCJ0b0lTT1N0cmluZyIsImlzQWN0aXZlIiwic291cmNlIiwicHVzaCIsImVudiIsIlJFU0VORF9BUElfS0VZIiwiUmVzZW5kIiwicmVxdWlyZSIsInJlc2VuZCIsIndlbGNvbWVFbWFpbEh0bWwiLCJORVhUX1BVQkxJQ19BUFBfVVJMIiwiZW1haWxzIiwic2VuZCIsImZyb20iLCJ0byIsInN1YmplY3QiLCJodG1sIiwiZW1haWxFcnJvciIsInN1Y2Nlc3MiLCJtZXNzYWdlIiwic3Vic2NyaWJlcklkIiwiR0VUIiwic2VhcmNoUGFyYW1zIiwiVVJMIiwidXJsIiwiZ2V0Iiwic2VhcmNoIiwicGFnZSIsInBhcnNlSW50IiwibGltaXQiLCJzb3J0QnkiLCJzb3J0T3JkZXIiLCJmaWx0ZXIiLCJ0b0xvd2VyQ2FzZSIsImluY2x1ZGVzIiwic29ydCIsImEiLCJiIiwiYVZhbHVlIiwiYlZhbHVlIiwiZ2V0VGltZSIsInRvdGFsIiwibGVuZ3RoIiwic3RhcnRJbmRleCIsImVuZEluZGV4IiwicGFnaW5hdGVkU3Vic2NyaWJlcnMiLCJzbGljZSIsInN0YXRzIiwiYWN0aXZlIiwiaW5hY3RpdmUiLCJ0aGlzV2VlayIsInN1YkRhdGUiLCJ3ZWVrQWdvIiwic2V0RGF0ZSIsImdldERhdGUiLCJ0aGlzTW9udGgiLCJtb250aEFnbyIsInNldE1vbnRoIiwiZ2V0TW9udGgiLCJwYWdpbmF0aW9uIiwidG90YWxQYWdlcyIsImNlaWwiLCJoYXNOZXh0IiwiaGFzUHJldiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/newsletter/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "async_hooks":
/*!******************************!*\
  !*** external "async_hooks" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("async_hooks");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "fs/promises":
/*!******************************!*\
  !*** external "fs/promises" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("fs/promises");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "prettier/plugins/html":
/*!****************************************!*\
  !*** external "prettier/plugins/html" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = import("prettier/plugins/html");;

/***/ }),

/***/ "prettier/standalone":
/*!**************************************!*\
  !*** external "prettier/standalone" ***!
  \**************************************/
/***/ ((module) => {

"use strict";
module.exports = import("prettier/standalone");;

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/resend"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fnewsletter%2Froute&page=%2Fapi%2Fnewsletter%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnewsletter%2Froute.ts&appDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();