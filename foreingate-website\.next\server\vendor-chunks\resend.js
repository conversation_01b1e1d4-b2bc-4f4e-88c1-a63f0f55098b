"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/resend";
exports.ids = ["vendor-chunks/resend"];
exports.modules = {

/***/ "(rsc)/../node_modules/resend/dist/index.js":
/*!********************************************!*\
  !*** ../node_modules/resend/dist/index.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __defProps = Object.defineProperties;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropDescs = Object.getOwnPropertyDescriptors;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar __async = (__this, __arguments, generator) => {\n  return new Promise((resolve, reject) => {\n    var fulfilled = (value) => {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    };\n    var rejected = (value) => {\n      try {\n        step(generator.throw(value));\n      } catch (e) {\n        reject(e);\n      }\n    };\n    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);\n    step((generator = generator.apply(__this, __arguments)).next());\n  });\n};\n\n// src/index.ts\nvar src_exports = {};\n__export(src_exports, {\n  Resend: () => Resend\n});\nmodule.exports = __toCommonJS(src_exports);\n\n// package.json\nvar version = \"4.6.0\";\n\n// src/api-keys/api-keys.ts\nvar ApiKeys = class {\n  constructor(resend) {\n    this.resend = resend;\n  }\n  create(_0) {\n    return __async(this, arguments, function* (payload, options = {}) {\n      const data = yield this.resend.post(\n        \"/api-keys\",\n        payload,\n        options\n      );\n      return data;\n    });\n  }\n  list() {\n    return __async(this, null, function* () {\n      const data = yield this.resend.get(\"/api-keys\");\n      return data;\n    });\n  }\n  remove(id) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.delete(\n        `/api-keys/${id}`\n      );\n      return data;\n    });\n  }\n};\n\n// src/audiences/audiences.ts\nvar Audiences = class {\n  constructor(resend) {\n    this.resend = resend;\n  }\n  create(_0) {\n    return __async(this, arguments, function* (payload, options = {}) {\n      const data = yield this.resend.post(\n        \"/audiences\",\n        payload,\n        options\n      );\n      return data;\n    });\n  }\n  list() {\n    return __async(this, null, function* () {\n      const data = yield this.resend.get(\"/audiences\");\n      return data;\n    });\n  }\n  get(id) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.get(\n        `/audiences/${id}`\n      );\n      return data;\n    });\n  }\n  remove(id) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.delete(\n        `/audiences/${id}`\n      );\n      return data;\n    });\n  }\n};\n\n// src/common/utils/parse-email-to-api-options.ts\nfunction parseEmailToApiOptions(email) {\n  return {\n    attachments: email.attachments,\n    bcc: email.bcc,\n    cc: email.cc,\n    from: email.from,\n    headers: email.headers,\n    html: email.html,\n    reply_to: email.replyTo,\n    scheduled_at: email.scheduledAt,\n    subject: email.subject,\n    tags: email.tags,\n    text: email.text,\n    to: email.to\n  };\n}\n\n// src/batch/batch.ts\nvar Batch = class {\n  constructor(resend) {\n    this.resend = resend;\n  }\n  send(_0) {\n    return __async(this, arguments, function* (payload, options = {}) {\n      return this.create(payload, options);\n    });\n  }\n  create(_0) {\n    return __async(this, arguments, function* (payload, options = {}) {\n      const emails = [];\n      for (const email of payload) {\n        if (email.react) {\n          if (!this.renderAsync) {\n            try {\n              const { renderAsync } = yield Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/next\"), __webpack_require__.e(\"vendor-chunks/entities\"), __webpack_require__.e(\"vendor-chunks/domutils\"), __webpack_require__.e(\"vendor-chunks/htmlparser2\"), __webpack_require__.e(\"vendor-chunks/peberminta\"), __webpack_require__.e(\"vendor-chunks/domhandler\"), __webpack_require__.e(\"vendor-chunks/dom-serializer\"), __webpack_require__.e(\"vendor-chunks/selderee\"), __webpack_require__.e(\"vendor-chunks/parseley\"), __webpack_require__.e(\"vendor-chunks/leac\"), __webpack_require__.e(\"vendor-chunks/html-to-text\"), __webpack_require__.e(\"vendor-chunks/domelementtype\"), __webpack_require__.e(\"vendor-chunks/@selderee\"), __webpack_require__.e(\"vendor-chunks/@react-email\"), __webpack_require__.e(\"vendor-chunks/deepmerge\")]).then(__webpack_require__.bind(__webpack_require__, /*! @react-email/render */ \"(rsc)/../node_modules/@react-email/render/dist/node/index.mjs\"));\n              this.renderAsync = renderAsync;\n            } catch (error) {\n              throw new Error(\n                \"Failed to render React component. Make sure to install `@react-email/render`\"\n              );\n            }\n          }\n          email.html = yield this.renderAsync(email.react);\n          email.react = void 0;\n        }\n        emails.push(parseEmailToApiOptions(email));\n      }\n      const data = yield this.resend.post(\n        \"/emails/batch\",\n        emails,\n        options\n      );\n      return data;\n    });\n  }\n};\n\n// src/broadcasts/broadcasts.ts\nvar Broadcasts = class {\n  constructor(resend) {\n    this.resend = resend;\n  }\n  create(_0) {\n    return __async(this, arguments, function* (payload, options = {}) {\n      if (payload.react) {\n        if (!this.renderAsync) {\n          try {\n            const { renderAsync } = yield Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/next\"), __webpack_require__.e(\"vendor-chunks/entities\"), __webpack_require__.e(\"vendor-chunks/domutils\"), __webpack_require__.e(\"vendor-chunks/htmlparser2\"), __webpack_require__.e(\"vendor-chunks/peberminta\"), __webpack_require__.e(\"vendor-chunks/domhandler\"), __webpack_require__.e(\"vendor-chunks/dom-serializer\"), __webpack_require__.e(\"vendor-chunks/selderee\"), __webpack_require__.e(\"vendor-chunks/parseley\"), __webpack_require__.e(\"vendor-chunks/leac\"), __webpack_require__.e(\"vendor-chunks/html-to-text\"), __webpack_require__.e(\"vendor-chunks/domelementtype\"), __webpack_require__.e(\"vendor-chunks/@selderee\"), __webpack_require__.e(\"vendor-chunks/@react-email\"), __webpack_require__.e(\"vendor-chunks/deepmerge\")]).then(__webpack_require__.bind(__webpack_require__, /*! @react-email/render */ \"(rsc)/../node_modules/@react-email/render/dist/node/index.mjs\"));\n            this.renderAsync = renderAsync;\n          } catch (error) {\n            throw new Error(\n              \"Failed to render React component. Make sure to install `@react-email/render`\"\n            );\n          }\n        }\n        payload.html = yield this.renderAsync(\n          payload.react\n        );\n      }\n      const data = yield this.resend.post(\n        \"/broadcasts\",\n        {\n          name: payload.name,\n          audience_id: payload.audienceId,\n          preview_text: payload.previewText,\n          from: payload.from,\n          html: payload.html,\n          reply_to: payload.replyTo,\n          subject: payload.subject,\n          text: payload.text\n        },\n        options\n      );\n      return data;\n    });\n  }\n  send(id, payload) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.post(\n        `/broadcasts/${id}/send`,\n        { scheduled_at: payload == null ? void 0 : payload.scheduledAt }\n      );\n      return data;\n    });\n  }\n  list() {\n    return __async(this, null, function* () {\n      const data = yield this.resend.get(\"/broadcasts\");\n      return data;\n    });\n  }\n  get(id) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.get(\n        `/broadcasts/${id}`\n      );\n      return data;\n    });\n  }\n  remove(id) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.delete(\n        `/broadcasts/${id}`\n      );\n      return data;\n    });\n  }\n  update(id, payload) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.patch(\n        `/broadcasts/${id}`,\n        {\n          name: payload.name,\n          audience_id: payload.audienceId,\n          from: payload.from,\n          html: payload.html,\n          text: payload.text,\n          subject: payload.subject,\n          reply_to: payload.replyTo,\n          preview_text: payload.previewText\n        }\n      );\n      return data;\n    });\n  }\n};\n\n// src/contacts/contacts.ts\nvar Contacts = class {\n  constructor(resend) {\n    this.resend = resend;\n  }\n  create(_0) {\n    return __async(this, arguments, function* (payload, options = {}) {\n      const data = yield this.resend.post(\n        `/audiences/${payload.audienceId}/contacts`,\n        {\n          unsubscribed: payload.unsubscribed,\n          email: payload.email,\n          first_name: payload.firstName,\n          last_name: payload.lastName\n        },\n        options\n      );\n      return data;\n    });\n  }\n  list(options) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.get(\n        `/audiences/${options.audienceId}/contacts`\n      );\n      return data;\n    });\n  }\n  get(options) {\n    return __async(this, null, function* () {\n      if (!options.id && !options.email) {\n        return {\n          data: null,\n          error: {\n            message: \"Missing `id` or `email` field.\",\n            name: \"missing_required_field\"\n          }\n        };\n      }\n      const data = yield this.resend.get(\n        `/audiences/${options.audienceId}/contacts/${(options == null ? void 0 : options.email) ? options == null ? void 0 : options.email : options == null ? void 0 : options.id}`\n      );\n      return data;\n    });\n  }\n  update(payload) {\n    return __async(this, null, function* () {\n      if (!payload.id && !payload.email) {\n        return {\n          data: null,\n          error: {\n            message: \"Missing `id` or `email` field.\",\n            name: \"missing_required_field\"\n          }\n        };\n      }\n      const data = yield this.resend.patch(\n        `/audiences/${payload.audienceId}/contacts/${(payload == null ? void 0 : payload.email) ? payload == null ? void 0 : payload.email : payload == null ? void 0 : payload.id}`,\n        {\n          unsubscribed: payload.unsubscribed,\n          first_name: payload.firstName,\n          last_name: payload.lastName\n        }\n      );\n      return data;\n    });\n  }\n  remove(payload) {\n    return __async(this, null, function* () {\n      if (!payload.id && !payload.email) {\n        return {\n          data: null,\n          error: {\n            message: \"Missing `id` or `email` field.\",\n            name: \"missing_required_field\"\n          }\n        };\n      }\n      const data = yield this.resend.delete(\n        `/audiences/${payload.audienceId}/contacts/${(payload == null ? void 0 : payload.email) ? payload == null ? void 0 : payload.email : payload == null ? void 0 : payload.id}`\n      );\n      return data;\n    });\n  }\n};\n\n// src/common/utils/parse-domain-to-api-options.ts\nfunction parseDomainToApiOptions(domain) {\n  return {\n    name: domain.name,\n    region: domain.region,\n    custom_return_path: domain.customReturnPath\n  };\n}\n\n// src/domains/domains.ts\nvar Domains = class {\n  constructor(resend) {\n    this.resend = resend;\n  }\n  create(_0) {\n    return __async(this, arguments, function* (payload, options = {}) {\n      const data = yield this.resend.post(\n        \"/domains\",\n        parseDomainToApiOptions(payload),\n        options\n      );\n      return data;\n    });\n  }\n  list() {\n    return __async(this, null, function* () {\n      const data = yield this.resend.get(\"/domains\");\n      return data;\n    });\n  }\n  get(id) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.get(\n        `/domains/${id}`\n      );\n      return data;\n    });\n  }\n  update(payload) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.patch(\n        `/domains/${payload.id}`,\n        {\n          click_tracking: payload.clickTracking,\n          open_tracking: payload.openTracking,\n          tls: payload.tls\n        }\n      );\n      return data;\n    });\n  }\n  remove(id) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.delete(\n        `/domains/${id}`\n      );\n      return data;\n    });\n  }\n  verify(id) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.post(\n        `/domains/${id}/verify`\n      );\n      return data;\n    });\n  }\n};\n\n// src/emails/emails.ts\nvar Emails = class {\n  constructor(resend) {\n    this.resend = resend;\n  }\n  send(_0) {\n    return __async(this, arguments, function* (payload, options = {}) {\n      return this.create(payload, options);\n    });\n  }\n  create(_0) {\n    return __async(this, arguments, function* (payload, options = {}) {\n      if (payload.react) {\n        if (!this.renderAsync) {\n          try {\n            const { renderAsync } = yield Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/next\"), __webpack_require__.e(\"vendor-chunks/entities\"), __webpack_require__.e(\"vendor-chunks/domutils\"), __webpack_require__.e(\"vendor-chunks/htmlparser2\"), __webpack_require__.e(\"vendor-chunks/peberminta\"), __webpack_require__.e(\"vendor-chunks/domhandler\"), __webpack_require__.e(\"vendor-chunks/dom-serializer\"), __webpack_require__.e(\"vendor-chunks/selderee\"), __webpack_require__.e(\"vendor-chunks/parseley\"), __webpack_require__.e(\"vendor-chunks/leac\"), __webpack_require__.e(\"vendor-chunks/html-to-text\"), __webpack_require__.e(\"vendor-chunks/domelementtype\"), __webpack_require__.e(\"vendor-chunks/@selderee\"), __webpack_require__.e(\"vendor-chunks/@react-email\"), __webpack_require__.e(\"vendor-chunks/deepmerge\")]).then(__webpack_require__.bind(__webpack_require__, /*! @react-email/render */ \"(rsc)/../node_modules/@react-email/render/dist/node/index.mjs\"));\n            this.renderAsync = renderAsync;\n          } catch (error) {\n            throw new Error(\n              \"Failed to render React component. Make sure to install `@react-email/render`\"\n            );\n          }\n        }\n        payload.html = yield this.renderAsync(\n          payload.react\n        );\n      }\n      const data = yield this.resend.post(\n        \"/emails\",\n        parseEmailToApiOptions(payload),\n        options\n      );\n      return data;\n    });\n  }\n  get(id) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.get(\n        `/emails/${id}`\n      );\n      return data;\n    });\n  }\n  update(payload) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.patch(\n        `/emails/${payload.id}`,\n        {\n          scheduled_at: payload.scheduledAt\n        }\n      );\n      return data;\n    });\n  }\n  cancel(id) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.post(\n        `/emails/${id}/cancel`\n      );\n      return data;\n    });\n  }\n};\n\n// src/resend.ts\nvar defaultBaseUrl = \"https://api.resend.com\";\nvar defaultUserAgent = `resend-node:${version}`;\nvar baseUrl = typeof process !== \"undefined\" && process.env ? process.env.RESEND_BASE_URL || defaultBaseUrl : defaultBaseUrl;\nvar userAgent = typeof process !== \"undefined\" && process.env ? process.env.RESEND_USER_AGENT || defaultUserAgent : defaultUserAgent;\nvar Resend = class {\n  constructor(key) {\n    this.key = key;\n    this.apiKeys = new ApiKeys(this);\n    this.audiences = new Audiences(this);\n    this.batch = new Batch(this);\n    this.broadcasts = new Broadcasts(this);\n    this.contacts = new Contacts(this);\n    this.domains = new Domains(this);\n    this.emails = new Emails(this);\n    if (!key) {\n      if (typeof process !== \"undefined\" && process.env) {\n        this.key = process.env.RESEND_API_KEY;\n      }\n      if (!this.key) {\n        throw new Error(\n          'Missing API key. Pass it to the constructor `new Resend(\"re_123\")`'\n        );\n      }\n    }\n    this.headers = new Headers({\n      Authorization: `Bearer ${this.key}`,\n      \"User-Agent\": userAgent,\n      \"Content-Type\": \"application/json\"\n    });\n  }\n  fetchRequest(_0) {\n    return __async(this, arguments, function* (path, options = {}) {\n      try {\n        const response = yield fetch(`${baseUrl}${path}`, options);\n        if (!response.ok) {\n          try {\n            const rawError = yield response.text();\n            return { data: null, error: JSON.parse(rawError) };\n          } catch (err) {\n            if (err instanceof SyntaxError) {\n              return {\n                data: null,\n                error: {\n                  name: \"application_error\",\n                  message: \"Internal server error. We are unable to process your request right now, please try again later.\"\n                }\n              };\n            }\n            const error = {\n              message: response.statusText,\n              name: \"application_error\"\n            };\n            if (err instanceof Error) {\n              return { data: null, error: __spreadProps(__spreadValues({}, error), { message: err.message }) };\n            }\n            return { data: null, error };\n          }\n        }\n        const data = yield response.json();\n        return { data, error: null };\n      } catch (error) {\n        return {\n          data: null,\n          error: {\n            name: \"application_error\",\n            message: \"Unable to fetch data. The request could not be resolved.\"\n          }\n        };\n      }\n    });\n  }\n  post(_0, _1) {\n    return __async(this, arguments, function* (path, entity, options = {}) {\n      const headers = new Headers(this.headers);\n      if (options.idempotencyKey) {\n        headers.set(\"Idempotency-Key\", options.idempotencyKey);\n      }\n      const requestOptions = __spreadValues({\n        method: \"POST\",\n        headers,\n        body: JSON.stringify(entity)\n      }, options);\n      return this.fetchRequest(path, requestOptions);\n    });\n  }\n  get(_0) {\n    return __async(this, arguments, function* (path, options = {}) {\n      const requestOptions = __spreadValues({\n        method: \"GET\",\n        headers: this.headers\n      }, options);\n      return this.fetchRequest(path, requestOptions);\n    });\n  }\n  put(_0, _1) {\n    return __async(this, arguments, function* (path, entity, options = {}) {\n      const requestOptions = __spreadValues({\n        method: \"PUT\",\n        headers: this.headers,\n        body: JSON.stringify(entity)\n      }, options);\n      return this.fetchRequest(path, requestOptions);\n    });\n  }\n  patch(_0, _1) {\n    return __async(this, arguments, function* (path, entity, options = {}) {\n      const requestOptions = __spreadValues({\n        method: \"PATCH\",\n        headers: this.headers,\n        body: JSON.stringify(entity)\n      }, options);\n      return this.fetchRequest(path, requestOptions);\n    });\n  }\n  delete(path, query) {\n    return __async(this, null, function* () {\n      const requestOptions = {\n        method: \"DELETE\",\n        headers: this.headers,\n        body: JSON.stringify(query)\n      };\n      return this.fetchRequest(path, requestOptions);\n    });\n  }\n};\n// Annotate the CommonJS export names for ESM import in node:\n0 && (0);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/resend/dist/index.js\n");

/***/ }),

/***/ "(rsc)/../node_modules/resend/dist/index.mjs":
/*!*********************************************!*\
  !*** ../node_modules/resend/dist/index.mjs ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Resend: () => (/* binding */ Resend)\n/* harmony export */ });\nvar __defProp = Object.defineProperty;\nvar __defProps = Object.defineProperties;\nvar __getOwnPropDescs = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));\nvar __async = (__this, __arguments, generator) => {\n  return new Promise((resolve, reject) => {\n    var fulfilled = (value) => {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    };\n    var rejected = (value) => {\n      try {\n        step(generator.throw(value));\n      } catch (e) {\n        reject(e);\n      }\n    };\n    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);\n    step((generator = generator.apply(__this, __arguments)).next());\n  });\n};\n\n// package.json\nvar version = \"4.6.0\";\n\n// src/api-keys/api-keys.ts\nvar ApiKeys = class {\n  constructor(resend) {\n    this.resend = resend;\n  }\n  create(_0) {\n    return __async(this, arguments, function* (payload, options = {}) {\n      const data = yield this.resend.post(\n        \"/api-keys\",\n        payload,\n        options\n      );\n      return data;\n    });\n  }\n  list() {\n    return __async(this, null, function* () {\n      const data = yield this.resend.get(\"/api-keys\");\n      return data;\n    });\n  }\n  remove(id) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.delete(\n        `/api-keys/${id}`\n      );\n      return data;\n    });\n  }\n};\n\n// src/audiences/audiences.ts\nvar Audiences = class {\n  constructor(resend) {\n    this.resend = resend;\n  }\n  create(_0) {\n    return __async(this, arguments, function* (payload, options = {}) {\n      const data = yield this.resend.post(\n        \"/audiences\",\n        payload,\n        options\n      );\n      return data;\n    });\n  }\n  list() {\n    return __async(this, null, function* () {\n      const data = yield this.resend.get(\"/audiences\");\n      return data;\n    });\n  }\n  get(id) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.get(\n        `/audiences/${id}`\n      );\n      return data;\n    });\n  }\n  remove(id) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.delete(\n        `/audiences/${id}`\n      );\n      return data;\n    });\n  }\n};\n\n// src/common/utils/parse-email-to-api-options.ts\nfunction parseEmailToApiOptions(email) {\n  return {\n    attachments: email.attachments,\n    bcc: email.bcc,\n    cc: email.cc,\n    from: email.from,\n    headers: email.headers,\n    html: email.html,\n    reply_to: email.replyTo,\n    scheduled_at: email.scheduledAt,\n    subject: email.subject,\n    tags: email.tags,\n    text: email.text,\n    to: email.to\n  };\n}\n\n// src/batch/batch.ts\nvar Batch = class {\n  constructor(resend) {\n    this.resend = resend;\n  }\n  send(_0) {\n    return __async(this, arguments, function* (payload, options = {}) {\n      return this.create(payload, options);\n    });\n  }\n  create(_0) {\n    return __async(this, arguments, function* (payload, options = {}) {\n      const emails = [];\n      for (const email of payload) {\n        if (email.react) {\n          if (!this.renderAsync) {\n            try {\n              const { renderAsync } = yield Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/next\"), __webpack_require__.e(\"vendor-chunks/entities\"), __webpack_require__.e(\"vendor-chunks/domutils\"), __webpack_require__.e(\"vendor-chunks/htmlparser2\"), __webpack_require__.e(\"vendor-chunks/peberminta\"), __webpack_require__.e(\"vendor-chunks/domhandler\"), __webpack_require__.e(\"vendor-chunks/dom-serializer\"), __webpack_require__.e(\"vendor-chunks/selderee\"), __webpack_require__.e(\"vendor-chunks/parseley\"), __webpack_require__.e(\"vendor-chunks/leac\"), __webpack_require__.e(\"vendor-chunks/html-to-text\"), __webpack_require__.e(\"vendor-chunks/domelementtype\"), __webpack_require__.e(\"vendor-chunks/@selderee\"), __webpack_require__.e(\"vendor-chunks/@react-email\"), __webpack_require__.e(\"vendor-chunks/deepmerge\")]).then(__webpack_require__.bind(__webpack_require__, /*! @react-email/render */ \"(rsc)/../node_modules/@react-email/render/dist/node/index.mjs\"));\n              this.renderAsync = renderAsync;\n            } catch (error) {\n              throw new Error(\n                \"Failed to render React component. Make sure to install `@react-email/render`\"\n              );\n            }\n          }\n          email.html = yield this.renderAsync(email.react);\n          email.react = void 0;\n        }\n        emails.push(parseEmailToApiOptions(email));\n      }\n      const data = yield this.resend.post(\n        \"/emails/batch\",\n        emails,\n        options\n      );\n      return data;\n    });\n  }\n};\n\n// src/broadcasts/broadcasts.ts\nvar Broadcasts = class {\n  constructor(resend) {\n    this.resend = resend;\n  }\n  create(_0) {\n    return __async(this, arguments, function* (payload, options = {}) {\n      if (payload.react) {\n        if (!this.renderAsync) {\n          try {\n            const { renderAsync } = yield Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/next\"), __webpack_require__.e(\"vendor-chunks/entities\"), __webpack_require__.e(\"vendor-chunks/domutils\"), __webpack_require__.e(\"vendor-chunks/htmlparser2\"), __webpack_require__.e(\"vendor-chunks/peberminta\"), __webpack_require__.e(\"vendor-chunks/domhandler\"), __webpack_require__.e(\"vendor-chunks/dom-serializer\"), __webpack_require__.e(\"vendor-chunks/selderee\"), __webpack_require__.e(\"vendor-chunks/parseley\"), __webpack_require__.e(\"vendor-chunks/leac\"), __webpack_require__.e(\"vendor-chunks/html-to-text\"), __webpack_require__.e(\"vendor-chunks/domelementtype\"), __webpack_require__.e(\"vendor-chunks/@selderee\"), __webpack_require__.e(\"vendor-chunks/@react-email\"), __webpack_require__.e(\"vendor-chunks/deepmerge\")]).then(__webpack_require__.bind(__webpack_require__, /*! @react-email/render */ \"(rsc)/../node_modules/@react-email/render/dist/node/index.mjs\"));\n            this.renderAsync = renderAsync;\n          } catch (error) {\n            throw new Error(\n              \"Failed to render React component. Make sure to install `@react-email/render`\"\n            );\n          }\n        }\n        payload.html = yield this.renderAsync(\n          payload.react\n        );\n      }\n      const data = yield this.resend.post(\n        \"/broadcasts\",\n        {\n          name: payload.name,\n          audience_id: payload.audienceId,\n          preview_text: payload.previewText,\n          from: payload.from,\n          html: payload.html,\n          reply_to: payload.replyTo,\n          subject: payload.subject,\n          text: payload.text\n        },\n        options\n      );\n      return data;\n    });\n  }\n  send(id, payload) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.post(\n        `/broadcasts/${id}/send`,\n        { scheduled_at: payload == null ? void 0 : payload.scheduledAt }\n      );\n      return data;\n    });\n  }\n  list() {\n    return __async(this, null, function* () {\n      const data = yield this.resend.get(\"/broadcasts\");\n      return data;\n    });\n  }\n  get(id) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.get(\n        `/broadcasts/${id}`\n      );\n      return data;\n    });\n  }\n  remove(id) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.delete(\n        `/broadcasts/${id}`\n      );\n      return data;\n    });\n  }\n  update(id, payload) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.patch(\n        `/broadcasts/${id}`,\n        {\n          name: payload.name,\n          audience_id: payload.audienceId,\n          from: payload.from,\n          html: payload.html,\n          text: payload.text,\n          subject: payload.subject,\n          reply_to: payload.replyTo,\n          preview_text: payload.previewText\n        }\n      );\n      return data;\n    });\n  }\n};\n\n// src/contacts/contacts.ts\nvar Contacts = class {\n  constructor(resend) {\n    this.resend = resend;\n  }\n  create(_0) {\n    return __async(this, arguments, function* (payload, options = {}) {\n      const data = yield this.resend.post(\n        `/audiences/${payload.audienceId}/contacts`,\n        {\n          unsubscribed: payload.unsubscribed,\n          email: payload.email,\n          first_name: payload.firstName,\n          last_name: payload.lastName\n        },\n        options\n      );\n      return data;\n    });\n  }\n  list(options) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.get(\n        `/audiences/${options.audienceId}/contacts`\n      );\n      return data;\n    });\n  }\n  get(options) {\n    return __async(this, null, function* () {\n      if (!options.id && !options.email) {\n        return {\n          data: null,\n          error: {\n            message: \"Missing `id` or `email` field.\",\n            name: \"missing_required_field\"\n          }\n        };\n      }\n      const data = yield this.resend.get(\n        `/audiences/${options.audienceId}/contacts/${(options == null ? void 0 : options.email) ? options == null ? void 0 : options.email : options == null ? void 0 : options.id}`\n      );\n      return data;\n    });\n  }\n  update(payload) {\n    return __async(this, null, function* () {\n      if (!payload.id && !payload.email) {\n        return {\n          data: null,\n          error: {\n            message: \"Missing `id` or `email` field.\",\n            name: \"missing_required_field\"\n          }\n        };\n      }\n      const data = yield this.resend.patch(\n        `/audiences/${payload.audienceId}/contacts/${(payload == null ? void 0 : payload.email) ? payload == null ? void 0 : payload.email : payload == null ? void 0 : payload.id}`,\n        {\n          unsubscribed: payload.unsubscribed,\n          first_name: payload.firstName,\n          last_name: payload.lastName\n        }\n      );\n      return data;\n    });\n  }\n  remove(payload) {\n    return __async(this, null, function* () {\n      if (!payload.id && !payload.email) {\n        return {\n          data: null,\n          error: {\n            message: \"Missing `id` or `email` field.\",\n            name: \"missing_required_field\"\n          }\n        };\n      }\n      const data = yield this.resend.delete(\n        `/audiences/${payload.audienceId}/contacts/${(payload == null ? void 0 : payload.email) ? payload == null ? void 0 : payload.email : payload == null ? void 0 : payload.id}`\n      );\n      return data;\n    });\n  }\n};\n\n// src/common/utils/parse-domain-to-api-options.ts\nfunction parseDomainToApiOptions(domain) {\n  return {\n    name: domain.name,\n    region: domain.region,\n    custom_return_path: domain.customReturnPath\n  };\n}\n\n// src/domains/domains.ts\nvar Domains = class {\n  constructor(resend) {\n    this.resend = resend;\n  }\n  create(_0) {\n    return __async(this, arguments, function* (payload, options = {}) {\n      const data = yield this.resend.post(\n        \"/domains\",\n        parseDomainToApiOptions(payload),\n        options\n      );\n      return data;\n    });\n  }\n  list() {\n    return __async(this, null, function* () {\n      const data = yield this.resend.get(\"/domains\");\n      return data;\n    });\n  }\n  get(id) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.get(\n        `/domains/${id}`\n      );\n      return data;\n    });\n  }\n  update(payload) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.patch(\n        `/domains/${payload.id}`,\n        {\n          click_tracking: payload.clickTracking,\n          open_tracking: payload.openTracking,\n          tls: payload.tls\n        }\n      );\n      return data;\n    });\n  }\n  remove(id) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.delete(\n        `/domains/${id}`\n      );\n      return data;\n    });\n  }\n  verify(id) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.post(\n        `/domains/${id}/verify`\n      );\n      return data;\n    });\n  }\n};\n\n// src/emails/emails.ts\nvar Emails = class {\n  constructor(resend) {\n    this.resend = resend;\n  }\n  send(_0) {\n    return __async(this, arguments, function* (payload, options = {}) {\n      return this.create(payload, options);\n    });\n  }\n  create(_0) {\n    return __async(this, arguments, function* (payload, options = {}) {\n      if (payload.react) {\n        if (!this.renderAsync) {\n          try {\n            const { renderAsync } = yield Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/next\"), __webpack_require__.e(\"vendor-chunks/entities\"), __webpack_require__.e(\"vendor-chunks/domutils\"), __webpack_require__.e(\"vendor-chunks/htmlparser2\"), __webpack_require__.e(\"vendor-chunks/peberminta\"), __webpack_require__.e(\"vendor-chunks/domhandler\"), __webpack_require__.e(\"vendor-chunks/dom-serializer\"), __webpack_require__.e(\"vendor-chunks/selderee\"), __webpack_require__.e(\"vendor-chunks/parseley\"), __webpack_require__.e(\"vendor-chunks/leac\"), __webpack_require__.e(\"vendor-chunks/html-to-text\"), __webpack_require__.e(\"vendor-chunks/domelementtype\"), __webpack_require__.e(\"vendor-chunks/@selderee\"), __webpack_require__.e(\"vendor-chunks/@react-email\"), __webpack_require__.e(\"vendor-chunks/deepmerge\")]).then(__webpack_require__.bind(__webpack_require__, /*! @react-email/render */ \"(rsc)/../node_modules/@react-email/render/dist/node/index.mjs\"));\n            this.renderAsync = renderAsync;\n          } catch (error) {\n            throw new Error(\n              \"Failed to render React component. Make sure to install `@react-email/render`\"\n            );\n          }\n        }\n        payload.html = yield this.renderAsync(\n          payload.react\n        );\n      }\n      const data = yield this.resend.post(\n        \"/emails\",\n        parseEmailToApiOptions(payload),\n        options\n      );\n      return data;\n    });\n  }\n  get(id) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.get(\n        `/emails/${id}`\n      );\n      return data;\n    });\n  }\n  update(payload) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.patch(\n        `/emails/${payload.id}`,\n        {\n          scheduled_at: payload.scheduledAt\n        }\n      );\n      return data;\n    });\n  }\n  cancel(id) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.post(\n        `/emails/${id}/cancel`\n      );\n      return data;\n    });\n  }\n};\n\n// src/resend.ts\nvar defaultBaseUrl = \"https://api.resend.com\";\nvar defaultUserAgent = `resend-node:${version}`;\nvar baseUrl = typeof process !== \"undefined\" && process.env ? process.env.RESEND_BASE_URL || defaultBaseUrl : defaultBaseUrl;\nvar userAgent = typeof process !== \"undefined\" && process.env ? process.env.RESEND_USER_AGENT || defaultUserAgent : defaultUserAgent;\nvar Resend = class {\n  constructor(key) {\n    this.key = key;\n    this.apiKeys = new ApiKeys(this);\n    this.audiences = new Audiences(this);\n    this.batch = new Batch(this);\n    this.broadcasts = new Broadcasts(this);\n    this.contacts = new Contacts(this);\n    this.domains = new Domains(this);\n    this.emails = new Emails(this);\n    if (!key) {\n      if (typeof process !== \"undefined\" && process.env) {\n        this.key = process.env.RESEND_API_KEY;\n      }\n      if (!this.key) {\n        throw new Error(\n          'Missing API key. Pass it to the constructor `new Resend(\"re_123\")`'\n        );\n      }\n    }\n    this.headers = new Headers({\n      Authorization: `Bearer ${this.key}`,\n      \"User-Agent\": userAgent,\n      \"Content-Type\": \"application/json\"\n    });\n  }\n  fetchRequest(_0) {\n    return __async(this, arguments, function* (path, options = {}) {\n      try {\n        const response = yield fetch(`${baseUrl}${path}`, options);\n        if (!response.ok) {\n          try {\n            const rawError = yield response.text();\n            return { data: null, error: JSON.parse(rawError) };\n          } catch (err) {\n            if (err instanceof SyntaxError) {\n              return {\n                data: null,\n                error: {\n                  name: \"application_error\",\n                  message: \"Internal server error. We are unable to process your request right now, please try again later.\"\n                }\n              };\n            }\n            const error = {\n              message: response.statusText,\n              name: \"application_error\"\n            };\n            if (err instanceof Error) {\n              return { data: null, error: __spreadProps(__spreadValues({}, error), { message: err.message }) };\n            }\n            return { data: null, error };\n          }\n        }\n        const data = yield response.json();\n        return { data, error: null };\n      } catch (error) {\n        return {\n          data: null,\n          error: {\n            name: \"application_error\",\n            message: \"Unable to fetch data. The request could not be resolved.\"\n          }\n        };\n      }\n    });\n  }\n  post(_0, _1) {\n    return __async(this, arguments, function* (path, entity, options = {}) {\n      const headers = new Headers(this.headers);\n      if (options.idempotencyKey) {\n        headers.set(\"Idempotency-Key\", options.idempotencyKey);\n      }\n      const requestOptions = __spreadValues({\n        method: \"POST\",\n        headers,\n        body: JSON.stringify(entity)\n      }, options);\n      return this.fetchRequest(path, requestOptions);\n    });\n  }\n  get(_0) {\n    return __async(this, arguments, function* (path, options = {}) {\n      const requestOptions = __spreadValues({\n        method: \"GET\",\n        headers: this.headers\n      }, options);\n      return this.fetchRequest(path, requestOptions);\n    });\n  }\n  put(_0, _1) {\n    return __async(this, arguments, function* (path, entity, options = {}) {\n      const requestOptions = __spreadValues({\n        method: \"PUT\",\n        headers: this.headers,\n        body: JSON.stringify(entity)\n      }, options);\n      return this.fetchRequest(path, requestOptions);\n    });\n  }\n  patch(_0, _1) {\n    return __async(this, arguments, function* (path, entity, options = {}) {\n      const requestOptions = __spreadValues({\n        method: \"PATCH\",\n        headers: this.headers,\n        body: JSON.stringify(entity)\n      }, options);\n      return this.fetchRequest(path, requestOptions);\n    });\n  }\n  delete(path, query) {\n    return __async(this, null, function* () {\n      const requestOptions = {\n        method: \"DELETE\",\n        headers: this.headers,\n        body: JSON.stringify(query)\n      };\n      return this.fetchRequest(path, requestOptions);\n    });\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/resend/dist/index.mjs\n");

/***/ })

};
;