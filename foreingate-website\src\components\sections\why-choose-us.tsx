'use client'

import { motion } from 'framer-motion'
import { useInView } from 'react-intersection-observer'
import { 
  Shield, 
  Clock, 
  Users, 
  Award, 
  HeadphonesIcon, 
  Globe, 
  CheckCircle, 
  TrendingUp 
} from 'lucide-react'

const reasons = [
  {
    icon: Shield,
    title: 'Trusted Expertise',
    description: 'Nearly a decade of experience in international education consulting with a proven track record of success.',
    stats: '98% Success Rate'
  },
  {
    icon: Users,
    title: 'Personalized Service',
    description: 'One-on-one guidance tailored to your unique academic goals, background, and aspirations.',
    stats: '1:1 Counseling'
  },
  {
    icon: Globe,
    title: 'Global Network',
    description: 'Strong partnerships with 25+ universities and connections across 50+ countries worldwide.',
    stats: '25+ Partner Universities'
  },
  {
    icon: HeadphonesIcon,
    title: '24/7 Support',
    description: 'Round-the-clock assistance throughout your entire educational journey, from application to graduation.',
    stats: 'Always Available'
  },
  {
    icon: Award,
    title: 'Scholarship Assistance',
    description: 'Expert guidance in securing scholarships and financial aid to make education more affordable.',
    stats: '$2M+ Scholarships Secured'
  },
  {
    icon: TrendingUp,
    title: 'Career Guidance',
    description: 'Comprehensive career counseling to help you choose programs that align with your professional goals.',
    stats: '90% Career Success'
  }
]

const achievements = [
  { number: '5,000+', label: 'Students Successfully Placed' },
  { number: '25+', label: 'Partner Universities' },
  { number: '50+', label: 'Countries Served' },
  { number: '98%', label: 'Visa Success Rate' },
  { number: '$2M+', label: 'Scholarships Secured' },
  { number: '24/7', label: 'Student Support' }
]

export function WhyChooseUsSection() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1
  })

  return (
    <section ref={ref} className="section-padding">
      <div className="container">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
          className="text-center max-w-3xl mx-auto mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Why Choose{' '}
            <span className="gradient-text">Foreingate Group</span>
          </h2>
          <p className="text-xl text-muted-foreground">
            Discover what sets us apart and makes us the preferred choice for thousands of students worldwide
          </p>
        </motion.div>

        {/* Main Reasons Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {reasons.map((reason, index) => (
            <motion.div
              key={reason.title}
              initial={{ opacity: 0, y: 30 }}
              animate={inView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              className="bg-background rounded-xl p-6 shadow-sm border hover:shadow-lg transition-all duration-300 hover:border-primary/20 group"
            >
              <div className="flex items-start space-x-4">
                <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0 group-hover:bg-primary/20 transition-colors">
                  <reason.icon className="w-6 h-6 text-primary" />
                </div>
                <div className="flex-1">
                  <h3 className="text-lg font-semibold mb-2 group-hover:text-primary transition-colors">
                    {reason.title}
                  </h3>
                  <p className="text-muted-foreground text-sm leading-relaxed mb-3">
                    {reason.description}
                  </p>
                  <div className="inline-flex items-center text-xs font-medium text-primary bg-primary/10 px-2 py-1 rounded-full">
                    <CheckCircle className="w-3 h-3 mr-1" />
                    {reason.stats}
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Achievements Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="bg-gradient-to-r from-primary/5 to-secondary/5 rounded-2xl p-8 md:p-12 mb-16"
        >
          <div className="text-center mb-8">
            <h3 className="text-2xl md:text-3xl font-bold mb-4">Our Achievements Speak for Themselves</h3>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Numbers that reflect our commitment to excellence and student success
            </p>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
            {achievements.map((achievement, index) => (
              <motion.div
                key={achievement.label}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={inView ? { opacity: 1, scale: 1 } : {}}
                transition={{ duration: 0.5, delay: 0.8 + index * 0.1 }}
                className="text-center"
              >
                <div className="text-2xl md:text-3xl font-bold text-primary mb-2">
                  {achievement.number}
                </div>
                <div className="text-sm text-muted-foreground">
                  {achievement.label}
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Process Overview */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 0.8 }}
          className="text-center"
        >
          <h3 className="text-2xl md:text-3xl font-bold mb-6">Our Proven Process</h3>
          <p className="text-muted-foreground max-w-3xl mx-auto mb-8">
            We've refined our approach over years of experience to ensure the smoothest possible journey for our students
          </p>
          
          <div className="grid md:grid-cols-4 gap-6">
            {[
              { step: '01', title: 'Consultation', desc: 'Free initial assessment of your goals and profile' },
              { step: '02', title: 'Planning', desc: 'Customized roadmap with university and program selection' },
              { step: '03', title: 'Application', desc: 'Complete application support and document preparation' },
              { step: '04', title: 'Success', desc: 'Visa assistance and pre-departure orientation' }
            ].map((process, index) => (
              <motion.div
                key={process.step}
                initial={{ opacity: 0, y: 20 }}
                animate={inView ? { opacity: 1, y: 0 } : {}}
                transition={{ duration: 0.5, delay: 1 + index * 0.1 }}
                className="relative"
              >
                <div className="w-16 h-16 bg-primary text-primary-foreground rounded-full flex items-center justify-center font-bold text-lg mx-auto mb-4">
                  {process.step}
                </div>
                <h4 className="font-semibold mb-2">{process.title}</h4>
                <p className="text-sm text-muted-foreground">{process.desc}</p>
                
                {/* Connector Line */}
                {index < 3 && (
                  <div className="hidden md:block absolute top-8 left-full w-full h-0.5 bg-gradient-to-r from-primary to-transparent -translate-y-1/2" />
                )}
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  )
}
