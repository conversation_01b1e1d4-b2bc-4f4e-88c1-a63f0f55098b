/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "m12 17-5-5 5-5", key: "1s3y5u" }],
  ["path", { d: "M22 18v-2a4 4 0 0 0-4-4H7", key: "1fcyog" }],
  ["path", { d: "m7 17-5-5 5-5", key: "1ed8i2" }]
];
const ReplyAll = createLucideIcon("reply-all", __iconNode);

export { __iconNode, ReplyAll as default };
//# sourceMappingURL=reply-all.js.map
