/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/applications/route";
exports.ids = ["app/api/applications/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fapplications%2Froute&page=%2Fapi%2Fapplications%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fapplications%2Froute.ts&appDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fapplications%2Froute&page=%2Fapi%2Fapplications%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fapplications%2Froute.ts&appDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Nidhal_Documents_augment_projects_foreingate_groupe_foreingate_website_src_app_api_applications_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/applications/route.ts */ \"(rsc)/./src/app/api/applications/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/applications/route\",\n        pathname: \"/api/applications\",\n        filename: \"route\",\n        bundlePath: \"app/api/applications/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\api\\\\applications\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Nidhal_Documents_augment_projects_foreingate_groupe_foreingate_website_src_app_api_applications_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fapplications%2Froute&page=%2Fapi%2Fapplications%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fapplications%2Froute.ts&appDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/applications/route.ts":
/*!*******************************************!*\
  !*** ./src/app/api/applications/route.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fs/promises */ \"fs/promises\");\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fs_promises__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nconst DATA_DIR = path__WEBPACK_IMPORTED_MODULE_3___default().join(process.cwd(), 'data');\nconst APPLICATIONS_FILE = path__WEBPACK_IMPORTED_MODULE_3___default().join(DATA_DIR, 'applications.json');\n// Ensure data directory exists\nasync function ensureDataDir() {\n    if (!(0,fs__WEBPACK_IMPORTED_MODULE_2__.existsSync)(DATA_DIR)) {\n        await (0,fs_promises__WEBPACK_IMPORTED_MODULE_1__.mkdir)(DATA_DIR, {\n            recursive: true\n        });\n    }\n}\n// Read applications from file\nasync function readApplications() {\n    try {\n        await ensureDataDir();\n        if (!(0,fs__WEBPACK_IMPORTED_MODULE_2__.existsSync)(APPLICATIONS_FILE)) {\n            return [];\n        }\n        const data = await (0,fs_promises__WEBPACK_IMPORTED_MODULE_1__.readFile)(APPLICATIONS_FILE, 'utf-8');\n        return JSON.parse(data);\n    } catch (error) {\n        console.error('Error reading applications:', error);\n        return [];\n    }\n}\n// Write applications to file\nasync function writeApplications(applications) {\n    try {\n        await ensureDataDir();\n        await (0,fs_promises__WEBPACK_IMPORTED_MODULE_1__.writeFile)(APPLICATIONS_FILE, JSON.stringify(applications, null, 2));\n    } catch (error) {\n        console.error('Error writing applications:', error);\n        throw error;\n    }\n}\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { firstName, lastName, email, phone, dateOfBirth, nationality, passportNumber, highSchoolName, graduationYear, gpa, englishProficiency, preferredUniversity, firstChoiceProgram, secondChoiceProgram, intakeYear, intakeSemester } = body;\n        // Validate required fields\n        if (!firstName || !lastName || !email) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Missing required fields: firstName, lastName, email'\n            }, {\n                status: 400\n            });\n        }\n        // Generate unique application ID\n        const applicationId = `FG-${Date.now()}-${Math.random().toString(36).substr(2, 9).toUpperCase()}`;\n        // Create application object\n        const application = {\n            id: Math.random().toString(36).substr(2, 9),\n            applicationId,\n            createdAt: new Date().toISOString(),\n            updatedAt: new Date().toISOString(),\n            status: 'PENDING',\n            // Personal Information\n            firstName,\n            lastName,\n            email,\n            phone: phone || null,\n            dateOfBirth: dateOfBirth || null,\n            nationality: nationality || null,\n            passportNumber: passportNumber || null,\n            // Academic Background\n            highSchoolName: highSchoolName || null,\n            graduationYear: graduationYear || null,\n            gpa: gpa || null,\n            englishProficiency: englishProficiency || null,\n            // Program Selection\n            preferredUniversity: preferredUniversity || null,\n            firstChoiceProgram: firstChoiceProgram || null,\n            secondChoiceProgram: secondChoiceProgram || null,\n            intakeYear: intakeYear || null,\n            intakeSemester: intakeSemester || null,\n            // Additional fields\n            assignedCounselor: null,\n            notes: null\n        };\n        // Read existing applications\n        const applications = await readApplications();\n        // Add new application\n        applications.push(application);\n        // Write back to file\n        await writeApplications(applications);\n        // Send confirmation email (if email service is configured)\n        try {\n            if (process.env.RESEND_API_KEY) {\n                const { Resend } = __webpack_require__(/*! resend */ \"(rsc)/../node_modules/resend/dist/index.js\");\n                const resend = new Resend(process.env.RESEND_API_KEY);\n                const confirmationEmailHtml = `\n          <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n            <div style=\"background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center;\">\n              <h1 style=\"color: white; margin: 0;\">Application Submitted Successfully!</h1>\n              <p style=\"color: white; opacity: 0.9; margin: 10px 0 0 0;\">Foreingate Group</p>\n            </div>\n            \n            <div style=\"padding: 30px; background: #f8f9fa;\">\n              <p style=\"color: #333; font-size: 16px; line-height: 1.6;\">Dear ${firstName} ${lastName},</p>\n              \n              <p style=\"color: #555; line-height: 1.6;\">\n                Thank you for submitting your university application through Foreingate Group! We have successfully received your application and our admissions team will begin processing it immediately.\n              </p>\n\n              <div style=\"background: #e8f5e8; padding: 20px; border-radius: 8px; border-left: 4px solid #4caf50; margin: 20px 0;\">\n                <h3 style=\"color: #2e7d32; margin: 0 0 10px 0;\">📋 Application Details</h3>\n                <p style=\"margin: 5px 0; color: #555;\"><strong>Application ID:</strong> ${applicationId}</p>\n                <p style=\"margin: 5px 0; color: #555;\"><strong>Preferred University:</strong> ${preferredUniversity || 'Not specified'}</p>\n                <p style=\"margin: 5px 0; color: #555;\"><strong>First Choice Program:</strong> ${firstChoiceProgram || 'Not specified'}</p>\n                <p style=\"margin: 5px 0; color: #555;\"><strong>Intake:</strong> ${intakeSemester || 'Not specified'} ${intakeYear || ''}</p>\n                <p style=\"margin: 5px 0; color: #555;\"><strong>Submitted:</strong> ${new Date().toLocaleString()}</p>\n              </div>\n\n              <div style=\"background: #fff3e0; padding: 20px; border-radius: 8px; border-left: 4px solid #ff9800; margin: 20px 0;\">\n                <h3 style=\"color: #f57c00; margin: 0 0 10px 0;\">📋 Next Steps</h3>\n                <ol style=\"color: #555; margin: 0; padding-left: 20px;\">\n                  <li>Our admissions team will review your application within 24-48 hours</li>\n                  <li>A dedicated counselor will be assigned to your case</li>\n                  <li>You'll receive an email with required documents list</li>\n                  <li>We'll guide you through the entire admission process</li>\n                  <li>Visa support and accommodation assistance will be provided</li>\n                </ol>\n              </div>\n\n              <div style=\"background: #e3f2fd; padding: 20px; border-radius: 8px; border-left: 4px solid #2196f3; margin: 20px 0;\">\n                <h3 style=\"color: #1976d2; margin: 0 0 10px 0;\">📞 Contact Information</h3>\n                <p style=\"color: #555; margin: 0;\">\n                  <strong>Phone:</strong> +90 ************<br>\n                  <strong>WhatsApp:</strong> +90 ************<br>\n                  <strong>Email:</strong> <EMAIL>\n                </p>\n              </div>\n\n              <p style=\"color: #555; line-height: 1.6;\">\n                We're excited to help you achieve your educational goals and look forward to welcoming you to our community of successful students!\n              </p>\n\n              <p style=\"color: #555; line-height: 1.6;\">\n                Best regards,<br>\n                <strong>The Foreingate Group Admissions Team</strong><br>\n                <em>Your Gateway to International Education</em>\n              </p>\n            </div>\n\n            <div style=\"background: #333; padding: 20px; text-align: center;\">\n              <p style=\"color: #ccc; margin: 0; font-size: 14px;\">\n                © 2024 Foreingate Group. All rights reserved.<br>\n                Please save your Application ID: <strong>${applicationId}</strong>\n              </p>\n            </div>\n          </div>\n        `;\n                await resend.emails.send({\n                    from: 'Foreingate Admissions <<EMAIL>>',\n                    to: [\n                        email\n                    ],\n                    subject: `Application Received - ${applicationId}`,\n                    html: confirmationEmailHtml\n                });\n                // Send notification to admin\n                await resend.emails.send({\n                    from: 'Foreingate System <<EMAIL>>',\n                    to: [\n                        '<EMAIL>'\n                    ],\n                    subject: `New Application: ${firstName} ${lastName} - ${applicationId}`,\n                    html: `\n            <h2>New University Application Received</h2>\n            <p><strong>Application ID:</strong> ${applicationId}</p>\n            <p><strong>Student:</strong> ${firstName} ${lastName}</p>\n            <p><strong>Email:</strong> ${email}</p>\n            <p><strong>Phone:</strong> ${phone || 'Not provided'}</p>\n            <p><strong>Preferred University:</strong> ${preferredUniversity || 'Not specified'}</p>\n            <p><strong>First Choice Program:</strong> ${firstChoiceProgram || 'Not specified'}</p>\n            <p><strong>Intake:</strong> ${intakeSemester || 'Not specified'} ${intakeYear || ''}</p>\n            <p><strong>Submitted:</strong> ${new Date().toLocaleString()}</p>\n            <hr>\n            <p>Please assign a counselor and begin the application review process.</p>\n          `\n                });\n            }\n        } catch (emailError) {\n            console.error('Email sending failed:', emailError);\n        // Don't fail the application if email fails\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'Application submitted successfully!',\n            applicationId,\n            status: 'PENDING'\n        });\n    } catch (error) {\n        console.error('Application submission error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to submit application. Please try again.'\n        }, {\n            status: 500\n        });\n    }\n}\n// GET endpoint to retrieve applications (for admin use)\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const status = searchParams.get('status');\n        const search = searchParams.get('search');\n        const page = parseInt(searchParams.get('page') || '1');\n        const limit = parseInt(searchParams.get('limit') || '10');\n        const sortBy = searchParams.get('sortBy') || 'createdAt';\n        const sortOrder = searchParams.get('sortOrder') || 'desc';\n        let applications = await readApplications();\n        // Apply filters\n        if (status && status !== 'all') {\n            applications = applications.filter((app)=>app.status.toLowerCase() === status.toLowerCase());\n        }\n        if (search) {\n            applications = applications.filter((app)=>app.firstName.toLowerCase().includes(search.toLowerCase()) || app.lastName.toLowerCase().includes(search.toLowerCase()) || app.email.toLowerCase().includes(search.toLowerCase()) || app.applicationId.toLowerCase().includes(search.toLowerCase()) || app.preferredUniversity && app.preferredUniversity.toLowerCase().includes(search.toLowerCase()) || app.firstChoiceProgram && app.firstChoiceProgram.toLowerCase().includes(search.toLowerCase()));\n        }\n        // Sort applications\n        applications.sort((a, b)=>{\n            let aValue = a[sortBy];\n            let bValue = b[sortBy];\n            if (sortBy === 'createdAt' || sortBy === 'updatedAt') {\n                aValue = new Date(aValue).getTime();\n                bValue = new Date(bValue).getTime();\n            }\n            if (sortOrder === 'desc') {\n                return bValue > aValue ? 1 : -1;\n            } else {\n                return aValue > bValue ? 1 : -1;\n            }\n        });\n        // Pagination\n        const total = applications.length;\n        const startIndex = (page - 1) * limit;\n        const endIndex = startIndex + limit;\n        const paginatedApplications = applications.slice(startIndex, endIndex);\n        // Calculate statistics\n        const stats = {\n            total: applications.length,\n            pending: applications.filter((app)=>app.status === 'PENDING').length,\n            underReview: applications.filter((app)=>app.status === 'UNDER_REVIEW').length,\n            approved: applications.filter((app)=>app.status === 'APPROVED').length,\n            rejected: applications.filter((app)=>app.status === 'REJECTED').length,\n            enrolled: applications.filter((app)=>app.status === 'ENROLLED').length\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: paginatedApplications,\n            pagination: {\n                page,\n                limit,\n                total,\n                totalPages: Math.ceil(total / limit),\n                hasNext: endIndex < total,\n                hasPrev: page > 1\n            },\n            stats\n        });\n    } catch (error) {\n        console.error('Error fetching applications:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to fetch applications'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/applications/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "async_hooks":
/*!******************************!*\
  !*** external "async_hooks" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("async_hooks");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "fs/promises":
/*!******************************!*\
  !*** external "fs/promises" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("fs/promises");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "prettier/plugins/html":
/*!****************************************!*\
  !*** external "prettier/plugins/html" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = import("prettier/plugins/html");;

/***/ }),

/***/ "prettier/standalone":
/*!**************************************!*\
  !*** external "prettier/standalone" ***!
  \**************************************/
/***/ ((module) => {

"use strict";
module.exports = import("prettier/standalone");;

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/resend"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fapplications%2Froute&page=%2Fapi%2Fapplications%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fapplications%2Froute.ts&appDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();