/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/applications/route";
exports.ids = ["app/api/applications/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fapplications%2Froute&page=%2Fapi%2Fapplications%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fapplications%2Froute.ts&appDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fapplications%2Froute&page=%2Fapi%2Fapplications%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fapplications%2Froute.ts&appDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Nidhal_Documents_augment_projects_foreingate_groupe_foreingate_website_src_app_api_applications_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/applications/route.ts */ \"(rsc)/./src/app/api/applications/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/applications/route\",\n        pathname: \"/api/applications\",\n        filename: \"route\",\n        bundlePath: \"app/api/applications/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\api\\\\applications\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Nidhal_Documents_augment_projects_foreingate_groupe_foreingate_website_src_app_api_applications_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fapplications%2Froute&page=%2Fapi%2Fapplications%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fapplications%2Froute.ts&appDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/applications/route.ts":
/*!*******************************************!*\
  !*** ./src/app/api/applications/route.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fs/promises */ \"fs/promises\");\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fs_promises__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nconst DATA_DIR = path__WEBPACK_IMPORTED_MODULE_3___default().join(process.cwd(), 'data');\nconst APPLICATIONS_FILE = path__WEBPACK_IMPORTED_MODULE_3___default().join(DATA_DIR, 'applications.json');\n// Ensure data directory exists\nasync function ensureDataDir() {\n    if (!(0,fs__WEBPACK_IMPORTED_MODULE_2__.existsSync)(DATA_DIR)) {\n        await (0,fs_promises__WEBPACK_IMPORTED_MODULE_1__.mkdir)(DATA_DIR, {\n            recursive: true\n        });\n    }\n}\n// Read applications from file\nasync function readApplications() {\n    try {\n        await ensureDataDir();\n        if (!(0,fs__WEBPACK_IMPORTED_MODULE_2__.existsSync)(APPLICATIONS_FILE)) {\n            return [];\n        }\n        const data = await (0,fs_promises__WEBPACK_IMPORTED_MODULE_1__.readFile)(APPLICATIONS_FILE, 'utf-8');\n        return JSON.parse(data);\n    } catch (error) {\n        console.error('Error reading applications:', error);\n        return [];\n    }\n}\n// Write applications to file\nasync function writeApplications(applications) {\n    try {\n        await ensureDataDir();\n        await (0,fs_promises__WEBPACK_IMPORTED_MODULE_1__.writeFile)(APPLICATIONS_FILE, JSON.stringify(applications, null, 2));\n    } catch (error) {\n        console.error('Error writing applications:', error);\n        throw error;\n    }\n}\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { firstName, lastName, email, phone, dateOfBirth, nationality, passportNumber, highSchoolName, graduationYear, gpa, englishProficiency, preferredUniversity, firstChoiceProgram, secondChoiceProgram, intakeYear, intakeSemester } = body;\n        // Validate required fields\n        if (!firstName || !lastName || !email) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Missing required fields: firstName, lastName, email'\n            }, {\n                status: 400\n            });\n        }\n        // Generate unique application ID\n        const applicationId = `FG-${Date.now()}-${Math.random().toString(36).substr(2, 9).toUpperCase()}`;\n        // Create application object\n        const application = {\n            id: Math.random().toString(36).substr(2, 9),\n            applicationId,\n            createdAt: new Date().toISOString(),\n            updatedAt: new Date().toISOString(),\n            status: 'PENDING',\n            // Personal Information\n            firstName,\n            lastName,\n            email,\n            phone: phone || null,\n            dateOfBirth: dateOfBirth || null,\n            nationality: nationality || null,\n            passportNumber: passportNumber || null,\n            // Academic Background\n            highSchoolName: highSchoolName || null,\n            graduationYear: graduationYear || null,\n            gpa: gpa || null,\n            englishProficiency: englishProficiency || null,\n            // Program Selection\n            preferredUniversity: preferredUniversity || null,\n            firstChoiceProgram: firstChoiceProgram || null,\n            secondChoiceProgram: secondChoiceProgram || null,\n            intakeYear: intakeYear || null,\n            intakeSemester: intakeSemester || null,\n            // Additional fields\n            assignedCounselor: null,\n            notes: null\n        };\n        // Read existing applications\n        const applications = await readApplications();\n        // Add new application\n        applications.push(application);\n        // Write back to file\n        await writeApplications(applications);\n        // Send confirmation email (if email service is configured)\n        try {\n            if (process.env.RESEND_API_KEY) {\n                const { Resend } = __webpack_require__(/*! resend */ \"(rsc)/../node_modules/resend/dist/index.js\");\n                const resend = new Resend(process.env.RESEND_API_KEY);\n                const confirmationEmailHtml = `\n          <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n            <div style=\"background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center;\">\n              <h1 style=\"color: white; margin: 0;\">Application Submitted Successfully!</h1>\n              <p style=\"color: white; opacity: 0.9; margin: 10px 0 0 0;\">Foreingate Group</p>\n            </div>\n            \n            <div style=\"padding: 30px; background: #f8f9fa;\">\n              <p style=\"color: #333; font-size: 16px; line-height: 1.6;\">Dear ${firstName} ${lastName},</p>\n              \n              <p style=\"color: #555; line-height: 1.6;\">\n                Thank you for submitting your university application through Foreingate Group! We have successfully received your application and our admissions team will begin processing it immediately.\n              </p>\n\n              <div style=\"background: #e8f5e8; padding: 20px; border-radius: 8px; border-left: 4px solid #4caf50; margin: 20px 0;\">\n                <h3 style=\"color: #2e7d32; margin: 0 0 10px 0;\">📋 Application Details</h3>\n                <p style=\"margin: 5px 0; color: #555;\"><strong>Application ID:</strong> ${applicationId}</p>\n                <p style=\"margin: 5px 0; color: #555;\"><strong>Preferred University:</strong> ${preferredUniversity || 'Not specified'}</p>\n                <p style=\"margin: 5px 0; color: #555;\"><strong>First Choice Program:</strong> ${firstChoiceProgram || 'Not specified'}</p>\n                <p style=\"margin: 5px 0; color: #555;\"><strong>Intake:</strong> ${intakeSemester || 'Not specified'} ${intakeYear || ''}</p>\n                <p style=\"margin: 5px 0; color: #555;\"><strong>Submitted:</strong> ${new Date().toLocaleString()}</p>\n              </div>\n\n              <div style=\"background: #fff3e0; padding: 20px; border-radius: 8px; border-left: 4px solid #ff9800; margin: 20px 0;\">\n                <h3 style=\"color: #f57c00; margin: 0 0 10px 0;\">📋 Next Steps</h3>\n                <ol style=\"color: #555; margin: 0; padding-left: 20px;\">\n                  <li>Our admissions team will review your application within 24-48 hours</li>\n                  <li>A dedicated counselor will be assigned to your case</li>\n                  <li>You'll receive an email with required documents list</li>\n                  <li>We'll guide you through the entire admission process</li>\n                  <li>Visa support and accommodation assistance will be provided</li>\n                </ol>\n              </div>\n\n              <div style=\"background: #e3f2fd; padding: 20px; border-radius: 8px; border-left: 4px solid #2196f3; margin: 20px 0;\">\n                <h3 style=\"color: #1976d2; margin: 0 0 10px 0;\">📞 Contact Information</h3>\n                <p style=\"color: #555; margin: 0;\">\n                  <strong>Phone:</strong> +90 ************<br>\n                  <strong>WhatsApp:</strong> +90 ************<br>\n                  <strong>Email:</strong> <EMAIL>\n                </p>\n              </div>\n\n              <p style=\"color: #555; line-height: 1.6;\">\n                We're excited to help you achieve your educational goals and look forward to welcoming you to our community of successful students!\n              </p>\n\n              <p style=\"color: #555; line-height: 1.6;\">\n                Best regards,<br>\n                <strong>The Foreingate Group Admissions Team</strong><br>\n                <em>Your Gateway to International Education</em>\n              </p>\n            </div>\n\n            <div style=\"background: #333; padding: 20px; text-align: center;\">\n              <p style=\"color: #ccc; margin: 0; font-size: 14px;\">\n                © 2024 Foreingate Group. All rights reserved.<br>\n                Please save your Application ID: <strong>${applicationId}</strong>\n              </p>\n            </div>\n          </div>\n        `;\n                await resend.emails.send({\n                    from: 'Foreingate Admissions <<EMAIL>>',\n                    to: [\n                        email\n                    ],\n                    subject: `Application Received - ${applicationId}`,\n                    html: confirmationEmailHtml\n                });\n                // Send notification to admin\n                await resend.emails.send({\n                    from: 'Foreingate System <<EMAIL>>',\n                    to: [\n                        '<EMAIL>'\n                    ],\n                    subject: `New Application: ${firstName} ${lastName} - ${applicationId}`,\n                    html: `\n            <h2>New University Application Received</h2>\n            <p><strong>Application ID:</strong> ${applicationId}</p>\n            <p><strong>Student:</strong> ${firstName} ${lastName}</p>\n            <p><strong>Email:</strong> ${email}</p>\n            <p><strong>Phone:</strong> ${phone || 'Not provided'}</p>\n            <p><strong>Preferred University:</strong> ${preferredUniversity || 'Not specified'}</p>\n            <p><strong>First Choice Program:</strong> ${firstChoiceProgram || 'Not specified'}</p>\n            <p><strong>Intake:</strong> ${intakeSemester || 'Not specified'} ${intakeYear || ''}</p>\n            <p><strong>Submitted:</strong> ${new Date().toLocaleString()}</p>\n            <hr>\n            <p>Please assign a counselor and begin the application review process.</p>\n          `\n                });\n            }\n        } catch (emailError) {\n            console.error('Email sending failed:', emailError);\n        // Don't fail the application if email fails\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'Application submitted successfully!',\n            applicationId,\n            status: 'PENDING'\n        });\n    } catch (error) {\n        console.error('Application submission error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to submit application. Please try again.'\n        }, {\n            status: 500\n        });\n    }\n}\n// GET endpoint to retrieve applications (for admin use)\nasync function GET(request) {\n    try {\n        const applications = await readApplications();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            applications,\n            count: applications.length\n        });\n    } catch (error) {\n        console.error('Error fetching applications:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to fetch applications'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/applications/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "async_hooks":
/*!******************************!*\
  !*** external "async_hooks" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("async_hooks");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "fs/promises":
/*!******************************!*\
  !*** external "fs/promises" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("fs/promises");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "prettier/plugins/html":
/*!****************************************!*\
  !*** external "prettier/plugins/html" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = import("prettier/plugins/html");;

/***/ }),

/***/ "prettier/standalone":
/*!**************************************!*\
  !*** external "prettier/standalone" ***!
  \**************************************/
/***/ ((module) => {

"use strict";
module.exports = import("prettier/standalone");;

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/resend"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fapplications%2Froute&page=%2Fapi%2Fapplications%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fapplications%2Froute.ts&appDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();