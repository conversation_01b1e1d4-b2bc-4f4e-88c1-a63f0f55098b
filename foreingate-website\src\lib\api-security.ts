import { NextRequest, NextResponse } from 'next/server'
import { RateLimiter, SecurityLogger, ValidationSchemas } from './security'

// Rate limiter instances
const globalRateLimiter = new RateLimiter(100, 60000) // 100 requests per minute
const apiRateLimiter = new RateLimiter(50, 60000) // 50 API requests per minute
const authRateLimiter = new RateLimiter(5, 300000) // 5 auth attempts per 5 minutes

/**
 * API Security Middleware
 */
export function withApiSecurity(handler: Function) {
  return async (request: NextRequest) => {
    const ip = getClientIP(request)
    const userAgent = request.headers.get('user-agent') || 'unknown'
    const path = request.nextUrl.pathname

    try {
      // 1. Rate Limiting
      if (!apiRateLimiter.isAllowed(ip)) {
        SecurityLogger.logSuspiciousActivity(ip, `Rate limit exceeded for ${path}`)
        return NextResponse.json(
          { error: 'Too many requests. Please try again later.' },
          { 
            status: 429,
            headers: {
              'Retry-After': '60',
              'X-RateLimit-Limit': '50',
              'X-RateLimit-Remaining': '0',
              'X-RateLimit-Reset': String(Date.now() + 60000)
            }
          }
        )
      }

      // 2. Method validation
      const allowedMethods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH']
      if (!allowedMethods.includes(request.method)) {
        SecurityLogger.logSuspiciousActivity(ip, `Invalid method ${request.method} for ${path}`)
        return NextResponse.json(
          { error: 'Method not allowed' },
          { status: 405 }
        )
      }

      // 3. Content-Type validation for POST/PUT requests
      if (['POST', 'PUT', 'PATCH'].includes(request.method)) {
        const contentType = request.headers.get('content-type')
        if (!contentType || !contentType.includes('application/json')) {
          return NextResponse.json(
            { error: 'Content-Type must be application/json' },
            { status: 400 }
          )
        }
      }

      // 4. Request size validation
      const contentLength = request.headers.get('content-length')
      if (contentLength && parseInt(contentLength) > 1024 * 1024) { // 1MB limit
        SecurityLogger.logSuspiciousActivity(ip, `Large request size: ${contentLength} bytes`)
        return NextResponse.json(
          { error: 'Request too large' },
          { status: 413 }
        )
      }

      // 5. Suspicious headers check
      const suspiciousHeaders = ['x-forwarded-host', 'x-real-ip']
      for (const header of suspiciousHeaders) {
        if (request.headers.get(header)) {
          SecurityLogger.logSuspiciousActivity(ip, `Suspicious header: ${header}`)
        }
      }

      // 6. Log API access
      SecurityLogger.logDataAccess(path, ip)

      // Call the actual handler
      const response = await handler(request)

      // Add security headers to response
      if (response instanceof NextResponse) {
        response.headers.set('X-Content-Type-Options', 'nosniff')
        response.headers.set('X-Frame-Options', 'DENY')
        response.headers.set('X-XSS-Protection', '1; mode=block')
        response.headers.set('Cache-Control', 'no-store, max-age=0')
        response.headers.set('X-RateLimit-Remaining', String(apiRateLimiter.getRemainingRequests(ip)))
      }

      return response

    } catch (error) {
      SecurityLogger.log('API_ERROR', { path, ip, error: String(error) }, 'error')
      return NextResponse.json(
        { error: 'Internal server error' },
        { status: 500 }
      )
    }
  }
}

/**
 * Admin API Security Middleware
 */
export function withAdminSecurity(handler: Function) {
  return withApiSecurity(async (request: NextRequest) => {
    const ip = getClientIP(request)
    
    // Additional admin-specific security checks
    
    // 1. Admin rate limiting (stricter)
    if (!authRateLimiter.isAllowed(ip)) {
      SecurityLogger.logSuspiciousActivity(ip, 'Admin rate limit exceeded')
      return NextResponse.json(
        { error: 'Too many admin requests. Please try again later.' },
        { status: 429 }
      )
    }

    // 2. Admin authentication check (basic implementation)
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      SecurityLogger.logFailedLogin(ip, request.headers.get('user-agent') || 'unknown')
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // 3. Log admin access
    SecurityLogger.log('ADMIN_ACCESS', { 
      path: request.nextUrl.pathname, 
      ip, 
      method: request.method 
    }, 'info')

    return handler(request)
  })
}

/**
 * Input Validation Middleware
 */
export function validateInput(schema: any) {
  return (handler: Function) => {
    return async (request: NextRequest) => {
      // Skip validation for GET requests
      if (!['POST', 'PUT', 'PATCH'].includes(request.method)) {
        return handler(request)
      }

      try {
        // Clone the request to avoid body consumption issues
        const clonedRequest = request.clone()
        const body = await clonedRequest.json()

        // Validate each field according to schema
        for (const [field, validator] of Object.entries(schema)) {
          const value = body[field]
          if (typeof validator === 'function' && !validator(value)) {
            return NextResponse.json(
              { error: `Invalid ${field}` },
              { status: 400 }
            )
          }
        }

        // Add validated body to request for later use
        ;(request as any).validatedBody = body

      } catch (error) {
        return NextResponse.json(
          { error: 'Invalid JSON' },
          { status: 400 }
        )
      }

      return handler(request)
    }
  }
}

/**
 * CORS Middleware
 */
export function withCORS(handler: Function, options: {
  origin?: string[]
  methods?: string[]
  credentials?: boolean
} = {}) {
  return async (request: NextRequest) => {
    const origin = request.headers.get('origin')
    const allowedOrigins = options.origin || [
      'https://localhost:3443',
      'https://foreingate.com',
      'https://www.foreingate.com'
    ]
    
    // Handle preflight requests
    if (request.method === 'OPTIONS') {
      const response = new NextResponse(null, { status: 200 })
      
      if (origin && allowedOrigins.includes(origin)) {
        response.headers.set('Access-Control-Allow-Origin', origin)
      }
      
      response.headers.set('Access-Control-Allow-Methods', options.methods?.join(', ') || 'GET, POST, PUT, DELETE')
      response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization')
      
      if (options.credentials) {
        response.headers.set('Access-Control-Allow-Credentials', 'true')
      }
      
      return response
    }
    
    const response = await handler(request)
    
    // Add CORS headers to actual response
    if (response instanceof NextResponse) {
      if (origin && allowedOrigins.includes(origin)) {
        response.headers.set('Access-Control-Allow-Origin', origin)
      }
      
      if (options.credentials) {
        response.headers.set('Access-Control-Allow-Credentials', 'true')
      }
    }
    
    return response
  }
}

/**
 * Get client IP address
 */
function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for')
  const realIP = request.headers.get('x-real-ip')
  const remoteAddr = request.ip
  
  if (forwarded) {
    return forwarded.split(',')[0].trim()
  }
  
  return realIP || remoteAddr || 'unknown'
}

/**
 * Sanitize API response
 */
export function sanitizeResponse(data: any): any {
  if (typeof data === 'string') {
    return data.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
  }
  
  if (Array.isArray(data)) {
    return data.map(sanitizeResponse)
  }
  
  if (typeof data === 'object' && data !== null) {
    const sanitized: any = {}
    for (const [key, value] of Object.entries(data)) {
      sanitized[key] = sanitizeResponse(value)
    }
    return sanitized
  }
  
  return data
}

/**
 * API Response wrapper with security
 */
export function secureResponse(data: any, status: number = 200) {
  const sanitizedData = sanitizeResponse(data)
  
  return NextResponse.json(sanitizedData, {
    status,
    headers: {
      'X-Content-Type-Options': 'nosniff',
      'X-Frame-Options': 'DENY',
      'Cache-Control': 'no-store, max-age=0',
      'X-XSS-Protection': '1; mode=block'
    }
  })
}
