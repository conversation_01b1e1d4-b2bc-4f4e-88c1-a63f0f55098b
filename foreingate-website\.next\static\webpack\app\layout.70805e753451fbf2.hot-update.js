/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5Cnavigation.tsx%22%2C%22ids%22%3A%5B%22Navigation%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5Ctranslation-provider.tsx%22%2C%22ids%22%3A%5B%22TranslationProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Cerror-boundary.tsx%22%2C%22ids%22%3A%5B%22ErrorBoundary%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Cnewsletter-signup.tsx%22%2C%22ids%22%3A%5B%22NewsletterSignup%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Csmart-chatbot.tsx%22%2C%22ids%22%3A%5B%22SmartChatbot%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Ctoast.tsx%22%2C%22ids%22%3A%5B%22ToastProvider%22%5D%7D&server=false!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5Cnavigation.tsx%22%2C%22ids%22%3A%5B%22Navigation%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5Ctranslation-provider.tsx%22%2C%22ids%22%3A%5B%22TranslationProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Cerror-boundary.tsx%22%2C%22ids%22%3A%5B%22ErrorBoundary%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Cnewsletter-signup.tsx%22%2C%22ids%22%3A%5B%22NewsletterSignup%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Csmart-chatbot.tsx%22%2C%22ids%22%3A%5B%22SmartChatbot%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Ctoast.tsx%22%2C%22ids%22%3A%5B%22ToastProvider%22%5D%7D&server=false! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(app-pages-browser)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(app-pages-browser)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/globals.css */ \"(app-pages-browser)/./src/app/globals.css\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/navigation.tsx */ \"(app-pages-browser)/./src/components/layout/navigation.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/theme-provider.tsx */ \"(app-pages-browser)/./src/components/providers/theme-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/translation-provider.tsx */ \"(app-pages-browser)/./src/components/providers/translation-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/error-boundary.tsx */ \"(app-pages-browser)/./src/components/ui/error-boundary.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/newsletter-signup.tsx */ \"(app-pages-browser)/./src/components/ui/newsletter-signup.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/smart-chatbot.tsx */ \"(app-pages-browser)/./src/components/ui/smart-chatbot.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/toast.tsx */ \"(app-pages-browser)/./src/components/ui/toast.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5Cnavigation.tsx%22%2C%22ids%22%3A%5B%22Navigation%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5Ctranslation-provider.tsx%22%2C%22ids%22%3A%5B%22TranslationProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Cerror-boundary.tsx%22%2C%22ids%22%3A%5B%22ErrorBoundary%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Cnewsletter-signup.tsx%22%2C%22ids%22%3A%5B%22NewsletterSignup%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Csmart-chatbot.tsx%22%2C%22ids%22%3A%5B%22SmartChatbot%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Ctoast.tsx%22%2C%22ids%22%3A%5B%22ToastProvider%22%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"a8ca45bd3798\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXE5pZGhhbFxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxmb3JlaW5nYXRlX2dyb3VwZVxcZm9yZWluZ2F0ZS13ZWJzaXRlXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJhOGNhNDViZDM3OThcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/providers/translation-provider.tsx":
/*!***********************************************************!*\
  !*** ./src/components/providers/translation-provider.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TranslationProvider: () => (/* binding */ TranslationProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_i18n__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/i18n */ \"(app-pages-browser)/./src/lib/i18n.ts\");\n/* harmony import */ var _hooks_use_translation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/use-translation */ \"(app-pages-browser)/./src/hooks/use-translation.ts\");\n/* __next_internal_client_entry_do_not_use__ TranslationProvider auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction TranslationProvider(param) {\n    let { children, initialLocale = _lib_i18n__WEBPACK_IMPORTED_MODULE_2__.defaultLocale } = param;\n    _s();\n    const [locale, setLocaleState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialLocale);\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Hydration fix\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TranslationProvider.useEffect\": ()=>{\n            setIsClient(true);\n            // Load locale from localStorage or browser preference\n            const savedLocale = localStorage.getItem('locale');\n            const browserLocale = navigator.language.split('-')[0];\n            let preferredLocale = _lib_i18n__WEBPACK_IMPORTED_MODULE_2__.defaultLocale;\n            if (savedLocale && (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.isValidLocale)(savedLocale)) {\n                preferredLocale = savedLocale;\n            } else if ((0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.isValidLocale)(browserLocale)) {\n                preferredLocale = browserLocale;\n            }\n            if (preferredLocale !== locale) {\n                setLocaleState(preferredLocale);\n            }\n        }\n    }[\"TranslationProvider.useEffect\"], [\n        locale\n    ]);\n    const setLocale = (newLocale)=>{\n        if ((0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.isValidLocale)(newLocale)) {\n            setLocaleState(newLocale);\n            localStorage.setItem('locale', newLocale);\n            // Update document attributes\n            document.documentElement.lang = newLocale;\n            document.documentElement.dir = (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.getLocaleDirection)(newLocale);\n            // Update page title if needed\n            const currentTitle = document.title;\n            if (currentTitle.includes('Foreingate')) {\n            // You can add locale-specific title updates here\n            }\n        }\n    };\n    // Update document attributes when locale changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TranslationProvider.useEffect\": ()=>{\n            if (isClient) {\n                document.documentElement.lang = locale;\n                document.documentElement.dir = (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.getLocaleDirection)(locale);\n            }\n        }\n    }[\"TranslationProvider.useEffect\"], [\n        locale,\n        isClient\n    ]);\n    const translations = (0,_hooks_use_translation__WEBPACK_IMPORTED_MODULE_3__.getTranslations)(locale);\n    const isRTL = (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.getLocaleDirection)(locale) === 'rtl';\n    const contextValue = {\n        locale,\n        setLocale,\n        t: translations,\n        isRTL\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_hooks_use_translation__WEBPACK_IMPORTED_MODULE_3__.TranslationContext.Provider, {\n        value: contextValue,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\providers\\\\translation-provider.tsx\",\n        lineNumber: 76,\n        columnNumber: 5\n    }, this);\n}\n_s(TranslationProvider, \"rXsnfMSoj5HNV0x7izpCSgvSq20=\");\n_c = TranslationProvider;\nvar _c;\n$RefreshReg$(_c, \"TranslationProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/providers/translation-provider.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/hooks/use-translation.ts":
/*!**************************************!*\
  !*** ./src/hooks/use-translation.ts ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TranslationContext: () => (/* binding */ TranslationContext),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   formatRelativeTime: () => (/* binding */ formatRelativeTime),\n/* harmony export */   getNestedTranslation: () => (/* binding */ getNestedTranslation),\n/* harmony export */   getTranslations: () => (/* binding */ getTranslations),\n/* harmony export */   pluralize: () => (/* binding */ pluralize),\n/* harmony export */   translateWithInterpolation: () => (/* binding */ translateWithInterpolation),\n/* harmony export */   useTranslation: () => (/* binding */ useTranslation)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_i18n__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/i18n */ \"(app-pages-browser)/./src/lib/i18n.ts\");\n/* harmony import */ var _locales_en__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/locales/en */ \"(app-pages-browser)/./src/locales/en.ts\");\n/* harmony import */ var _locales_tr__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/locales/tr */ \"(app-pages-browser)/./src/locales/tr.ts\");\n/* harmony import */ var _locales_ar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/locales/ar */ \"(app-pages-browser)/./src/locales/ar.ts\");\n/* __next_internal_client_entry_do_not_use__ TranslationContext,useTranslation,getTranslations,getNestedTranslation,translateWithInterpolation,pluralize,formatDate,formatNumber,formatCurrency,formatRelativeTime auto */ \n\n// Import all translations\n\n\n\n// Translation map\nconst translations = {\n    en: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    tr: _locales_tr__WEBPACK_IMPORTED_MODULE_3__.tr,\n    ar: _locales_ar__WEBPACK_IMPORTED_MODULE_4__.ar,\n    // Add more languages as they are created\n    fr: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    es: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    de: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ru: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    zh: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ja: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ko: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    pt: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    it: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    nl: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    sv: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    no: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    da: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    fi: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    pl: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    cs: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    hu: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ro: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    bg: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    hr: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    sk: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    sl: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    et: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    lv: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    lt: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    mt: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    cy: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ga: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    is: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    mk: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    sq: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    sr: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    bs: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    me: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    uk: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    be: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    kk: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ky: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    uz: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    tg: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    tm: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    mn: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ka: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    hy: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    az: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    fa: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ur: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    hi: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    bn: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ta: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    te: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ml: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    kn: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    gu: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    pa: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    or: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    as: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ne: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    si: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    my: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    th: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    lo: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    km: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    vi: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    id: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ms: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    tl: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    haw: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    mi: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    sm: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    to: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    fj: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    sw: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    am: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ti: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    om: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    so: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    rw: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    rn: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    lg: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ak: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    tw: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    yo: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ig: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ha: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ff: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    wo: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    sn: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    zu: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    xh: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    af: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    st: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    tn: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ss: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ve: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ts: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    nr: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    he: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    yi: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    jv: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    su: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    mad: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ban: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    bug: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    mak: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    min: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ace: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    bjn: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    bbc: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    nij: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    rej: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    sas: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    tet: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en\n};\nconst TranslationContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(undefined);\nfunction useTranslation() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(TranslationContext);\n    if (!context) {\n        throw new Error('useTranslation must be used within a TranslationProvider');\n    }\n    return context;\n}\n// Helper function to get translations for a specific locale\nfunction getTranslations(locale) {\n    return translations[locale] || translations[_lib_i18n__WEBPACK_IMPORTED_MODULE_1__.defaultLocale];\n}\n// Helper function to get nested translation value\nfunction getNestedTranslation(translations, key) {\n    const keys = key.split('.');\n    let value = translations;\n    for (const k of keys){\n        if (value && typeof value === 'object' && k in value) {\n            value = value[k];\n        } else {\n            return key // Return the key if translation not found\n            ;\n        }\n    }\n    return typeof value === 'string' ? value : key;\n}\n// Translation function with interpolation support\nfunction translateWithInterpolation(template) {\n    let values = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    return template.replace(/\\{\\{(\\w+)\\}\\}/g, (match, key)=>{\n        var _values_key;\n        return ((_values_key = values[key]) === null || _values_key === void 0 ? void 0 : _values_key.toString()) || match;\n    });\n}\n// Pluralization helper\nfunction pluralize(count, singular, plural) {\n    if (count === 1) {\n        return singular;\n    }\n    return plural || \"\".concat(singular, \"s\");\n}\n// Date formatting helper\nfunction formatDate(date, locale, options) {\n    try {\n        return new Intl.DateTimeFormat(locale, options).format(date);\n    } catch (error) {\n        // Fallback to English if locale is not supported\n        return new Intl.DateTimeFormat('en', options).format(date);\n    }\n}\n// Number formatting helper\nfunction formatNumber(number, locale, options) {\n    try {\n        return new Intl.NumberFormat(locale, options).format(number);\n    } catch (error) {\n        // Fallback to English if locale is not supported\n        return new Intl.NumberFormat('en', options).format(number);\n    }\n}\n// Currency formatting helper\nfunction formatCurrency(amount, locale) {\n    let currency = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 'USD';\n    try {\n        return new Intl.NumberFormat(locale, {\n            style: 'currency',\n            currency\n        }).format(amount);\n    } catch (error) {\n        // Fallback to English if locale is not supported\n        return new Intl.NumberFormat('en', {\n            style: 'currency',\n            currency\n        }).format(amount);\n    }\n}\n// Relative time formatting helper\nfunction formatRelativeTime(date, locale) {\n    try {\n        const rtf = new Intl.RelativeTimeFormat(locale, {\n            numeric: 'auto'\n        });\n        const now = new Date();\n        const diffInSeconds = Math.floor((date.getTime() - now.getTime()) / 1000);\n        if (Math.abs(diffInSeconds) < 60) {\n            return rtf.format(diffInSeconds, 'second');\n        }\n        const diffInMinutes = Math.floor(diffInSeconds / 60);\n        if (Math.abs(diffInMinutes) < 60) {\n            return rtf.format(diffInMinutes, 'minute');\n        }\n        const diffInHours = Math.floor(diffInMinutes / 60);\n        if (Math.abs(diffInHours) < 24) {\n            return rtf.format(diffInHours, 'hour');\n        }\n        const diffInDays = Math.floor(diffInHours / 24);\n        return rtf.format(diffInDays, 'day');\n    } catch (error) {\n        // Fallback to simple date formatting\n        return formatDate(date, locale);\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/use-translation.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/i18n.ts":
/*!*************************!*\
  !*** ./src/lib/i18n.ts ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultLocale: () => (/* binding */ defaultLocale),\n/* harmony export */   getLocaleDirection: () => (/* binding */ getLocaleDirection),\n/* harmony export */   getLocaleFontFamily: () => (/* binding */ getLocaleFontFamily),\n/* harmony export */   isValidLocale: () => (/* binding */ isValidLocale),\n/* harmony export */   languageNames: () => (/* binding */ languageNames),\n/* harmony export */   locales: () => (/* binding */ locales)\n/* harmony export */ });\n// Internationalization configuration\nconst defaultLocale = 'en';\nconst locales = [\n    'en',\n    'tr',\n    'ar',\n    'fr',\n    'es',\n    'de',\n    'ru',\n    'zh',\n    'ja',\n    'ko',\n    'pt',\n    'it',\n    'nl',\n    'sv',\n    'no',\n    'da',\n    'fi',\n    'pl',\n    'cs',\n    'hu',\n    'ro',\n    'bg',\n    'hr',\n    'sk',\n    'sl',\n    'et',\n    'lv',\n    'lt',\n    'mt',\n    'cy',\n    'ga',\n    'is',\n    'mk',\n    'sq',\n    'sr',\n    'bs',\n    'me',\n    'uk',\n    'be',\n    'kk',\n    'ky',\n    'uz',\n    'tg',\n    'tm',\n    'mn',\n    'ka',\n    'hy',\n    'az',\n    'fa',\n    'ur',\n    'hi',\n    'bn',\n    'ta',\n    'te',\n    'ml',\n    'kn',\n    'gu',\n    'pa',\n    'or',\n    'as',\n    'ne',\n    'si',\n    'my',\n    'th',\n    'lo',\n    'km',\n    'vi',\n    'id',\n    'ms',\n    'tl',\n    'haw',\n    'mi',\n    'sm',\n    'to',\n    'fj',\n    'sw',\n    'am',\n    'ti',\n    'om',\n    'so',\n    'rw',\n    'rn',\n    'lg',\n    'ak',\n    'tw',\n    'yo',\n    'ig',\n    'ha',\n    'ff',\n    'wo',\n    'sn',\n    'zu',\n    'xh',\n    'af',\n    'st',\n    'tn',\n    'ss',\n    've',\n    'ts',\n    'nr',\n    'he',\n    'yi',\n    'jv',\n    'su',\n    'mad',\n    'ban',\n    'bug',\n    'mak',\n    'min',\n    'ace',\n    'bjn',\n    'bbc',\n    'nij',\n    'rej',\n    'sas',\n    'tet'\n];\nconst languageNames = {\n    en: {\n        native: 'English',\n        english: 'English',\n        flag: '🇺🇸'\n    },\n    tr: {\n        native: 'Türkçe',\n        english: 'Turkish',\n        flag: '🇹🇷'\n    },\n    ar: {\n        native: 'العربية',\n        english: 'Arabic',\n        flag: '🇸🇦'\n    },\n    fr: {\n        native: 'Français',\n        english: 'French',\n        flag: '🇫🇷'\n    },\n    es: {\n        native: 'Español',\n        english: 'Spanish',\n        flag: '🇪🇸'\n    },\n    de: {\n        native: 'Deutsch',\n        english: 'German',\n        flag: '🇩🇪'\n    },\n    ru: {\n        native: 'Русский',\n        english: 'Russian',\n        flag: '🇷🇺'\n    },\n    zh: {\n        native: '中文',\n        english: 'Chinese',\n        flag: '🇨🇳'\n    },\n    ja: {\n        native: '日本語',\n        english: 'Japanese',\n        flag: '🇯🇵'\n    },\n    ko: {\n        native: '한국어',\n        english: 'Korean',\n        flag: '🇰🇷'\n    },\n    pt: {\n        native: 'Português',\n        english: 'Portuguese',\n        flag: '🇵🇹'\n    },\n    it: {\n        native: 'Italiano',\n        english: 'Italian',\n        flag: '🇮🇹'\n    },\n    nl: {\n        native: 'Nederlands',\n        english: 'Dutch',\n        flag: '🇳🇱'\n    },\n    sv: {\n        native: 'Svenska',\n        english: 'Swedish',\n        flag: '🇸🇪'\n    },\n    no: {\n        native: 'Norsk',\n        english: 'Norwegian',\n        flag: '🇳🇴'\n    },\n    da: {\n        native: 'Dansk',\n        english: 'Danish',\n        flag: '🇩🇰'\n    },\n    fi: {\n        native: 'Suomi',\n        english: 'Finnish',\n        flag: '🇫🇮'\n    },\n    pl: {\n        native: 'Polski',\n        english: 'Polish',\n        flag: '🇵🇱'\n    },\n    cs: {\n        native: 'Čeština',\n        english: 'Czech',\n        flag: '🇨🇿'\n    },\n    hu: {\n        native: 'Magyar',\n        english: 'Hungarian',\n        flag: '🇭🇺'\n    },\n    ro: {\n        native: 'Română',\n        english: 'Romanian',\n        flag: '🇷🇴'\n    },\n    bg: {\n        native: 'Български',\n        english: 'Bulgarian',\n        flag: '🇧🇬'\n    },\n    hr: {\n        native: 'Hrvatski',\n        english: 'Croatian',\n        flag: '🇭🇷'\n    },\n    sk: {\n        native: 'Slovenčina',\n        english: 'Slovak',\n        flag: '🇸🇰'\n    },\n    sl: {\n        native: 'Slovenščina',\n        english: 'Slovenian',\n        flag: '🇸🇮'\n    },\n    et: {\n        native: 'Eesti',\n        english: 'Estonian',\n        flag: '🇪🇪'\n    },\n    lv: {\n        native: 'Latviešu',\n        english: 'Latvian',\n        flag: '🇱🇻'\n    },\n    lt: {\n        native: 'Lietuvių',\n        english: 'Lithuanian',\n        flag: '🇱🇹'\n    },\n    mt: {\n        native: 'Malti',\n        english: 'Maltese',\n        flag: '🇲🇹'\n    },\n    cy: {\n        native: 'Cymraeg',\n        english: 'Welsh',\n        flag: '🏴󠁧󠁢󠁷󠁬󠁳󠁿'\n    },\n    ga: {\n        native: 'Gaeilge',\n        english: 'Irish',\n        flag: '🇮🇪'\n    },\n    is: {\n        native: 'Íslenska',\n        english: 'Icelandic',\n        flag: '🇮🇸'\n    },\n    mk: {\n        native: 'Македонски',\n        english: 'Macedonian',\n        flag: '🇲🇰'\n    },\n    sq: {\n        native: 'Shqip',\n        english: 'Albanian',\n        flag: '🇦🇱'\n    },\n    sr: {\n        native: 'Српски',\n        english: 'Serbian',\n        flag: '🇷🇸'\n    },\n    bs: {\n        native: 'Bosanski',\n        english: 'Bosnian',\n        flag: '🇧🇦'\n    },\n    me: {\n        native: 'Crnogorski',\n        english: 'Montenegrin',\n        flag: '🇲🇪'\n    },\n    uk: {\n        native: 'Українська',\n        english: 'Ukrainian',\n        flag: '🇺🇦'\n    },\n    be: {\n        native: 'Беларуская',\n        english: 'Belarusian',\n        flag: '🇧🇾'\n    },\n    kk: {\n        native: 'Қазақша',\n        english: 'Kazakh',\n        flag: '🇰🇿'\n    },\n    ky: {\n        native: 'Кыргызча',\n        english: 'Kyrgyz',\n        flag: '🇰🇬'\n    },\n    uz: {\n        native: 'Oʻzbekcha',\n        english: 'Uzbek',\n        flag: '🇺🇿'\n    },\n    tg: {\n        native: 'Тоҷикӣ',\n        english: 'Tajik',\n        flag: '🇹🇯'\n    },\n    tm: {\n        native: 'Türkmençe',\n        english: 'Turkmen',\n        flag: '🇹🇲'\n    },\n    mn: {\n        native: 'Монгол',\n        english: 'Mongolian',\n        flag: '🇲🇳'\n    },\n    ka: {\n        native: 'ქართული',\n        english: 'Georgian',\n        flag: '🇬🇪'\n    },\n    hy: {\n        native: 'Հայերեն',\n        english: 'Armenian',\n        flag: '🇦🇲'\n    },\n    az: {\n        native: 'Azərbaycan',\n        english: 'Azerbaijani',\n        flag: '🇦🇿'\n    },\n    fa: {\n        native: 'فارسی',\n        english: 'Persian',\n        flag: '🇮🇷'\n    },\n    ur: {\n        native: 'اردو',\n        english: 'Urdu',\n        flag: '🇵🇰'\n    },\n    hi: {\n        native: 'हिन्दी',\n        english: 'Hindi',\n        flag: '🇮🇳'\n    },\n    bn: {\n        native: 'বাংলা',\n        english: 'Bengali',\n        flag: '🇧🇩'\n    },\n    ta: {\n        native: 'தமிழ்',\n        english: 'Tamil',\n        flag: '🇱🇰'\n    },\n    te: {\n        native: 'తెలుగు',\n        english: 'Telugu',\n        flag: '🇮🇳'\n    },\n    ml: {\n        native: 'മലയാളം',\n        english: 'Malayalam',\n        flag: '🇮🇳'\n    },\n    kn: {\n        native: 'ಕನ್ನಡ',\n        english: 'Kannada',\n        flag: '🇮🇳'\n    },\n    gu: {\n        native: 'ગુજરાતી',\n        english: 'Gujarati',\n        flag: '🇮🇳'\n    },\n    pa: {\n        native: 'ਪੰਜਾਬੀ',\n        english: 'Punjabi',\n        flag: '🇮🇳'\n    },\n    or: {\n        native: 'ଓଡ଼ିଆ',\n        english: 'Odia',\n        flag: '🇮🇳'\n    },\n    as: {\n        native: 'অসমীয়া',\n        english: 'Assamese',\n        flag: '🇮🇳'\n    },\n    ne: {\n        native: 'नेपाली',\n        english: 'Nepali',\n        flag: '🇳🇵'\n    },\n    si: {\n        native: 'සිංහල',\n        english: 'Sinhala',\n        flag: '🇱🇰'\n    },\n    my: {\n        native: 'မြန်မာ',\n        english: 'Burmese',\n        flag: '🇲🇲'\n    },\n    th: {\n        native: 'ไทย',\n        english: 'Thai',\n        flag: '🇹🇭'\n    },\n    lo: {\n        native: 'ລາວ',\n        english: 'Lao',\n        flag: '🇱🇦'\n    },\n    km: {\n        native: 'ខ្មែរ',\n        english: 'Khmer',\n        flag: '🇰🇭'\n    },\n    vi: {\n        native: 'Tiếng Việt',\n        english: 'Vietnamese',\n        flag: '🇻🇳'\n    },\n    id: {\n        native: 'Bahasa Indonesia',\n        english: 'Indonesian',\n        flag: '🇮🇩'\n    },\n    ms: {\n        native: 'Bahasa Melayu',\n        english: 'Malay',\n        flag: '🇲🇾'\n    },\n    tl: {\n        native: 'Filipino',\n        english: 'Filipino',\n        flag: '🇵🇭'\n    },\n    haw: {\n        native: 'ʻŌlelo Hawaiʻi',\n        english: 'Hawaiian',\n        flag: '🏝️'\n    },\n    mi: {\n        native: 'Te Reo Māori',\n        english: 'Maori',\n        flag: '🇳🇿'\n    },\n    sm: {\n        native: 'Gagana Samoa',\n        english: 'Samoan',\n        flag: '🇼🇸'\n    },\n    to: {\n        native: 'Lea Fakatonga',\n        english: 'Tongan',\n        flag: '🇹🇴'\n    },\n    fj: {\n        native: 'Na Vosa Vakaviti',\n        english: 'Fijian',\n        flag: '🇫🇯'\n    },\n    sw: {\n        native: 'Kiswahili',\n        english: 'Swahili',\n        flag: '🇰🇪'\n    },\n    am: {\n        native: 'አማርኛ',\n        english: 'Amharic',\n        flag: '🇪🇹'\n    },\n    ti: {\n        native: 'ትግርኛ',\n        english: 'Tigrinya',\n        flag: '🇪🇷'\n    },\n    om: {\n        native: 'Afaan Oromoo',\n        english: 'Oromo',\n        flag: '🇪🇹'\n    },\n    so: {\n        native: 'Soomaali',\n        english: 'Somali',\n        flag: '🇸🇴'\n    },\n    rw: {\n        native: 'Ikinyarwanda',\n        english: 'Kinyarwanda',\n        flag: '🇷🇼'\n    },\n    rn: {\n        native: 'Ikirundi',\n        english: 'Kirundi',\n        flag: '🇧🇮'\n    },\n    lg: {\n        native: 'Luganda',\n        english: 'Luganda',\n        flag: '🇺🇬'\n    },\n    ak: {\n        native: 'Akan',\n        english: 'Akan',\n        flag: '🇬🇭'\n    },\n    tw: {\n        native: 'Twi',\n        english: 'Twi',\n        flag: '🇬🇭'\n    },\n    yo: {\n        native: 'Yorùbá',\n        english: 'Yoruba',\n        flag: '🇳🇬'\n    },\n    ig: {\n        native: 'Igbo',\n        english: 'Igbo',\n        flag: '🇳🇬'\n    },\n    ha: {\n        native: 'Hausa',\n        english: 'Hausa',\n        flag: '🇳🇬'\n    },\n    ff: {\n        native: 'Fulfulde',\n        english: 'Fulah',\n        flag: '🇸🇳'\n    },\n    wo: {\n        native: 'Wolof',\n        english: 'Wolof',\n        flag: '🇸🇳'\n    },\n    sn: {\n        native: 'ChiShona',\n        english: 'Shona',\n        flag: '🇿🇼'\n    },\n    zu: {\n        native: 'IsiZulu',\n        english: 'Zulu',\n        flag: '🇿🇦'\n    },\n    xh: {\n        native: 'IsiXhosa',\n        english: 'Xhosa',\n        flag: '🇿🇦'\n    },\n    af: {\n        native: 'Afrikaans',\n        english: 'Afrikaans',\n        flag: '🇿🇦'\n    },\n    st: {\n        native: 'Sesotho',\n        english: 'Sesotho',\n        flag: '🇱🇸'\n    },\n    tn: {\n        native: 'Setswana',\n        english: 'Setswana',\n        flag: '🇧🇼'\n    },\n    ss: {\n        native: 'SiSwati',\n        english: 'Siswati',\n        flag: '🇸🇿'\n    },\n    ve: {\n        native: 'Tshivenḓa',\n        english: 'Tshivenda',\n        flag: '🇿🇦'\n    },\n    ts: {\n        native: 'Xitsonga',\n        english: 'Xitsonga',\n        flag: '🇿🇦'\n    },\n    nr: {\n        native: 'IsiNdebele',\n        english: 'Ndebele',\n        flag: '🇿🇦'\n    },\n    he: {\n        native: 'עברית',\n        english: 'Hebrew',\n        flag: '🇮🇱'\n    },\n    yi: {\n        native: 'ייִדיש',\n        english: 'Yiddish',\n        flag: '🏳️'\n    },\n    jv: {\n        native: 'Basa Jawa',\n        english: 'Javanese',\n        flag: '🇮🇩'\n    },\n    su: {\n        native: 'Basa Sunda',\n        english: 'Sundanese',\n        flag: '🇮🇩'\n    },\n    mad: {\n        native: 'Basa Madhura',\n        english: 'Madurese',\n        flag: '🇮🇩'\n    },\n    ban: {\n        native: 'Basa Bali',\n        english: 'Balinese',\n        flag: '🇮🇩'\n    },\n    bug: {\n        native: 'Basa Ugi',\n        english: 'Buginese',\n        flag: '🇮🇩'\n    },\n    mak: {\n        native: 'Basa Mangkasara',\n        english: 'Makasar',\n        flag: '🇮🇩'\n    },\n    min: {\n        native: 'Baso Minangkabau',\n        english: 'Minangkabau',\n        flag: '🇮🇩'\n    },\n    ace: {\n        native: 'Bahsa Acèh',\n        english: 'Acehnese',\n        flag: '🇮🇩'\n    },\n    bjn: {\n        native: 'Bahasa Banjar',\n        english: 'Banjar',\n        flag: '🇮🇩'\n    },\n    bbc: {\n        native: 'Hata Batak Toba',\n        english: 'Batak Toba',\n        flag: '🇮🇩'\n    },\n    nij: {\n        native: 'Bahasa Ngaju',\n        english: 'Ngaju',\n        flag: '🇮🇩'\n    },\n    rej: {\n        native: 'Bahasa Rejang',\n        english: 'Rejang',\n        flag: '🇮🇩'\n    },\n    sas: {\n        native: 'Basa Sasak',\n        english: 'Sasak',\n        flag: '🇮🇩'\n    },\n    tet: {\n        native: 'Tetun',\n        english: 'Tetum',\n        flag: '🇹🇱'\n    }\n};\nfunction isValidLocale(locale) {\n    return locales.includes(locale);\n}\nfunction getLocaleDirection(locale) {\n    const rtlLocales = [\n        'ar',\n        'fa',\n        'ur',\n        'he',\n        'yi'\n    ];\n    return rtlLocales.includes(locale) ? 'rtl' : 'ltr';\n}\nfunction getLocaleFontFamily(locale) {\n    const fontFamilies = {\n        ar: 'Noto Sans Arabic, Arial, sans-serif',\n        fa: 'Noto Sans Arabic, Arial, sans-serif',\n        ur: 'Noto Sans Arabic, Arial, sans-serif',\n        he: 'Noto Sans Hebrew, Arial, sans-serif',\n        zh: 'Noto Sans SC, Arial, sans-serif',\n        ja: 'Noto Sans JP, Arial, sans-serif',\n        ko: 'Noto Sans KR, Arial, sans-serif',\n        th: 'Noto Sans Thai, Arial, sans-serif',\n        hi: 'Noto Sans Devanagari, Arial, sans-serif',\n        bn: 'Noto Sans Bengali, Arial, sans-serif',\n        ta: 'Noto Sans Tamil, Arial, sans-serif',\n        te: 'Noto Sans Telugu, Arial, sans-serif',\n        ml: 'Noto Sans Malayalam, Arial, sans-serif',\n        kn: 'Noto Sans Kannada, Arial, sans-serif',\n        gu: 'Noto Sans Gujarati, Arial, sans-serif',\n        pa: 'Noto Sans Gurmukhi, Arial, sans-serif',\n        or: 'Noto Sans Oriya, Arial, sans-serif',\n        as: 'Noto Sans Bengali, Arial, sans-serif',\n        ne: 'Noto Sans Devanagari, Arial, sans-serif',\n        si: 'Noto Sans Sinhala, Arial, sans-serif',\n        my: 'Noto Sans Myanmar, Arial, sans-serif',\n        lo: 'Noto Sans Lao, Arial, sans-serif',\n        km: 'Noto Sans Khmer, Arial, sans-serif',\n        vi: 'Noto Sans Vietnamese, Arial, sans-serif',\n        ka: 'Noto Sans Georgian, Arial, sans-serif',\n        hy: 'Noto Sans Armenian, Arial, sans-serif',\n        am: 'Noto Sans Ethiopic, Arial, sans-serif',\n        ti: 'Noto Sans Ethiopic, Arial, sans-serif'\n    };\n    return fontFamilies[locale] || 'Inter, system-ui, sans-serif';\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvaTE4bi50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBQSxxQ0FBcUM7QUFDOUIsTUFBTUEsZ0JBQWdCLEtBQUk7QUFFMUIsTUFBTUMsVUFBVTtJQUNyQjtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0NBQ0QsQ0FBUztBQUlILE1BQU1DLGdCQUFtRjtJQUM5RkMsSUFBSTtRQUFFQyxRQUFRO1FBQVdDLFNBQVM7UUFBV0MsTUFBTTtJQUFPO0lBQzFEQyxJQUFJO1FBQUVILFFBQVE7UUFBVUMsU0FBUztRQUFXQyxNQUFNO0lBQU87SUFDekRFLElBQUk7UUFBRUosUUFBUTtRQUFXQyxTQUFTO1FBQVVDLE1BQU07SUFBTztJQUN6REcsSUFBSTtRQUFFTCxRQUFRO1FBQVlDLFNBQVM7UUFBVUMsTUFBTTtJQUFPO0lBQzFESSxJQUFJO1FBQUVOLFFBQVE7UUFBV0MsU0FBUztRQUFXQyxNQUFNO0lBQU87SUFDMURLLElBQUk7UUFBRVAsUUFBUTtRQUFXQyxTQUFTO1FBQVVDLE1BQU07SUFBTztJQUN6RE0sSUFBSTtRQUFFUixRQUFRO1FBQVdDLFNBQVM7UUFBV0MsTUFBTTtJQUFPO0lBQzFETyxJQUFJO1FBQUVULFFBQVE7UUFBTUMsU0FBUztRQUFXQyxNQUFNO0lBQU87SUFDckRRLElBQUk7UUFBRVYsUUFBUTtRQUFPQyxTQUFTO1FBQVlDLE1BQU07SUFBTztJQUN2RFMsSUFBSTtRQUFFWCxRQUFRO1FBQU9DLFNBQVM7UUFBVUMsTUFBTTtJQUFPO0lBQ3JEVSxJQUFJO1FBQUVaLFFBQVE7UUFBYUMsU0FBUztRQUFjQyxNQUFNO0lBQU87SUFDL0RXLElBQUk7UUFBRWIsUUFBUTtRQUFZQyxTQUFTO1FBQVdDLE1BQU07SUFBTztJQUMzRFksSUFBSTtRQUFFZCxRQUFRO1FBQWNDLFNBQVM7UUFBU0MsTUFBTTtJQUFPO0lBQzNEYSxJQUFJO1FBQUVmLFFBQVE7UUFBV0MsU0FBUztRQUFXQyxNQUFNO0lBQU87SUFDMURjLElBQUk7UUFBRWhCLFFBQVE7UUFBU0MsU0FBUztRQUFhQyxNQUFNO0lBQU87SUFDMURlLElBQUk7UUFBRWpCLFFBQVE7UUFBU0MsU0FBUztRQUFVQyxNQUFNO0lBQU87SUFDdkRnQixJQUFJO1FBQUVsQixRQUFRO1FBQVNDLFNBQVM7UUFBV0MsTUFBTTtJQUFPO0lBQ3hEaUIsSUFBSTtRQUFFbkIsUUFBUTtRQUFVQyxTQUFTO1FBQVVDLE1BQU07SUFBTztJQUN4RGtCLElBQUk7UUFBRXBCLFFBQVE7UUFBV0MsU0FBUztRQUFTQyxNQUFNO0lBQU87SUFDeERtQixJQUFJO1FBQUVyQixRQUFRO1FBQVVDLFNBQVM7UUFBYUMsTUFBTTtJQUFPO0lBQzNEb0IsSUFBSTtRQUFFdEIsUUFBUTtRQUFVQyxTQUFTO1FBQVlDLE1BQU07SUFBTztJQUMxRHFCLElBQUk7UUFBRXZCLFFBQVE7UUFBYUMsU0FBUztRQUFhQyxNQUFNO0lBQU87SUFDOURzQixJQUFJO1FBQUV4QixRQUFRO1FBQVlDLFNBQVM7UUFBWUMsTUFBTTtJQUFPO0lBQzVEdUIsSUFBSTtRQUFFekIsUUFBUTtRQUFjQyxTQUFTO1FBQVVDLE1BQU07SUFBTztJQUM1RHdCLElBQUk7UUFBRTFCLFFBQVE7UUFBZUMsU0FBUztRQUFhQyxNQUFNO0lBQU87SUFDaEV5QixJQUFJO1FBQUUzQixRQUFRO1FBQVNDLFNBQVM7UUFBWUMsTUFBTTtJQUFPO0lBQ3pEMEIsSUFBSTtRQUFFNUIsUUFBUTtRQUFZQyxTQUFTO1FBQVdDLE1BQU07SUFBTztJQUMzRDJCLElBQUk7UUFBRTdCLFFBQVE7UUFBWUMsU0FBUztRQUFjQyxNQUFNO0lBQU87SUFDOUQ0QixJQUFJO1FBQUU5QixRQUFRO1FBQVNDLFNBQVM7UUFBV0MsTUFBTTtJQUFPO0lBQ3hENkIsSUFBSTtRQUFFL0IsUUFBUTtRQUFXQyxTQUFTO1FBQVNDLE1BQU07SUFBaUI7SUFDbEU4QixJQUFJO1FBQUVoQyxRQUFRO1FBQVdDLFNBQVM7UUFBU0MsTUFBTTtJQUFPO0lBQ3hEK0IsSUFBSTtRQUFFakMsUUFBUTtRQUFZQyxTQUFTO1FBQWFDLE1BQU07SUFBTztJQUM3RGdDLElBQUk7UUFBRWxDLFFBQVE7UUFBY0MsU0FBUztRQUFjQyxNQUFNO0lBQU87SUFDaEVpQyxJQUFJO1FBQUVuQyxRQUFRO1FBQVNDLFNBQVM7UUFBWUMsTUFBTTtJQUFPO0lBQ3pEa0MsSUFBSTtRQUFFcEMsUUFBUTtRQUFVQyxTQUFTO1FBQVdDLE1BQU07SUFBTztJQUN6RG1DLElBQUk7UUFBRXJDLFFBQVE7UUFBWUMsU0FBUztRQUFXQyxNQUFNO0lBQU87SUFDM0RvQyxJQUFJO1FBQUV0QyxRQUFRO1FBQWNDLFNBQVM7UUFBZUMsTUFBTTtJQUFPO0lBQ2pFcUMsSUFBSTtRQUFFdkMsUUFBUTtRQUFjQyxTQUFTO1FBQWFDLE1BQU07SUFBTztJQUMvRHNDLElBQUk7UUFBRXhDLFFBQVE7UUFBY0MsU0FBUztRQUFjQyxNQUFNO0lBQU87SUFDaEV1QyxJQUFJO1FBQUV6QyxRQUFRO1FBQVdDLFNBQVM7UUFBVUMsTUFBTTtJQUFPO0lBQ3pEd0MsSUFBSTtRQUFFMUMsUUFBUTtRQUFZQyxTQUFTO1FBQVVDLE1BQU07SUFBTztJQUMxRHlDLElBQUk7UUFBRTNDLFFBQVE7UUFBYUMsU0FBUztRQUFTQyxNQUFNO0lBQU87SUFDMUQwQyxJQUFJO1FBQUU1QyxRQUFRO1FBQVVDLFNBQVM7UUFBU0MsTUFBTTtJQUFPO0lBQ3ZEMkMsSUFBSTtRQUFFN0MsUUFBUTtRQUFhQyxTQUFTO1FBQVdDLE1BQU07SUFBTztJQUM1RDRDLElBQUk7UUFBRTlDLFFBQVE7UUFBVUMsU0FBUztRQUFhQyxNQUFNO0lBQU87SUFDM0Q2QyxJQUFJO1FBQUUvQyxRQUFRO1FBQVdDLFNBQVM7UUFBWUMsTUFBTTtJQUFPO0lBQzNEOEMsSUFBSTtRQUFFaEQsUUFBUTtRQUFXQyxTQUFTO1FBQVlDLE1BQU07SUFBTztJQUMzRCtDLElBQUk7UUFBRWpELFFBQVE7UUFBY0MsU0FBUztRQUFlQyxNQUFNO0lBQU87SUFDakVnRCxJQUFJO1FBQUVsRCxRQUFRO1FBQVNDLFNBQVM7UUFBV0MsTUFBTTtJQUFPO0lBQ3hEaUQsSUFBSTtRQUFFbkQsUUFBUTtRQUFRQyxTQUFTO1FBQVFDLE1BQU07SUFBTztJQUNwRGtELElBQUk7UUFBRXBELFFBQVE7UUFBVUMsU0FBUztRQUFTQyxNQUFNO0lBQU87SUFDdkRtRCxJQUFJO1FBQUVyRCxRQUFRO1FBQVNDLFNBQVM7UUFBV0MsTUFBTTtJQUFPO0lBQ3hEb0QsSUFBSTtRQUFFdEQsUUFBUTtRQUFTQyxTQUFTO1FBQVNDLE1BQU07SUFBTztJQUN0RHFELElBQUk7UUFBRXZELFFBQVE7UUFBVUMsU0FBUztRQUFVQyxNQUFNO0lBQU87SUFDeERzRCxJQUFJO1FBQUV4RCxRQUFRO1FBQVVDLFNBQVM7UUFBYUMsTUFBTTtJQUFPO0lBQzNEdUQsSUFBSTtRQUFFekQsUUFBUTtRQUFTQyxTQUFTO1FBQVdDLE1BQU07SUFBTztJQUN4RHdELElBQUk7UUFBRTFELFFBQVE7UUFBV0MsU0FBUztRQUFZQyxNQUFNO0lBQU87SUFDM0R5RCxJQUFJO1FBQUUzRCxRQUFRO1FBQVVDLFNBQVM7UUFBV0MsTUFBTTtJQUFPO0lBQ3pEMEQsSUFBSTtRQUFFNUQsUUFBUTtRQUFTQyxTQUFTO1FBQVFDLE1BQU07SUFBTztJQUNyRDJELElBQUk7UUFBRTdELFFBQVE7UUFBV0MsU0FBUztRQUFZQyxNQUFNO0lBQU87SUFDM0Q0RCxJQUFJO1FBQUU5RCxRQUFRO1FBQVVDLFNBQVM7UUFBVUMsTUFBTTtJQUFPO0lBQ3hENkQsSUFBSTtRQUFFL0QsUUFBUTtRQUFTQyxTQUFTO1FBQVdDLE1BQU07SUFBTztJQUN4RDhELElBQUk7UUFBRWhFLFFBQVE7UUFBVUMsU0FBUztRQUFXQyxNQUFNO0lBQU87SUFDekQrRCxJQUFJO1FBQUVqRSxRQUFRO1FBQU9DLFNBQVM7UUFBUUMsTUFBTTtJQUFPO0lBQ25EZ0UsSUFBSTtRQUFFbEUsUUFBUTtRQUFPQyxTQUFTO1FBQU9DLE1BQU07SUFBTztJQUNsRGlFLElBQUk7UUFBRW5FLFFBQVE7UUFBU0MsU0FBUztRQUFTQyxNQUFNO0lBQU87SUFDdERrRSxJQUFJO1FBQUVwRSxRQUFRO1FBQWNDLFNBQVM7UUFBY0MsTUFBTTtJQUFPO0lBQ2hFbUUsSUFBSTtRQUFFckUsUUFBUTtRQUFvQkMsU0FBUztRQUFjQyxNQUFNO0lBQU87SUFDdEVvRSxJQUFJO1FBQUV0RSxRQUFRO1FBQWlCQyxTQUFTO1FBQVNDLE1BQU07SUFBTztJQUM5RHFFLElBQUk7UUFBRXZFLFFBQVE7UUFBWUMsU0FBUztRQUFZQyxNQUFNO0lBQU87SUFDNURzRSxLQUFLO1FBQUV4RSxRQUFRO1FBQWtCQyxTQUFTO1FBQVlDLE1BQU07SUFBTTtJQUNsRXVFLElBQUk7UUFBRXpFLFFBQVE7UUFBZ0JDLFNBQVM7UUFBU0MsTUFBTTtJQUFPO0lBQzdEd0UsSUFBSTtRQUFFMUUsUUFBUTtRQUFnQkMsU0FBUztRQUFVQyxNQUFNO0lBQU87SUFDOUR5RSxJQUFJO1FBQUUzRSxRQUFRO1FBQWlCQyxTQUFTO1FBQVVDLE1BQU07SUFBTztJQUMvRDBFLElBQUk7UUFBRTVFLFFBQVE7UUFBb0JDLFNBQVM7UUFBVUMsTUFBTTtJQUFPO0lBQ2xFMkUsSUFBSTtRQUFFN0UsUUFBUTtRQUFhQyxTQUFTO1FBQVdDLE1BQU07SUFBTztJQUM1RDRFLElBQUk7UUFBRTlFLFFBQVE7UUFBUUMsU0FBUztRQUFXQyxNQUFNO0lBQU87SUFDdkQ2RSxJQUFJO1FBQUUvRSxRQUFRO1FBQVFDLFNBQVM7UUFBWUMsTUFBTTtJQUFPO0lBQ3hEOEUsSUFBSTtRQUFFaEYsUUFBUTtRQUFnQkMsU0FBUztRQUFTQyxNQUFNO0lBQU87SUFDN0QrRSxJQUFJO1FBQUVqRixRQUFRO1FBQVlDLFNBQVM7UUFBVUMsTUFBTTtJQUFPO0lBQzFEZ0YsSUFBSTtRQUFFbEYsUUFBUTtRQUFnQkMsU0FBUztRQUFlQyxNQUFNO0lBQU87SUFDbkVpRixJQUFJO1FBQUVuRixRQUFRO1FBQVlDLFNBQVM7UUFBV0MsTUFBTTtJQUFPO0lBQzNEa0YsSUFBSTtRQUFFcEYsUUFBUTtRQUFXQyxTQUFTO1FBQVdDLE1BQU07SUFBTztJQUMxRG1GLElBQUk7UUFBRXJGLFFBQVE7UUFBUUMsU0FBUztRQUFRQyxNQUFNO0lBQU87SUFDcERvRixJQUFJO1FBQUV0RixRQUFRO1FBQU9DLFNBQVM7UUFBT0MsTUFBTTtJQUFPO0lBQ2xEcUYsSUFBSTtRQUFFdkYsUUFBUTtRQUFVQyxTQUFTO1FBQVVDLE1BQU07SUFBTztJQUN4RHNGLElBQUk7UUFBRXhGLFFBQVE7UUFBUUMsU0FBUztRQUFRQyxNQUFNO0lBQU87SUFDcER1RixJQUFJO1FBQUV6RixRQUFRO1FBQVNDLFNBQVM7UUFBU0MsTUFBTTtJQUFPO0lBQ3REd0YsSUFBSTtRQUFFMUYsUUFBUTtRQUFZQyxTQUFTO1FBQVNDLE1BQU07SUFBTztJQUN6RHlGLElBQUk7UUFBRTNGLFFBQVE7UUFBU0MsU0FBUztRQUFTQyxNQUFNO0lBQU87SUFDdEQwRixJQUFJO1FBQUU1RixRQUFRO1FBQVlDLFNBQVM7UUFBU0MsTUFBTTtJQUFPO0lBQ3pEMkYsSUFBSTtRQUFFN0YsUUFBUTtRQUFXQyxTQUFTO1FBQVFDLE1BQU07SUFBTztJQUN2RDRGLElBQUk7UUFBRTlGLFFBQVE7UUFBWUMsU0FBUztRQUFTQyxNQUFNO0lBQU87SUFDekQ2RixJQUFJO1FBQUUvRixRQUFRO1FBQWFDLFNBQVM7UUFBYUMsTUFBTTtJQUFPO0lBQzlEOEYsSUFBSTtRQUFFaEcsUUFBUTtRQUFXQyxTQUFTO1FBQVdDLE1BQU07SUFBTztJQUMxRCtGLElBQUk7UUFBRWpHLFFBQVE7UUFBWUMsU0FBUztRQUFZQyxNQUFNO0lBQU87SUFDNURnRyxJQUFJO1FBQUVsRyxRQUFRO1FBQVdDLFNBQVM7UUFBV0MsTUFBTTtJQUFPO0lBQzFEaUcsSUFBSTtRQUFFbkcsUUFBUTtRQUFhQyxTQUFTO1FBQWFDLE1BQU07SUFBTztJQUM5RGtHLElBQUk7UUFBRXBHLFFBQVE7UUFBWUMsU0FBUztRQUFZQyxNQUFNO0lBQU87SUFDNURtRyxJQUFJO1FBQUVyRyxRQUFRO1FBQWNDLFNBQVM7UUFBV0MsTUFBTTtJQUFPO0lBQzdEb0csSUFBSTtRQUFFdEcsUUFBUTtRQUFTQyxTQUFTO1FBQVVDLE1BQU07SUFBTztJQUN2RHFHLElBQUk7UUFBRXZHLFFBQVE7UUFBVUMsU0FBUztRQUFXQyxNQUFNO0lBQU07SUFDeERzRyxJQUFJO1FBQUV4RyxRQUFRO1FBQWFDLFNBQVM7UUFBWUMsTUFBTTtJQUFPO0lBQzdEdUcsSUFBSTtRQUFFekcsUUFBUTtRQUFjQyxTQUFTO1FBQWFDLE1BQU07SUFBTztJQUMvRHdHLEtBQUs7UUFBRTFHLFFBQVE7UUFBZ0JDLFNBQVM7UUFBWUMsTUFBTTtJQUFPO0lBQ2pFeUcsS0FBSztRQUFFM0csUUFBUTtRQUFhQyxTQUFTO1FBQVlDLE1BQU07SUFBTztJQUM5RDBHLEtBQUs7UUFBRTVHLFFBQVE7UUFBWUMsU0FBUztRQUFZQyxNQUFNO0lBQU87SUFDN0QyRyxLQUFLO1FBQUU3RyxRQUFRO1FBQW1CQyxTQUFTO1FBQVdDLE1BQU07SUFBTztJQUNuRTRHLEtBQUs7UUFBRTlHLFFBQVE7UUFBb0JDLFNBQVM7UUFBZUMsTUFBTTtJQUFPO0lBQ3hFNkcsS0FBSztRQUFFL0csUUFBUTtRQUFjQyxTQUFTO1FBQVlDLE1BQU07SUFBTztJQUMvRDhHLEtBQUs7UUFBRWhILFFBQVE7UUFBaUJDLFNBQVM7UUFBVUMsTUFBTTtJQUFPO0lBQ2hFK0csS0FBSztRQUFFakgsUUFBUTtRQUFtQkMsU0FBUztRQUFjQyxNQUFNO0lBQU87SUFDdEVnSCxLQUFLO1FBQUVsSCxRQUFRO1FBQWdCQyxTQUFTO1FBQVNDLE1BQU07SUFBTztJQUM5RGlILEtBQUs7UUFBRW5ILFFBQVE7UUFBaUJDLFNBQVM7UUFBVUMsTUFBTTtJQUFPO0lBQ2hFa0gsS0FBSztRQUFFcEgsUUFBUTtRQUFjQyxTQUFTO1FBQVNDLE1BQU07SUFBTztJQUM1RG1ILEtBQUs7UUFBRXJILFFBQVE7UUFBU0MsU0FBUztRQUFTQyxNQUFNO0lBQU87QUFDekQsRUFBQztBQUVNLFNBQVNvSCxjQUFjQyxNQUFjO0lBQzFDLE9BQU8xSCxRQUFRMkgsUUFBUSxDQUFDRDtBQUMxQjtBQUVPLFNBQVNFLG1CQUFtQkYsTUFBYztJQUMvQyxNQUFNRyxhQUF1QjtRQUFDO1FBQU07UUFBTTtRQUFNO1FBQU07S0FBSztJQUMzRCxPQUFPQSxXQUFXRixRQUFRLENBQUNELFVBQVUsUUFBUTtBQUMvQztBQUVPLFNBQVNJLG9CQUFvQkosTUFBYztJQUNoRCxNQUFNSyxlQUFnRDtRQUNwRHhILElBQUk7UUFDSjhDLElBQUk7UUFDSkMsSUFBSTtRQUNKbUQsSUFBSTtRQUNKN0YsSUFBSTtRQUNKQyxJQUFJO1FBQ0pDLElBQUk7UUFDSnNELElBQUk7UUFDSmIsSUFBSTtRQUNKQyxJQUFJO1FBQ0pDLElBQUk7UUFDSkMsSUFBSTtRQUNKQyxJQUFJO1FBQ0pDLElBQUk7UUFDSkMsSUFBSTtRQUNKQyxJQUFJO1FBQ0pDLElBQUk7UUFDSkMsSUFBSTtRQUNKQyxJQUFJO1FBQ0pDLElBQUk7UUFDSkMsSUFBSTtRQUNKRSxJQUFJO1FBQ0pDLElBQUk7UUFDSkMsSUFBSTtRQUNKckIsSUFBSTtRQUNKQyxJQUFJO1FBQ0o4QixJQUFJO1FBQ0pDLElBQUk7SUFDTjtJQUVBLE9BQU82QyxZQUFZLENBQUNMLE9BQU8sSUFBSTtBQUNqQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxOaWRoYWxcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcZm9yZWluZ2F0ZV9ncm91cGVcXGZvcmVpbmdhdGUtd2Vic2l0ZVxcc3JjXFxsaWJcXGkxOG4udHMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gSW50ZXJuYXRpb25hbGl6YXRpb24gY29uZmlndXJhdGlvblxuZXhwb3J0IGNvbnN0IGRlZmF1bHRMb2NhbGUgPSAnZW4nXG5cbmV4cG9ydCBjb25zdCBsb2NhbGVzID0gW1xuICAnZW4nLCAgICAvLyBFbmdsaXNoXG4gICd0cicsICAgIC8vIFR1cmtpc2hcbiAgJ2FyJywgICAgLy8gQXJhYmljXG4gICdmcicsICAgIC8vIEZyZW5jaFxuICAnZXMnLCAgICAvLyBTcGFuaXNoXG4gICdkZScsICAgIC8vIEdlcm1hblxuICAncnUnLCAgICAvLyBSdXNzaWFuXG4gICd6aCcsICAgIC8vIENoaW5lc2VcbiAgJ2phJywgICAgLy8gSmFwYW5lc2VcbiAgJ2tvJywgICAgLy8gS29yZWFuXG4gICdwdCcsICAgIC8vIFBvcnR1Z3Vlc2VcbiAgJ2l0JywgICAgLy8gSXRhbGlhblxuICAnbmwnLCAgICAvLyBEdXRjaFxuICAnc3YnLCAgICAvLyBTd2VkaXNoXG4gICdubycsICAgIC8vIE5vcndlZ2lhblxuICAnZGEnLCAgICAvLyBEYW5pc2hcbiAgJ2ZpJywgICAgLy8gRmlubmlzaFxuICAncGwnLCAgICAvLyBQb2xpc2hcbiAgJ2NzJywgICAgLy8gQ3plY2hcbiAgJ2h1JywgICAgLy8gSHVuZ2FyaWFuXG4gICdybycsICAgIC8vIFJvbWFuaWFuXG4gICdiZycsICAgIC8vIEJ1bGdhcmlhblxuICAnaHInLCAgICAvLyBDcm9hdGlhblxuICAnc2snLCAgICAvLyBTbG92YWtcbiAgJ3NsJywgICAgLy8gU2xvdmVuaWFuXG4gICdldCcsICAgIC8vIEVzdG9uaWFuXG4gICdsdicsICAgIC8vIExhdHZpYW5cbiAgJ2x0JywgICAgLy8gTGl0aHVhbmlhblxuICAnbXQnLCAgICAvLyBNYWx0ZXNlXG4gICdjeScsICAgIC8vIFdlbHNoXG4gICdnYScsICAgIC8vIElyaXNoXG4gICdpcycsICAgIC8vIEljZWxhbmRpY1xuICAnbWsnLCAgICAvLyBNYWNlZG9uaWFuXG4gICdzcScsICAgIC8vIEFsYmFuaWFuXG4gICdzcicsICAgIC8vIFNlcmJpYW5cbiAgJ2JzJywgICAgLy8gQm9zbmlhblxuICAnbWUnLCAgICAvLyBNb250ZW5lZ3JpblxuICAndWsnLCAgICAvLyBVa3JhaW5pYW5cbiAgJ2JlJywgICAgLy8gQmVsYXJ1c2lhblxuICAna2snLCAgICAvLyBLYXpha2hcbiAgJ2t5JywgICAgLy8gS3lyZ3l6XG4gICd1eicsICAgIC8vIFV6YmVrXG4gICd0ZycsICAgIC8vIFRhamlrXG4gICd0bScsICAgIC8vIFR1cmttZW5cbiAgJ21uJywgICAgLy8gTW9uZ29saWFuXG4gICdrYScsICAgIC8vIEdlb3JnaWFuXG4gICdoeScsICAgIC8vIEFybWVuaWFuXG4gICdheicsICAgIC8vIEF6ZXJiYWlqYW5pXG4gICdmYScsICAgIC8vIFBlcnNpYW5cbiAgJ3VyJywgICAgLy8gVXJkdVxuICAnaGknLCAgICAvLyBIaW5kaVxuICAnYm4nLCAgICAvLyBCZW5nYWxpXG4gICd0YScsICAgIC8vIFRhbWlsXG4gICd0ZScsICAgIC8vIFRlbHVndVxuICAnbWwnLCAgICAvLyBNYWxheWFsYW1cbiAgJ2tuJywgICAgLy8gS2FubmFkYVxuICAnZ3UnLCAgICAvLyBHdWphcmF0aVxuICAncGEnLCAgICAvLyBQdW5qYWJpXG4gICdvcicsICAgIC8vIE9kaWFcbiAgJ2FzJywgICAgLy8gQXNzYW1lc2VcbiAgJ25lJywgICAgLy8gTmVwYWxpXG4gICdzaScsICAgIC8vIFNpbmhhbGFcbiAgJ215JywgICAgLy8gQnVybWVzZVxuICAndGgnLCAgICAvLyBUaGFpXG4gICdsbycsICAgIC8vIExhb1xuICAna20nLCAgICAvLyBLaG1lclxuICAndmknLCAgICAvLyBWaWV0bmFtZXNlXG4gICdpZCcsICAgIC8vIEluZG9uZXNpYW5cbiAgJ21zJywgICAgLy8gTWFsYXlcbiAgJ3RsJywgICAgLy8gRmlsaXBpbm9cbiAgJ2hhdycsICAgLy8gSGF3YWlpYW5cbiAgJ21pJywgICAgLy8gTWFvcmlcbiAgJ3NtJywgICAgLy8gU2Ftb2FuXG4gICd0bycsICAgIC8vIFRvbmdhblxuICAnZmonLCAgICAvLyBGaWppYW5cbiAgJ3N3JywgICAgLy8gU3dhaGlsaVxuICAnYW0nLCAgICAvLyBBbWhhcmljXG4gICd0aScsICAgIC8vIFRpZ3JpbnlhXG4gICdvbScsICAgIC8vIE9yb21vXG4gICdzbycsICAgIC8vIFNvbWFsaVxuICAncncnLCAgICAvLyBLaW55YXJ3YW5kYVxuICAncm4nLCAgICAvLyBLaXJ1bmRpXG4gICdsZycsICAgIC8vIEx1Z2FuZGFcbiAgJ2FrJywgICAgLy8gQWthblxuICAndHcnLCAgICAvLyBUd2lcbiAgJ3lvJywgICAgLy8gWW9ydWJhXG4gICdpZycsICAgIC8vIElnYm9cbiAgJ2hhJywgICAgLy8gSGF1c2FcbiAgJ2ZmJywgICAgLy8gRnVsYWhcbiAgJ3dvJywgICAgLy8gV29sb2ZcbiAgJ3NuJywgICAgLy8gU2hvbmFcbiAgJ3p1JywgICAgLy8gWnVsdVxuICAneGgnLCAgICAvLyBYaG9zYVxuICAnYWYnLCAgICAvLyBBZnJpa2FhbnNcbiAgJ3N0JywgICAgLy8gU2Vzb3Rob1xuICAndG4nLCAgICAvLyBTZXRzd2FuYVxuICAnc3MnLCAgICAvLyBTaXN3YXRpXG4gICd2ZScsICAgIC8vIFRzaGl2ZW5kYVxuICAndHMnLCAgICAvLyBYaXRzb25nYVxuICAnbnInLCAgICAvLyBOZGViZWxlXG4gICdoZScsICAgIC8vIEhlYnJld1xuICAneWknLCAgICAvLyBZaWRkaXNoXG4gICdqdicsICAgIC8vIEphdmFuZXNlXG4gICdzdScsICAgIC8vIFN1bmRhbmVzZVxuICAnbWFkJywgICAvLyBNYWR1cmVzZVxuICAnYmFuJywgICAvLyBCYWxpbmVzZVxuICAnYnVnJywgICAvLyBCdWdpbmVzZVxuICAnbWFrJywgICAvLyBNYWthc2FyXG4gICdtaW4nLCAgIC8vIE1pbmFuZ2thYmF1XG4gICdhY2UnLCAgIC8vIEFjZWhuZXNlXG4gICdiam4nLCAgIC8vIEJhbmphclxuICAnYmJjJywgICAvLyBCYXRhayBUb2JhXG4gICduaWonLCAgIC8vIE5nYWp1XG4gICdyZWonLCAgIC8vIFJlamFuZ1xuICAnc2FzJywgICAvLyBTYXNha1xuICAndGV0JywgICAvLyBUZXR1bVxuXSBhcyBjb25zdFxuXG5leHBvcnQgdHlwZSBMb2NhbGUgPSB0eXBlb2YgbG9jYWxlc1tudW1iZXJdXG5cbmV4cG9ydCBjb25zdCBsYW5ndWFnZU5hbWVzOiBSZWNvcmQ8TG9jYWxlLCB7IG5hdGl2ZTogc3RyaW5nOyBlbmdsaXNoOiBzdHJpbmc7IGZsYWc6IHN0cmluZyB9PiA9IHtcbiAgZW46IHsgbmF0aXZlOiAnRW5nbGlzaCcsIGVuZ2xpc2g6ICdFbmdsaXNoJywgZmxhZzogJ/Cfh7rwn4e4JyB9LFxuICB0cjogeyBuYXRpdmU6ICdUw7xya8OnZScsIGVuZ2xpc2g6ICdUdXJraXNoJywgZmxhZzogJ/Cfh7nwn4e3JyB9LFxuICBhcjogeyBuYXRpdmU6ICfYp9mE2LnYsdio2YrYqScsIGVuZ2xpc2g6ICdBcmFiaWMnLCBmbGFnOiAn8J+HuPCfh6YnIH0sXG4gIGZyOiB7IG5hdGl2ZTogJ0ZyYW7Dp2FpcycsIGVuZ2xpc2g6ICdGcmVuY2gnLCBmbGFnOiAn8J+Hq/Cfh7cnIH0sXG4gIGVzOiB7IG5hdGl2ZTogJ0VzcGHDsW9sJywgZW5nbGlzaDogJ1NwYW5pc2gnLCBmbGFnOiAn8J+HqvCfh7gnIH0sXG4gIGRlOiB7IG5hdGl2ZTogJ0RldXRzY2gnLCBlbmdsaXNoOiAnR2VybWFuJywgZmxhZzogJ/Cfh6nwn4eqJyB9LFxuICBydTogeyBuYXRpdmU6ICfQoNGD0YHRgdC60LjQuScsIGVuZ2xpc2g6ICdSdXNzaWFuJywgZmxhZzogJ/Cfh7fwn4e6JyB9LFxuICB6aDogeyBuYXRpdmU6ICfkuK3mlocnLCBlbmdsaXNoOiAnQ2hpbmVzZScsIGZsYWc6ICfwn4eo8J+HsycgfSxcbiAgamE6IHsgbmF0aXZlOiAn5pel5pys6KqeJywgZW5nbGlzaDogJ0phcGFuZXNlJywgZmxhZzogJ/Cfh6/wn4e1JyB9LFxuICBrbzogeyBuYXRpdmU6ICftlZzqta3slrQnLCBlbmdsaXNoOiAnS29yZWFuJywgZmxhZzogJ/Cfh7Dwn4e3JyB9LFxuICBwdDogeyBuYXRpdmU6ICdQb3J0dWd1w6pzJywgZW5nbGlzaDogJ1BvcnR1Z3Vlc2UnLCBmbGFnOiAn8J+HtfCfh7knIH0sXG4gIGl0OiB7IG5hdGl2ZTogJ0l0YWxpYW5vJywgZW5nbGlzaDogJ0l0YWxpYW4nLCBmbGFnOiAn8J+HrvCfh7knIH0sXG4gIG5sOiB7IG5hdGl2ZTogJ05lZGVybGFuZHMnLCBlbmdsaXNoOiAnRHV0Y2gnLCBmbGFnOiAn8J+Hs/Cfh7EnIH0sXG4gIHN2OiB7IG5hdGl2ZTogJ1N2ZW5za2EnLCBlbmdsaXNoOiAnU3dlZGlzaCcsIGZsYWc6ICfwn4e48J+HqicgfSxcbiAgbm86IHsgbmF0aXZlOiAnTm9yc2snLCBlbmdsaXNoOiAnTm9yd2VnaWFuJywgZmxhZzogJ/Cfh7Pwn4e0JyB9LFxuICBkYTogeyBuYXRpdmU6ICdEYW5zaycsIGVuZ2xpc2g6ICdEYW5pc2gnLCBmbGFnOiAn8J+HqfCfh7AnIH0sXG4gIGZpOiB7IG5hdGl2ZTogJ1N1b21pJywgZW5nbGlzaDogJ0Zpbm5pc2gnLCBmbGFnOiAn8J+Hq/Cfh64nIH0sXG4gIHBsOiB7IG5hdGl2ZTogJ1BvbHNraScsIGVuZ2xpc2g6ICdQb2xpc2gnLCBmbGFnOiAn8J+HtfCfh7EnIH0sXG4gIGNzOiB7IG5hdGl2ZTogJ8SMZcWhdGluYScsIGVuZ2xpc2g6ICdDemVjaCcsIGZsYWc6ICfwn4eo8J+HvycgfSxcbiAgaHU6IHsgbmF0aXZlOiAnTWFneWFyJywgZW5nbGlzaDogJ0h1bmdhcmlhbicsIGZsYWc6ICfwn4et8J+HuicgfSxcbiAgcm86IHsgbmF0aXZlOiAnUm9tw6JuxIMnLCBlbmdsaXNoOiAnUm9tYW5pYW4nLCBmbGFnOiAn8J+Ht/Cfh7QnIH0sXG4gIGJnOiB7IG5hdGl2ZTogJ9CR0YrQu9Cz0LDRgNGB0LrQuCcsIGVuZ2xpc2g6ICdCdWxnYXJpYW4nLCBmbGFnOiAn8J+Hp/Cfh6wnIH0sXG4gIGhyOiB7IG5hdGl2ZTogJ0hydmF0c2tpJywgZW5nbGlzaDogJ0Nyb2F0aWFuJywgZmxhZzogJ/Cfh63wn4e3JyB9LFxuICBzazogeyBuYXRpdmU6ICdTbG92ZW7EjWluYScsIGVuZ2xpc2g6ICdTbG92YWsnLCBmbGFnOiAn8J+HuPCfh7AnIH0sXG4gIHNsOiB7IG5hdGl2ZTogJ1Nsb3ZlbsWhxI1pbmEnLCBlbmdsaXNoOiAnU2xvdmVuaWFuJywgZmxhZzogJ/Cfh7jwn4euJyB9LFxuICBldDogeyBuYXRpdmU6ICdFZXN0aScsIGVuZ2xpc2g6ICdFc3RvbmlhbicsIGZsYWc6ICfwn4eq8J+HqicgfSxcbiAgbHY6IHsgbmF0aXZlOiAnTGF0dmllxaF1JywgZW5nbGlzaDogJ0xhdHZpYW4nLCBmbGFnOiAn8J+HsfCfh7snIH0sXG4gIGx0OiB7IG5hdGl2ZTogJ0xpZXR1dmnFsycsIGVuZ2xpc2g6ICdMaXRodWFuaWFuJywgZmxhZzogJ/Cfh7Hwn4e5JyB9LFxuICBtdDogeyBuYXRpdmU6ICdNYWx0aScsIGVuZ2xpc2g6ICdNYWx0ZXNlJywgZmxhZzogJ/Cfh7Lwn4e5JyB9LFxuICBjeTogeyBuYXRpdmU6ICdDeW1yYWVnJywgZW5nbGlzaDogJ1dlbHNoJywgZmxhZzogJ/Cfj7TzoIGn86CBovOggbfzoIGs86CBs/Oggb8nIH0sXG4gIGdhOiB7IG5hdGl2ZTogJ0dhZWlsZ2UnLCBlbmdsaXNoOiAnSXJpc2gnLCBmbGFnOiAn8J+HrvCfh6onIH0sXG4gIGlzOiB7IG5hdGl2ZTogJ8ONc2xlbnNrYScsIGVuZ2xpc2g6ICdJY2VsYW5kaWMnLCBmbGFnOiAn8J+HrvCfh7gnIH0sXG4gIG1rOiB7IG5hdGl2ZTogJ9Cc0LDQutC10LTQvtC90YHQutC4JywgZW5nbGlzaDogJ01hY2Vkb25pYW4nLCBmbGFnOiAn8J+HsvCfh7AnIH0sXG4gIHNxOiB7IG5hdGl2ZTogJ1NocWlwJywgZW5nbGlzaDogJ0FsYmFuaWFuJywgZmxhZzogJ/Cfh6bwn4exJyB9LFxuICBzcjogeyBuYXRpdmU6ICfQodGA0L/RgdC60LgnLCBlbmdsaXNoOiAnU2VyYmlhbicsIGZsYWc6ICfwn4e38J+HuCcgfSxcbiAgYnM6IHsgbmF0aXZlOiAnQm9zYW5za2knLCBlbmdsaXNoOiAnQm9zbmlhbicsIGZsYWc6ICfwn4en8J+HpicgfSxcbiAgbWU6IHsgbmF0aXZlOiAnQ3Jub2dvcnNraScsIGVuZ2xpc2g6ICdNb250ZW5lZ3JpbicsIGZsYWc6ICfwn4ey8J+HqicgfSxcbiAgdWs6IHsgbmF0aXZlOiAn0KPQutGA0LDRl9C90YHRjNC60LAnLCBlbmdsaXNoOiAnVWtyYWluaWFuJywgZmxhZzogJ/Cfh7rwn4emJyB9LFxuICBiZTogeyBuYXRpdmU6ICfQkdC10LvQsNGA0YPRgdC60LDRjycsIGVuZ2xpc2g6ICdCZWxhcnVzaWFuJywgZmxhZzogJ/Cfh6fwn4e+JyB9LFxuICBrazogeyBuYXRpdmU6ICfSmtCw0LfQsNKb0YjQsCcsIGVuZ2xpc2g6ICdLYXpha2gnLCBmbGFnOiAn8J+HsPCfh78nIH0sXG4gIGt5OiB7IG5hdGl2ZTogJ9Ca0YvRgNCz0YvQt9GH0LAnLCBlbmdsaXNoOiAnS3lyZ3l6JywgZmxhZzogJ/Cfh7Dwn4esJyB9LFxuICB1ejogeyBuYXRpdmU6ICdPyrt6YmVrY2hhJywgZW5nbGlzaDogJ1V6YmVrJywgZmxhZzogJ/Cfh7rwn4e/JyB9LFxuICB0ZzogeyBuYXRpdmU6ICfQotC+0rfQuNC606MnLCBlbmdsaXNoOiAnVGFqaWsnLCBmbGFnOiAn8J+HufCfh68nIH0sXG4gIHRtOiB7IG5hdGl2ZTogJ1TDvHJrbWVuw6dlJywgZW5nbGlzaDogJ1R1cmttZW4nLCBmbGFnOiAn8J+HufCfh7InIH0sXG4gIG1uOiB7IG5hdGl2ZTogJ9Cc0L7QvdCz0L7QuycsIGVuZ2xpc2g6ICdNb25nb2xpYW4nLCBmbGFnOiAn8J+HsvCfh7MnIH0sXG4gIGthOiB7IG5hdGl2ZTogJ+GDpeGDkOGDoOGDl+GDo+GDmuGDmCcsIGVuZ2xpc2g6ICdHZW9yZ2lhbicsIGZsYWc6ICfwn4es8J+HqicgfSxcbiAgaHk6IHsgbmF0aXZlOiAn1YDVodW11aXWgNWl1bYnLCBlbmdsaXNoOiAnQXJtZW5pYW4nLCBmbGFnOiAn8J+HpvCfh7InIH0sXG4gIGF6OiB7IG5hdGl2ZTogJ0F6yZlyYmF5Y2FuJywgZW5nbGlzaDogJ0F6ZXJiYWlqYW5pJywgZmxhZzogJ/Cfh6bwn4e/JyB9LFxuICBmYTogeyBuYXRpdmU6ICfZgdin2LHYs9uMJywgZW5nbGlzaDogJ1BlcnNpYW4nLCBmbGFnOiAn8J+HrvCfh7cnIH0sXG4gIHVyOiB7IG5hdGl2ZTogJ9in2LHYr9mIJywgZW5nbGlzaDogJ1VyZHUnLCBmbGFnOiAn8J+HtfCfh7AnIH0sXG4gIGhpOiB7IG5hdGl2ZTogJ+CkueCkv+CkqOCljeCkpuClgCcsIGVuZ2xpc2g6ICdIaW5kaScsIGZsYWc6ICfwn4eu8J+HsycgfSxcbiAgYm46IHsgbmF0aXZlOiAn4Kas4Ka+4KaC4Kay4Ka+JywgZW5nbGlzaDogJ0JlbmdhbGknLCBmbGFnOiAn8J+Hp/Cfh6knIH0sXG4gIHRhOiB7IG5hdGl2ZTogJ+CupOCuruCuv+CutOCvjScsIGVuZ2xpc2g6ICdUYW1pbCcsIGZsYWc6ICfwn4ex8J+HsCcgfSxcbiAgdGU6IHsgbmF0aXZlOiAn4LCk4LGG4LCy4LGB4LCX4LGBJywgZW5nbGlzaDogJ1RlbHVndScsIGZsYWc6ICfwn4eu8J+HsycgfSxcbiAgbWw6IHsgbmF0aXZlOiAn4LSu4LSy4LSv4LS+4LSz4LSCJywgZW5nbGlzaDogJ01hbGF5YWxhbScsIGZsYWc6ICfwn4eu8J+HsycgfSxcbiAga246IHsgbmF0aXZlOiAn4LKV4LKo4LON4LKo4LKhJywgZW5nbGlzaDogJ0thbm5hZGEnLCBmbGFnOiAn8J+HrvCfh7MnIH0sXG4gIGd1OiB7IG5hdGl2ZTogJ+Cql+CrgeCqnOCqsOCqvuCqpOCrgCcsIGVuZ2xpc2g6ICdHdWphcmF0aScsIGZsYWc6ICfwn4eu8J+HsycgfSxcbiAgcGE6IHsgbmF0aXZlOiAn4Kiq4Kmw4Kic4Ki+4Kis4KmAJywgZW5nbGlzaDogJ1B1bmphYmknLCBmbGFnOiAn8J+HrvCfh7MnIH0sXG4gIG9yOiB7IG5hdGl2ZTogJ+Csk+CsoeCsvOCsv+CshicsIGVuZ2xpc2g6ICdPZGlhJywgZmxhZzogJ/Cfh67wn4ezJyB9LFxuICBhczogeyBuYXRpdmU6ICfgpoXgprjgpq7gp4Dgpq/gprzgpr4nLCBlbmdsaXNoOiAnQXNzYW1lc2UnLCBmbGFnOiAn8J+HrvCfh7MnIH0sXG4gIG5lOiB7IG5hdGl2ZTogJ+CkqOClh+CkquCkvuCksuClgCcsIGVuZ2xpc2g6ICdOZXBhbGknLCBmbGFnOiAn8J+Hs/Cfh7UnIH0sXG4gIHNpOiB7IG5hdGl2ZTogJ+C3g+C3kuC2guC3hOC2vScsIGVuZ2xpc2g6ICdTaW5oYWxhJywgZmxhZzogJ/Cfh7Hwn4ewJyB9LFxuICBteTogeyBuYXRpdmU6ICfhgJnhgLzhgJThgLrhgJnhgKwnLCBlbmdsaXNoOiAnQnVybWVzZScsIGZsYWc6ICfwn4ey8J+HsicgfSxcbiAgdGg6IHsgbmF0aXZlOiAn4LmE4LiX4LiiJywgZW5nbGlzaDogJ1RoYWknLCBmbGFnOiAn8J+HufCfh60nIH0sXG4gIGxvOiB7IG5hdGl2ZTogJ+C6peC6suC6pycsIGVuZ2xpc2g6ICdMYW8nLCBmbGFnOiAn8J+HsfCfh6YnIH0sXG4gIGttOiB7IG5hdGl2ZTogJ+GegeGfkuGemOGfguGemicsIGVuZ2xpc2g6ICdLaG1lcicsIGZsYWc6ICfwn4ew8J+HrScgfSxcbiAgdmk6IHsgbmF0aXZlOiAnVGnhur9uZyBWaeG7h3QnLCBlbmdsaXNoOiAnVmlldG5hbWVzZScsIGZsYWc6ICfwn4e78J+HsycgfSxcbiAgaWQ6IHsgbmF0aXZlOiAnQmFoYXNhIEluZG9uZXNpYScsIGVuZ2xpc2g6ICdJbmRvbmVzaWFuJywgZmxhZzogJ/Cfh67wn4epJyB9LFxuICBtczogeyBuYXRpdmU6ICdCYWhhc2EgTWVsYXl1JywgZW5nbGlzaDogJ01hbGF5JywgZmxhZzogJ/Cfh7Lwn4e+JyB9LFxuICB0bDogeyBuYXRpdmU6ICdGaWxpcGlubycsIGVuZ2xpc2g6ICdGaWxpcGlubycsIGZsYWc6ICfwn4e18J+HrScgfSxcbiAgaGF3OiB7IG5hdGl2ZTogJ8q7xYxsZWxvIEhhd2FpyrtpJywgZW5nbGlzaDogJ0hhd2FpaWFuJywgZmxhZzogJ/Cfj53vuI8nIH0sXG4gIG1pOiB7IG5hdGl2ZTogJ1RlIFJlbyBNxIFvcmknLCBlbmdsaXNoOiAnTWFvcmknLCBmbGFnOiAn8J+Hs/Cfh78nIH0sXG4gIHNtOiB7IG5hdGl2ZTogJ0dhZ2FuYSBTYW1vYScsIGVuZ2xpc2g6ICdTYW1vYW4nLCBmbGFnOiAn8J+HvPCfh7gnIH0sXG4gIHRvOiB7IG5hdGl2ZTogJ0xlYSBGYWthdG9uZ2EnLCBlbmdsaXNoOiAnVG9uZ2FuJywgZmxhZzogJ/Cfh7nwn4e0JyB9LFxuICBmajogeyBuYXRpdmU6ICdOYSBWb3NhIFZha2F2aXRpJywgZW5nbGlzaDogJ0ZpamlhbicsIGZsYWc6ICfwn4er8J+HrycgfSxcbiAgc3c6IHsgbmF0aXZlOiAnS2lzd2FoaWxpJywgZW5nbGlzaDogJ1N3YWhpbGknLCBmbGFnOiAn8J+HsPCfh6onIH0sXG4gIGFtOiB7IG5hdGl2ZTogJ+GKoOGIm+GIreGKmycsIGVuZ2xpc2g6ICdBbWhhcmljJywgZmxhZzogJ/Cfh6rwn4e5JyB9LFxuICB0aTogeyBuYXRpdmU6ICfhibXhjI3hiK3hipsnLCBlbmdsaXNoOiAnVGlncmlueWEnLCBmbGFnOiAn8J+HqvCfh7cnIH0sXG4gIG9tOiB7IG5hdGl2ZTogJ0FmYWFuIE9yb21vbycsIGVuZ2xpc2g6ICdPcm9tbycsIGZsYWc6ICfwn4eq8J+HuScgfSxcbiAgc286IHsgbmF0aXZlOiAnU29vbWFhbGknLCBlbmdsaXNoOiAnU29tYWxpJywgZmxhZzogJ/Cfh7jwn4e0JyB9LFxuICBydzogeyBuYXRpdmU6ICdJa2lueWFyd2FuZGEnLCBlbmdsaXNoOiAnS2lueWFyd2FuZGEnLCBmbGFnOiAn8J+Ht/Cfh7wnIH0sXG4gIHJuOiB7IG5hdGl2ZTogJ0lraXJ1bmRpJywgZW5nbGlzaDogJ0tpcnVuZGknLCBmbGFnOiAn8J+Hp/Cfh64nIH0sXG4gIGxnOiB7IG5hdGl2ZTogJ0x1Z2FuZGEnLCBlbmdsaXNoOiAnTHVnYW5kYScsIGZsYWc6ICfwn4e68J+HrCcgfSxcbiAgYWs6IHsgbmF0aXZlOiAnQWthbicsIGVuZ2xpc2g6ICdBa2FuJywgZmxhZzogJ/Cfh6zwn4etJyB9LFxuICB0dzogeyBuYXRpdmU6ICdUd2knLCBlbmdsaXNoOiAnVHdpJywgZmxhZzogJ/Cfh6zwn4etJyB9LFxuICB5bzogeyBuYXRpdmU6ICdZb3LDuWLDoScsIGVuZ2xpc2g6ICdZb3J1YmEnLCBmbGFnOiAn8J+Hs/Cfh6wnIH0sXG4gIGlnOiB7IG5hdGl2ZTogJ0lnYm8nLCBlbmdsaXNoOiAnSWdibycsIGZsYWc6ICfwn4ez8J+HrCcgfSxcbiAgaGE6IHsgbmF0aXZlOiAnSGF1c2EnLCBlbmdsaXNoOiAnSGF1c2EnLCBmbGFnOiAn8J+Hs/Cfh6wnIH0sXG4gIGZmOiB7IG5hdGl2ZTogJ0Z1bGZ1bGRlJywgZW5nbGlzaDogJ0Z1bGFoJywgZmxhZzogJ/Cfh7jwn4ezJyB9LFxuICB3bzogeyBuYXRpdmU6ICdXb2xvZicsIGVuZ2xpc2g6ICdXb2xvZicsIGZsYWc6ICfwn4e48J+HsycgfSxcbiAgc246IHsgbmF0aXZlOiAnQ2hpU2hvbmEnLCBlbmdsaXNoOiAnU2hvbmEnLCBmbGFnOiAn8J+Hv/Cfh7wnIH0sXG4gIHp1OiB7IG5hdGl2ZTogJ0lzaVp1bHUnLCBlbmdsaXNoOiAnWnVsdScsIGZsYWc6ICfwn4e/8J+HpicgfSxcbiAgeGg6IHsgbmF0aXZlOiAnSXNpWGhvc2EnLCBlbmdsaXNoOiAnWGhvc2EnLCBmbGFnOiAn8J+Hv/Cfh6YnIH0sXG4gIGFmOiB7IG5hdGl2ZTogJ0FmcmlrYWFucycsIGVuZ2xpc2g6ICdBZnJpa2FhbnMnLCBmbGFnOiAn8J+Hv/Cfh6YnIH0sXG4gIHN0OiB7IG5hdGl2ZTogJ1Nlc290aG8nLCBlbmdsaXNoOiAnU2Vzb3RobycsIGZsYWc6ICfwn4ex8J+HuCcgfSxcbiAgdG46IHsgbmF0aXZlOiAnU2V0c3dhbmEnLCBlbmdsaXNoOiAnU2V0c3dhbmEnLCBmbGFnOiAn8J+Hp/Cfh7wnIH0sXG4gIHNzOiB7IG5hdGl2ZTogJ1NpU3dhdGknLCBlbmdsaXNoOiAnU2lzd2F0aScsIGZsYWc6ICfwn4e48J+HvycgfSxcbiAgdmU6IHsgbmF0aXZlOiAnVHNoaXZlbuG4k2EnLCBlbmdsaXNoOiAnVHNoaXZlbmRhJywgZmxhZzogJ/Cfh7/wn4emJyB9LFxuICB0czogeyBuYXRpdmU6ICdYaXRzb25nYScsIGVuZ2xpc2g6ICdYaXRzb25nYScsIGZsYWc6ICfwn4e/8J+HpicgfSxcbiAgbnI6IHsgbmF0aXZlOiAnSXNpTmRlYmVsZScsIGVuZ2xpc2g6ICdOZGViZWxlJywgZmxhZzogJ/Cfh7/wn4emJyB9LFxuICBoZTogeyBuYXRpdmU6ICfXoteR16jXmdeqJywgZW5nbGlzaDogJ0hlYnJldycsIGZsYWc6ICfwn4eu8J+HsScgfSxcbiAgeWk6IHsgbmF0aXZlOiAn15nXmda015PXmdepJywgZW5nbGlzaDogJ1lpZGRpc2gnLCBmbGFnOiAn8J+Ps++4jycgfSxcbiAganY6IHsgbmF0aXZlOiAnQmFzYSBKYXdhJywgZW5nbGlzaDogJ0phdmFuZXNlJywgZmxhZzogJ/Cfh67wn4epJyB9LFxuICBzdTogeyBuYXRpdmU6ICdCYXNhIFN1bmRhJywgZW5nbGlzaDogJ1N1bmRhbmVzZScsIGZsYWc6ICfwn4eu8J+HqScgfSxcbiAgbWFkOiB7IG5hdGl2ZTogJ0Jhc2EgTWFkaHVyYScsIGVuZ2xpc2g6ICdNYWR1cmVzZScsIGZsYWc6ICfwn4eu8J+HqScgfSxcbiAgYmFuOiB7IG5hdGl2ZTogJ0Jhc2EgQmFsaScsIGVuZ2xpc2g6ICdCYWxpbmVzZScsIGZsYWc6ICfwn4eu8J+HqScgfSxcbiAgYnVnOiB7IG5hdGl2ZTogJ0Jhc2EgVWdpJywgZW5nbGlzaDogJ0J1Z2luZXNlJywgZmxhZzogJ/Cfh67wn4epJyB9LFxuICBtYWs6IHsgbmF0aXZlOiAnQmFzYSBNYW5na2FzYXJhJywgZW5nbGlzaDogJ01ha2FzYXInLCBmbGFnOiAn8J+HrvCfh6knIH0sXG4gIG1pbjogeyBuYXRpdmU6ICdCYXNvIE1pbmFuZ2thYmF1JywgZW5nbGlzaDogJ01pbmFuZ2thYmF1JywgZmxhZzogJ/Cfh67wn4epJyB9LFxuICBhY2U6IHsgbmF0aXZlOiAnQmFoc2EgQWPDqGgnLCBlbmdsaXNoOiAnQWNlaG5lc2UnLCBmbGFnOiAn8J+HrvCfh6knIH0sXG4gIGJqbjogeyBuYXRpdmU6ICdCYWhhc2EgQmFuamFyJywgZW5nbGlzaDogJ0JhbmphcicsIGZsYWc6ICfwn4eu8J+HqScgfSxcbiAgYmJjOiB7IG5hdGl2ZTogJ0hhdGEgQmF0YWsgVG9iYScsIGVuZ2xpc2g6ICdCYXRhayBUb2JhJywgZmxhZzogJ/Cfh67wn4epJyB9LFxuICBuaWo6IHsgbmF0aXZlOiAnQmFoYXNhIE5nYWp1JywgZW5nbGlzaDogJ05nYWp1JywgZmxhZzogJ/Cfh67wn4epJyB9LFxuICByZWo6IHsgbmF0aXZlOiAnQmFoYXNhIFJlamFuZycsIGVuZ2xpc2g6ICdSZWphbmcnLCBmbGFnOiAn8J+HrvCfh6knIH0sXG4gIHNhczogeyBuYXRpdmU6ICdCYXNhIFNhc2FrJywgZW5nbGlzaDogJ1Nhc2FrJywgZmxhZzogJ/Cfh67wn4epJyB9LFxuICB0ZXQ6IHsgbmF0aXZlOiAnVGV0dW4nLCBlbmdsaXNoOiAnVGV0dW0nLCBmbGFnOiAn8J+HufCfh7EnIH0sXG59XG5cbmV4cG9ydCBmdW5jdGlvbiBpc1ZhbGlkTG9jYWxlKGxvY2FsZTogc3RyaW5nKTogbG9jYWxlIGlzIExvY2FsZSB7XG4gIHJldHVybiBsb2NhbGVzLmluY2x1ZGVzKGxvY2FsZSBhcyBMb2NhbGUpXG59XG5cbmV4cG9ydCBmdW5jdGlvbiBnZXRMb2NhbGVEaXJlY3Rpb24obG9jYWxlOiBMb2NhbGUpOiAnbHRyJyB8ICdydGwnIHtcbiAgY29uc3QgcnRsTG9jYWxlczogTG9jYWxlW10gPSBbJ2FyJywgJ2ZhJywgJ3VyJywgJ2hlJywgJ3lpJ11cbiAgcmV0dXJuIHJ0bExvY2FsZXMuaW5jbHVkZXMobG9jYWxlKSA/ICdydGwnIDogJ2x0cidcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIGdldExvY2FsZUZvbnRGYW1pbHkobG9jYWxlOiBMb2NhbGUpOiBzdHJpbmcge1xuICBjb25zdCBmb250RmFtaWxpZXM6IFBhcnRpYWw8UmVjb3JkPExvY2FsZSwgc3RyaW5nPj4gPSB7XG4gICAgYXI6ICdOb3RvIFNhbnMgQXJhYmljLCBBcmlhbCwgc2Fucy1zZXJpZicsXG4gICAgZmE6ICdOb3RvIFNhbnMgQXJhYmljLCBBcmlhbCwgc2Fucy1zZXJpZicsXG4gICAgdXI6ICdOb3RvIFNhbnMgQXJhYmljLCBBcmlhbCwgc2Fucy1zZXJpZicsXG4gICAgaGU6ICdOb3RvIFNhbnMgSGVicmV3LCBBcmlhbCwgc2Fucy1zZXJpZicsXG4gICAgemg6ICdOb3RvIFNhbnMgU0MsIEFyaWFsLCBzYW5zLXNlcmlmJyxcbiAgICBqYTogJ05vdG8gU2FucyBKUCwgQXJpYWwsIHNhbnMtc2VyaWYnLFxuICAgIGtvOiAnTm90byBTYW5zIEtSLCBBcmlhbCwgc2Fucy1zZXJpZicsXG4gICAgdGg6ICdOb3RvIFNhbnMgVGhhaSwgQXJpYWwsIHNhbnMtc2VyaWYnLFxuICAgIGhpOiAnTm90byBTYW5zIERldmFuYWdhcmksIEFyaWFsLCBzYW5zLXNlcmlmJyxcbiAgICBibjogJ05vdG8gU2FucyBCZW5nYWxpLCBBcmlhbCwgc2Fucy1zZXJpZicsXG4gICAgdGE6ICdOb3RvIFNhbnMgVGFtaWwsIEFyaWFsLCBzYW5zLXNlcmlmJyxcbiAgICB0ZTogJ05vdG8gU2FucyBUZWx1Z3UsIEFyaWFsLCBzYW5zLXNlcmlmJyxcbiAgICBtbDogJ05vdG8gU2FucyBNYWxheWFsYW0sIEFyaWFsLCBzYW5zLXNlcmlmJyxcbiAgICBrbjogJ05vdG8gU2FucyBLYW5uYWRhLCBBcmlhbCwgc2Fucy1zZXJpZicsXG4gICAgZ3U6ICdOb3RvIFNhbnMgR3VqYXJhdGksIEFyaWFsLCBzYW5zLXNlcmlmJyxcbiAgICBwYTogJ05vdG8gU2FucyBHdXJtdWtoaSwgQXJpYWwsIHNhbnMtc2VyaWYnLFxuICAgIG9yOiAnTm90byBTYW5zIE9yaXlhLCBBcmlhbCwgc2Fucy1zZXJpZicsXG4gICAgYXM6ICdOb3RvIFNhbnMgQmVuZ2FsaSwgQXJpYWwsIHNhbnMtc2VyaWYnLFxuICAgIG5lOiAnTm90byBTYW5zIERldmFuYWdhcmksIEFyaWFsLCBzYW5zLXNlcmlmJyxcbiAgICBzaTogJ05vdG8gU2FucyBTaW5oYWxhLCBBcmlhbCwgc2Fucy1zZXJpZicsXG4gICAgbXk6ICdOb3RvIFNhbnMgTXlhbm1hciwgQXJpYWwsIHNhbnMtc2VyaWYnLFxuICAgIGxvOiAnTm90byBTYW5zIExhbywgQXJpYWwsIHNhbnMtc2VyaWYnLFxuICAgIGttOiAnTm90byBTYW5zIEtobWVyLCBBcmlhbCwgc2Fucy1zZXJpZicsXG4gICAgdmk6ICdOb3RvIFNhbnMgVmlldG5hbWVzZSwgQXJpYWwsIHNhbnMtc2VyaWYnLFxuICAgIGthOiAnTm90byBTYW5zIEdlb3JnaWFuLCBBcmlhbCwgc2Fucy1zZXJpZicsXG4gICAgaHk6ICdOb3RvIFNhbnMgQXJtZW5pYW4sIEFyaWFsLCBzYW5zLXNlcmlmJyxcbiAgICBhbTogJ05vdG8gU2FucyBFdGhpb3BpYywgQXJpYWwsIHNhbnMtc2VyaWYnLFxuICAgIHRpOiAnTm90byBTYW5zIEV0aGlvcGljLCBBcmlhbCwgc2Fucy1zZXJpZicsXG4gIH1cbiAgXG4gIHJldHVybiBmb250RmFtaWxpZXNbbG9jYWxlXSB8fCAnSW50ZXIsIHN5c3RlbS11aSwgc2Fucy1zZXJpZidcbn1cbiJdLCJuYW1lcyI6WyJkZWZhdWx0TG9jYWxlIiwibG9jYWxlcyIsImxhbmd1YWdlTmFtZXMiLCJlbiIsIm5hdGl2ZSIsImVuZ2xpc2giLCJmbGFnIiwidHIiLCJhciIsImZyIiwiZXMiLCJkZSIsInJ1IiwiemgiLCJqYSIsImtvIiwicHQiLCJpdCIsIm5sIiwic3YiLCJubyIsImRhIiwiZmkiLCJwbCIsImNzIiwiaHUiLCJybyIsImJnIiwiaHIiLCJzayIsInNsIiwiZXQiLCJsdiIsImx0IiwibXQiLCJjeSIsImdhIiwiaXMiLCJtayIsInNxIiwic3IiLCJicyIsIm1lIiwidWsiLCJiZSIsImtrIiwia3kiLCJ1eiIsInRnIiwidG0iLCJtbiIsImthIiwiaHkiLCJheiIsImZhIiwidXIiLCJoaSIsImJuIiwidGEiLCJ0ZSIsIm1sIiwia24iLCJndSIsInBhIiwib3IiLCJhcyIsIm5lIiwic2kiLCJteSIsInRoIiwibG8iLCJrbSIsInZpIiwiaWQiLCJtcyIsInRsIiwiaGF3IiwibWkiLCJzbSIsInRvIiwiZmoiLCJzdyIsImFtIiwidGkiLCJvbSIsInNvIiwicnciLCJybiIsImxnIiwiYWsiLCJ0dyIsInlvIiwiaWciLCJoYSIsImZmIiwid28iLCJzbiIsInp1IiwieGgiLCJhZiIsInN0IiwidG4iLCJzcyIsInZlIiwidHMiLCJuciIsImhlIiwieWkiLCJqdiIsInN1IiwibWFkIiwiYmFuIiwiYnVnIiwibWFrIiwibWluIiwiYWNlIiwiYmpuIiwiYmJjIiwibmlqIiwicmVqIiwic2FzIiwidGV0IiwiaXNWYWxpZExvY2FsZSIsImxvY2FsZSIsImluY2x1ZGVzIiwiZ2V0TG9jYWxlRGlyZWN0aW9uIiwicnRsTG9jYWxlcyIsImdldExvY2FsZUZvbnRGYW1pbHkiLCJmb250RmFtaWxpZXMiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/i18n.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/locales/ar.ts":
/*!***************************!*\
  !*** ./src/locales/ar.ts ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ar: () => (/* binding */ ar)\n/* harmony export */ });\nconst ar = {\n    nav: {\n        home: 'الرئيسية',\n        about: 'من نحن',\n        services: 'الخدمات',\n        universities: 'الجامعات',\n        programs: 'البرامج',\n        blog: 'المدونة',\n        contact: 'اتصل بنا',\n        applyNow: 'قدم الآن',\n        getStarted: 'ابدأ',\n        language: 'اللغة'\n    },\n    hero: {\n        title: 'بوابتك إلى التعليم العالمي في شمال قبرص',\n        subtitle: 'ادرس في الخارج بثقة',\n        description: 'إرشاد خبير للطلاب الدوليين الساعين للحصول على تعليم عالي الجودة في أفضل الجامعات في شمال قبرص. من التقديم إلى التخرج، نحن معك في كل خطوة.',\n        ctaPrimary: 'ابدأ رحلتك',\n        ctaSecondary: 'استكشف الجامعات',\n        trustBadge: 'موثوق من قبل أكثر من 10,000 طالب حول العالم',\n        studentsServed: 'طالب تم خدمتهم',\n        successRate: 'معدل النجاح',\n        yearsExperience: 'سنوات الخبرة'\n    },\n    about: {\n        title: 'حول مجموعة فورين جيت',\n        subtitle: 'شريكك التعليمي الموثوق',\n        description: 'نحن استشارة تعليمية رائدة متخصصة في مساعدة الطلاب الدوليين على تحقيق أحلامهم الأكاديمية في شمال قبرص.',\n        mission: 'تقديم إرشاد تعليمي شامل وخدمات دعم تمكن الطلاب من النجاح في رحلتهم الأكاديمية الدولية.',\n        vision: 'أن نكون الجسر الأكثر ثقة الذي يربط الطلاب في جميع أنحاء العالم بفرص التعليم الجيد في شمال قبرص.',\n        values: 'التميز والنزاهة والابتكار ونجاح الطلاب',\n        whyChooseUs: 'لماذا تختارنا',\n        experience: 'أكثر من 15 سنة خبرة',\n        expertise: 'إرشاد خبير',\n        support: 'دعم 24/7',\n        success: '98% معدل نجاح'\n    },\n    services: {\n        title: 'خدماتنا',\n        subtitle: 'دعم شامل لرحلتك التعليمية',\n        universitySelection: 'اختيار الجامعة',\n        universitySelectionDesc: 'إرشاد خبير لاختيار الجامعة والبرنامج المناسبين بناءً على أهدافك وتفضيلاتك.',\n        admissionGuidance: 'إرشاد القبول',\n        admissionGuidanceDesc: 'دعم كامل خلال عملية التقديم، من إعداد الوثائق إلى التقديم.',\n        visaSupport: 'دعم التأشيرة',\n        visaSupportDesc: 'مساعدة مهنية في طلبات التأشيرة وإجراءات الهجرة.',\n        accommodationHelp: 'مساعدة السكن',\n        accommodationHelpDesc: 'العثور على خيارات سكن مناسبة بالقرب من جامعتك مع خدمات الإقامة لدينا.',\n        scholarshipAssistance: 'مساعدة المنح الدراسية',\n        scholarshipAssistanceDesc: 'تحديد والتقدم لفرص المنح الدراسية والمساعدة المالية.',\n        ongoingSupport: 'الدعم المستمر',\n        ongoingSupportDesc: 'دعم مستمر طوال دراستك، من الوصول إلى التخرج.'\n    },\n    universities: {\n        title: 'الجامعات الشريكة',\n        subtitle: 'أفضل الجامعات في شمال قبرص',\n        emu: 'جامعة شرق البحر المتوسط',\n        neu: 'جامعة الشرق الأدنى',\n        ciu: 'جامعة قبرص الدولية',\n        programs: 'البرامج',\n        students: 'الطلاب',\n        established: 'تأسست',\n        accreditation: 'الاعتماد',\n        tuitionFrom: 'الرسوم الدراسية من',\n        learnMore: 'اعرف المزيد',\n        applyNow: 'قدم الآن'\n    },\n    programs: {\n        title: 'البرامج الدراسية',\n        subtitle: 'برامج أكاديمية متنوعة تناسب اهتماماتك',\n        engineering: 'الهندسة',\n        medicine: 'الطب',\n        business: 'الأعمال',\n        arts: 'الفنون والعلوم الإنسانية',\n        sciences: 'العلوم',\n        law: 'القانون',\n        architecture: 'العمارة',\n        education: 'التعليم',\n        duration: 'المدة',\n        language: 'اللغة',\n        degree: 'الدرجة',\n        bachelor: 'البكالوريوس',\n        master: 'الماجستير',\n        doctorate: 'الدكتوراه'\n    },\n    testimonials: {\n        title: 'قصص نجاح الطلاب',\n        subtitle: 'استمع من طلابنا الناجحين',\n        readMore: 'اقرأ المزيد',\n        showLess: 'أظهر أقل',\n        verified: 'طالب موثق',\n        graduate: 'خريج',\n        currentStudent: 'طالب حالي'\n    },\n    contact: {\n        title: 'اتصل بنا',\n        subtitle: 'تواصل مع خبراء التعليم لدينا',\n        getInTouch: 'تواصل معنا',\n        name: 'الاسم الكامل',\n        email: 'عنوان البريد الإلكتروني',\n        phone: 'رقم الهاتف',\n        message: 'الرسالة',\n        subject: 'الموضوع',\n        send: 'إرسال الرسالة',\n        sending: 'جاري الإرسال...',\n        sent: 'تم إرسال الرسالة بنجاح!',\n        error: 'فشل في إرسال الرسالة. يرجى المحاولة مرة أخرى.',\n        required: 'هذا الحقل مطلوب',\n        invalidEmail: 'يرجى إدخال عنوان بريد إلكتروني صحيح',\n        office: 'ساعات العمل',\n        hours: 'الاثنين - الجمعة: 9:00 صباحاً - 6:00 مساءً',\n        emergency: 'دعم الطوارئ متاح 24/7'\n    },\n    footer: {\n        description: 'شريكك الموثوق للتعليم الدولي في شمال قبرص. إرشاد خبير من التقديم إلى التخرج.',\n        quickLinks: 'روابط سريعة',\n        services: 'الخدمات',\n        contact: 'معلومات الاتصال',\n        followUs: 'تابعنا',\n        newsletter: 'النشرة الإخبارية',\n        newsletterDesc: 'اشترك للحصول على آخر التحديثات حول الجامعات والبرامج والمنح الدراسية.',\n        subscribe: 'اشترك',\n        subscribing: 'جاري الاشتراك...',\n        subscribed: 'تم الاشتراك بنجاح!',\n        privacy: 'سياسة الخصوصية',\n        terms: 'شروط الخدمة',\n        cookies: 'سياسة ملفات تعريف الارتباط',\n        sitemap: 'خريطة الموقع',\n        allRightsReserved: 'جميع الحقوق محفوظة.'\n    },\n    common: {\n        loading: 'جاري التحميل...',\n        error: 'خطأ',\n        success: 'نجح',\n        warning: 'تحذير',\n        info: 'معلومات',\n        close: 'إغلاق',\n        cancel: 'إلغاء',\n        confirm: 'تأكيد',\n        save: 'حفظ',\n        edit: 'تحرير',\n        delete: 'حذف',\n        search: 'بحث',\n        filter: 'تصفية',\n        sort: 'ترتيب',\n        next: 'التالي',\n        previous: 'السابق',\n        page: 'صفحة',\n        of: 'من',\n        showing: 'عرض',\n        results: 'نتائج',\n        noResults: 'لم يتم العثور على نتائج',\n        tryAgain: 'حاول مرة أخرى',\n        learnMore: 'اعرف المزيد',\n        readMore: 'اقرأ المزيد',\n        showMore: 'أظهر المزيد',\n        showLess: 'أظهر أقل',\n        viewAll: 'عرض الكل',\n        backToTop: 'العودة إلى الأعلى'\n    },\n    chatbot: {\n        title: 'مساعد التعليم',\n        placeholder: 'اسأل عن الجامعات والبرامج والتكاليف...',\n        send: 'إرسال',\n        thinking: 'يفكر...',\n        error: 'عذراً، واجهت خطأ. يرجى المحاولة مرة أخرى.',\n        retry: 'إعادة المحاولة',\n        clear: 'مسح المحادثة',\n        minimize: 'تصغير',\n        maximize: 'تكبير',\n        close: 'إغلاق',\n        greeting: 'مرحباً! أنا هنا لمساعدتك في رحلتك التعليمية. ماذا تريد أن تعرف؟',\n        suggestions: 'أسئلة مقترحة',\n        typing: 'يكتب...',\n        offline: 'غير متصل',\n        online: 'متصل'\n    },\n    forms: {\n        firstName: 'الاسم الأول',\n        lastName: 'اسم العائلة',\n        fullName: 'الاسم الكامل',\n        email: 'عنوان البريد الإلكتروني',\n        phone: 'رقم الهاتف',\n        country: 'البلد',\n        city: 'المدينة',\n        address: 'العنوان',\n        zipCode: 'الرمز البريدي',\n        dateOfBirth: 'تاريخ الميلاد',\n        gender: 'الجنس',\n        male: 'ذكر',\n        female: 'أنثى',\n        other: 'آخر',\n        preferNotToSay: 'أفضل عدم القول',\n        nationality: 'الجنسية',\n        passportNumber: 'رقم جواز السفر',\n        education: 'مستوى التعليم',\n        highSchool: 'المدرسة الثانوية',\n        bachelor: 'درجة البكالوريوس',\n        master: 'درجة الماجستير',\n        doctorate: 'الدكتوراه',\n        workExperience: 'خبرة العمل',\n        englishLevel: 'مستوى الإنجليزية',\n        beginner: 'مبتدئ',\n        intermediate: 'متوسط',\n        advanced: 'متقدم',\n        native: 'لغة أم',\n        interestedProgram: 'البرنامج المهتم به',\n        interestedUniversity: 'الجامعة المهتم بها',\n        startDate: 'تاريخ البدء المفضل',\n        additionalInfo: 'معلومات إضافية',\n        agreeTerms: 'أوافق على شروط الخدمة',\n        agreePrivacy: 'أوافق على سياسة الخصوصية',\n        agreeMarketing: 'أوافق على تلقي الاتصالات التسويقية',\n        submit: 'إرسال',\n        submitting: 'جاري الإرسال...',\n        submitted: 'تم الإرسال بنجاح!',\n        required: 'حقل مطلوب',\n        invalid: 'تنسيق غير صحيح',\n        tooShort: 'قصير جداً',\n        tooLong: 'طويل جداً',\n        passwordMismatch: 'كلمات المرور غير متطابقة'\n    },\n    costs: {\n        title: 'التكاليف والمنح الدراسية',\n        subtitle: 'تعليم بأسعار معقولة مع خيارات الدعم المالي',\n        tuitionFees: 'الرسوم الدراسية',\n        livingCosts: 'تكاليف المعيشة',\n        totalCost: 'التكلفة الإجمالية',\n        scholarships: 'المنح الدراسية',\n        financialAid: 'المساعدة المالية',\n        paymentPlans: 'خطط الدفع',\n        currency: 'دولار أمريكي',\n        perYear: 'سنوياً',\n        perMonth: 'شهرياً',\n        accommodation: 'الإقامة',\n        food: 'الطعام والوجبات',\n        transportation: 'النقل',\n        books: 'الكتب واللوازم',\n        personal: 'المصاريف الشخصية',\n        insurance: 'التأمين الصحي',\n        visa: 'التأشيرة والهجرة',\n        other: 'مصاريف أخرى',\n        meritScholarship: 'منحة الجدارة',\n        needBasedAid: 'مساعدة قائمة على الحاجة',\n        earlyBird: 'خصم التسجيل المبكر',\n        siblingDiscount: 'خصم الأشقاء',\n        calculate: 'احسب التكاليف',\n        getQuote: 'احصل على عرض سعر'\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/locales/ar.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/locales/en.ts":
/*!***************************!*\
  !*** ./src/locales/en.ts ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   en: () => (/* binding */ en)\n/* harmony export */ });\nconst en = {\n    nav: {\n        home: 'Home',\n        about: 'About',\n        services: 'Services',\n        universities: 'Universities',\n        programs: 'Programs',\n        blog: 'Blog',\n        contact: 'Contact',\n        applyNow: 'Apply Now',\n        getStarted: 'Get Started',\n        language: 'Language'\n    },\n    hero: {\n        title: 'Your Gateway to World-Class Education in Northern Cyprus',\n        subtitle: 'Study Abroad with Confidence',\n        description: 'Expert guidance for international students seeking quality education at top universities in Northern Cyprus. From application to graduation, we\\'re with you every step of the way.',\n        ctaPrimary: 'Start Your Journey',\n        ctaSecondary: 'Explore Universities',\n        trustBadge: 'Trusted by 10,000+ Students Worldwide',\n        studentsServed: 'Students Served',\n        successRate: 'Success Rate',\n        yearsExperience: 'Years Experience'\n    },\n    about: {\n        title: 'About Foreingate Group',\n        subtitle: 'Your Trusted Education Partner',\n        description: 'We are a leading educational consultancy specializing in helping international students achieve their academic dreams in Northern Cyprus.',\n        mission: 'To provide comprehensive educational guidance and support services that empower students to succeed in their international academic journey.',\n        vision: 'To be the most trusted bridge connecting students worldwide with quality education opportunities in Northern Cyprus.',\n        values: 'Excellence, Integrity, Innovation, and Student Success',\n        whyChooseUs: 'Why Choose Us',\n        experience: '15+ Years Experience',\n        expertise: 'Expert Guidance',\n        support: '24/7 Support',\n        success: '98% Success Rate'\n    },\n    services: {\n        title: 'Our Services',\n        subtitle: 'Comprehensive Support for Your Educational Journey',\n        universitySelection: 'University Selection',\n        universitySelectionDesc: 'Expert guidance to choose the right university and program based on your goals and preferences.',\n        admissionGuidance: 'Admission Guidance',\n        admissionGuidanceDesc: 'Complete support through the application process, from document preparation to submission.',\n        visaSupport: 'Visa Support',\n        visaSupportDesc: 'Professional assistance with visa applications and immigration procedures.',\n        accommodationHelp: 'Accommodation Help',\n        accommodationHelpDesc: 'Find suitable housing options near your university with our accommodation services.',\n        scholarshipAssistance: 'Scholarship Assistance',\n        scholarshipAssistanceDesc: 'Identify and apply for scholarships and financial aid opportunities.',\n        ongoingSupport: 'Ongoing Support',\n        ongoingSupportDesc: 'Continuous support throughout your studies, from arrival to graduation.'\n    },\n    universities: {\n        title: 'Partner Universities',\n        subtitle: 'Top-Ranked Universities in Northern Cyprus',\n        emu: 'Eastern Mediterranean University',\n        neu: 'Near East University',\n        ciu: 'Cyprus International University',\n        programs: 'Programs',\n        students: 'Students',\n        established: 'Established',\n        accreditation: 'Accreditation',\n        tuitionFrom: 'Tuition from',\n        learnMore: 'Learn More',\n        applyNow: 'Apply Now'\n    },\n    programs: {\n        title: 'Study Programs',\n        subtitle: 'Diverse Academic Programs to Match Your Interests',\n        engineering: 'Engineering',\n        medicine: 'Medicine',\n        business: 'Business',\n        arts: 'Arts & Humanities',\n        sciences: 'Sciences',\n        law: 'Law',\n        architecture: 'Architecture',\n        education: 'Education',\n        duration: 'Duration',\n        language: 'Language',\n        degree: 'Degree',\n        bachelor: 'Bachelor\\'s',\n        master: 'Master\\'s',\n        doctorate: 'Doctorate'\n    },\n    testimonials: {\n        title: 'Student Success Stories',\n        subtitle: 'Hear from Our Successful Students',\n        readMore: 'Read More',\n        showLess: 'Show Less',\n        verified: 'Verified Student',\n        graduate: 'Graduate',\n        currentStudent: 'Current Student'\n    },\n    contact: {\n        title: 'Contact Us',\n        subtitle: 'Get in Touch with Our Education Experts',\n        getInTouch: 'Get in Touch',\n        name: 'Full Name',\n        email: 'Email Address',\n        phone: 'Phone Number',\n        message: 'Message',\n        subject: 'Subject',\n        send: 'Send Message',\n        sending: 'Sending...',\n        sent: 'Message Sent Successfully!',\n        error: 'Failed to send message. Please try again.',\n        required: 'This field is required',\n        invalidEmail: 'Please enter a valid email address',\n        office: 'Office Hours',\n        hours: 'Monday - Friday: 9:00 AM - 6:00 PM',\n        emergency: '24/7 Emergency Support Available'\n    },\n    footer: {\n        description: 'Your trusted partner for international education in Northern Cyprus. Expert guidance from application to graduation.',\n        quickLinks: 'Quick Links',\n        services: 'Services',\n        contact: 'Contact Info',\n        followUs: 'Follow Us',\n        newsletter: 'Newsletter',\n        newsletterDesc: 'Subscribe to get the latest updates on universities, programs, and scholarships.',\n        subscribe: 'Subscribe',\n        subscribing: 'Subscribing...',\n        subscribed: 'Subscribed Successfully!',\n        privacy: 'Privacy Policy',\n        terms: 'Terms of Service',\n        cookies: 'Cookie Policy',\n        sitemap: 'Sitemap',\n        allRightsReserved: 'All rights reserved.'\n    },\n    common: {\n        loading: 'Loading...',\n        error: 'Error',\n        success: 'Success',\n        warning: 'Warning',\n        info: 'Information',\n        close: 'Close',\n        cancel: 'Cancel',\n        confirm: 'Confirm',\n        save: 'Save',\n        edit: 'Edit',\n        delete: 'Delete',\n        search: 'Search',\n        filter: 'Filter',\n        sort: 'Sort',\n        next: 'Next',\n        previous: 'Previous',\n        page: 'Page',\n        of: 'of',\n        showing: 'Showing',\n        results: 'results',\n        noResults: 'No results found',\n        tryAgain: 'Try Again',\n        learnMore: 'Learn More',\n        readMore: 'Read More',\n        showMore: 'Show More',\n        showLess: 'Show Less',\n        viewAll: 'View All',\n        backToTop: 'Back to Top'\n    },\n    chatbot: {\n        title: 'Education Assistant',\n        placeholder: 'Ask me about universities, programs, costs...',\n        send: 'Send',\n        thinking: 'Thinking...',\n        error: 'Sorry, I encountered an error. Please try again.',\n        retry: 'Retry',\n        clear: 'Clear Chat',\n        minimize: 'Minimize',\n        maximize: 'Maximize',\n        close: 'Close',\n        greeting: 'Hello! I\\'m here to help you with your education journey. What would you like to know?',\n        suggestions: 'Suggested Questions',\n        typing: 'Typing...',\n        offline: 'Offline',\n        online: 'Online'\n    },\n    forms: {\n        firstName: 'First Name',\n        lastName: 'Last Name',\n        fullName: 'Full Name',\n        email: 'Email Address',\n        phone: 'Phone Number',\n        country: 'Country',\n        city: 'City',\n        address: 'Address',\n        zipCode: 'ZIP Code',\n        dateOfBirth: 'Date of Birth',\n        gender: 'Gender',\n        male: 'Male',\n        female: 'Female',\n        other: 'Other',\n        preferNotToSay: 'Prefer not to say',\n        nationality: 'Nationality',\n        passportNumber: 'Passport Number',\n        education: 'Education Level',\n        highSchool: 'High School',\n        bachelor: 'Bachelor\\'s Degree',\n        master: 'Master\\'s Degree',\n        doctorate: 'Doctorate',\n        workExperience: 'Work Experience',\n        englishLevel: 'English Level',\n        beginner: 'Beginner',\n        intermediate: 'Intermediate',\n        advanced: 'Advanced',\n        native: 'Native',\n        interestedProgram: 'Interested Program',\n        interestedUniversity: 'Interested University',\n        startDate: 'Preferred Start Date',\n        additionalInfo: 'Additional Information',\n        agreeTerms: 'I agree to the Terms of Service',\n        agreePrivacy: 'I agree to the Privacy Policy',\n        agreeMarketing: 'I agree to receive marketing communications',\n        submit: 'Submit',\n        submitting: 'Submitting...',\n        submitted: 'Submitted Successfully!',\n        required: 'Required field',\n        invalid: 'Invalid format',\n        tooShort: 'Too short',\n        tooLong: 'Too long',\n        passwordMismatch: 'Passwords do not match'\n    },\n    costs: {\n        title: 'Costs & Scholarships',\n        subtitle: 'Affordable Education with Financial Support Options',\n        tuitionFees: 'Tuition Fees',\n        livingCosts: 'Living Costs',\n        totalCost: 'Total Cost',\n        scholarships: 'Scholarships',\n        financialAid: 'Financial Aid',\n        paymentPlans: 'Payment Plans',\n        currency: 'USD',\n        perYear: 'per year',\n        perMonth: 'per month',\n        accommodation: 'Accommodation',\n        food: 'Food & Meals',\n        transportation: 'Transportation',\n        books: 'Books & Supplies',\n        personal: 'Personal Expenses',\n        insurance: 'Health Insurance',\n        visa: 'Visa & Immigration',\n        other: 'Other Expenses',\n        meritScholarship: 'Merit Scholarship',\n        needBasedAid: 'Need-Based Aid',\n        earlyBird: 'Early Bird Discount',\n        siblingDiscount: 'Sibling Discount',\n        calculate: 'Calculate Costs',\n        getQuote: 'Get Quote'\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/locales/en.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/locales/tr.ts":
/*!***************************!*\
  !*** ./src/locales/tr.ts ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   tr: () => (/* binding */ tr)\n/* harmony export */ });\nconst tr = {\n    nav: {\n        home: 'Ana Sayfa',\n        about: 'Hakkımızda',\n        services: 'Hizmetler',\n        universities: 'Üniversiteler',\n        programs: 'Programlar',\n        blog: 'Blog',\n        contact: 'İletişim',\n        applyNow: 'Başvur',\n        getStarted: 'Başla',\n        language: 'Dil'\n    },\n    hero: {\n        title: 'Kuzey Kıbrıs\\'ta Dünya Standartlarında Eğitime Açılan Kapınız',\n        subtitle: 'Güvenle Yurtdışında Eğitim',\n        description: 'Kuzey Kıbrıs\\'taki en iyi üniversitelerde kaliteli eğitim arayan uluslararası öğrenciler için uzman rehberlik. Başvurudan mezuniyete kadar her adımda yanınızdayız.',\n        ctaPrimary: 'Yolculuğuna Başla',\n        ctaSecondary: 'Üniversiteleri Keşfet',\n        trustBadge: 'Dünya Çapında 10.000+ Öğrenci Tarafından Güvenilir',\n        studentsServed: 'Hizmet Verilen Öğrenci',\n        successRate: 'Başarı Oranı',\n        yearsExperience: 'Yıl Deneyim'\n    },\n    about: {\n        title: 'Foreingate Group Hakkında',\n        subtitle: 'Güvenilir Eğitim Ortağınız',\n        description: 'Uluslararası öğrencilerin Kuzey Kıbrıs\\'taki akademik hayallerini gerçekleştirmelerine yardımcı olan önde gelen eğitim danışmanlığıyız.',\n        mission: 'Öğrencilerin uluslararası akademik yolculuklarında başarılı olmalarını sağlayan kapsamlı eğitim rehberliği ve destek hizmetleri sunmak.',\n        vision: 'Dünya çapındaki öğrencileri Kuzey Kıbrıs\\'taki kaliteli eğitim fırsatlarıyla buluşturan en güvenilir köprü olmak.',\n        values: 'Mükemmellik, Dürüstlük, İnovasyon ve Öğrenci Başarısı',\n        whyChooseUs: 'Neden Bizi Seçmelisiniz',\n        experience: '15+ Yıl Deneyim',\n        expertise: 'Uzman Rehberlik',\n        support: '7/24 Destek',\n        success: '%98 Başarı Oranı'\n    },\n    services: {\n        title: 'Hizmetlerimiz',\n        subtitle: 'Eğitim Yolculuğunuz İçin Kapsamlı Destek',\n        universitySelection: 'Üniversite Seçimi',\n        universitySelectionDesc: 'Hedefleriniz ve tercihlerinize göre doğru üniversite ve programı seçmek için uzman rehberlik.',\n        admissionGuidance: 'Kabul Rehberliği',\n        admissionGuidanceDesc: 'Belge hazırlığından başvuru sürecine kadar tam destek.',\n        visaSupport: 'Vize Desteği',\n        visaSupportDesc: 'Vize başvuruları ve göçmenlik prosedürleri için profesyonel yardım.',\n        accommodationHelp: 'Konaklama Yardımı',\n        accommodationHelpDesc: 'Konaklama hizmetlerimizle üniversitenizin yakınında uygun barınma seçenekleri bulun.',\n        scholarshipAssistance: 'Burs Yardımı',\n        scholarshipAssistanceDesc: 'Burs ve mali yardım fırsatlarını belirleyin ve başvurun.',\n        ongoingSupport: 'Sürekli Destek',\n        ongoingSupportDesc: 'Gelişinizden mezuniyetinize kadar eğitiminiz boyunca sürekli destek.'\n    },\n    universities: {\n        title: 'Partner Üniversiteler',\n        subtitle: 'Kuzey Kıbrıs\\'ın En İyi Üniversiteleri',\n        emu: 'Doğu Akdeniz Üniversitesi',\n        neu: 'Yakın Doğu Üniversitesi',\n        ciu: 'Kıbrıs Uluslararası Üniversitesi',\n        programs: 'Program',\n        students: 'Öğrenci',\n        established: 'Kuruluş',\n        accreditation: 'Akreditasyon',\n        tuitionFrom: 'Öğrenim ücreti',\n        learnMore: 'Daha Fazla Bilgi',\n        applyNow: 'Başvur'\n    },\n    programs: {\n        title: 'Eğitim Programları',\n        subtitle: 'İlgi Alanlarınıza Uygun Çeşitli Akademik Programlar',\n        engineering: 'Mühendislik',\n        medicine: 'Tıp',\n        business: 'İşletme',\n        arts: 'Sanat ve Beşeri Bilimler',\n        sciences: 'Fen Bilimleri',\n        law: 'Hukuk',\n        architecture: 'Mimarlık',\n        education: 'Eğitim',\n        duration: 'Süre',\n        language: 'Dil',\n        degree: 'Derece',\n        bachelor: 'Lisans',\n        master: 'Yüksek Lisans',\n        doctorate: 'Doktora'\n    },\n    testimonials: {\n        title: 'Öğrenci Başarı Hikayeleri',\n        subtitle: 'Başarılı Öğrencilerimizden Dinleyin',\n        readMore: 'Devamını Oku',\n        showLess: 'Daha Az Göster',\n        verified: 'Doğrulanmış Öğrenci',\n        graduate: 'Mezun',\n        currentStudent: 'Mevcut Öğrenci'\n    },\n    contact: {\n        title: 'İletişim',\n        subtitle: 'Eğitim Uzmanlarımızla İletişime Geçin',\n        getInTouch: 'İletişime Geç',\n        name: 'Ad Soyad',\n        email: 'E-posta Adresi',\n        phone: 'Telefon Numarası',\n        message: 'Mesaj',\n        subject: 'Konu',\n        send: 'Mesaj Gönder',\n        sending: 'Gönderiliyor...',\n        sent: 'Mesaj Başarıyla Gönderildi!',\n        error: 'Mesaj gönderilemedi. Lütfen tekrar deneyin.',\n        required: 'Bu alan zorunludur',\n        invalidEmail: 'Lütfen geçerli bir e-posta adresi girin',\n        office: 'Ofis Saatleri',\n        hours: 'Pazartesi - Cuma: 09:00 - 18:00',\n        emergency: '7/24 Acil Durum Desteği Mevcuttur'\n    },\n    footer: {\n        description: 'Kuzey Kıbrıs\\'ta uluslararası eğitim için güvenilir ortağınız. Başvurudan mezuniyete uzman rehberlik.',\n        quickLinks: 'Hızlı Bağlantılar',\n        services: 'Hizmetler',\n        contact: 'İletişim Bilgileri',\n        followUs: 'Bizi Takip Edin',\n        newsletter: 'Bülten',\n        newsletterDesc: 'Üniversiteler, programlar ve burslar hakkında en son güncellemeleri almak için abone olun.',\n        subscribe: 'Abone Ol',\n        subscribing: 'Abone Oluyor...',\n        subscribed: 'Başarıyla Abone Oldunuz!',\n        privacy: 'Gizlilik Politikası',\n        terms: 'Hizmet Şartları',\n        cookies: 'Çerez Politikası',\n        sitemap: 'Site Haritası',\n        allRightsReserved: 'Tüm hakları saklıdır.'\n    },\n    common: {\n        loading: 'Yükleniyor...',\n        error: 'Hata',\n        success: 'Başarılı',\n        warning: 'Uyarı',\n        info: 'Bilgi',\n        close: 'Kapat',\n        cancel: 'İptal',\n        confirm: 'Onayla',\n        save: 'Kaydet',\n        edit: 'Düzenle',\n        delete: 'Sil',\n        search: 'Ara',\n        filter: 'Filtrele',\n        sort: 'Sırala',\n        next: 'Sonraki',\n        previous: 'Önceki',\n        page: 'Sayfa',\n        of: '/',\n        showing: 'Gösteriliyor',\n        results: 'sonuç',\n        noResults: 'Sonuç bulunamadı',\n        tryAgain: 'Tekrar Dene',\n        learnMore: 'Daha Fazla Bilgi',\n        readMore: 'Devamını Oku',\n        showMore: 'Daha Fazla Göster',\n        showLess: 'Daha Az Göster',\n        viewAll: 'Tümünü Görüntüle',\n        backToTop: 'Başa Dön'\n    },\n    chatbot: {\n        title: 'Eğitim Asistanı',\n        placeholder: 'Üniversiteler, programlar, maliyetler hakkında sor...',\n        send: 'Gönder',\n        thinking: 'Düşünüyor...',\n        error: 'Üzgünüm, bir hatayla karşılaştım. Lütfen tekrar deneyin.',\n        retry: 'Tekrar Dene',\n        clear: 'Sohbeti Temizle',\n        minimize: 'Küçült',\n        maximize: 'Büyüt',\n        close: 'Kapat',\n        greeting: 'Merhaba! Eğitim yolculuğunuzda size yardımcı olmak için buradayım. Ne öğrenmek istiyorsunuz?',\n        suggestions: 'Önerilen Sorular',\n        typing: 'Yazıyor...',\n        offline: 'Çevrimdışı',\n        online: 'Çevrimiçi'\n    },\n    forms: {\n        firstName: 'Ad',\n        lastName: 'Soyad',\n        fullName: 'Ad Soyad',\n        email: 'E-posta Adresi',\n        phone: 'Telefon Numarası',\n        country: 'Ülke',\n        city: 'Şehir',\n        address: 'Adres',\n        zipCode: 'Posta Kodu',\n        dateOfBirth: 'Doğum Tarihi',\n        gender: 'Cinsiyet',\n        male: 'Erkek',\n        female: 'Kadın',\n        other: 'Diğer',\n        preferNotToSay: 'Belirtmek istemiyorum',\n        nationality: 'Uyruk',\n        passportNumber: 'Pasaport Numarası',\n        education: 'Eğitim Seviyesi',\n        highSchool: 'Lise',\n        bachelor: 'Lisans Derecesi',\n        master: 'Yüksek Lisans Derecesi',\n        doctorate: 'Doktora',\n        workExperience: 'İş Deneyimi',\n        englishLevel: 'İngilizce Seviyesi',\n        beginner: 'Başlangıç',\n        intermediate: 'Orta',\n        advanced: 'İleri',\n        native: 'Ana Dil',\n        interestedProgram: 'İlgilenilen Program',\n        interestedUniversity: 'İlgilenilen Üniversite',\n        startDate: 'Tercih Edilen Başlangıç Tarihi',\n        additionalInfo: 'Ek Bilgiler',\n        agreeTerms: 'Hizmet Şartlarını kabul ediyorum',\n        agreePrivacy: 'Gizlilik Politikasını kabul ediyorum',\n        agreeMarketing: 'Pazarlama iletişimi almayı kabul ediyorum',\n        submit: 'Gönder',\n        submitting: 'Gönderiliyor...',\n        submitted: 'Başarıyla Gönderildi!',\n        required: 'Zorunlu alan',\n        invalid: 'Geçersiz format',\n        tooShort: 'Çok kısa',\n        tooLong: 'Çok uzun',\n        passwordMismatch: 'Şifreler eşleşmiyor'\n    },\n    costs: {\n        title: 'Maliyetler ve Burslar',\n        subtitle: 'Mali Destek Seçenekleri ile Uygun Fiyatlı Eğitim',\n        tuitionFees: 'Öğrenim Ücretleri',\n        livingCosts: 'Yaşam Maliyetleri',\n        totalCost: 'Toplam Maliyet',\n        scholarships: 'Burslar',\n        financialAid: 'Mali Yardım',\n        paymentPlans: 'Ödeme Planları',\n        currency: 'USD',\n        perYear: 'yıllık',\n        perMonth: 'aylık',\n        accommodation: 'Konaklama',\n        food: 'Yemek ve Beslenme',\n        transportation: 'Ulaşım',\n        books: 'Kitap ve Malzemeler',\n        personal: 'Kişisel Harcamalar',\n        insurance: 'Sağlık Sigortası',\n        visa: 'Vize ve Göçmenlik',\n        other: 'Diğer Harcamalar',\n        meritScholarship: 'Başarı Bursu',\n        needBasedAid: 'İhtiyaç Temelli Yardım',\n        earlyBird: 'Erken Başvuru İndirimi',\n        siblingDiscount: 'Kardeş İndirimi',\n        calculate: 'Maliyetleri Hesapla',\n        getQuote: 'Teklif Al'\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/locales/tr.ts\n"));

/***/ })

});