{"id": "engineering-programs-guide-1752247240791", "title": "Engineering Programs Guide", "content": "Engineering programs in Northern Cyprus universities are highly regarded and internationally recognized. Computer Engineering: Covers software development, algorithms, computer systems, artificial intelligence, and cybersecurity. Graduates work in tech companies, startups, and multinational corporations. Civil Engineering: Focuses on infrastructure design, construction management, structural analysis, and environmental engineering. Strong emphasis on practical projects and internships. Electrical Engineering: Includes power systems, electronics, telecommunications, and renewable energy. Modern laboratories with industry-standard equipment. Mechanical Engineering: Covers thermodynamics, fluid mechanics, manufacturing, and automotive engineering. Hands-on experience with CAD software and manufacturing processes. All engineering programs require strong mathematics and physics background. English proficiency (IELTS 6.0 or TOEFL 79) is mandatory. Duration is typically 4 years for bachelors degree.", "chunks": [{"id": "engineering-programs-guide-1752247240791-chunk-0", "content": "Engineering programs in Northern Cyprus universities are highly regarded and internationally recognized Computer Engineering: Covers software development, algorithms, computer systems, artificial intelligence, and cybersecurity Graduates work in tech companies, startups, and multinational corporations", "metadata": {"documentId": "engineering-programs-guide-1752247240791", "chunkIndex": 0, "startPosition": 0, "endPosition": 2, "keywords": ["engineering", "computer", "cyprus"], "importance": 4}}, {"id": "engineering-programs-guide-1752247240791-chunk-1", "content": "Graduates work in tech companies, startups, and multinational corporations Civil Engineering: Focuses on infrastructure design, construction management, structural analysis, and environmental engineering Strong emphasis on practical projects and internships", "metadata": {"documentId": "engineering-programs-guide-1752247240791", "chunkIndex": 1, "startPosition": 2, "endPosition": 4, "keywords": ["engineering"], "importance": 2}}, {"id": "engineering-programs-guide-1752247240791-chunk-2", "content": "Strong emphasis on practical projects and internships Electrical Engineering: Includes power systems, electronics, telecommunications, and renewable energy Modern laboratories with industry-standard equipment", "metadata": {"documentId": "engineering-programs-guide-1752247240791", "chunkIndex": 2, "startPosition": 4, "endPosition": 6, "keywords": ["engineering"], "importance": 2}}, {"id": "engineering-programs-guide-1752247240791-chunk-3", "content": "Modern laboratories with industry-standard equipment Mechanical Engineering: Covers thermodynamics, fluid mechanics, manufacturing, and automotive engineering Hands-on experience with CAD software and manufacturing processes", "metadata": {"documentId": "engineering-programs-guide-1752247240791", "chunkIndex": 3, "startPosition": 6, "endPosition": 8, "keywords": ["with", "engineering", "manufacturing"], "importance": 2}}, {"id": "engineering-programs-guide-1752247240791-chunk-4", "content": "Hands-on experience with CAD software and manufacturing processes All engineering programs require strong mathematics and physics background English proficiency (IELTS 6", "metadata": {"documentId": "engineering-programs-guide-1752247240791", "chunkIndex": 4, "startPosition": 8, "endPosition": 10, "keywords": ["engineering", "english", "ielts"], "importance": 4}}, {"id": "engineering-programs-guide-1752247240791-chunk-5", "content": "English proficiency (IELTS 6 0 or TOEFL 79) is mandatory Duration is typically 4 years for bachelors degree", "metadata": {"documentId": "engineering-programs-guide-1752247240791", "chunkIndex": 5, "startPosition": 10, "endPosition": 12, "keywords": ["english", "ielts", "toefl", "degree"], "importance": 0}}, {"id": "engineering-programs-guide-1752247240791-chunk-6", "content": "Duration is typically 4 years for bachelors degree", "metadata": {"documentId": "engineering-programs-guide-1752247240791", "chunkIndex": 6, "startPosition": 12, "endPosition": 12, "keywords": ["degree"], "importance": 0}}], "metadata": {"source": "academic", "type": "program", "lastUpdated": "2025-07-11T15:20:40.792Z", "language": "en", "category": "programs"}}