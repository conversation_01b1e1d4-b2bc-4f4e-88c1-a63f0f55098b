'use client'

import { motion } from 'framer-motion'
import { useInView } from 'react-intersection-observer'
import { Mail, Phone, Linkedin, Globe } from 'lucide-react'
import { useTeamMembers } from '@/hooks/use-api'
import { But<PERSON> } from '@/components/ui/button'

// Extended mock team data for the About page
const extendedTeamMembers = [
  {
    id: '1',
    name: 'Dr. <PERSON>',
    position: 'CEO & Founder',
    image: '/images/team/michael.jpg',
    bio: 'With over 15 years of experience in international education, Dr<PERSON> founded Foreingate Group to help students achieve their academic dreams. He holds a PhD in Educational Leadership and has personally guided thousands of students.',
    email: '<EMAIL>',
    phone: '+90 ************',
    linkedin: 'https://linkedin.com/in/michael-thompson',
    languages: ['English', 'Turkish', 'Arabic'],
    specializations: ['University Admissions', 'Educational Consulting', 'International Relations']
  },
  {
    id: '2',
    name: '<PERSON>',
    position: 'Director of Student Services',
    image: '/images/team/sarah.jpg',
    bio: 'Sarah brings 12 years of experience in student counseling and has helped over 2,000 students navigate their educational journey. She specializes in personalized guidance and career counseling.',
    email: '<EMAIL>',
    phone: '+90 ************',
    linkedin: 'https://linkedin.com/in/sarah-johnson',
    languages: ['English', 'French', 'Spanish'],
    specializations: ['Student Counseling', 'Career Guidance', 'Program Selection']
  },
  {
    id: '3',
    name: 'Ahmed Al-<PERSON>',
    position: 'Visa & Immigration Specialist',
    image: '/images/team/ahmed.jpg',
    bio: 'Ahmed is our visa expert with 10 years of experience in immigration law and procedures. He has achieved a 98% visa success rate and specializes in complex immigration cases.',
    email: '<EMAIL>',
    phone: '+90 ************',
    linkedin: 'https://linkedin.com/in/ahmed-alrashid',
    languages: ['Arabic', 'English', 'Turkish'],
    specializations: ['Visa Processing', 'Immigration Law', 'Document Verification']
  },
  {
    id: '4',
    name: 'Elena Rodriguez',
    position: 'Scholarship Coordinator',
    image: '/images/team/elena.jpg',
    bio: 'Elena has secured over $2 million in scholarships for our students. With her background in financial aid and scholarship programs, she helps make education affordable for everyone.',
    email: '<EMAIL>',
    phone: '+90 ************',
    linkedin: 'https://linkedin.com/in/elena-rodriguez',
    languages: ['Spanish', 'English', 'Portuguese'],
    specializations: ['Scholarship Applications', 'Financial Aid', 'Grant Writing']
  },
  {
    id: '5',
    name: 'David Chen',
    position: 'Technology & Innovation Lead',
    image: '/images/team/david.jpg',
    bio: 'David leads our digital transformation initiatives and develops innovative solutions to enhance the student experience. He has a background in EdTech and software development.',
    email: '<EMAIL>',
    phone: '+90 ************',
    linkedin: 'https://linkedin.com/in/david-chen',
    languages: ['English', 'Mandarin', 'Japanese'],
    specializations: ['EdTech Solutions', 'Digital Innovation', 'Student Platforms']
  },
  {
    id: '6',
    name: 'Fatima Hassan',
    position: 'Regional Manager - Middle East',
    image: '/images/team/fatima.jpg',
    bio: 'Fatima oversees our operations in the Middle East region and has deep understanding of the educational needs of students from this region. She has 8 years of experience in international education.',
    email: '<EMAIL>',
    phone: '+90 ************',
    linkedin: 'https://linkedin.com/in/fatima-hassan',
    languages: ['Arabic', 'English', 'French'],
    specializations: ['Regional Operations', 'Cultural Adaptation', 'Student Support']
  }
]

export function TeamSection() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1
  })

  return (
    <section ref={ref} className="section-padding bg-muted/30">
      <div className="container">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
          className="text-center max-w-3xl mx-auto mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Meet Our{' '}
            <span className="gradient-text">Expert Team</span>
          </h2>
          <p className="text-xl text-muted-foreground">
            Dedicated professionals committed to your educational success, bringing decades of combined experience in international education
          </p>
        </motion.div>

        {/* Team Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {extendedTeamMembers.map((member, index) => (
            <motion.div
              key={member.id}
              initial={{ opacity: 0, y: 30 }}
              animate={inView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              className="bg-background rounded-xl overflow-hidden shadow-sm border hover:shadow-lg transition-all duration-300 group"
            >
              {/* Member Image */}
              <div className="relative h-64 overflow-hidden">
                <img
                  src={member.image}
                  alt={member.name}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  onError={(e) => {
                    e.currentTarget.src = `data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='300' height='300' viewBox='0 0 300 300'%3E%3Crect width='300' height='300' fill='%23f3f4f6'/%3E%3Ctext x='50%25' y='50%25' dominant-baseline='middle' text-anchor='middle' font-family='Arial, sans-serif' font-size='60' fill='%236b7280'%3E${member.name.charAt(0)}%3C/text%3E%3C/svg%3E`
                  }}
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
              </div>

              {/* Member Info */}
              <div className="p-6">
                <h3 className="text-xl font-semibold mb-1">{member.name}</h3>
                <p className="text-primary font-medium mb-3">{member.position}</p>
                <p className="text-muted-foreground text-sm leading-relaxed mb-4">
                  {member.bio}
                </p>

                {/* Languages */}
                <div className="mb-4">
                  <h4 className="text-sm font-medium mb-2">Languages:</h4>
                  <div className="flex flex-wrap gap-1">
                    {member.languages.map((language) => (
                      <span
                        key={language}
                        className="text-xs bg-muted px-2 py-1 rounded-full"
                      >
                        {language}
                      </span>
                    ))}
                  </div>
                </div>

                {/* Specializations */}
                <div className="mb-4">
                  <h4 className="text-sm font-medium mb-2">Specializations:</h4>
                  <div className="flex flex-wrap gap-1">
                    {member.specializations.map((spec) => (
                      <span
                        key={spec}
                        className="text-xs bg-primary/10 text-primary px-2 py-1 rounded-full"
                      >
                        {spec}
                      </span>
                    ))}
                  </div>
                </div>

                {/* Contact Info */}
                <div className="flex items-center space-x-3 pt-4 border-t">
                  <a
                    href={`mailto:${member.email}`}
                    className="text-muted-foreground hover:text-primary transition-colors"
                    title="Email"
                  >
                    <Mail className="w-4 h-4" />
                  </a>
                  {member.phone && (
                    <a
                      href={`tel:${member.phone}`}
                      className="text-muted-foreground hover:text-primary transition-colors"
                      title="Phone"
                    >
                      <Phone className="w-4 h-4" />
                    </a>
                  )}
                  {member.linkedin && (
                    <a
                      href={member.linkedin}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-muted-foreground hover:text-primary transition-colors"
                      title="LinkedIn"
                    >
                      <Linkedin className="w-4 h-4" />
                    </a>
                  )}
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Team Stats */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="mt-16 bg-gradient-to-r from-primary/5 to-secondary/5 rounded-2xl p-8 md:p-12"
        >
          <div className="text-center mb-8">
            <h3 className="text-2xl md:text-3xl font-bold mb-4">Our Team's Collective Impact</h3>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Together, our team brings decades of experience and a passion for student success
            </p>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 text-center">
            <div>
              <div className="text-3xl font-bold text-primary mb-2">80+</div>
              <div className="text-sm text-muted-foreground">Years Combined Experience</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-primary mb-2">15+</div>
              <div className="text-sm text-muted-foreground">Languages Spoken</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-primary mb-2">25+</div>
              <div className="text-sm text-muted-foreground">Countries Represented</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-primary mb-2">100%</div>
              <div className="text-sm text-muted-foreground">Dedicated to Your Success</div>
            </div>
          </div>
        </motion.div>

        {/* CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 0.8 }}
          className="text-center mt-12"
        >
          <h3 className="text-2xl font-bold mb-4">Ready to Work with Our Team?</h3>
          <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
            Schedule a free consultation with one of our experts and take the first step toward your educational goals.
          </p>
          <Button size="lg" asChild>
            <a href="/contact">
              Schedule Free Consultation
            </a>
          </Button>
        </motion.div>
      </div>
    </section>
  )
}
