'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import Link from 'next/link'
import { 
  Users, Mail, FileText, TrendingUp, Calendar, 
  ArrowUpRight, ArrowDownRight, Eye, Settings,
  BarChart3, PieChart, Activity
} from 'lucide-react'
import { Button } from '@/components/ui/button'

export default function AdminDashboard() {
  const [stats, setStats] = useState<any>({})
  const [recentApplications, setRecentApplications] = useState<any[]>([])
  const [recentSubscribers, setRecentSubscribers] = useState<any[]>([])
  const [recentContacts, setRecentContacts] = useState<any[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchDashboardData()
  }, [])

  const fetchDashboardData = async () => {
    try {
      setLoading(true)
      
      // Fetch applications stats
      const appsResponse = await fetch('/api/applications?limit=5')
      const appsData = await appsResponse.json()
      if (appsData.success) {
        setRecentApplications(appsData.data)
        setStats(prev => ({ ...prev, applications: appsData.stats }))
      }

      // Fetch newsletter stats
      const newsletterResponse = await fetch('/api/newsletter?limit=5')
      const newsletterData = await newsletterResponse.json()
      if (newsletterData.success) {
        setRecentSubscribers(newsletterData.data)
        setStats(prev => ({ ...prev, newsletter: newsletterData.stats }))
      }

      // Fetch contact inquiries
      const contactsResponse = await fetch('/api/contact-inquiries?limit=5')
      const contactsData = await contactsResponse.json()
      if (contactsData.success) {
        setRecentContacts(contactsData.data)
        setStats(prev => ({ ...prev, contacts: contactsData.stats }))
      }

    } catch (error) {
      console.error('Error fetching dashboard data:', error)
    } finally {
      setLoading(false)
    }
  }

  const dashboardStats = [
    {
      title: 'Total Applications',
      value: stats.applications?.total || 0,
      change: '+12%',
      changeType: 'increase',
      icon: FileText,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
      href: '/admin/applications'
    },
    {
      title: 'Pending Review',
      value: stats.applications?.pending || 0,
      change: '+5%',
      changeType: 'increase',
      icon: Calendar,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-100',
      href: '/admin/applications?status=pending'
    },
    {
      title: 'Newsletter Subscribers',
      value: stats.newsletter?.active || 0,
      change: '+8%',
      changeType: 'increase',
      icon: Mail,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
      href: '/admin/newsletter'
    },
    {
      title: 'This Month',
      value: (stats.applications?.thisMonth || 0) + (stats.newsletter?.thisMonth || 0),
      change: '+15%',
      changeType: 'increase',
      icon: TrendingUp,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
      href: '/admin/analytics'
    }
  ]

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
              <p className="text-gray-600">Welcome back! Here's what's happening with your platform.</p>
            </div>
            <div className="flex space-x-3">
              <Button onClick={fetchDashboardData}>
                Refresh Data
              </Button>
              <Link href="/admin/settings">
                <Button variant="outline">
                  <Settings className="w-4 h-4 mr-2" />
                  Settings
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {dashboardStats.map((stat, index) => (
            <motion.div
              key={stat.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Link href={stat.href}>
                <div className="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow cursor-pointer">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                      <p className={`text-2xl font-bold ${stat.color}`}>{stat.value}</p>
                      <div className="flex items-center mt-2">
                        {stat.changeType === 'increase' ? (
                          <ArrowUpRight className="w-4 h-4 text-green-500" />
                        ) : (
                          <ArrowDownRight className="w-4 h-4 text-red-500" />
                        )}
                        <span className={`text-sm ml-1 ${
                          stat.changeType === 'increase' ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {stat.change}
                        </span>
                        <span className="text-sm text-gray-500 ml-1">vs last month</span>
                      </div>
                    </div>
                    <div className={`${stat.bgColor} p-3 rounded-lg`}>
                      <stat.icon className={`w-6 h-6 ${stat.color}`} />
                    </div>
                  </div>
                </div>
              </Link>
            </motion.div>
          ))}
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2 }}
          >
            <Link href="/admin/applications">
              <div className="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow cursor-pointer">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900">Manage Applications</h3>
                  <FileText className="w-5 h-5 text-blue-600" />
                </div>
                <p className="text-gray-600 mb-4">Review, approve, and manage student applications</p>
                <div className="flex items-center text-blue-600 font-medium">
                  View Applications
                  <ArrowUpRight className="w-4 h-4 ml-1" />
                </div>
              </div>
            </Link>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 0 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.3 }}
          >
            <Link href="/admin/newsletter">
              <div className="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow cursor-pointer">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900">Newsletter Management</h3>
                  <Mail className="w-5 h-5 text-green-600" />
                </div>
                <p className="text-gray-600 mb-4">Manage subscribers and send email campaigns</p>
                <div className="flex items-center text-green-600 font-medium">
                  Manage Newsletter
                  <ArrowUpRight className="w-4 h-4 ml-1" />
                </div>
              </div>
            </Link>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.4 }}
          >
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Analytics</h3>
                <BarChart3 className="w-5 h-5 text-purple-600" />
              </div>
              <p className="text-gray-600 mb-4">View detailed analytics and reports</p>
              <div className="flex items-center text-purple-600 font-medium">
                Coming Soon
                <Activity className="w-4 h-4 ml-1" />
              </div>
            </div>
          </motion.div>
        </div>

        {/* Recent Activity */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Recent Applications */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
            className="bg-white rounded-lg shadow"
          >
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">Recent Applications</h3>
                <Link href="/admin/applications">
                  <Button variant="outline" size="sm">
                    <Eye className="w-4 h-4 mr-2" />
                    View All
                  </Button>
                </Link>
              </div>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {recentApplications.slice(0, 5).map((app) => (
                  <div key={app.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div>
                      <p className="font-medium text-gray-900">{app.firstName} {app.lastName}</p>
                      <p className="text-sm text-gray-600">{app.preferredUniversity}</p>
                      <p className="text-xs text-gray-500">{new Date(app.createdAt).toLocaleDateString()}</p>
                    </div>
                    <span className={`px-2 py-1 text-xs rounded-full ${
                      app.status === 'PENDING' ? 'bg-yellow-100 text-yellow-800' :
                      app.status === 'APPROVED' ? 'bg-green-100 text-green-800' :
                      app.status === 'UNDER_REVIEW' ? 'bg-blue-100 text-blue-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {app.status}
                    </span>
                  </div>
                ))}
                {recentApplications.length === 0 && (
                  <p className="text-gray-500 text-center py-4">No recent applications</p>
                )}
              </div>
            </div>
          </motion.div>

          {/* Recent Newsletter Subscribers */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
            className="bg-white rounded-lg shadow"
          >
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">Recent Subscribers</h3>
                <Link href="/admin/newsletter">
                  <Button variant="outline" size="sm">
                    <Eye className="w-4 h-4 mr-2" />
                    View All
                  </Button>
                </Link>
              </div>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {recentSubscribers.slice(0, 5).map((subscriber) => (
                  <div key={subscriber.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div>
                      <p className="font-medium text-gray-900">{subscriber.email}</p>
                      <p className="text-sm text-gray-600">Source: {subscriber.source}</p>
                      <p className="text-xs text-gray-500">{new Date(subscriber.subscribedAt).toLocaleDateString()}</p>
                    </div>
                    <span className={`px-2 py-1 text-xs rounded-full ${
                      subscriber.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    }`}>
                      {subscriber.isActive ? 'Active' : 'Inactive'}
                    </span>
                  </div>
                ))}
                {recentSubscribers.length === 0 && (
                  <p className="text-gray-500 text-center py-4">No recent subscribers</p>
                )}
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  )
}
