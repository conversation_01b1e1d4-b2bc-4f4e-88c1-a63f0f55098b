'use client'

import { useState, useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { ChevronDown, Globe, Check, Search } from 'lucide-react'
import { useTranslation } from '@/hooks/use-translation'
import { Locale, languageNames, locales } from '@/lib/i18n'
import { Button } from './button'
import { Input } from './input'

interface LanguageSwitcherProps {
  variant?: 'default' | 'compact' | 'minimal'
  showFlag?: boolean
  showNativeName?: boolean
  className?: string
}

export function LanguageSwitcher({
  variant = 'default',
  showFlag = true,
  showNativeName = true,
  className = ''
}: LanguageSwitcherProps) {
  const { locale, setLocale, t } = useTranslation()
  const [isOpen, setIsOpen] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const dropdownRef = useRef<HTMLDivElement>(null)

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
        setSearchQuery('')
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  // Filter languages based on search query
  const filteredLocales = locales.filter(loc => {
    const lang = languageNames[loc]
    const query = searchQuery.toLowerCase()
    return (
      lang.native.toLowerCase().includes(query) ||
      lang.english.toLowerCase().includes(query) ||
      loc.toLowerCase().includes(query)
    )
  })

  // Popular languages (shown first)
  const popularLocales: Locale[] = ['en', 'tr', 'ar', 'fr', 'es', 'de', 'ru', 'zh', 'ja', 'ko']
  const otherLocales = filteredLocales.filter(loc => !popularLocales.includes(loc))
  const sortedPopularLocales = popularLocales.filter(loc => filteredLocales.includes(loc))

  const handleLanguageChange = (newLocale: Locale) => {
    setLocale(newLocale)
    setIsOpen(false)
    setSearchQuery('')
  }

  const currentLanguage = languageNames[locale]

  if (variant === 'minimal') {
    return (
      <div className={`relative ${className}`} ref={dropdownRef}>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsOpen(!isOpen)}
          className="p-2"
        >
          {showFlag && <span className="text-lg">{currentLanguage.flag}</span>}
          <span className="ml-1 text-sm font-medium">{locale.toUpperCase()}</span>
        </Button>

        <AnimatePresence>
          {isOpen && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="absolute top-full right-0 mt-2 w-64 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50 max-h-80 overflow-hidden"
            >
              <LanguageList
                locales={filteredLocales}
                currentLocale={locale}
                onSelect={handleLanguageChange}
                showFlag={showFlag}
                showNativeName={showNativeName}
                searchQuery={searchQuery}
                onSearchChange={setSearchQuery}
              />
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    )
  }

  if (variant === 'compact') {
    return (
      <div className={`relative ${className}`} ref={dropdownRef}>
        <Button
          variant="outline"
          size="sm"
          onClick={() => setIsOpen(!isOpen)}
          className="flex items-center space-x-2"
        >
          {showFlag && <span>{currentLanguage.flag}</span>}
          <span className="text-sm">
            {showNativeName ? currentLanguage.native : currentLanguage.english}
          </span>
          <ChevronDown className={`w-4 h-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
        </Button>

        <AnimatePresence>
          {isOpen && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="absolute top-full right-0 mt-2 w-80 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50 max-h-96 overflow-hidden"
            >
              <LanguageList
                locales={filteredLocales}
                currentLocale={locale}
                onSelect={handleLanguageChange}
                showFlag={showFlag}
                showNativeName={showNativeName}
                searchQuery={searchQuery}
                onSearchChange={setSearchQuery}
                showSearch={true}
                popularLocales={sortedPopularLocales}
                otherLocales={otherLocales}
              />
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    )
  }

  // Default variant
  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      <Button
        variant="outline"
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 min-w-[140px] justify-between"
      >
        <div className="flex items-center space-x-2">
          <Globe className="w-4 h-4" />
          {showFlag && <span>{currentLanguage.flag}</span>}
          <span className="text-sm">
            {showNativeName ? currentLanguage.native : currentLanguage.english}
          </span>
        </div>
        <ChevronDown className={`w-4 h-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </Button>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="absolute top-full right-0 mt-2 w-96 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50 max-h-[500px] overflow-hidden"
          >
            <LanguageList
              locales={filteredLocales}
              currentLocale={locale}
              onSelect={handleLanguageChange}
              showFlag={showFlag}
              showNativeName={showNativeName}
              searchQuery={searchQuery}
              onSearchChange={setSearchQuery}
              showSearch={true}
              popularLocales={sortedPopularLocales}
              otherLocales={otherLocales}
            />
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

interface LanguageListProps {
  locales: Locale[]
  currentLocale: Locale
  onSelect: (locale: Locale) => void
  showFlag: boolean
  showNativeName: boolean
  searchQuery: string
  onSearchChange: (query: string) => void
  showSearch?: boolean
  popularLocales?: Locale[]
  otherLocales?: Locale[]
}

function LanguageList({
  locales,
  currentLocale,
  onSelect,
  showFlag,
  showNativeName,
  searchQuery,
  onSearchChange,
  showSearch = false,
  popularLocales = [],
  otherLocales = []
}: LanguageListProps) {
  return (
    <div className="flex flex-col">
      {showSearch && (
        <div className="p-3 border-b border-gray-200 dark:border-gray-700">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              type="text"
              placeholder="Search languages..."
              value={searchQuery}
              onChange={(e) => onSearchChange(e.target.value)}
              className="pl-10 text-sm"
            />
          </div>
        </div>
      )}

      <div className="overflow-y-auto max-h-80">
        {popularLocales.length > 0 && (
          <div>
            <div className="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wide bg-gray-50 dark:bg-gray-700">
              Popular Languages
            </div>
            {popularLocales.map((loc) => (
              <LanguageItem
                key={loc}
                locale={loc}
                isSelected={loc === currentLocale}
                onSelect={onSelect}
                showFlag={showFlag}
                showNativeName={showNativeName}
              />
            ))}
          </div>
        )}

        {otherLocales.length > 0 && (
          <div>
            {popularLocales.length > 0 && (
              <div className="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wide bg-gray-50 dark:bg-gray-700">
                All Languages
              </div>
            )}
            {otherLocales.map((loc) => (
              <LanguageItem
                key={loc}
                locale={loc}
                isSelected={loc === currentLocale}
                onSelect={onSelect}
                showFlag={showFlag}
                showNativeName={showNativeName}
              />
            ))}
          </div>
        )}

        {popularLocales.length === 0 && otherLocales.length === 0 && (
          <div className="p-4 text-center text-gray-500">
            No languages found
          </div>
        )}
      </div>
    </div>
  )
}

interface LanguageItemProps {
  locale: Locale
  isSelected: boolean
  onSelect: (locale: Locale) => void
  showFlag: boolean
  showNativeName: boolean
}

function LanguageItem({
  locale,
  isSelected,
  onSelect,
  showFlag,
  showNativeName
}: LanguageItemProps) {
  const language = languageNames[locale]

  return (
    <motion.button
      whileHover={{ backgroundColor: 'rgba(0, 0, 0, 0.05)' }}
      onClick={() => onSelect(locale)}
      className={`w-full px-3 py-2 text-left flex items-center justify-between hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ${
        isSelected ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400' : ''
      }`}
    >
      <div className="flex items-center space-x-3">
        {showFlag && <span className="text-lg">{language.flag}</span>}
        <div className="flex flex-col">
          <span className="text-sm font-medium">
            {showNativeName ? language.native : language.english}
          </span>
          {showNativeName && language.native !== language.english && (
            <span className="text-xs text-gray-500">{language.english}</span>
          )}
        </div>
      </div>
      {isSelected && <Check className="w-4 h-4" />}
    </motion.button>
  )
}
