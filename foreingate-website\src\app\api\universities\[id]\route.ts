import { NextRequest, NextResponse } from 'next/server'
import { readFile } from 'fs/promises'
import { existsSync } from 'fs'
import path from 'path'

const DATA_DIR = path.join(process.cwd(), 'data')
const UNIVERSITIES_FILE = path.join(DATA_DIR, 'universities.json')

async function readUniversities() {
  try {
    if (!existsSync(UNIVERSITIES_FILE)) {
      return []
    }
    const data = await readFile(UNIVERSITIES_FILE, 'utf-8')
    return JSON.parse(data)
  } catch (error) {
    console.error('Error reading universities:', error)
    return []
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const universities = await readUniversities()
    const university = universities.find((uni: any) => 
      uni.id === params.id || uni.slug === params.id
    )

    if (!university) {
      return NextResponse.json(
        { success: false, error: 'University not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: university
    })

  } catch (error) {
    console.error('University API error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch university' },
      { status: 500 }
    )
  }
}
