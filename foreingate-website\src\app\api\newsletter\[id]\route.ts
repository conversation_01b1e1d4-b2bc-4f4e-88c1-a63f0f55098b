import { NextRequest, NextResponse } from 'next/server'
import { readFile, writeFile } from 'fs/promises'
import { existsSync } from 'fs'
import path from 'path'

const DATA_DIR = path.join(process.cwd(), 'data')
const NEWSLETTER_FILE = path.join(DATA_DIR, 'newsletter.json')

async function readSubscribers() {
  try {
    if (!existsSync(NEWSLETTER_FILE)) {
      return []
    }
    const data = await readFile(NEWSLETTER_FILE, 'utf-8')
    return JSON.parse(data)
  } catch (error) {
    console.error('Error reading newsletter subscribers:', error)
    return []
  }
}

async function writeSubscribers(subscribers: any[]) {
  try {
    await writeFile(NEWSLETTER_FILE, JSON.stringify(subscribers, null, 2))
  } catch (error) {
    console.error('Error writing newsletter subscribers:', error)
    throw error
  }
}

// GET individual subscriber
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const subscribers = await readSubscribers()
    const subscriber = subscribers.find((sub: any) => 
      sub.id === params.id || sub.email === params.id
    )

    if (!subscriber) {
      return NextResponse.json(
        { success: false, error: 'Subscriber not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: subscriber
    })

  } catch (error) {
    console.error('Subscriber fetch error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch subscriber' },
      { status: 500 }
    )
  }
}

// PUT - Update subscriber (Admin only)
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json()
    const subscribers = await readSubscribers()
    
    const subscriberIndex = subscribers.findIndex((sub: any) => 
      sub.id === params.id || sub.email === params.id
    )

    if (subscriberIndex === -1) {
      return NextResponse.json(
        { success: false, error: 'Subscriber not found' },
        { status: 404 }
      )
    }

    // Update subscriber
    const updatedSubscriber = {
      ...subscribers[subscriberIndex],
      ...body,
      updatedAt: new Date().toISOString()
    }

    subscribers[subscriberIndex] = updatedSubscriber

    await writeSubscribers(subscribers)

    return NextResponse.json({
      success: true,
      data: updatedSubscriber,
      message: 'Subscriber updated successfully'
    })

  } catch (error) {
    console.error('Subscriber update error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to update subscriber' },
      { status: 500 }
    )
  }
}

// DELETE subscriber (Admin only)
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const subscribers = await readSubscribers()
    
    const subscriberIndex = subscribers.findIndex((sub: any) => 
      sub.id === params.id || sub.email === params.id
    )

    if (subscriberIndex === -1) {
      return NextResponse.json(
        { success: false, error: 'Subscriber not found' },
        { status: 404 }
      )
    }

    const deletedSubscriber = subscribers[subscriberIndex]
    subscribers.splice(subscriberIndex, 1)

    await writeSubscribers(subscribers)

    return NextResponse.json({
      success: true,
      message: 'Subscriber deleted successfully',
      data: deletedSubscriber
    })

  } catch (error) {
    console.error('Subscriber deletion error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to delete subscriber' },
      { status: 500 }
    )
  }
}
