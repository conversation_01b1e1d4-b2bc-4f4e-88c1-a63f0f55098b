// Internationalization configuration
export const defaultLocale = 'en'

export const locales = [
  'en',    // English
  'tr',    // Turkish
  'ar',    // Arabic
  'fr',    // French
  'es',    // Spanish
  'de',    // German
  'ru',    // Russian
  'zh',    // Chinese
  'ja',    // Japanese
  'ko',    // Korean
  'pt',    // Portuguese
  'it',    // Italian
  'nl',    // Dutch
  'sv',    // Swedish
  'no',    // Norwegian
  'da',    // Danish
  'fi',    // Finnish
  'pl',    // Polish
  'cs',    // Czech
  'hu',    // Hungarian
  'ro',    // Romanian
  'bg',    // Bulgarian
  'hr',    // Croatian
  'sk',    // Slovak
  'sl',    // Slovenian
  'et',    // Estonian
  'lv',    // Latvian
  'lt',    // Lithuanian
  'mt',    // Maltese
  'cy',    // Welsh
  'ga',    // Irish
  'is',    // Icelandic
  'mk',    // Macedonian
  'sq',    // Albanian
  'sr',    // Serbian
  'bs',    // Bosnian
  'me',    // Montenegrin
  'uk',    // Ukrainian
  'be',    // Belarusian
  'kk',    // Kazakh
  'ky',    // Kyrgyz
  'uz',    // Uzbek
  'tg',    // Tajik
  'tm',    // Turkmen
  'mn',    // Mongolian
  'ka',    // Georgian
  'hy',    // Armenian
  'az',    // Azerbaijani
  'fa',    // Persian
  'ur',    // Urdu
  'hi',    // Hindi
  'bn',    // Bengali
  'ta',    // Tamil
  'te',    // Telugu
  'ml',    // Malayalam
  'kn',    // Kannada
  'gu',    // Gujarati
  'pa',    // Punjabi
  'or',    // Odia
  'as',    // Assamese
  'ne',    // Nepali
  'si',    // Sinhala
  'my',    // Burmese
  'th',    // Thai
  'lo',    // Lao
  'km',    // Khmer
  'vi',    // Vietnamese
  'id',    // Indonesian
  'ms',    // Malay
  'tl',    // Filipino
  'haw',   // Hawaiian
  'mi',    // Maori
  'sm',    // Samoan
  'to',    // Tongan
  'fj',    // Fijian
  'sw',    // Swahili
  'am',    // Amharic
  'ti',    // Tigrinya
  'om',    // Oromo
  'so',    // Somali
  'rw',    // Kinyarwanda
  'rn',    // Kirundi
  'lg',    // Luganda
  'ak',    // Akan
  'tw',    // Twi
  'yo',    // Yoruba
  'ig',    // Igbo
  'ha',    // Hausa
  'ff',    // Fulah
  'wo',    // Wolof
  'sn',    // Shona
  'zu',    // Zulu
  'xh',    // Xhosa
  'af',    // Afrikaans
  'st',    // Sesotho
  'tn',    // Setswana
  'ss',    // Siswati
  've',    // Tshivenda
  'ts',    // Xitsonga
  'nr',    // Ndebele
  'he',    // Hebrew
  'yi',    // Yiddish
  'jv',    // Javanese
  'su',    // Sundanese
  'mad',   // Madurese
  'ban',   // Balinese
  'bug',   // Buginese
  'mak',   // Makasar
  'min',   // Minangkabau
  'ace',   // Acehnese
  'bjn',   // Banjar
  'bbc',   // Batak Toba
  'nij',   // Ngaju
  'rej',   // Rejang
  'sas',   // Sasak
  'tet',   // Tetum
] as const

export type Locale = typeof locales[number]

export const languageNames: Record<Locale, { native: string; english: string; flag: string }> = {
  en: { native: 'English', english: 'English', flag: '🇺🇸' },
  tr: { native: 'Türkçe', english: 'Turkish', flag: '🇹🇷' },
  ar: { native: 'العربية', english: 'Arabic', flag: '🇸🇦' },
  fr: { native: 'Français', english: 'French', flag: '🇫🇷' },
  es: { native: 'Español', english: 'Spanish', flag: '🇪🇸' },
  de: { native: 'Deutsch', english: 'German', flag: '🇩🇪' },
  ru: { native: 'Русский', english: 'Russian', flag: '🇷🇺' },
  zh: { native: '中文', english: 'Chinese', flag: '🇨🇳' },
  ja: { native: '日本語', english: 'Japanese', flag: '🇯🇵' },
  ko: { native: '한국어', english: 'Korean', flag: '🇰🇷' },
  pt: { native: 'Português', english: 'Portuguese', flag: '🇵🇹' },
  it: { native: 'Italiano', english: 'Italian', flag: '🇮🇹' },
  nl: { native: 'Nederlands', english: 'Dutch', flag: '🇳🇱' },
  sv: { native: 'Svenska', english: 'Swedish', flag: '🇸🇪' },
  no: { native: 'Norsk', english: 'Norwegian', flag: '🇳🇴' },
  da: { native: 'Dansk', english: 'Danish', flag: '🇩🇰' },
  fi: { native: 'Suomi', english: 'Finnish', flag: '🇫🇮' },
  pl: { native: 'Polski', english: 'Polish', flag: '🇵🇱' },
  cs: { native: 'Čeština', english: 'Czech', flag: '🇨🇿' },
  hu: { native: 'Magyar', english: 'Hungarian', flag: '🇭🇺' },
  ro: { native: 'Română', english: 'Romanian', flag: '🇷🇴' },
  bg: { native: 'Български', english: 'Bulgarian', flag: '🇧🇬' },
  hr: { native: 'Hrvatski', english: 'Croatian', flag: '🇭🇷' },
  sk: { native: 'Slovenčina', english: 'Slovak', flag: '🇸🇰' },
  sl: { native: 'Slovenščina', english: 'Slovenian', flag: '🇸🇮' },
  et: { native: 'Eesti', english: 'Estonian', flag: '🇪🇪' },
  lv: { native: 'Latviešu', english: 'Latvian', flag: '🇱🇻' },
  lt: { native: 'Lietuvių', english: 'Lithuanian', flag: '🇱🇹' },
  mt: { native: 'Malti', english: 'Maltese', flag: '🇲🇹' },
  cy: { native: 'Cymraeg', english: 'Welsh', flag: '🏴󠁧󠁢󠁷󠁬󠁳󠁿' },
  ga: { native: 'Gaeilge', english: 'Irish', flag: '🇮🇪' },
  is: { native: 'Íslenska', english: 'Icelandic', flag: '🇮🇸' },
  mk: { native: 'Македонски', english: 'Macedonian', flag: '🇲🇰' },
  sq: { native: 'Shqip', english: 'Albanian', flag: '🇦🇱' },
  sr: { native: 'Српски', english: 'Serbian', flag: '🇷🇸' },
  bs: { native: 'Bosanski', english: 'Bosnian', flag: '🇧🇦' },
  me: { native: 'Crnogorski', english: 'Montenegrin', flag: '🇲🇪' },
  uk: { native: 'Українська', english: 'Ukrainian', flag: '🇺🇦' },
  be: { native: 'Беларуская', english: 'Belarusian', flag: '🇧🇾' },
  kk: { native: 'Қазақша', english: 'Kazakh', flag: '🇰🇿' },
  ky: { native: 'Кыргызча', english: 'Kyrgyz', flag: '🇰🇬' },
  uz: { native: 'Oʻzbekcha', english: 'Uzbek', flag: '🇺🇿' },
  tg: { native: 'Тоҷикӣ', english: 'Tajik', flag: '🇹🇯' },
  tm: { native: 'Türkmençe', english: 'Turkmen', flag: '🇹🇲' },
  mn: { native: 'Монгол', english: 'Mongolian', flag: '🇲🇳' },
  ka: { native: 'ქართული', english: 'Georgian', flag: '🇬🇪' },
  hy: { native: 'Հայերեն', english: 'Armenian', flag: '🇦🇲' },
  az: { native: 'Azərbaycan', english: 'Azerbaijani', flag: '🇦🇿' },
  fa: { native: 'فارسی', english: 'Persian', flag: '🇮🇷' },
  ur: { native: 'اردو', english: 'Urdu', flag: '🇵🇰' },
  hi: { native: 'हिन्दी', english: 'Hindi', flag: '🇮🇳' },
  bn: { native: 'বাংলা', english: 'Bengali', flag: '🇧🇩' },
  ta: { native: 'தமிழ்', english: 'Tamil', flag: '🇱🇰' },
  te: { native: 'తెలుగు', english: 'Telugu', flag: '🇮🇳' },
  ml: { native: 'മലയാളം', english: 'Malayalam', flag: '🇮🇳' },
  kn: { native: 'ಕನ್ನಡ', english: 'Kannada', flag: '🇮🇳' },
  gu: { native: 'ગુજરાતી', english: 'Gujarati', flag: '🇮🇳' },
  pa: { native: 'ਪੰਜਾਬੀ', english: 'Punjabi', flag: '🇮🇳' },
  or: { native: 'ଓଡ଼ିଆ', english: 'Odia', flag: '🇮🇳' },
  as: { native: 'অসমীয়া', english: 'Assamese', flag: '🇮🇳' },
  ne: { native: 'नेपाली', english: 'Nepali', flag: '🇳🇵' },
  si: { native: 'සිංහල', english: 'Sinhala', flag: '🇱🇰' },
  my: { native: 'မြန်မာ', english: 'Burmese', flag: '🇲🇲' },
  th: { native: 'ไทย', english: 'Thai', flag: '🇹🇭' },
  lo: { native: 'ລາວ', english: 'Lao', flag: '🇱🇦' },
  km: { native: 'ខ្មែរ', english: 'Khmer', flag: '🇰🇭' },
  vi: { native: 'Tiếng Việt', english: 'Vietnamese', flag: '🇻🇳' },
  id: { native: 'Bahasa Indonesia', english: 'Indonesian', flag: '🇮🇩' },
  ms: { native: 'Bahasa Melayu', english: 'Malay', flag: '🇲🇾' },
  tl: { native: 'Filipino', english: 'Filipino', flag: '🇵🇭' },
  haw: { native: 'ʻŌlelo Hawaiʻi', english: 'Hawaiian', flag: '🏝️' },
  mi: { native: 'Te Reo Māori', english: 'Maori', flag: '🇳🇿' },
  sm: { native: 'Gagana Samoa', english: 'Samoan', flag: '🇼🇸' },
  to: { native: 'Lea Fakatonga', english: 'Tongan', flag: '🇹🇴' },
  fj: { native: 'Na Vosa Vakaviti', english: 'Fijian', flag: '🇫🇯' },
  sw: { native: 'Kiswahili', english: 'Swahili', flag: '🇰🇪' },
  am: { native: 'አማርኛ', english: 'Amharic', flag: '🇪🇹' },
  ti: { native: 'ትግርኛ', english: 'Tigrinya', flag: '🇪🇷' },
  om: { native: 'Afaan Oromoo', english: 'Oromo', flag: '🇪🇹' },
  so: { native: 'Soomaali', english: 'Somali', flag: '🇸🇴' },
  rw: { native: 'Ikinyarwanda', english: 'Kinyarwanda', flag: '🇷🇼' },
  rn: { native: 'Ikirundi', english: 'Kirundi', flag: '🇧🇮' },
  lg: { native: 'Luganda', english: 'Luganda', flag: '🇺🇬' },
  ak: { native: 'Akan', english: 'Akan', flag: '🇬🇭' },
  tw: { native: 'Twi', english: 'Twi', flag: '🇬🇭' },
  yo: { native: 'Yorùbá', english: 'Yoruba', flag: '🇳🇬' },
  ig: { native: 'Igbo', english: 'Igbo', flag: '🇳🇬' },
  ha: { native: 'Hausa', english: 'Hausa', flag: '🇳🇬' },
  ff: { native: 'Fulfulde', english: 'Fulah', flag: '🇸🇳' },
  wo: { native: 'Wolof', english: 'Wolof', flag: '🇸🇳' },
  sn: { native: 'ChiShona', english: 'Shona', flag: '🇿🇼' },
  zu: { native: 'IsiZulu', english: 'Zulu', flag: '🇿🇦' },
  xh: { native: 'IsiXhosa', english: 'Xhosa', flag: '🇿🇦' },
  af: { native: 'Afrikaans', english: 'Afrikaans', flag: '🇿🇦' },
  st: { native: 'Sesotho', english: 'Sesotho', flag: '🇱🇸' },
  tn: { native: 'Setswana', english: 'Setswana', flag: '🇧🇼' },
  ss: { native: 'SiSwati', english: 'Siswati', flag: '🇸🇿' },
  ve: { native: 'Tshivenḓa', english: 'Tshivenda', flag: '🇿🇦' },
  ts: { native: 'Xitsonga', english: 'Xitsonga', flag: '🇿🇦' },
  nr: { native: 'IsiNdebele', english: 'Ndebele', flag: '🇿🇦' },
  he: { native: 'עברית', english: 'Hebrew', flag: '🇮🇱' },
  yi: { native: 'ייִדיש', english: 'Yiddish', flag: '🏳️' },
  jv: { native: 'Basa Jawa', english: 'Javanese', flag: '🇮🇩' },
  su: { native: 'Basa Sunda', english: 'Sundanese', flag: '🇮🇩' },
  mad: { native: 'Basa Madhura', english: 'Madurese', flag: '🇮🇩' },
  ban: { native: 'Basa Bali', english: 'Balinese', flag: '🇮🇩' },
  bug: { native: 'Basa Ugi', english: 'Buginese', flag: '🇮🇩' },
  mak: { native: 'Basa Mangkasara', english: 'Makasar', flag: '🇮🇩' },
  min: { native: 'Baso Minangkabau', english: 'Minangkabau', flag: '🇮🇩' },
  ace: { native: 'Bahsa Acèh', english: 'Acehnese', flag: '🇮🇩' },
  bjn: { native: 'Bahasa Banjar', english: 'Banjar', flag: '🇮🇩' },
  bbc: { native: 'Hata Batak Toba', english: 'Batak Toba', flag: '🇮🇩' },
  nij: { native: 'Bahasa Ngaju', english: 'Ngaju', flag: '🇮🇩' },
  rej: { native: 'Bahasa Rejang', english: 'Rejang', flag: '🇮🇩' },
  sas: { native: 'Basa Sasak', english: 'Sasak', flag: '🇮🇩' },
  tet: { native: 'Tetun', english: 'Tetum', flag: '🇹🇱' },
}

export function isValidLocale(locale: string): locale is Locale {
  return locales.includes(locale as Locale)
}

export function getLocaleDirection(locale: Locale): 'ltr' | 'rtl' {
  const rtlLocales: Locale[] = ['ar', 'fa', 'ur', 'he', 'yi']
  return rtlLocales.includes(locale) ? 'rtl' : 'ltr'
}

export function getLocaleFontFamily(locale: Locale): string {
  const fontFamilies: Partial<Record<Locale, string>> = {
    ar: 'Noto Sans Arabic, Arial, sans-serif',
    fa: 'Noto Sans Arabic, Arial, sans-serif',
    ur: 'Noto Sans Arabic, Arial, sans-serif',
    he: 'Noto Sans Hebrew, Arial, sans-serif',
    zh: 'Noto Sans SC, Arial, sans-serif',
    ja: 'Noto Sans JP, Arial, sans-serif',
    ko: 'Noto Sans KR, Arial, sans-serif',
    th: 'Noto Sans Thai, Arial, sans-serif',
    hi: 'Noto Sans Devanagari, Arial, sans-serif',
    bn: 'Noto Sans Bengali, Arial, sans-serif',
    ta: 'Noto Sans Tamil, Arial, sans-serif',
    te: 'Noto Sans Telugu, Arial, sans-serif',
    ml: 'Noto Sans Malayalam, Arial, sans-serif',
    kn: 'Noto Sans Kannada, Arial, sans-serif',
    gu: 'Noto Sans Gujarati, Arial, sans-serif',
    pa: 'Noto Sans Gurmukhi, Arial, sans-serif',
    or: 'Noto Sans Oriya, Arial, sans-serif',
    as: 'Noto Sans Bengali, Arial, sans-serif',
    ne: 'Noto Sans Devanagari, Arial, sans-serif',
    si: 'Noto Sans Sinhala, Arial, sans-serif',
    my: 'Noto Sans Myanmar, Arial, sans-serif',
    lo: 'Noto Sans Lao, Arial, sans-serif',
    km: 'Noto Sans Khmer, Arial, sans-serif',
    vi: 'Noto Sans Vietnamese, Arial, sans-serif',
    ka: 'Noto Sans Georgian, Arial, sans-serif',
    hy: 'Noto Sans Armenian, Arial, sans-serif',
    am: 'Noto Sans Ethiopic, Arial, sans-serif',
    ti: 'Noto Sans Ethiopic, Arial, sans-serif',
  }
  
  return fontFamilies[locale] || 'Inter, system-ui, sans-serif'
}
