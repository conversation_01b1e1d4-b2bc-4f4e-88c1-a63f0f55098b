'use client'

import { motion } from 'framer-motion'
import Link from 'next/link'
import { Phone, Mail, MapPin, Clock, MessageCircle, Calendar } from 'lucide-react'
import { Button } from '@/components/ui/button'

export function ContactHeroSection() {
  return (
    <section className="relative min-h-[60vh] flex items-center justify-center overflow-hidden bg-gradient-to-br from-primary/10 via-background to-secondary/10">
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-primary/10 rounded-full blur-3xl animate-pulse-slow" />
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-secondary/10 rounded-full blur-3xl animate-pulse-slow" />
      </div>

      <div className="container relative z-10 px-4 py-20">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Content */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            className="space-y-8"
          >
            {/* Badge */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2, duration: 0.6 }}
              className="inline-flex items-center space-x-2 bg-primary/10 text-primary px-4 py-2 rounded-full text-sm font-medium"
            >
              <MessageCircle className="w-4 h-4" />
              <span>Expert Educational Consultants</span>
            </motion.div>

            {/* Main Heading */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4, duration: 0.8 }}
              className="space-y-4"
            >
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight">
                Let's Start Your{' '}
                <span className="gradient-text">Educational Journey</span>
              </h1>
              <p className="text-xl text-muted-foreground max-w-2xl">
                Ready to take the next step? Our expert team is here to guide you through
                every aspect of studying in Northern Cyprus. Get personalized advice and
                support tailored to your goals.
              </p>
            </motion.div>

            {/* Quick Contact Options */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6, duration: 0.8 }}
              className="grid md:grid-cols-2 gap-4"
            >
              <div className="flex items-center space-x-3 p-4 bg-background/50 rounded-lg border">
                <Phone className="w-5 h-5 text-primary" />
                <div>
                  <div className="font-medium">+90 ************</div>
                  <div className="text-sm text-muted-foreground">Call us now</div>
                </div>
              </div>
              <div className="flex items-center space-x-3 p-4 bg-background/50 rounded-lg border">
                <Mail className="w-5 h-5 text-primary" />
                <div>
                  <div className="font-medium"><EMAIL></div>
                  <div className="text-sm text-muted-foreground">Email us</div>
                </div>
              </div>
            </motion.div>

            {/* CTA Buttons */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.8, duration: 0.8 }}
              className="flex flex-col sm:flex-row gap-4"
            >
              <Button size="lg" className="group" asChild>
                <Link href="#contact-form">
                  Send Message
                  <MessageCircle className="ml-2 h-4 w-4 transition-transform group-hover:scale-110" />
                </Link>
              </Button>
              <Button size="lg" variant="outline" asChild>
                <Link href="/apply">
                  <Calendar className="mr-2 h-4 w-4" />
                  Book Consultation
                </Link>
              </Button>
            </motion.div>
          </motion.div>

          {/* Contact Information Card */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="relative"
          >
            <div className="bg-background rounded-2xl p-8 shadow-xl border">
              <h3 className="text-2xl font-bold mb-6">Get in Touch</h3>

              <div className="space-y-6">
                {/* Office Hours */}
                <div className="flex items-start space-x-4">
                  <Clock className="w-6 h-6 text-primary mt-1" />
                  <div>
                    <h4 className="font-semibold mb-1">Office Hours</h4>
                    <div className="text-muted-foreground space-y-1">
                      <div>Monday - Friday: 9:00 AM - 6:00 PM</div>
                      <div>Saturday: 10:00 AM - 4:00 PM</div>
                      <div>Sunday: Closed</div>
                    </div>
                  </div>
                </div>

                {/* Location */}
                <div className="flex items-start space-x-4">
                  <MapPin className="w-6 h-6 text-primary mt-1" />
                  <div>
                    <h4 className="font-semibold mb-1">Our Location</h4>
                    <div className="text-muted-foreground">
                      <div>Foreingate Group Headquarters</div>
                      <div>Nicosia, Northern Cyprus</div>
                      <div>TRNC via Mersin 10, Turkey</div>
                    </div>
                  </div>
                </div>

                {/* Response Time */}
                <div className="bg-gradient-to-r from-primary/5 to-secondary/5 rounded-lg p-4">
                  <h4 className="font-semibold mb-2">Quick Response Guarantee</h4>
                  <p className="text-sm text-muted-foreground">
                    We respond to all inquiries within 24 hours. For urgent matters,
                    call our hotline for immediate assistance.
                  </p>
                </div>

                {/* Social Proof */}
                <div className="grid grid-cols-3 gap-4 text-center pt-4 border-t">
                  <div>
                    <div className="text-2xl font-bold text-primary">5,000+</div>
                    <div className="text-xs text-muted-foreground">Students Helped</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-primary">98%</div>
                    <div className="text-xs text-muted-foreground">Success Rate</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-primary">24/7</div>
                    <div className="text-xs text-muted-foreground">Support</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Floating Elements */}
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 1, duration: 0.6 }}
              className="absolute -top-4 -right-4 w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center"
            >
              <MessageCircle className="w-8 h-8 text-primary" />
            </motion.div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}

export function ContactFormSection() {
  return (
    <PlaceholderSection
      title="Contact Form"
      description="Send us a message and we'll get back to you within 24 hours."
      className="bg-muted/30"
    />
  )
}

export function ContactInfoSection() {
  return (
    <PlaceholderSection
      title="Contact Information"
      description="Find our office locations, phone numbers, and business hours."
    />
  )
}
