import { NextRequest, NextResponse } from 'next/server'
import { readFile, writeFile, mkdir } from 'fs/promises'
import { existsSync } from 'fs'
import path from 'path'

const DATA_DIR = path.join(process.cwd(), 'data')
const CONTACT_FILE = path.join(DATA_DIR, 'contact-inquiries.json')

// Ensure data directory exists
async function ensureDataDir() {
  if (!existsSync(DATA_DIR)) {
    await mkdir(DATA_DIR, { recursive: true })
  }
}

// Read contact inquiries from file
async function readContactInquiries() {
  try {
    await ensureDataDir()
    if (!existsSync(CONTACT_FILE)) {
      return []
    }
    const data = await readFile(CONTACT_FILE, 'utf-8')
    return JSON.parse(data)
  } catch (error) {
    console.error('Error reading contact inquiries:', error)
    return []
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status') // 'new', 'in_progress', 'responded', 'closed'
    const search = searchParams.get('search')
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const sortBy = searchParams.get('sortBy') || 'createdAt'
    const sortOrder = searchParams.get('sortOrder') || 'desc'

    let inquiries = await readContactInquiries()

    // Apply filters
    if (status && status !== 'all') {
      inquiries = inquiries.filter((inquiry: any) => 
        inquiry.status?.toLowerCase() === status.toLowerCase()
      )
    }

    if (search) {
      inquiries = inquiries.filter((inquiry: any) =>
        inquiry.name?.toLowerCase().includes(search.toLowerCase()) ||
        inquiry.email?.toLowerCase().includes(search.toLowerCase()) ||
        inquiry.subject?.toLowerCase().includes(search.toLowerCase()) ||
        inquiry.inquiryId?.toLowerCase().includes(search.toLowerCase())
      )
    }

    // Sort inquiries
    inquiries.sort((a: any, b: any) => {
      let aValue = a[sortBy]
      let bValue = b[sortBy]
      
      if (sortBy === 'createdAt' || sortBy === 'updatedAt') {
        aValue = new Date(aValue).getTime()
        bValue = new Date(bValue).getTime()
      }
      
      if (sortOrder === 'desc') {
        return bValue > aValue ? 1 : -1
      } else {
        return aValue > bValue ? 1 : -1
      }
    })

    // Pagination
    const total = inquiries.length
    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit
    const paginatedInquiries = inquiries.slice(startIndex, endIndex)

    // Calculate statistics
    const stats = {
      total: inquiries.length,
      new: inquiries.filter((inquiry: any) => !inquiry.status || inquiry.status === 'NEW').length,
      inProgress: inquiries.filter((inquiry: any) => inquiry.status === 'IN_PROGRESS').length,
      responded: inquiries.filter((inquiry: any) => inquiry.status === 'RESPONDED').length,
      closed: inquiries.filter((inquiry: any) => inquiry.status === 'CLOSED').length,
      thisWeek: inquiries.filter((inquiry: any) => {
        const inquiryDate = new Date(inquiry.createdAt)
        const weekAgo = new Date()
        weekAgo.setDate(weekAgo.getDate() - 7)
        return inquiryDate > weekAgo
      }).length
    }

    return NextResponse.json({
      success: true,
      data: paginatedInquiries,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: endIndex < total,
        hasPrev: page > 1
      },
      stats
    })

  } catch (error) {
    console.error('Contact inquiries API error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch contact inquiries' },
      { status: 500 }
    )
  }
}
