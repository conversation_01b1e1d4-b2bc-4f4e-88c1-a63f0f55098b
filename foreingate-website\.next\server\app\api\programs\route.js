/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/programs/route";
exports.ids = ["app/api/programs/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fprograms%2Froute&page=%2Fapi%2Fprograms%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fprograms%2Froute.ts&appDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fprograms%2Froute&page=%2Fapi%2Fprograms%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fprograms%2Froute.ts&appDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Nidhal_Documents_augment_projects_foreingate_groupe_foreingate_website_src_app_api_programs_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/programs/route.ts */ \"(rsc)/./src/app/api/programs/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/programs/route\",\n        pathname: \"/api/programs\",\n        filename: \"route\",\n        bundlePath: \"app/api/programs/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\api\\\\programs\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Nidhal_Documents_augment_projects_foreingate_groupe_foreingate_website_src_app_api_programs_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fprograms%2Froute&page=%2Fapi%2Fprograms%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fprograms%2Froute.ts&appDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/programs/route.ts":
/*!***************************************!*\
  !*** ./src/app/api/programs/route.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fs/promises */ \"fs/promises\");\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fs_promises__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nconst DATA_DIR = path__WEBPACK_IMPORTED_MODULE_3___default().join(process.cwd(), 'data');\nconst PROGRAMS_FILE = path__WEBPACK_IMPORTED_MODULE_3___default().join(DATA_DIR, 'programs.json');\n// Ensure data directory exists\nasync function ensureDataDir() {\n    if (!(0,fs__WEBPACK_IMPORTED_MODULE_2__.existsSync)(DATA_DIR)) {\n        await (0,fs_promises__WEBPACK_IMPORTED_MODULE_1__.mkdir)(DATA_DIR, {\n            recursive: true\n        });\n    }\n}\n// Read programs from file\nasync function readPrograms() {\n    try {\n        await ensureDataDir();\n        if (!(0,fs__WEBPACK_IMPORTED_MODULE_2__.existsSync)(PROGRAMS_FILE)) {\n            // Create initial data if file doesn't exist\n            const initialData = [\n                {\n                    id: '1',\n                    name: 'Computer Engineering',\n                    slug: 'computer-engineering',\n                    universityId: '1',\n                    universityName: 'Eastern Mediterranean University',\n                    degree: 'Bachelor',\n                    field: 'Engineering',\n                    duration: '4 years',\n                    language: 'English',\n                    tuition: 4500,\n                    currency: 'USD',\n                    description: 'Comprehensive computer engineering program covering software development, hardware design, and emerging technologies.',\n                    requirements: [\n                        'High school diploma with mathematics and physics',\n                        'IELTS 6.0 or TOEFL 79',\n                        'SAT or equivalent entrance exam'\n                    ],\n                    courses: [\n                        'Programming Fundamentals',\n                        'Data Structures and Algorithms',\n                        'Computer Architecture',\n                        'Software Engineering',\n                        'Database Systems',\n                        'Artificial Intelligence'\n                    ],\n                    careerOpportunities: [\n                        'Software Developer',\n                        'Systems Engineer',\n                        'Data Scientist',\n                        'Cybersecurity Specialist',\n                        'AI/ML Engineer'\n                    ],\n                    accreditation: 'ABET',\n                    intakes: [\n                        'Fall',\n                        'Spring'\n                    ],\n                    createdAt: new Date().toISOString(),\n                    updatedAt: new Date().toISOString()\n                },\n                {\n                    id: '2',\n                    name: 'Business Administration',\n                    slug: 'business-administration',\n                    universityId: '2',\n                    universityName: 'Near East University',\n                    degree: 'Bachelor',\n                    field: 'Business',\n                    duration: '4 years',\n                    language: 'English',\n                    tuition: 3800,\n                    currency: 'USD',\n                    description: 'Comprehensive business program preparing students for leadership roles in global markets.',\n                    requirements: [\n                        'High school diploma',\n                        'IELTS 6.0 or TOEFL 79',\n                        'Mathematics background preferred'\n                    ],\n                    courses: [\n                        'Principles of Management',\n                        'Marketing Management',\n                        'Financial Accounting',\n                        'Business Statistics',\n                        'International Business',\n                        'Strategic Management'\n                    ],\n                    careerOpportunities: [\n                        'Business Manager',\n                        'Marketing Specialist',\n                        'Financial Analyst',\n                        'Consultant',\n                        'Entrepreneur'\n                    ],\n                    accreditation: 'AACSB',\n                    intakes: [\n                        'Fall',\n                        'Spring',\n                        'Summer'\n                    ],\n                    createdAt: new Date().toISOString(),\n                    updatedAt: new Date().toISOString()\n                },\n                {\n                    id: '3',\n                    name: 'Medicine',\n                    slug: 'medicine',\n                    universityId: '2',\n                    universityName: 'Near East University',\n                    degree: 'Doctor',\n                    field: 'Medicine',\n                    duration: '6 years',\n                    language: 'English',\n                    tuition: 12000,\n                    currency: 'USD',\n                    description: 'Comprehensive medical education program with hands-on clinical experience and modern facilities.',\n                    requirements: [\n                        'High school diploma with biology, chemistry, physics',\n                        'IELTS 6.5 or TOEFL 85',\n                        'Medical entrance exam',\n                        'Interview'\n                    ],\n                    courses: [\n                        'Anatomy',\n                        'Physiology',\n                        'Biochemistry',\n                        'Pathology',\n                        'Pharmacology',\n                        'Clinical Medicine'\n                    ],\n                    careerOpportunities: [\n                        'General Practitioner',\n                        'Specialist Doctor',\n                        'Surgeon',\n                        'Researcher',\n                        'Medical Consultant'\n                    ],\n                    accreditation: 'WHO/WFME',\n                    intakes: [\n                        'Fall'\n                    ],\n                    createdAt: new Date().toISOString(),\n                    updatedAt: new Date().toISOString()\n                },\n                {\n                    id: '4',\n                    name: 'Architecture',\n                    slug: 'architecture',\n                    universityId: '1',\n                    universityName: 'Eastern Mediterranean University',\n                    degree: 'Bachelor',\n                    field: 'Architecture',\n                    duration: '5 years',\n                    language: 'English',\n                    tuition: 5200,\n                    currency: 'USD',\n                    description: 'Creative and technical architecture program combining design theory with practical application.',\n                    requirements: [\n                        'High school diploma',\n                        'IELTS 6.0 or TOEFL 79',\n                        'Portfolio submission',\n                        'Aptitude test'\n                    ],\n                    courses: [\n                        'Architectural Design',\n                        'Building Technology',\n                        'Urban Planning',\n                        'Structural Systems',\n                        'Environmental Design',\n                        'Digital Design'\n                    ],\n                    careerOpportunities: [\n                        'Architect',\n                        'Urban Planner',\n                        'Interior Designer',\n                        'Project Manager',\n                        'Design Consultant'\n                    ],\n                    accreditation: 'NAAB',\n                    intakes: [\n                        'Fall'\n                    ],\n                    createdAt: new Date().toISOString(),\n                    updatedAt: new Date().toISOString()\n                }\n            ];\n            await (0,fs_promises__WEBPACK_IMPORTED_MODULE_1__.writeFile)(PROGRAMS_FILE, JSON.stringify(initialData, null, 2));\n            return initialData;\n        }\n        const data = await (0,fs_promises__WEBPACK_IMPORTED_MODULE_1__.readFile)(PROGRAMS_FILE, 'utf-8');\n        return JSON.parse(data);\n    } catch (error) {\n        console.error('Error reading programs:', error);\n        return [];\n    }\n}\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const degrees = searchParams.get('degrees')?.split(',');\n        const fields = searchParams.get('fields')?.split(',');\n        const languages = searchParams.get('languages')?.split(',');\n        const universities = searchParams.get('universities')?.split(',');\n        const search = searchParams.get('search');\n        const page = parseInt(searchParams.get('page') || '1');\n        const limit = parseInt(searchParams.get('limit') || '10');\n        let programs = await readPrograms();\n        // Apply filters\n        if (degrees && degrees.length > 0) {\n            programs = programs.filter((program)=>degrees.includes(program.degree));\n        }\n        if (fields && fields.length > 0) {\n            programs = programs.filter((program)=>fields.includes(program.field));\n        }\n        if (languages && languages.length > 0) {\n            programs = programs.filter((program)=>languages.includes(program.language));\n        }\n        if (universities && universities.length > 0) {\n            programs = programs.filter((program)=>universities.includes(program.universityId) || universities.includes(program.universityName));\n        }\n        if (search) {\n            programs = programs.filter((program)=>program.name.toLowerCase().includes(search.toLowerCase()) || program.description.toLowerCase().includes(search.toLowerCase()) || program.field.toLowerCase().includes(search.toLowerCase()));\n        }\n        // Pagination\n        const total = programs.length;\n        const startIndex = (page - 1) * limit;\n        const endIndex = startIndex + limit;\n        const paginatedPrograms = programs.slice(startIndex, endIndex);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: paginatedPrograms,\n            pagination: {\n                page,\n                limit,\n                total,\n                totalPages: Math.ceil(total / limit),\n                hasNext: endIndex < total,\n                hasPrev: page > 1\n            }\n        });\n    } catch (error) {\n        console.error('Programs API error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Failed to fetch programs'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/programs/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "fs/promises":
/*!******************************!*\
  !*** external "fs/promises" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("fs/promises");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fprograms%2Froute&page=%2Fapi%2Fprograms%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fprograms%2Froute.ts&appDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();