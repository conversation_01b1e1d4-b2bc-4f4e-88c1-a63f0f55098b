'use client'

import { useState, useEffect } from 'react'

interface ApiResponse<T> {
  success: boolean
  data: T
  pagination?: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
  error?: string
}

// Universities hooks
export function useUniversities(filters?: {
  location?: string
  minTuition?: number
  maxTuition?: number
  search?: string
}) {
  const [data, setData] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchUniversities = async () => {
      try {
        setLoading(true)
        setError(null)

        const params = new URLSearchParams()
        if (filters?.location) params.append('location', filters.location)
        if (filters?.minTuition) params.append('minTuition', filters.minTuition.toString())
        if (filters?.maxTuition) params.append('maxTuition', filters.maxTuition.toString())
        if (filters?.search) params.append('search', filters.search)

        const response = await fetch(`/api/universities?${params.toString()}`)
        const result: ApiResponse<any[]> = await response.json()

        if (result.success) {
          setData(result.data)
        } else {
          setError('Failed to fetch universities')
        }
      } catch (err) {
        setError('Network error occurred')
        console.error('Universities fetch error:', err)
      } finally {
        setLoading(false)
      }
    }

    fetchUniversities()
  }, [filters?.location, filters?.minTuition, filters?.maxTuition, filters?.search])

  return { data, loading, error }
}

export function useUniversity(id: string) {
  const [data, setData] = useState<any | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchUniversity = async () => {
      try {
        setLoading(true)
        setError(null)

        const response = await fetch(`/api/universities/${id}`)
        const result: ApiResponse<any> = await response.json()

        if (result.success) {
          setData(result.data)
        } else {
          setError('Failed to fetch university')
        }
      } catch (err) {
        setError('Network error occurred')
        console.error('University fetch error:', err)
      } finally {
        setLoading(false)
      }
    }

    if (id) {
      fetchUniversity()
    }
  }, [id])

  return { data, loading, error }
}

// Programs hooks
export function usePrograms(filters?: {
  degrees?: string[]
  fields?: string[]
  languages?: string[]
  universities?: string[]
  search?: string
  page?: number
  limit?: number
}) {
  const [data, setData] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [pagination, setPagination] = useState<any>(null)

  useEffect(() => {
    const fetchPrograms = async () => {
      try {
        setLoading(true)
        setError(null)

        const params = new URLSearchParams()
        if (filters?.degrees) params.append('degrees', filters.degrees.join(','))
        if (filters?.fields) params.append('fields', filters.fields.join(','))
        if (filters?.languages) params.append('languages', filters.languages.join(','))
        if (filters?.universities) params.append('universities', filters.universities.join(','))
        if (filters?.search) params.append('search', filters.search)
        if (filters?.page) params.append('page', filters.page.toString())
        if (filters?.limit) params.append('limit', filters.limit.toString())

        const response = await fetch(`/api/programs?${params.toString()}`)
        const result: ApiResponse<any[]> = await response.json()

        if (result.success) {
          setData(result.data)
          setPagination(result.pagination)
        } else {
          setError('Failed to fetch programs')
        }
      } catch (err) {
        setError('Network error occurred')
        console.error('Programs fetch error:', err)
      } finally {
        setLoading(false)
      }
    }

    fetchPrograms()
  }, [
    filters?.degrees?.join(','),
    filters?.fields?.join(','),
    filters?.languages?.join(','),
    filters?.universities?.join(','),
    filters?.search,
    filters?.page,
    filters?.limit
  ])

  return { data, loading, error, pagination }
}

export function useProgram(id: string) {
  const [data, setData] = useState<any | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchProgram = async () => {
      try {
        setLoading(true)
        setError(null)

        const response = await fetch(`/api/programs/${id}`)
        const result: ApiResponse<any> = await response.json()

        if (result.success) {
          setData(result.data)
        } else {
          setError('Failed to fetch program')
        }
      } catch (err) {
        setError('Network error occurred')
        console.error('Program fetch error:', err)
      } finally {
        setLoading(false)
      }
    }

    if (id) {
      fetchProgram()
    }
  }, [id])

  return { data, loading, error }
}

// Blog hooks
export function useBlogPosts(filters?: {
  category?: string
  tags?: string[]
  search?: string
  featured?: boolean
  page?: number
  limit?: number
}) {
  const [data, setData] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [pagination, setPagination] = useState<any>(null)

  useEffect(() => {
    const fetchBlogPosts = async () => {
      try {
        setLoading(true)
        setError(null)

        const params = new URLSearchParams()
        if (filters?.category) params.append('category', filters.category)
        if (filters?.tags) params.append('tags', filters.tags.join(','))
        if (filters?.search) params.append('search', filters.search)
        if (filters?.featured) params.append('featured', 'true')
        if (filters?.page) params.append('page', filters.page.toString())
        if (filters?.limit) params.append('limit', filters.limit.toString())

        const response = await fetch(`/api/blog?${params.toString()}`)
        const result: ApiResponse<any[]> = await response.json()

        if (result.success) {
          setData(result.data)
          setPagination(result.pagination)
        } else {
          setError('Failed to fetch blog posts')
        }
      } catch (err) {
        setError('Network error occurred')
        console.error('Blog posts fetch error:', err)
      } finally {
        setLoading(false)
      }
    }

    fetchBlogPosts()
  }, [
    filters?.category,
    filters?.tags?.join(','),
    filters?.search,
    filters?.featured,
    filters?.page,
    filters?.limit
  ])

  return { data, loading, error, pagination }
}

export function useBlogPost(id: string) {
  const [data, setData] = useState<any | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchBlogPost = async () => {
      try {
        setLoading(true)
        setError(null)

        const response = await fetch(`/api/blog/${id}`)
        const result: ApiResponse<any> = await response.json()

        if (result.success) {
          setData(result.data)
        } else {
          setError('Failed to fetch blog post')
        }
      } catch (err) {
        setError('Network error occurred')
        console.error('Blog post fetch error:', err)
      } finally {
        setLoading(false)
      }
    }

    if (id) {
      fetchBlogPost()
    }
  }, [id])

  return { data, loading, error }
}

// Testimonials hooks
export function useTestimonials(filters?: {
  university?: string
  program?: string
  country?: string
  featured?: boolean
  rating?: number
  page?: number
  limit?: number
}) {
  const [data, setData] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [pagination, setPagination] = useState<any>(null)

  useEffect(() => {
    const fetchTestimonials = async () => {
      try {
        setLoading(true)
        setError(null)

        const params = new URLSearchParams()
        if (filters?.university) params.append('university', filters.university)
        if (filters?.program) params.append('program', filters.program)
        if (filters?.country) params.append('country', filters.country)
        if (filters?.featured) params.append('featured', 'true')
        if (filters?.rating) params.append('rating', filters.rating.toString())
        if (filters?.page) params.append('page', filters.page.toString())
        if (filters?.limit) params.append('limit', filters.limit.toString())

        const response = await fetch(`/api/testimonials?${params.toString()}`)
        const result: ApiResponse<any[]> = await response.json()

        if (result.success) {
          setData(result.data)
          setPagination(result.pagination)
        } else {
          setError('Failed to fetch testimonials')
        }
      } catch (err) {
        setError('Network error occurred')
        console.error('Testimonials fetch error:', err)
      } finally {
        setLoading(false)
      }
    }

    fetchTestimonials()
  }, [
    filters?.university,
    filters?.program,
    filters?.country,
    filters?.featured,
    filters?.rating,
    filters?.page,
    filters?.limit
  ])

  return { data, loading, error, pagination }
}
