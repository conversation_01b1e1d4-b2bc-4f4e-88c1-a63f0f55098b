/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/testimonials/route";
exports.ids = ["app/api/testimonials/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftestimonials%2Froute&page=%2Fapi%2Ftestimonials%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftestimonials%2Froute.ts&appDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftestimonials%2Froute&page=%2Fapi%2Ftestimonials%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftestimonials%2Froute.ts&appDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Nidhal_Documents_augment_projects_foreingate_groupe_foreingate_website_src_app_api_testimonials_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/testimonials/route.ts */ \"(rsc)/./src/app/api/testimonials/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/testimonials/route\",\n        pathname: \"/api/testimonials\",\n        filename: \"route\",\n        bundlePath: \"app/api/testimonials/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\api\\\\testimonials\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Nidhal_Documents_augment_projects_foreingate_groupe_foreingate_website_src_app_api_testimonials_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftestimonials%2Froute&page=%2Fapi%2Ftestimonials%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftestimonials%2Froute.ts&appDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/testimonials/route.ts":
/*!*******************************************!*\
  !*** ./src/app/api/testimonials/route.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fs/promises */ \"fs/promises\");\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fs_promises__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nconst DATA_DIR = path__WEBPACK_IMPORTED_MODULE_3___default().join(process.cwd(), 'data');\nconst TESTIMONIALS_FILE = path__WEBPACK_IMPORTED_MODULE_3___default().join(DATA_DIR, 'testimonials.json');\n// Ensure data directory exists\nasync function ensureDataDir() {\n    if (!(0,fs__WEBPACK_IMPORTED_MODULE_2__.existsSync)(DATA_DIR)) {\n        await (0,fs_promises__WEBPACK_IMPORTED_MODULE_1__.mkdir)(DATA_DIR, {\n            recursive: true\n        });\n    }\n}\n// Read testimonials from file\nasync function readTestimonials() {\n    try {\n        await ensureDataDir();\n        if (!(0,fs__WEBPACK_IMPORTED_MODULE_2__.existsSync)(TESTIMONIALS_FILE)) {\n            // Create initial data if file doesn't exist\n            const initialData = [\n                {\n                    id: '1',\n                    name: 'Ahmed Hassan',\n                    program: 'Computer Engineering',\n                    university: 'Eastern Mediterranean University',\n                    graduationYear: 2023,\n                    country: 'Egypt',\n                    image: '/images/testimonials/ahmed-hassan.jpg',\n                    rating: 5,\n                    content: 'Foreingate Group made my dream of studying abroad a reality. Their support throughout the entire process was exceptional - from university selection to visa assistance. I\\'m now pursuing my Master\\'s in Computer Engineering and couldn\\'t be happier!',\n                    featured: true,\n                    status: 'approved',\n                    createdAt: new Date(Date.now() - 86400000 * 30).toISOString(),\n                    updatedAt: new Date(Date.now() - 86400000 * 30).toISOString()\n                },\n                {\n                    id: '2',\n                    name: 'Maria Rodriguez',\n                    program: 'Business Administration',\n                    university: 'Near East University',\n                    graduationYear: 2022,\n                    country: 'Spain',\n                    image: '/images/testimonials/maria-rodriguez.jpg',\n                    rating: 5,\n                    content: 'The team at Foreingate was incredibly professional and helpful. They guided me through every step of the application process and helped me secure a scholarship. The university experience has been amazing!',\n                    featured: true,\n                    status: 'approved',\n                    createdAt: new Date(Date.now() - 86400000 * 45).toISOString(),\n                    updatedAt: new Date(Date.now() - 86400000 * 45).toISOString()\n                },\n                {\n                    id: '3',\n                    name: 'David Chen',\n                    program: 'Medicine',\n                    university: 'Near East University',\n                    graduationYear: 2024,\n                    country: 'China',\n                    image: '/images/testimonials/david-chen.jpg',\n                    rating: 5,\n                    content: 'Studying medicine in Northern Cyprus has been an incredible journey. Foreingate helped me navigate the complex application process and provided ongoing support. The education quality is world-class!',\n                    featured: false,\n                    status: 'approved',\n                    createdAt: new Date(Date.now() - 86400000 * 60).toISOString(),\n                    updatedAt: new Date(Date.now() - 86400000 * 60).toISOString()\n                },\n                {\n                    id: '4',\n                    name: 'Fatima Al-Zahra',\n                    program: 'Architecture',\n                    university: 'Eastern Mediterranean University',\n                    graduationYear: 2023,\n                    country: 'UAE',\n                    image: '/images/testimonials/fatima-alzahra.jpg',\n                    rating: 5,\n                    content: 'The architecture program exceeded my expectations. Foreingate\\'s counselors helped me choose the perfect university and program. The facilities and faculty are outstanding!',\n                    featured: false,\n                    status: 'approved',\n                    createdAt: new Date(Date.now() - 86400000 * 75).toISOString(),\n                    updatedAt: new Date(Date.now() - 86400000 * 75).toISOString()\n                },\n                {\n                    id: '5',\n                    name: 'James Wilson',\n                    program: 'Psychology',\n                    university: 'Cyprus International University',\n                    graduationYear: 2022,\n                    country: 'UK',\n                    image: '/images/testimonials/james-wilson.jpg',\n                    rating: 4,\n                    content: 'Great experience overall. The support from Foreingate was comprehensive, and the university provided excellent education. Would definitely recommend to other students!',\n                    featured: false,\n                    status: 'approved',\n                    createdAt: new Date(Date.now() - 86400000 * 90).toISOString(),\n                    updatedAt: new Date(Date.now() - 86400000 * 90).toISOString()\n                },\n                {\n                    id: '6',\n                    name: 'Priya Sharma',\n                    program: 'International Relations',\n                    university: 'Near East University',\n                    graduationYear: 2023,\n                    country: 'India',\n                    image: '/images/testimonials/priya-sharma.jpg',\n                    rating: 5,\n                    content: 'Foreingate made the entire process seamless. From application to arrival, they were there every step of the way. The international environment at the university is fantastic!',\n                    featured: false,\n                    status: 'approved',\n                    createdAt: new Date(Date.now() - 86400000 * 105).toISOString(),\n                    updatedAt: new Date(Date.now() - 86400000 * 105).toISOString()\n                },\n                {\n                    id: '7',\n                    name: 'Omar Khalil',\n                    program: 'Civil Engineering',\n                    university: 'Eastern Mediterranean University',\n                    graduationYear: 2024,\n                    country: 'Jordan',\n                    image: '/images/testimonials/omar-khalil.jpg',\n                    rating: 5,\n                    content: 'The engineering program is top-notch with modern labs and experienced faculty. Foreingate\\'s guidance was invaluable in choosing the right program and university.',\n                    featured: false,\n                    status: 'approved',\n                    createdAt: new Date(Date.now() - 86400000 * 120).toISOString(),\n                    updatedAt: new Date(Date.now() - 86400000 * 120).toISOString()\n                },\n                {\n                    id: '8',\n                    name: 'Elena Popovic',\n                    program: 'Dentistry',\n                    university: 'Near East University',\n                    graduationYear: 2023,\n                    country: 'Serbia',\n                    image: '/images/testimonials/elena-popovic.jpg',\n                    rating: 5,\n                    content: 'The dentistry program provided excellent hands-on experience. Foreingate\\'s support team was always available to help with any questions or concerns.',\n                    featured: false,\n                    status: 'approved',\n                    createdAt: new Date(Date.now() - 86400000 * 135).toISOString(),\n                    updatedAt: new Date(Date.now() - 86400000 * 135).toISOString()\n                }\n            ];\n            await (0,fs_promises__WEBPACK_IMPORTED_MODULE_1__.writeFile)(TESTIMONIALS_FILE, JSON.stringify(initialData, null, 2));\n            return initialData;\n        }\n        const data = await (0,fs_promises__WEBPACK_IMPORTED_MODULE_1__.readFile)(TESTIMONIALS_FILE, 'utf-8');\n        return JSON.parse(data);\n    } catch (error) {\n        console.error('Error reading testimonials:', error);\n        return [];\n    }\n}\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const university = searchParams.get('university');\n        const program = searchParams.get('program');\n        const country = searchParams.get('country');\n        const featured = searchParams.get('featured') === 'true';\n        const rating = searchParams.get('rating');\n        const page = parseInt(searchParams.get('page') || '1');\n        const limit = parseInt(searchParams.get('limit') || '10');\n        let testimonials = await readTestimonials();\n        // Apply filters\n        if (university) {\n            testimonials = testimonials.filter((testimonial)=>testimonial.university.toLowerCase().includes(university.toLowerCase()));\n        }\n        if (program) {\n            testimonials = testimonials.filter((testimonial)=>testimonial.program.toLowerCase().includes(program.toLowerCase()));\n        }\n        if (country) {\n            testimonials = testimonials.filter((testimonial)=>testimonial.country.toLowerCase().includes(country.toLowerCase()));\n        }\n        if (featured) {\n            testimonials = testimonials.filter((testimonial)=>testimonial.featured === true);\n        }\n        if (rating) {\n            testimonials = testimonials.filter((testimonial)=>testimonial.rating >= parseInt(rating));\n        }\n        // Sort by creation date (newest first)\n        testimonials.sort((a, b)=>new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());\n        // Pagination\n        const total = testimonials.length;\n        const startIndex = (page - 1) * limit;\n        const endIndex = startIndex + limit;\n        const paginatedTestimonials = testimonials.slice(startIndex, endIndex);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: paginatedTestimonials,\n            pagination: {\n                page,\n                limit,\n                total,\n                totalPages: Math.ceil(total / limit),\n                hasNext: endIndex < total,\n                hasPrev: page > 1\n            }\n        });\n    } catch (error) {\n        console.error('Testimonials API error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Failed to fetch testimonials'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/testimonials/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "fs/promises":
/*!******************************!*\
  !*** external "fs/promises" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("fs/promises");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftestimonials%2Froute&page=%2Fapi%2Ftestimonials%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftestimonials%2Froute.ts&appDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();