import { NextRequest, NextResponse } from 'next/server'
import { readFile, writeFile, mkdir } from 'fs/promises'
import { existsSync } from 'fs'
import path from 'path'

const DATA_DIR = path.join(process.cwd(), 'data')
const BLOG_FILE = path.join(DATA_DIR, 'blog-posts.json')

// Ensure data directory exists
async function ensureDataDir() {
  if (!existsSync(DATA_DIR)) {
    await mkdir(DATA_DIR, { recursive: true })
  }
}

// Read blog posts from file
async function readBlogPosts() {
  try {
    await ensureDataDir()
    if (!existsSync(BLOG_FILE)) {
      // Create initial data if file doesn't exist
      const initialData = [
        {
          id: '1',
          title: 'Complete Guide to Studying in Northern Cyprus 2024',
          slug: 'complete-guide-studying-northern-cyprus-2024',
          excerpt: 'Everything you need to know about universities, programs, costs, and student life in Northern Cyprus. Updated for 2024 admissions.',
          content: 'Northern Cyprus has emerged as one of the most attractive destinations for international students seeking quality education at affordable costs...',
          author: 'Foreingate Team',
          authorImage: '/images/authors/foreingate-team.jpg',
          image: '/images/blog/northern-cyprus-guide-2024.jpg',
          category: 'Study Guide',
          tags: ['Northern Cyprus', 'Study Abroad', 'Universities', 'Guide'],
          publishedAt: new Date(Date.now() - 86400000 * 2).toISOString(), // 2 days ago
          readTime: 8,
          featured: true,
          status: 'published',
          views: 1250,
          createdAt: new Date(Date.now() - 86400000 * 5).toISOString(),
          updatedAt: new Date(Date.now() - 86400000 * 2).toISOString()
        },
        {
          id: '2',
          title: 'Top 10 Universities in Northern Cyprus for International Students',
          slug: 'top-10-universities-northern-cyprus-international-students',
          excerpt: 'Discover the best universities in Northern Cyprus offering world-class education, modern facilities, and international recognition.',
          content: 'Choosing the right university is crucial for your academic and career success. Here are the top 10 universities in Northern Cyprus...',
          author: 'Sarah Johnson',
          authorImage: '/images/authors/sarah-johnson.jpg',
          image: '/images/blog/top-universities-northern-cyprus.jpg',
          category: 'Universities',
          tags: ['Universities', 'Rankings', 'Northern Cyprus', 'Education'],
          publishedAt: new Date(Date.now() - 86400000 * 5).toISOString(), // 5 days ago
          readTime: 6,
          featured: false,
          status: 'published',
          views: 890,
          createdAt: new Date(Date.now() - 86400000 * 7).toISOString(),
          updatedAt: new Date(Date.now() - 86400000 * 5).toISOString()
        },
        {
          id: '3',
          title: 'Student Visa Requirements for Northern Cyprus: Complete Checklist',
          slug: 'student-visa-requirements-northern-cyprus-checklist',
          excerpt: 'Step-by-step guide to obtaining a student visa for Northern Cyprus, including required documents and application process.',
          content: 'Getting a student visa for Northern Cyprus is straightforward if you have all the required documents...',
          author: 'Michael Chen',
          authorImage: '/images/authors/michael-chen.jpg',
          image: '/images/blog/student-visa-northern-cyprus.jpg',
          category: 'Visa Guide',
          tags: ['Visa', 'Student Visa', 'Northern Cyprus', 'Documentation'],
          publishedAt: new Date(Date.now() - 86400000 * 7).toISOString(), // 1 week ago
          readTime: 5,
          featured: false,
          status: 'published',
          views: 654,
          createdAt: new Date(Date.now() - 86400000 * 10).toISOString(),
          updatedAt: new Date(Date.now() - 86400000 * 7).toISOString()
        },
        {
          id: '4',
          title: 'Cost of Living for Students in Northern Cyprus',
          slug: 'cost-of-living-students-northern-cyprus',
          excerpt: 'Detailed breakdown of living costs including accommodation, food, transportation, and entertainment for students in Northern Cyprus.',
          content: 'Northern Cyprus offers one of the most affordable study destinations in Europe. Here\'s a detailed breakdown of costs...',
          author: 'Emma Davis',
          authorImage: '/images/authors/emma-davis.jpg',
          image: '/images/blog/cost-of-living-northern-cyprus.jpg',
          category: 'Student Life',
          tags: ['Cost of Living', 'Budget', 'Student Life', 'Northern Cyprus'],
          publishedAt: new Date(Date.now() - 86400000 * 10).toISOString(), // 10 days ago
          readTime: 7,
          featured: false,
          status: 'published',
          views: 432,
          createdAt: new Date(Date.now() - 86400000 * 12).toISOString(),
          updatedAt: new Date(Date.now() - 86400000 * 10).toISOString()
        },
        {
          id: '5',
          title: 'Engineering Programs in Northern Cyprus: Your Gateway to Success',
          slug: 'engineering-programs-northern-cyprus-gateway-success',
          excerpt: 'Explore the best engineering programs in Northern Cyprus with ABET accreditation and excellent career prospects.',
          content: 'Engineering education in Northern Cyprus has gained international recognition for its quality and innovation...',
          author: 'Dr. Ahmed Yilmaz',
          authorImage: '/images/authors/ahmed-yilmaz.jpg',
          image: '/images/blog/engineering-programs-northern-cyprus.jpg',
          category: 'Programs',
          tags: ['Engineering', 'Programs', 'ABET', 'Career'],
          publishedAt: new Date(Date.now() - 86400000 * 14).toISOString(), // 2 weeks ago
          readTime: 9,
          featured: false,
          status: 'published',
          views: 567,
          createdAt: new Date(Date.now() - 86400000 * 16).toISOString(),
          updatedAt: new Date(Date.now() - 86400000 * 14).toISOString()
        }
      ]
      await writeFile(BLOG_FILE, JSON.stringify(initialData, null, 2))
      return initialData
    }
    const data = await readFile(BLOG_FILE, 'utf-8')
    return JSON.parse(data)
  } catch (error) {
    console.error('Error reading blog posts:', error)
    return []
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const category = searchParams.get('category')
    const tags = searchParams.get('tags')?.split(',')
    const search = searchParams.get('search')
    const featured = searchParams.get('featured') === 'true'
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')

    let posts = await readBlogPosts()

    // Apply filters
    if (category) {
      posts = posts.filter((post: any) => 
        post.category.toLowerCase() === category.toLowerCase()
      )
    }

    if (tags && tags.length > 0) {
      posts = posts.filter((post: any) => 
        tags.some(tag => post.tags.includes(tag))
      )
    }

    if (featured) {
      posts = posts.filter((post: any) => post.featured === true)
    }

    if (search) {
      posts = posts.filter((post: any) =>
        post.title.toLowerCase().includes(search.toLowerCase()) ||
        post.excerpt.toLowerCase().includes(search.toLowerCase()) ||
        post.content.toLowerCase().includes(search.toLowerCase())
      )
    }

    // Sort by published date (newest first)
    posts.sort((a: any, b: any) => 
      new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime()
    )

    // Pagination
    const total = posts.length
    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit
    const paginatedPosts = posts.slice(startIndex, endIndex)

    return NextResponse.json({
      success: true,
      data: paginatedPosts,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: endIndex < total,
        hasPrev: page > 1
      }
    })

  } catch (error) {
    console.error('Blog API error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch blog posts' },
      { status: 500 }
    )
  }
}
