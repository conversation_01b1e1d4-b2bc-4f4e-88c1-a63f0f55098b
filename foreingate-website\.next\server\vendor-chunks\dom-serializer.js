"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/dom-serializer";
exports.ids = ["vendor-chunks/dom-serializer"];
exports.modules = {

/***/ "(rsc)/../node_modules/dom-serializer/lib/esm/foreignNames.js":
/*!**************************************************************!*\
  !*** ../node_modules/dom-serializer/lib/esm/foreignNames.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   attributeNames: () => (/* binding */ attributeNames),\n/* harmony export */   elementNames: () => (/* binding */ elementNames)\n/* harmony export */ });\nconst elementNames = new Map([\n    \"altGlyph\",\n    \"altGlyphDef\",\n    \"altGlyphItem\",\n    \"animateColor\",\n    \"animateMotion\",\n    \"animateTransform\",\n    \"clipPath\",\n    \"feBlend\",\n    \"feColorMatrix\",\n    \"feComponentTransfer\",\n    \"feComposite\",\n    \"feConvolveMatrix\",\n    \"feDiffuseLighting\",\n    \"feDisplacementMap\",\n    \"feDistantLight\",\n    \"feDropShadow\",\n    \"feFlood\",\n    \"feFuncA\",\n    \"feFuncB\",\n    \"feFuncG\",\n    \"feFuncR\",\n    \"feGaussianBlur\",\n    \"feImage\",\n    \"feMerge\",\n    \"feMergeNode\",\n    \"feMorphology\",\n    \"feOffset\",\n    \"fePointLight\",\n    \"feSpecularLighting\",\n    \"feSpotLight\",\n    \"feTile\",\n    \"feTurbulence\",\n    \"foreignObject\",\n    \"glyphRef\",\n    \"linearGradient\",\n    \"radialGradient\",\n    \"textPath\",\n].map((val) => [val.toLowerCase(), val]));\nconst attributeNames = new Map([\n    \"definitionURL\",\n    \"attributeName\",\n    \"attributeType\",\n    \"baseFrequency\",\n    \"baseProfile\",\n    \"calcMode\",\n    \"clipPathUnits\",\n    \"diffuseConstant\",\n    \"edgeMode\",\n    \"filterUnits\",\n    \"glyphRef\",\n    \"gradientTransform\",\n    \"gradientUnits\",\n    \"kernelMatrix\",\n    \"kernelUnitLength\",\n    \"keyPoints\",\n    \"keySplines\",\n    \"keyTimes\",\n    \"lengthAdjust\",\n    \"limitingConeAngle\",\n    \"markerHeight\",\n    \"markerUnits\",\n    \"markerWidth\",\n    \"maskContentUnits\",\n    \"maskUnits\",\n    \"numOctaves\",\n    \"pathLength\",\n    \"patternContentUnits\",\n    \"patternTransform\",\n    \"patternUnits\",\n    \"pointsAtX\",\n    \"pointsAtY\",\n    \"pointsAtZ\",\n    \"preserveAlpha\",\n    \"preserveAspectRatio\",\n    \"primitiveUnits\",\n    \"refX\",\n    \"refY\",\n    \"repeatCount\",\n    \"repeatDur\",\n    \"requiredExtensions\",\n    \"requiredFeatures\",\n    \"specularConstant\",\n    \"specularExponent\",\n    \"spreadMethod\",\n    \"startOffset\",\n    \"stdDeviation\",\n    \"stitchTiles\",\n    \"surfaceScale\",\n    \"systemLanguage\",\n    \"tableValues\",\n    \"targetX\",\n    \"targetY\",\n    \"textLength\",\n    \"viewBox\",\n    \"viewTarget\",\n    \"xChannelSelector\",\n    \"yChannelSelector\",\n    \"zoomAndPan\",\n].map((val) => [val.toLowerCase(), val]));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/dom-serializer/lib/esm/foreignNames.js\n");

/***/ }),

/***/ "(rsc)/../node_modules/dom-serializer/lib/esm/index.js":
/*!*******************************************************!*\
  !*** ../node_modules/dom-serializer/lib/esm/index.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   render: () => (/* binding */ render)\n/* harmony export */ });\n/* harmony import */ var domelementtype__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! domelementtype */ \"(rsc)/../node_modules/domelementtype/lib/esm/index.js\");\n/* harmony import */ var entities__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! entities */ \"(rsc)/../node_modules/entities/lib/esm/index.js\");\n/* harmony import */ var _foreignNames_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./foreignNames.js */ \"(rsc)/../node_modules/dom-serializer/lib/esm/foreignNames.js\");\n/*\n * Module dependencies\n */\n\n\n/**\n * Mixed-case SVG and MathML tags & attributes\n * recognized by the HTML parser.\n *\n * @see https://html.spec.whatwg.org/multipage/parsing.html#parsing-main-inforeign\n */\n\nconst unencodedElements = new Set([\n    \"style\",\n    \"script\",\n    \"xmp\",\n    \"iframe\",\n    \"noembed\",\n    \"noframes\",\n    \"plaintext\",\n    \"noscript\",\n]);\nfunction replaceQuotes(value) {\n    return value.replace(/\"/g, \"&quot;\");\n}\n/**\n * Format attributes\n */\nfunction formatAttributes(attributes, opts) {\n    var _a;\n    if (!attributes)\n        return;\n    const encode = ((_a = opts.encodeEntities) !== null && _a !== void 0 ? _a : opts.decodeEntities) === false\n        ? replaceQuotes\n        : opts.xmlMode || opts.encodeEntities !== \"utf8\"\n            ? entities__WEBPACK_IMPORTED_MODULE_1__.encodeXML\n            : entities__WEBPACK_IMPORTED_MODULE_1__.escapeAttribute;\n    return Object.keys(attributes)\n        .map((key) => {\n        var _a, _b;\n        const value = (_a = attributes[key]) !== null && _a !== void 0 ? _a : \"\";\n        if (opts.xmlMode === \"foreign\") {\n            /* Fix up mixed-case attribute names */\n            key = (_b = _foreignNames_js__WEBPACK_IMPORTED_MODULE_2__.attributeNames.get(key)) !== null && _b !== void 0 ? _b : key;\n        }\n        if (!opts.emptyAttrs && !opts.xmlMode && value === \"\") {\n            return key;\n        }\n        return `${key}=\"${encode(value)}\"`;\n    })\n        .join(\" \");\n}\n/**\n * Self-enclosing tags\n */\nconst singleTag = new Set([\n    \"area\",\n    \"base\",\n    \"basefont\",\n    \"br\",\n    \"col\",\n    \"command\",\n    \"embed\",\n    \"frame\",\n    \"hr\",\n    \"img\",\n    \"input\",\n    \"isindex\",\n    \"keygen\",\n    \"link\",\n    \"meta\",\n    \"param\",\n    \"source\",\n    \"track\",\n    \"wbr\",\n]);\n/**\n * Renders a DOM node or an array of DOM nodes to a string.\n *\n * Can be thought of as the equivalent of the `outerHTML` of the passed node(s).\n *\n * @param node Node to be rendered.\n * @param options Changes serialization behavior\n */\nfunction render(node, options = {}) {\n    const nodes = \"length\" in node ? node : [node];\n    let output = \"\";\n    for (let i = 0; i < nodes.length; i++) {\n        output += renderNode(nodes[i], options);\n    }\n    return output;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (render);\nfunction renderNode(node, options) {\n    switch (node.type) {\n        case domelementtype__WEBPACK_IMPORTED_MODULE_0__.Root:\n            return render(node.children, options);\n        // @ts-expect-error We don't use `Doctype` yet\n        case domelementtype__WEBPACK_IMPORTED_MODULE_0__.Doctype:\n        case domelementtype__WEBPACK_IMPORTED_MODULE_0__.Directive:\n            return renderDirective(node);\n        case domelementtype__WEBPACK_IMPORTED_MODULE_0__.Comment:\n            return renderComment(node);\n        case domelementtype__WEBPACK_IMPORTED_MODULE_0__.CDATA:\n            return renderCdata(node);\n        case domelementtype__WEBPACK_IMPORTED_MODULE_0__.Script:\n        case domelementtype__WEBPACK_IMPORTED_MODULE_0__.Style:\n        case domelementtype__WEBPACK_IMPORTED_MODULE_0__.Tag:\n            return renderTag(node, options);\n        case domelementtype__WEBPACK_IMPORTED_MODULE_0__.Text:\n            return renderText(node, options);\n    }\n}\nconst foreignModeIntegrationPoints = new Set([\n    \"mi\",\n    \"mo\",\n    \"mn\",\n    \"ms\",\n    \"mtext\",\n    \"annotation-xml\",\n    \"foreignObject\",\n    \"desc\",\n    \"title\",\n]);\nconst foreignElements = new Set([\"svg\", \"math\"]);\nfunction renderTag(elem, opts) {\n    var _a;\n    // Handle SVG / MathML in HTML\n    if (opts.xmlMode === \"foreign\") {\n        /* Fix up mixed-case element names */\n        elem.name = (_a = _foreignNames_js__WEBPACK_IMPORTED_MODULE_2__.elementNames.get(elem.name)) !== null && _a !== void 0 ? _a : elem.name;\n        /* Exit foreign mode at integration points */\n        if (elem.parent &&\n            foreignModeIntegrationPoints.has(elem.parent.name)) {\n            opts = { ...opts, xmlMode: false };\n        }\n    }\n    if (!opts.xmlMode && foreignElements.has(elem.name)) {\n        opts = { ...opts, xmlMode: \"foreign\" };\n    }\n    let tag = `<${elem.name}`;\n    const attribs = formatAttributes(elem.attribs, opts);\n    if (attribs) {\n        tag += ` ${attribs}`;\n    }\n    if (elem.children.length === 0 &&\n        (opts.xmlMode\n            ? // In XML mode or foreign mode, and user hasn't explicitly turned off self-closing tags\n                opts.selfClosingTags !== false\n            : // User explicitly asked for self-closing tags, even in HTML mode\n                opts.selfClosingTags && singleTag.has(elem.name))) {\n        if (!opts.xmlMode)\n            tag += \" \";\n        tag += \"/>\";\n    }\n    else {\n        tag += \">\";\n        if (elem.children.length > 0) {\n            tag += render(elem.children, opts);\n        }\n        if (opts.xmlMode || !singleTag.has(elem.name)) {\n            tag += `</${elem.name}>`;\n        }\n    }\n    return tag;\n}\nfunction renderDirective(elem) {\n    return `<${elem.data}>`;\n}\nfunction renderText(elem, opts) {\n    var _a;\n    let data = elem.data || \"\";\n    // If entities weren't decoded, no need to encode them back\n    if (((_a = opts.encodeEntities) !== null && _a !== void 0 ? _a : opts.decodeEntities) !== false &&\n        !(!opts.xmlMode &&\n            elem.parent &&\n            unencodedElements.has(elem.parent.name))) {\n        data =\n            opts.xmlMode || opts.encodeEntities !== \"utf8\"\n                ? (0,entities__WEBPACK_IMPORTED_MODULE_1__.encodeXML)(data)\n                : (0,entities__WEBPACK_IMPORTED_MODULE_1__.escapeText)(data);\n    }\n    return data;\n}\nfunction renderCdata(elem) {\n    return `<![CDATA[${elem.children[0].data}]]>`;\n}\nfunction renderComment(elem) {\n    return `<!--${elem.data}-->`;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vbm9kZV9tb2R1bGVzL2RvbS1zZXJpYWxpemVyL2xpYi9lc20vaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDOEM7QUFDb0I7QUFDbEU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ2lFO0FBQ2pFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQ0FBc0M7QUFDdEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGNBQWMsK0NBQVM7QUFDdkIsY0FBYyxxREFBZTtBQUM3QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IsNERBQWM7QUFDdEM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQkFBa0IsSUFBSSxJQUFJLGNBQWM7QUFDeEMsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ08sa0NBQWtDO0FBQ3pDO0FBQ0E7QUFDQSxvQkFBb0Isa0JBQWtCO0FBQ3RDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUVBQWUsTUFBTSxFQUFDO0FBQ3RCO0FBQ0E7QUFDQSxhQUFhLGdEQUFnQjtBQUM3QjtBQUNBO0FBQ0EsYUFBYSxtREFBbUI7QUFDaEMsYUFBYSxxREFBcUI7QUFDbEM7QUFDQSxhQUFhLG1EQUFtQjtBQUNoQztBQUNBLGFBQWEsaURBQWlCO0FBQzlCO0FBQ0EsYUFBYSxrREFBa0I7QUFDL0IsYUFBYSxpREFBaUI7QUFDOUIsYUFBYSwrQ0FBZTtBQUM1QjtBQUNBLGFBQWEsZ0RBQWdCO0FBQzdCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwQkFBMEIsMERBQVk7QUFDdEM7QUFDQTtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBLGtCQUFrQixVQUFVO0FBQzVCO0FBQ0E7QUFDQSxtQkFBbUIsUUFBUTtBQUMzQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLFVBQVU7QUFDbEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsVUFBVTtBQUN6QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCLG1EQUFTO0FBQzNCLGtCQUFrQixvREFBVTtBQUM1QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVCQUF1QixzQkFBc0I7QUFDN0M7QUFDQTtBQUNBLGtCQUFrQixVQUFVO0FBQzVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXE5pZGhhbFxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxmb3JlaW5nYXRlX2dyb3VwZVxcbm9kZV9tb2R1bGVzXFxkb20tc2VyaWFsaXplclxcbGliXFxlc21cXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qXG4gKiBNb2R1bGUgZGVwZW5kZW5jaWVzXG4gKi9cbmltcG9ydCAqIGFzIEVsZW1lbnRUeXBlIGZyb20gXCJkb21lbGVtZW50dHlwZVwiO1xuaW1wb3J0IHsgZW5jb2RlWE1MLCBlc2NhcGVBdHRyaWJ1dGUsIGVzY2FwZVRleHQgfSBmcm9tIFwiZW50aXRpZXNcIjtcbi8qKlxuICogTWl4ZWQtY2FzZSBTVkcgYW5kIE1hdGhNTCB0YWdzICYgYXR0cmlidXRlc1xuICogcmVjb2duaXplZCBieSB0aGUgSFRNTCBwYXJzZXIuXG4gKlxuICogQHNlZSBodHRwczovL2h0bWwuc3BlYy53aGF0d2cub3JnL211bHRpcGFnZS9wYXJzaW5nLmh0bWwjcGFyc2luZy1tYWluLWluZm9yZWlnblxuICovXG5pbXBvcnQgeyBlbGVtZW50TmFtZXMsIGF0dHJpYnV0ZU5hbWVzIH0gZnJvbSBcIi4vZm9yZWlnbk5hbWVzLmpzXCI7XG5jb25zdCB1bmVuY29kZWRFbGVtZW50cyA9IG5ldyBTZXQoW1xuICAgIFwic3R5bGVcIixcbiAgICBcInNjcmlwdFwiLFxuICAgIFwieG1wXCIsXG4gICAgXCJpZnJhbWVcIixcbiAgICBcIm5vZW1iZWRcIixcbiAgICBcIm5vZnJhbWVzXCIsXG4gICAgXCJwbGFpbnRleHRcIixcbiAgICBcIm5vc2NyaXB0XCIsXG5dKTtcbmZ1bmN0aW9uIHJlcGxhY2VRdW90ZXModmFsdWUpIHtcbiAgICByZXR1cm4gdmFsdWUucmVwbGFjZSgvXCIvZywgXCImcXVvdDtcIik7XG59XG4vKipcbiAqIEZvcm1hdCBhdHRyaWJ1dGVzXG4gKi9cbmZ1bmN0aW9uIGZvcm1hdEF0dHJpYnV0ZXMoYXR0cmlidXRlcywgb3B0cykge1xuICAgIHZhciBfYTtcbiAgICBpZiAoIWF0dHJpYnV0ZXMpXG4gICAgICAgIHJldHVybjtcbiAgICBjb25zdCBlbmNvZGUgPSAoKF9hID0gb3B0cy5lbmNvZGVFbnRpdGllcykgIT09IG51bGwgJiYgX2EgIT09IHZvaWQgMCA/IF9hIDogb3B0cy5kZWNvZGVFbnRpdGllcykgPT09IGZhbHNlXG4gICAgICAgID8gcmVwbGFjZVF1b3Rlc1xuICAgICAgICA6IG9wdHMueG1sTW9kZSB8fCBvcHRzLmVuY29kZUVudGl0aWVzICE9PSBcInV0ZjhcIlxuICAgICAgICAgICAgPyBlbmNvZGVYTUxcbiAgICAgICAgICAgIDogZXNjYXBlQXR0cmlidXRlO1xuICAgIHJldHVybiBPYmplY3Qua2V5cyhhdHRyaWJ1dGVzKVxuICAgICAgICAubWFwKChrZXkpID0+IHtcbiAgICAgICAgdmFyIF9hLCBfYjtcbiAgICAgICAgY29uc3QgdmFsdWUgPSAoX2EgPSBhdHRyaWJ1dGVzW2tleV0pICE9PSBudWxsICYmIF9hICE9PSB2b2lkIDAgPyBfYSA6IFwiXCI7XG4gICAgICAgIGlmIChvcHRzLnhtbE1vZGUgPT09IFwiZm9yZWlnblwiKSB7XG4gICAgICAgICAgICAvKiBGaXggdXAgbWl4ZWQtY2FzZSBhdHRyaWJ1dGUgbmFtZXMgKi9cbiAgICAgICAgICAgIGtleSA9IChfYiA9IGF0dHJpYnV0ZU5hbWVzLmdldChrZXkpKSAhPT0gbnVsbCAmJiBfYiAhPT0gdm9pZCAwID8gX2IgOiBrZXk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKCFvcHRzLmVtcHR5QXR0cnMgJiYgIW9wdHMueG1sTW9kZSAmJiB2YWx1ZSA9PT0gXCJcIikge1xuICAgICAgICAgICAgcmV0dXJuIGtleTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gYCR7a2V5fT1cIiR7ZW5jb2RlKHZhbHVlKX1cImA7XG4gICAgfSlcbiAgICAgICAgLmpvaW4oXCIgXCIpO1xufVxuLyoqXG4gKiBTZWxmLWVuY2xvc2luZyB0YWdzXG4gKi9cbmNvbnN0IHNpbmdsZVRhZyA9IG5ldyBTZXQoW1xuICAgIFwiYXJlYVwiLFxuICAgIFwiYmFzZVwiLFxuICAgIFwiYmFzZWZvbnRcIixcbiAgICBcImJyXCIsXG4gICAgXCJjb2xcIixcbiAgICBcImNvbW1hbmRcIixcbiAgICBcImVtYmVkXCIsXG4gICAgXCJmcmFtZVwiLFxuICAgIFwiaHJcIixcbiAgICBcImltZ1wiLFxuICAgIFwiaW5wdXRcIixcbiAgICBcImlzaW5kZXhcIixcbiAgICBcImtleWdlblwiLFxuICAgIFwibGlua1wiLFxuICAgIFwibWV0YVwiLFxuICAgIFwicGFyYW1cIixcbiAgICBcInNvdXJjZVwiLFxuICAgIFwidHJhY2tcIixcbiAgICBcIndiclwiLFxuXSk7XG4vKipcbiAqIFJlbmRlcnMgYSBET00gbm9kZSBvciBhbiBhcnJheSBvZiBET00gbm9kZXMgdG8gYSBzdHJpbmcuXG4gKlxuICogQ2FuIGJlIHRob3VnaHQgb2YgYXMgdGhlIGVxdWl2YWxlbnQgb2YgdGhlIGBvdXRlckhUTUxgIG9mIHRoZSBwYXNzZWQgbm9kZShzKS5cbiAqXG4gKiBAcGFyYW0gbm9kZSBOb2RlIHRvIGJlIHJlbmRlcmVkLlxuICogQHBhcmFtIG9wdGlvbnMgQ2hhbmdlcyBzZXJpYWxpemF0aW9uIGJlaGF2aW9yXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiByZW5kZXIobm9kZSwgb3B0aW9ucyA9IHt9KSB7XG4gICAgY29uc3Qgbm9kZXMgPSBcImxlbmd0aFwiIGluIG5vZGUgPyBub2RlIDogW25vZGVdO1xuICAgIGxldCBvdXRwdXQgPSBcIlwiO1xuICAgIGZvciAobGV0IGkgPSAwOyBpIDwgbm9kZXMubGVuZ3RoOyBpKyspIHtcbiAgICAgICAgb3V0cHV0ICs9IHJlbmRlck5vZGUobm9kZXNbaV0sIG9wdGlvbnMpO1xuICAgIH1cbiAgICByZXR1cm4gb3V0cHV0O1xufVxuZXhwb3J0IGRlZmF1bHQgcmVuZGVyO1xuZnVuY3Rpb24gcmVuZGVyTm9kZShub2RlLCBvcHRpb25zKSB7XG4gICAgc3dpdGNoIChub2RlLnR5cGUpIHtcbiAgICAgICAgY2FzZSBFbGVtZW50VHlwZS5Sb290OlxuICAgICAgICAgICAgcmV0dXJuIHJlbmRlcihub2RlLmNoaWxkcmVuLCBvcHRpb25zKTtcbiAgICAgICAgLy8gQHRzLWV4cGVjdC1lcnJvciBXZSBkb24ndCB1c2UgYERvY3R5cGVgIHlldFxuICAgICAgICBjYXNlIEVsZW1lbnRUeXBlLkRvY3R5cGU6XG4gICAgICAgIGNhc2UgRWxlbWVudFR5cGUuRGlyZWN0aXZlOlxuICAgICAgICAgICAgcmV0dXJuIHJlbmRlckRpcmVjdGl2ZShub2RlKTtcbiAgICAgICAgY2FzZSBFbGVtZW50VHlwZS5Db21tZW50OlxuICAgICAgICAgICAgcmV0dXJuIHJlbmRlckNvbW1lbnQobm9kZSk7XG4gICAgICAgIGNhc2UgRWxlbWVudFR5cGUuQ0RBVEE6XG4gICAgICAgICAgICByZXR1cm4gcmVuZGVyQ2RhdGEobm9kZSk7XG4gICAgICAgIGNhc2UgRWxlbWVudFR5cGUuU2NyaXB0OlxuICAgICAgICBjYXNlIEVsZW1lbnRUeXBlLlN0eWxlOlxuICAgICAgICBjYXNlIEVsZW1lbnRUeXBlLlRhZzpcbiAgICAgICAgICAgIHJldHVybiByZW5kZXJUYWcobm9kZSwgb3B0aW9ucyk7XG4gICAgICAgIGNhc2UgRWxlbWVudFR5cGUuVGV4dDpcbiAgICAgICAgICAgIHJldHVybiByZW5kZXJUZXh0KG5vZGUsIG9wdGlvbnMpO1xuICAgIH1cbn1cbmNvbnN0IGZvcmVpZ25Nb2RlSW50ZWdyYXRpb25Qb2ludHMgPSBuZXcgU2V0KFtcbiAgICBcIm1pXCIsXG4gICAgXCJtb1wiLFxuICAgIFwibW5cIixcbiAgICBcIm1zXCIsXG4gICAgXCJtdGV4dFwiLFxuICAgIFwiYW5ub3RhdGlvbi14bWxcIixcbiAgICBcImZvcmVpZ25PYmplY3RcIixcbiAgICBcImRlc2NcIixcbiAgICBcInRpdGxlXCIsXG5dKTtcbmNvbnN0IGZvcmVpZ25FbGVtZW50cyA9IG5ldyBTZXQoW1wic3ZnXCIsIFwibWF0aFwiXSk7XG5mdW5jdGlvbiByZW5kZXJUYWcoZWxlbSwgb3B0cykge1xuICAgIHZhciBfYTtcbiAgICAvLyBIYW5kbGUgU1ZHIC8gTWF0aE1MIGluIEhUTUxcbiAgICBpZiAob3B0cy54bWxNb2RlID09PSBcImZvcmVpZ25cIikge1xuICAgICAgICAvKiBGaXggdXAgbWl4ZWQtY2FzZSBlbGVtZW50IG5hbWVzICovXG4gICAgICAgIGVsZW0ubmFtZSA9IChfYSA9IGVsZW1lbnROYW1lcy5nZXQoZWxlbS5uYW1lKSkgIT09IG51bGwgJiYgX2EgIT09IHZvaWQgMCA/IF9hIDogZWxlbS5uYW1lO1xuICAgICAgICAvKiBFeGl0IGZvcmVpZ24gbW9kZSBhdCBpbnRlZ3JhdGlvbiBwb2ludHMgKi9cbiAgICAgICAgaWYgKGVsZW0ucGFyZW50ICYmXG4gICAgICAgICAgICBmb3JlaWduTW9kZUludGVncmF0aW9uUG9pbnRzLmhhcyhlbGVtLnBhcmVudC5uYW1lKSkge1xuICAgICAgICAgICAgb3B0cyA9IHsgLi4ub3B0cywgeG1sTW9kZTogZmFsc2UgfTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBpZiAoIW9wdHMueG1sTW9kZSAmJiBmb3JlaWduRWxlbWVudHMuaGFzKGVsZW0ubmFtZSkpIHtcbiAgICAgICAgb3B0cyA9IHsgLi4ub3B0cywgeG1sTW9kZTogXCJmb3JlaWduXCIgfTtcbiAgICB9XG4gICAgbGV0IHRhZyA9IGA8JHtlbGVtLm5hbWV9YDtcbiAgICBjb25zdCBhdHRyaWJzID0gZm9ybWF0QXR0cmlidXRlcyhlbGVtLmF0dHJpYnMsIG9wdHMpO1xuICAgIGlmIChhdHRyaWJzKSB7XG4gICAgICAgIHRhZyArPSBgICR7YXR0cmlic31gO1xuICAgIH1cbiAgICBpZiAoZWxlbS5jaGlsZHJlbi5sZW5ndGggPT09IDAgJiZcbiAgICAgICAgKG9wdHMueG1sTW9kZVxuICAgICAgICAgICAgPyAvLyBJbiBYTUwgbW9kZSBvciBmb3JlaWduIG1vZGUsIGFuZCB1c2VyIGhhc24ndCBleHBsaWNpdGx5IHR1cm5lZCBvZmYgc2VsZi1jbG9zaW5nIHRhZ3NcbiAgICAgICAgICAgICAgICBvcHRzLnNlbGZDbG9zaW5nVGFncyAhPT0gZmFsc2VcbiAgICAgICAgICAgIDogLy8gVXNlciBleHBsaWNpdGx5IGFza2VkIGZvciBzZWxmLWNsb3NpbmcgdGFncywgZXZlbiBpbiBIVE1MIG1vZGVcbiAgICAgICAgICAgICAgICBvcHRzLnNlbGZDbG9zaW5nVGFncyAmJiBzaW5nbGVUYWcuaGFzKGVsZW0ubmFtZSkpKSB7XG4gICAgICAgIGlmICghb3B0cy54bWxNb2RlKVxuICAgICAgICAgICAgdGFnICs9IFwiIFwiO1xuICAgICAgICB0YWcgKz0gXCIvPlwiO1xuICAgIH1cbiAgICBlbHNlIHtcbiAgICAgICAgdGFnICs9IFwiPlwiO1xuICAgICAgICBpZiAoZWxlbS5jaGlsZHJlbi5sZW5ndGggPiAwKSB7XG4gICAgICAgICAgICB0YWcgKz0gcmVuZGVyKGVsZW0uY2hpbGRyZW4sIG9wdHMpO1xuICAgICAgICB9XG4gICAgICAgIGlmIChvcHRzLnhtbE1vZGUgfHwgIXNpbmdsZVRhZy5oYXMoZWxlbS5uYW1lKSkge1xuICAgICAgICAgICAgdGFnICs9IGA8LyR7ZWxlbS5uYW1lfT5gO1xuICAgICAgICB9XG4gICAgfVxuICAgIHJldHVybiB0YWc7XG59XG5mdW5jdGlvbiByZW5kZXJEaXJlY3RpdmUoZWxlbSkge1xuICAgIHJldHVybiBgPCR7ZWxlbS5kYXRhfT5gO1xufVxuZnVuY3Rpb24gcmVuZGVyVGV4dChlbGVtLCBvcHRzKSB7XG4gICAgdmFyIF9hO1xuICAgIGxldCBkYXRhID0gZWxlbS5kYXRhIHx8IFwiXCI7XG4gICAgLy8gSWYgZW50aXRpZXMgd2VyZW4ndCBkZWNvZGVkLCBubyBuZWVkIHRvIGVuY29kZSB0aGVtIGJhY2tcbiAgICBpZiAoKChfYSA9IG9wdHMuZW5jb2RlRW50aXRpZXMpICE9PSBudWxsICYmIF9hICE9PSB2b2lkIDAgPyBfYSA6IG9wdHMuZGVjb2RlRW50aXRpZXMpICE9PSBmYWxzZSAmJlxuICAgICAgICAhKCFvcHRzLnhtbE1vZGUgJiZcbiAgICAgICAgICAgIGVsZW0ucGFyZW50ICYmXG4gICAgICAgICAgICB1bmVuY29kZWRFbGVtZW50cy5oYXMoZWxlbS5wYXJlbnQubmFtZSkpKSB7XG4gICAgICAgIGRhdGEgPVxuICAgICAgICAgICAgb3B0cy54bWxNb2RlIHx8IG9wdHMuZW5jb2RlRW50aXRpZXMgIT09IFwidXRmOFwiXG4gICAgICAgICAgICAgICAgPyBlbmNvZGVYTUwoZGF0YSlcbiAgICAgICAgICAgICAgICA6IGVzY2FwZVRleHQoZGF0YSk7XG4gICAgfVxuICAgIHJldHVybiBkYXRhO1xufVxuZnVuY3Rpb24gcmVuZGVyQ2RhdGEoZWxlbSkge1xuICAgIHJldHVybiBgPCFbQ0RBVEFbJHtlbGVtLmNoaWxkcmVuWzBdLmRhdGF9XV0+YDtcbn1cbmZ1bmN0aW9uIHJlbmRlckNvbW1lbnQoZWxlbSkge1xuICAgIHJldHVybiBgPCEtLSR7ZWxlbS5kYXRhfS0tPmA7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/dom-serializer/lib/esm/index.js\n");

/***/ })

};
;