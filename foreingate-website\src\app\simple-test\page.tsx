'use client'

import { useTranslation } from '@/hooks/use-translation'
import { LanguageSwitcher } from '@/components/ui/language-switcher'
import { Button } from '@/components/ui/button'

export default function SimpleTestPage() {
  const { t, locale, isRTL } = useTranslation()

  return (
    <div className={`min-h-screen bg-background p-8 ${isRTL ? 'rtl' : 'ltr'}`}>
      <div className="max-w-2xl mx-auto space-y-8">
        {/* Header */}
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold">Simple Language Test</h1>
          <p className="text-muted-foreground">
            Quick test of language switching functionality
          </p>
          <LanguageSwitcher variant="default" />
        </div>

        {/* Current Language Info */}
        <div className="bg-gray-100 dark:bg-gray-800 p-6 rounded-lg">
          <h2 className="text-xl font-semibold mb-4">Current Language Info</h2>
          <div className="space-y-2">
            <p><strong>Locale:</strong> {locale}</p>
            <p><strong>Direction:</strong> {isRTL ? 'Right-to-Left (RTL)' : 'Left-to-Right (LTR)'}</p>
            <p><strong>Language:</strong> {t.nav.language}</p>
          </div>
        </div>

        {/* Navigation Translations */}
        <div className="bg-white dark:bg-gray-900 p-6 rounded-lg border">
          <h2 className="text-xl font-semibold mb-4">Navigation Translations</h2>
          <div className="grid grid-cols-2 gap-4">
            <div><strong>Home:</strong> {t.nav.home}</div>
            <div><strong>About:</strong> {t.nav.about}</div>
            <div><strong>Services:</strong> {t.nav.services}</div>
            <div><strong>Contact:</strong> {t.nav.contact}</div>
          </div>
        </div>

        {/* Hero Section */}
        <div className="bg-blue-50 dark:bg-blue-900/20 p-6 rounded-lg">
          <h2 className="text-xl font-semibold mb-4">Hero Section</h2>
          <h3 className="text-lg font-medium mb-2">{t.hero.title}</h3>
          <p className="text-gray-600 dark:text-gray-300 mb-4">{t.hero.description}</p>
          <div className="space-x-4">
            <Button>{t.hero.ctaPrimary}</Button>
            <Button variant="outline">{t.hero.ctaSecondary}</Button>
          </div>
        </div>

        {/* Common UI Elements */}
        <div className="bg-green-50 dark:bg-green-900/20 p-6 rounded-lg">
          <h2 className="text-xl font-semibold mb-4">Common UI Elements</h2>
          <div className="grid grid-cols-2 gap-4">
            <div><strong>Loading:</strong> {t.common.loading}</div>
            <div><strong>Error:</strong> {t.common.error}</div>
            <div><strong>Success:</strong> {t.common.success}</div>
            <div><strong>Search:</strong> {t.common.search}</div>
            <div><strong>Save:</strong> {t.common.save}</div>
            <div><strong>Close:</strong> {t.common.close}</div>
          </div>
        </div>

        {/* Contact Form */}
        <div className="bg-purple-50 dark:bg-purple-900/20 p-6 rounded-lg">
          <h2 className="text-xl font-semibold mb-4">Contact Form</h2>
          <div className="grid grid-cols-2 gap-4">
            <div><strong>Name:</strong> {t.forms.fullName}</div>
            <div><strong>Email:</strong> {t.forms.email}</div>
            <div><strong>Phone:</strong> {t.forms.phone}</div>
            <div><strong>Message:</strong> {t.contact.message}</div>
            <div><strong>Send:</strong> {t.contact.send}</div>
            <div><strong>Required:</strong> {t.forms.required}</div>
          </div>
        </div>

        {/* Language Switcher Variants */}
        <div className="bg-orange-50 dark:bg-orange-900/20 p-6 rounded-lg">
          <h2 className="text-xl font-semibold mb-4">Language Switcher Variants</h2>
          <div className="space-y-4">
            <div>
              <h3 className="font-medium mb-2">Default</h3>
              <LanguageSwitcher variant="default" />
            </div>
            <div>
              <h3 className="font-medium mb-2">Compact</h3>
              <LanguageSwitcher variant="compact" />
            </div>
            <div>
              <h3 className="font-medium mb-2">Minimal</h3>
              <LanguageSwitcher variant="minimal" />
            </div>
          </div>
        </div>

        {/* Instructions */}
        <div className="bg-yellow-50 dark:bg-yellow-900/20 p-6 rounded-lg">
          <h2 className="text-xl font-semibold mb-4">Test Instructions</h2>
          <ol className="list-decimal list-inside space-y-2">
            <li>Use any language switcher above to change the language</li>
            <li>Observe how all text content updates immediately</li>
            <li>Try Arabic to see RTL (right-to-left) layout changes</li>
            <li>Test different variants of the language switcher</li>
            <li>Check that the browser remembers your language choice</li>
          </ol>
        </div>
      </div>
    </div>
  )
}
