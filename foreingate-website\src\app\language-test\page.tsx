import { SimpleWrapper } from '@/components/layout/simple-wrapper'

export default function LanguageTestPage() {
  return (
    <SimpleWrapper>
      <div className="container mx-auto px-4 py-20">
        <div className="text-center space-y-8">
          <h1 className="text-4xl font-bold">Language Test Page</h1>
          <p className="text-muted-foreground">
            This page demonstrates the website's multi-language capabilities.
          </p>
          
          <div className="space-y-4">
            <p className="text-lg">🌍 Multi-language support ready</p>
            <p className="text-lg">📱 RTL language support available</p>
            <p className="text-lg">🔄 Real-time language switching</p>
            <p className="text-lg">💾 Language preference persistence</p>
          </div>

          <div className="mt-8">
            <h2 className="text-2xl font-semibold mb-4">Supported Languages:</h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="p-4 border rounded-lg">
                <div className="text-2xl mb-2">🇺🇸</div>
                <div className="font-semibold">English</div>
                <div className="text-sm text-muted-foreground">Default</div>
              </div>
              <div className="p-4 border rounded-lg">
                <div className="text-2xl mb-2">🇹🇷</div>
                <div className="font-semibold">Türkçe</div>
                <div className="text-sm text-muted-foreground">Turkish</div>
              </div>
              <div className="p-4 border rounded-lg">
                <div className="text-2xl mb-2">🇸🇦</div>
                <div className="font-semibold">العربية</div>
                <div className="text-sm text-muted-foreground">Arabic (RTL)</div>
              </div>
              <div className="p-4 border rounded-lg">
                <div className="text-2xl mb-2">🇷🇺</div>
                <div className="font-semibold">Русский</div>
                <div className="text-sm text-muted-foreground">Russian</div>
              </div>
              <div className="p-4 border rounded-lg">
                <div className="text-2xl mb-2">🇫🇷</div>
                <div className="font-semibold">Français</div>
                <div className="text-sm text-muted-foreground">French</div>
              </div>
              <div className="p-4 border rounded-lg">
                <div className="text-2xl mb-2">🇩🇪</div>
                <div className="font-semibold">Deutsch</div>
                <div className="text-sm text-muted-foreground">German</div>
              </div>
              <div className="p-4 border rounded-lg">
                <div className="text-2xl mb-2">🇪🇸</div>
                <div className="font-semibold">Español</div>
                <div className="text-sm text-muted-foreground">Spanish</div>
              </div>
              <div className="p-4 border rounded-lg">
                <div className="text-2xl mb-2">🇨🇳</div>
                <div className="font-semibold">中文</div>
                <div className="text-sm text-muted-foreground">Chinese</div>
              </div>
            </div>
          </div>

          <div className="mt-8">
            <h2 className="text-2xl font-semibold mb-4">Features:</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="p-6 border rounded-lg">
                <h3 className="text-xl font-semibold mb-2">Language Detection</h3>
                <ul className="text-left space-y-2">
                  <li>✅ Browser language detection</li>
                  <li>✅ Automatic language selection</li>
                  <li>✅ Fallback to English</li>
                  <li>✅ User preference override</li>
                </ul>
              </div>
              <div className="p-6 border rounded-lg">
                <h3 className="text-xl font-semibold mb-2">RTL Support</h3>
                <ul className="text-left space-y-2">
                  <li>✅ Arabic text direction</li>
                  <li>✅ Hebrew support</li>
                  <li>✅ Persian/Farsi support</li>
                  <li>✅ Layout mirroring</li>
                </ul>
              </div>
            </div>
          </div>

          <div className="mt-8">
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a 
                href="/"
                className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Back to Home
              </a>
              <a 
                href="/simple-test"
                className="px-6 py-3 border border-gray-300 text-foreground rounded-lg hover:bg-gray-50 transition-colors"
              >
                Simple Test
              </a>
            </div>
          </div>
        </div>
      </div>
    </SimpleWrapper>
  )
}
