'use client'

import { PageWrapper } from '@/components/layout/page-wrapper'
import { useTranslation } from '@/hooks/use-translation'
import { LanguageSwitcher } from '@/components/ui/language-switcher'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'

function LanguageTestContent() {
  const { t, locale, isRTL } = useTranslation()

  return (
    <div className={`min-h-screen bg-background p-8 ${isRTL ? 'rtl' : 'ltr'}`}>
      <div className="max-w-4xl mx-auto space-y-8">
        {/* Header */}
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold">Language Test Page</h1>
          <p className="text-muted-foreground">
            Test all translations and language switching functionality
          </p>
          <div className="flex justify-center">
            <LanguageSwitcher variant="default" />
          </div>
          <div className="flex justify-center space-x-4">
            <Badge variant="outline">Current Locale: {locale}</Badge>
            <Badge variant="outline">Direction: {isRTL ? 'RTL' : 'LTR'}</Badge>
          </div>
        </div>

        {/* Navigation Translations */}
        <Card>
          <CardHeader>
            <CardTitle>Navigation Translations</CardTitle>
            <CardDescription>Testing navigation menu translations</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div>
                <strong>Home:</strong> {t.nav.home}
              </div>
              <div>
                <strong>About:</strong> {t.nav.about}
              </div>
              <div>
                <strong>Services:</strong> {t.nav.services}
              </div>
              <div>
                <strong>Universities:</strong> {t.nav.universities}
              </div>
              <div>
                <strong>Programs:</strong> {t.nav.programs}
              </div>
              <div>
                <strong>Blog:</strong> {t.nav.blog}
              </div>
              <div>
                <strong>Contact:</strong> {t.nav.contact}
              </div>
              <div>
                <strong>Apply Now:</strong> {t.nav.applyNow}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Hero Section Translations */}
        <Card>
          <CardHeader>
            <CardTitle>Hero Section Translations</CardTitle>
            <CardDescription>Testing hero section content</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h3 className="text-xl font-semibold mb-2">{t.hero.title}</h3>
              <p className="text-muted-foreground">{t.hero.description}</p>
            </div>
            <div className="flex space-x-4">
              <Button>{t.hero.ctaPrimary}</Button>
              <Button variant="outline">{t.hero.ctaSecondary}</Button>
            </div>
            <div className="text-sm text-muted-foreground">
              {t.hero.trustBadge}
            </div>
          </CardContent>
        </Card>

        {/* Services Translations */}
        <Card>
          <CardHeader>
            <CardTitle>Services Translations</CardTitle>
            <CardDescription>Testing services section content</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold">{t.services.universitySelection}</h4>
                <p className="text-sm text-muted-foreground">{t.services.universitySelectionDesc}</p>
              </div>
              <div>
                <h4 className="font-semibold">{t.services.admissionGuidance}</h4>
                <p className="text-sm text-muted-foreground">{t.services.admissionGuidanceDesc}</p>
              </div>
              <div>
                <h4 className="font-semibold">{t.services.visaSupport}</h4>
                <p className="text-sm text-muted-foreground">{t.services.visaSupportDesc}</p>
              </div>
              <div>
                <h4 className="font-semibold">{t.services.scholarshipAssistance}</h4>
                <p className="text-sm text-muted-foreground">{t.services.scholarshipAssistanceDesc}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Universities Translations */}
        <Card>
          <CardHeader>
            <CardTitle>Universities Translations</CardTitle>
            <CardDescription>Testing university section content</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h4 className="font-semibold">{t.universities.emu}</h4>
                <p className="text-sm text-muted-foreground">
                  {t.universities.programs}: 150+ | {t.universities.students}: 20,000+
                </p>
              </div>
              <div>
                <h4 className="font-semibold">{t.universities.neu}</h4>
                <p className="text-sm text-muted-foreground">
                  {t.universities.programs}: 200+ | {t.universities.students}: 25,000+
                </p>
              </div>
              <div>
                <h4 className="font-semibold">{t.universities.ciu}</h4>
                <p className="text-sm text-muted-foreground">
                  {t.universities.programs}: 100+ | {t.universities.students}: 15,000+
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Contact Form Translations */}
        <Card>
          <CardHeader>
            <CardTitle>Contact Form Translations</CardTitle>
            <CardDescription>Testing form field translations</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
              <div><strong>Name:</strong> {t.forms.fullName}</div>
              <div><strong>Email:</strong> {t.forms.email}</div>
              <div><strong>Phone:</strong> {t.forms.phone}</div>
              <div><strong>Country:</strong> {t.forms.country}</div>
              <div><strong>Message:</strong> {t.contact.message}</div>
              <div><strong>Submit:</strong> {t.forms.submit}</div>
              <div><strong>Required:</strong> {t.forms.required}</div>
              <div><strong>Send:</strong> {t.contact.send}</div>
              <div><strong>Sending:</strong> {t.contact.sending}</div>
            </div>
          </CardContent>
        </Card>

        {/* Common Translations */}
        <Card>
          <CardHeader>
            <CardTitle>Common Translations</CardTitle>
            <CardDescription>Testing common UI elements</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div><strong>Loading:</strong> {t.common.loading}</div>
              <div><strong>Error:</strong> {t.common.error}</div>
              <div><strong>Success:</strong> {t.common.success}</div>
              <div><strong>Search:</strong> {t.common.search}</div>
              <div><strong>Next:</strong> {t.common.next}</div>
              <div><strong>Previous:</strong> {t.common.previous}</div>
              <div><strong>Close:</strong> {t.common.close}</div>
              <div><strong>Save:</strong> {t.common.save}</div>
            </div>
          </CardContent>
        </Card>

        {/* Chatbot Translations */}
        <Card>
          <CardHeader>
            <CardTitle>Chatbot Translations</CardTitle>
            <CardDescription>Testing chatbot interface translations</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div><strong>Title:</strong> {t.chatbot.title}</div>
              <div><strong>Placeholder:</strong> {t.chatbot.placeholder}</div>
              <div><strong>Greeting:</strong> {t.chatbot.greeting}</div>
              <div><strong>Thinking:</strong> {t.chatbot.thinking}</div>
            </div>
          </CardContent>
        </Card>

        {/* Footer */}
        <Card>
          <CardHeader>
            <CardTitle>Footer Translations</CardTitle>
            <CardDescription>Testing footer content</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <p className="text-sm">{t.footer.description}</p>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div><strong>Quick Links:</strong> {t.footer.quickLinks}</div>
                <div><strong>Services:</strong> {t.footer.services}</div>
                <div><strong>Contact:</strong> {t.footer.contact}</div>
                <div><strong>Follow Us:</strong> {t.footer.followUs}</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Language Switcher Variants */}
        <Card>
          <CardHeader>
            <CardTitle>Language Switcher Variants</CardTitle>
            <CardDescription>Testing different language switcher styles</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h4 className="font-semibold mb-2">Default Variant</h4>
                <LanguageSwitcher variant="default" />
              </div>
              <div>
                <h4 className="font-semibold mb-2">Compact Variant</h4>
                <LanguageSwitcher variant="compact" />
              </div>
              <div>
                <h4 className="font-semibold mb-2">Minimal Variant</h4>
                <LanguageSwitcher variant="minimal" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export default function LanguageTestPage() {
  return (
    <PageWrapper>
      <LanguageTestContent />
    </PageWrapper>
  )
}
