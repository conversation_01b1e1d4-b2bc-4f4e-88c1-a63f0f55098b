// Comprehensive knowledge base for the Foreingate Group chatbot
export const websiteKnowledge = {
  company: {
    name: "Foreingate Group",
    description: "Leading educational consultancy specializing in university admissions and student services in Northern Cyprus",
    mission: "To bridge the gap between international students and quality higher education opportunities in Northern Cyprus",
    vision: "To be the most trusted partner for students seeking world-class education abroad",
    established: "2020",
    headquarters: "Nicosia, Northern Cyprus",
    languages: ["English", "Turkish", "Arabic"],
    accreditations: ["ICEF Certified", "British Council Partner"]
  },

  services: [
    {
      name: "University Admissions",
      description: "Complete application assistance for universities in Northern Cyprus",
      features: ["Application processing", "Document preparation", "Interview coaching", "Admission guarantee"],
      pricing: "Free consultation, competitive service fees"
    },
    {
      name: "Visa Support",
      description: "Comprehensive visa application and documentation services",
      features: ["Visa application", "Document translation", "Embassy appointments", "Visa tracking"],
      pricing: "Starting from $200"
    },
    {
      name: "Accommodation Services",
      description: "Student housing and accommodation arrangements",
      features: ["Dormitory booking", "Private housing", "Homestay options", "Airport pickup"],
      pricing: "Varies by accommodation type"
    },
    {
      name: "Academic Support",
      description: "Ongoing academic assistance and mentoring",
      features: ["Tutoring services", "Study groups", "Academic counseling", "Career guidance"],
      pricing: "Monthly packages available"
    }
  ],

  universities: [
    {
      name: "Eastern Mediterranean University (EMU)",
      location: "Famagusta, Northern Cyprus",
      established: 1979,
      students: 20000,
      international: 15000,
      programs: ["Engineering", "Medicine", "Business", "Arts & Sciences", "Law"],
      tuition: { min: 3500, max: 8000, currency: "USD" },
      language: "English",
      accreditation: "YÖK (Turkish Higher Education Council)"
    },
    {
      name: "Near East University (NEU)",
      location: "Nicosia, Northern Cyprus",
      established: 1988,
      students: 25000,
      international: 18000,
      programs: ["Medicine", "Dentistry", "Engineering", "Pharmacy", "Architecture"],
      tuition: { min: 4000, max: 12000, currency: "USD" },
      language: "English",
      accreditation: "YÖK, WHO recognized"
    },
    {
      name: "Cyprus International University (CIU)",
      location: "Nicosia, Northern Cyprus",
      established: 1997,
      students: 15000,
      international: 12000,
      programs: ["Business", "Engineering", "Communication", "Education", "Fine Arts"],
      tuition: { min: 3000, max: 7000, currency: "USD" },
      language: "English",
      accreditation: "YÖK certified"
    }
  ],

  admissionProcess: {
    steps: [
      "Initial consultation and assessment",
      "University and program selection",
      "Document preparation and verification",
      "Application submission",
      "Interview preparation (if required)",
      "Admission decision and acceptance",
      "Visa application process",
      "Pre-departure orientation",
      "Arrival and settlement support"
    ],
    timeline: "2-4 months average",
    requirements: {
      undergraduate: ["High school diploma", "English proficiency test", "Passport copy", "Academic transcripts"],
      graduate: ["Bachelor's degree", "GRE/GMAT (some programs)", "English proficiency", "Letters of recommendation", "Statement of purpose"]
    }
  },

  costs: {
    tuition: {
      undergraduate: { min: 3000, max: 8000, currency: "USD", period: "per year" },
      graduate: { min: 4000, max: 12000, currency: "USD", period: "per year" },
      medicine: { min: 8000, max: 15000, currency: "USD", period: "per year" }
    },
    living: {
      accommodation: { min: 200, max: 600, currency: "USD", period: "per month" },
      food: { min: 150, max: 300, currency: "USD", period: "per month" },
      transportation: { min: 50, max: 100, currency: "USD", period: "per month" },
      total: { min: 400, max: 1000, currency: "USD", period: "per month" }
    }
  },

  scholarships: [
    {
      name: "Merit Scholarship",
      amount: "25-50% tuition reduction",
      criteria: "High academic performance",
      eligibility: "GPA 3.5+ or equivalent"
    },
    {
      name: "Early Bird Discount",
      amount: "10-15% tuition reduction",
      criteria: "Early application submission",
      deadline: "Before March 31st"
    },
    {
      name: "Sibling Discount",
      amount: "10% tuition reduction",
      criteria: "Multiple family members enrolled",
      eligibility: "Second sibling onwards"
    }
  ],

  contact: {
    email: "<EMAIL>",
    phone: "+90 ************",
    whatsapp: "+90 ************",
    address: "Nicosia, Northern Cyprus",
    workingHours: "Monday-Friday: 9:00-18:00, Saturday: 9:00-14:00",
    languages: ["English", "Turkish", "Arabic", "French"]
  },

  website: {
    url: "https://foreingate.com",
    features: [
      "University search and comparison",
      "Online application system",
      "Document upload portal",
      "Application tracking",
      "Live chat support",
      "Newsletter subscription",
      "Blog with educational content",
      "Testimonials from students"
    ],
    pages: [
      { path: "/", name: "Home", description: "Main landing page with overview" },
      { path: "/about", name: "About Us", description: "Company information and team" },
      { path: "/services", name: "Services", description: "Detailed service offerings" },
      { path: "/universities", name: "Universities", description: "Partner universities and programs" },
      { path: "/apply", name: "Apply Now", description: "Online application form" },
      { path: "/blog", name: "Blog", description: "Educational articles and news" },
      { path: "/contact", name: "Contact", description: "Contact information and form" },
      { path: "/admin", name: "Admin Panel", description: "Administrative dashboard" }
    ]
  },

  faq: [
    {
      question: "What is the application deadline?",
      answer: "Applications are accepted year-round, but we recommend applying 3-4 months before your intended start date for better preparation time."
    },
    {
      question: "Do I need to know Turkish?",
      answer: "No, most programs are taught in English. However, basic Turkish knowledge can be helpful for daily life."
    },
    {
      question: "Is Northern Cyprus safe for international students?",
      answer: "Yes, Northern Cyprus is very safe with low crime rates and a welcoming community for international students."
    },
    {
      question: "What are the visa requirements?",
      answer: "Visa requirements vary by nationality. We provide complete visa support including document preparation and application assistance."
    },
    {
      question: "Can I work while studying?",
      answer: "Yes, students can work part-time (up to 20 hours per week) with proper permits."
    }
  ],

  testimonials: [
    {
      name: "Ahmed Hassan",
      country: "Egypt",
      program: "Computer Engineering",
      university: "Eastern Mediterranean University",
      year: 2023,
      rating: 5,
      comment: "Foreingate made my dream of studying abroad come true. Their support was exceptional throughout the entire process."
    },
    {
      name: "Maria Rodriguez",
      country: "Colombia",
      program: "Medicine",
      university: "Near East University",
      year: 2022,
      rating: 5,
      comment: "Professional service and genuine care. They helped me secure admission and scholarship. Highly recommended!"
    }
  ],

  technicalInfo: {
    security: {
      ssl: "Auto-signed SSL certificates implemented",
      headers: "15+ security headers active",
      encryption: "AES-256-GCM data encryption",
      rateLimit: "100 requests per minute",
      features: ["HTTPS enforcement", "XSS protection", "CSRF prevention", "Input sanitization"]
    },
    features: {
      responsive: "Mobile-first responsive design",
      performance: "Optimized loading times",
      accessibility: "WCAG 2.1 compliant",
      seo: "Search engine optimized",
      analytics: "Google Analytics integration ready"
    },
    admin: {
      dashboard: "Real-time statistics and management",
      applications: "Complete application lifecycle management",
      newsletter: "Email campaign system",
      contacts: "Customer inquiry management",
      security: "Enhanced admin protection"
    }
  }
}

// Common questions and their categories with enhanced keywords
export const questionCategories = {
  admissions: ["admission", "apply", "application", "requirements", "documents", "deadline", "enroll", "register", "submit", "process", "steps", "how to apply", "admission process", "requirements", "eligibility"],
  universities: ["university", "universities", "programs", "courses", "degrees", "study", "emu", "neu", "ciu", "eastern mediterranean", "near east", "cyprus international", "engineering", "medicine", "business", "programs", "majors", "faculties"],
  costs: ["cost", "fees", "tuition", "price", "expensive", "cheap", "scholarship", "financial", "money", "budget", "afford", "payment", "installment", "discount", "aid", "funding"],
  visa: ["visa", "permit", "immigration", "documents", "embassy", "passport", "student visa", "residence permit", "visa application", "visa process", "visa requirements"],
  accommodation: ["housing", "accommodation", "dormitory", "residence", "living", "room", "apartment", "dorm", "homestay", "where to live", "student housing"],
  services: ["services", "help", "support", "assistance", "consultation", "what do you do", "how can you help", "what services", "support services"],
  location: ["cyprus", "northern cyprus", "nicosia", "famagusta", "location", "where", "country", "island", "turkey", "mediterranean", "safe", "weather", "climate"],
  contact: ["contact", "phone", "email", "address", "office", "hours", "reach", "call", "write", "visit", "working hours", "office hours"],
  scholarships: ["scholarship", "scholarships", "financial aid", "discount", "merit", "early bird", "sibling", "funding", "grant", "bursary"],
  language: ["english", "turkish", "language", "speak", "communication", "language requirements", "english proficiency", "ielts", "toefl"],
  general: ["about", "company", "foreingate", "who", "what", "why", "how", "hello", "hi", "help", "info", "information"]
}

// Smart response templates
export const responseTemplates = {
  greeting: [
    "Hello! I'm your Foreingate assistant. How can I help you today?",
    "Hi there! I'm here to answer any questions about studying in Northern Cyprus. What would you like to know?",
    "Welcome to Foreingate! I can help you with information about universities, admissions, costs, and more. What interests you?"
  ],
  
  notFound: [
    "I don't have specific information about that, but I'd be happy to connect you with our human advisors who can help. You can contact us at +90 ************ or <EMAIL>.",
    "That's a great question! For detailed information about that topic, I recommend speaking with our education consultants. Would you like me to help you get in touch?",
    "I want to make sure I give you accurate information. For that specific question, our expert advisors would be the best resource. Shall I provide you with contact details?"
  ],
  
  followUp: [
    "Is there anything else you'd like to know about studying in Northern Cyprus?",
    "Do you have any other questions about our services or universities?",
    "Would you like more information about any specific university or program?",
    "Can I help you with anything else regarding your education plans?"
  ]
}
