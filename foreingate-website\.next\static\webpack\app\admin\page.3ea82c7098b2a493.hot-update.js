"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./src/app/admin/page.tsx":
/*!********************************!*\
  !*** ./src/app/admin/page.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminDashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/../node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Calendar_FileText_Mail_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,FileText,Mail,TrendingUp!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_FileText_Mail_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,FileText,Mail,TrendingUp!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_FileText_Mail_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,FileText,Mail,TrendingUp!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_FileText_Mail_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,FileText,Mail,TrendingUp!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction AdminDashboard() {\n    _s();\n    const [applications, setApplications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [newsletters, setNewsletters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('overview');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminDashboard.useEffect\": ()=>{\n            fetchData();\n        }\n    }[\"AdminDashboard.useEffect\"], []);\n    const fetchData = async ()=>{\n        try {\n            setLoading(true);\n            // Fetch applications\n            const appsResponse = await fetch('/api/applications');\n            const appsData = await appsResponse.json();\n            if (appsData.success) {\n                setApplications(appsData.applications);\n            }\n            // Fetch newsletter stats\n            const newsletterResponse = await fetch('/api/newsletter');\n            const newsletterData = await newsletterResponse.json();\n            if (newsletterData.success) {\n                setNewsletters(newsletterData.latestSubscriptions || []);\n            }\n        } catch (error) {\n            console.error('Error fetching admin data:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const stats = [\n        {\n            title: 'Total Applications',\n            value: applications.length,\n            icon: _barrel_optimize_names_Calendar_FileText_Mail_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            color: 'text-blue-600',\n            bgColor: 'bg-blue-100'\n        },\n        {\n            title: 'Pending Applications',\n            value: applications.filter((app)=>app.status === 'PENDING').length,\n            icon: _barrel_optimize_names_Calendar_FileText_Mail_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            color: 'text-yellow-600',\n            bgColor: 'bg-yellow-100'\n        },\n        {\n            title: 'Newsletter Subscribers',\n            value: newsletters.length,\n            icon: _barrel_optimize_names_Calendar_FileText_Mail_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            color: 'text-green-600',\n            bgColor: 'bg-green-100'\n        },\n        {\n            title: 'This Week',\n            value: applications.filter((app)=>{\n                const appDate = new Date(app.createdAt);\n                const weekAgo = new Date();\n                weekAgo.setDate(weekAgo.getDate() - 7);\n                return appDate > weekAgo;\n            }).length,\n            icon: _barrel_optimize_names_Calendar_FileText_Mail_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            color: 'text-purple-600',\n            bgColor: 'bg-purple-100'\n        }\n    ];\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-primary\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 121,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n            lineNumber: 120,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white shadow-sm border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center py-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-gray-900\",\n                                        children: \"Admin Dashboard\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Foreingate Group - Real-time Data\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: fetchData,\n                                children: \"Refresh Data\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 130,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 129,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n                        children: stats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    delay: index * 0.1\n                                },\n                                className: \"bg-white rounded-lg shadow p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"\".concat(stat.bgColor, \" p-3 rounded-lg\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(stat.icon, {\n                                                className: \"w-6 h-6 \".concat(stat.color)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 156,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: stat.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 159,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: stat.value\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 160,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 15\n                                }, this)\n                            }, stat.title, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-b border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"-mb-px flex space-x-8 px-6\",\n                                    children: [\n                                        {\n                                            id: 'overview',\n                                            name: 'Overview'\n                                        },\n                                        {\n                                            id: 'applications',\n                                            name: 'Applications'\n                                        },\n                                        {\n                                            id: 'newsletter',\n                                            name: 'Newsletter'\n                                        }\n                                    ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab(tab.id),\n                                            className: \"py-4 px-1 border-b-2 font-medium text-sm \".concat(activeTab === tab.id ? 'border-primary text-primary' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'),\n                                            children: tab.name\n                                        }, tab.id, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: [\n                                    activeTab === 'overview' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium\",\n                                                children: \"Recent Activity\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid md:grid-cols-2 gap-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium mb-3\",\n                                                                children: \"Latest Applications\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 198,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-3\",\n                                                                children: applications.slice(0, 5).map((app)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-between p-3 bg-gray-50 rounded-lg\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"font-medium\",\n                                                                                        children: [\n                                                                                            app.firstName,\n                                                                                            \" \",\n                                                                                            app.lastName\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                        lineNumber: 203,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-sm text-gray-600\",\n                                                                                        children: [\n                                                                                            app.preferredUniversity,\n                                                                                            \" - \",\n                                                                                            app.firstChoiceProgram\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                        lineNumber: 204,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                lineNumber: 202,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full\",\n                                                                                children: app.status\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                lineNumber: 206,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, app.id, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 201,\n                                                                        columnNumber: 25\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 199,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 197,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium mb-3\",\n                                                                children: \"Newsletter Growth\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 214,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-3\",\n                                                                children: newsletters.slice(0, 5).map((sub)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-between p-3 bg-gray-50 rounded-lg\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"font-medium\",\n                                                                                        children: sub.email\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                        lineNumber: 219,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-sm text-gray-600\",\n                                                                                        children: new Date(sub.subscribedAt).toLocaleDateString()\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                        lineNumber: 220,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                lineNumber: 218,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full\",\n                                                                                children: \"Active\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                lineNumber: 224,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, sub.id, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 217,\n                                                                        columnNumber: 25\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 215,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 213,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 15\n                                    }, this),\n                                    activeTab === 'applications' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium mb-4\",\n                                                children: \"All Applications\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"overflow-x-auto\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                    className: \"min-w-full divide-y divide-gray-200\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                            className: \"bg-gray-50\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                        children: \"Student\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 243,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                        children: \"Contact\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 246,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                        children: \"Program\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 249,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                        children: \"Status\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 252,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                        children: \"Date\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 255,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 242,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 241,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                            className: \"bg-white divide-y divide-gray-200\",\n                                                            children: applications.map((app)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-sm font-medium text-gray-900\",\n                                                                                        children: [\n                                                                                            app.firstName,\n                                                                                            \" \",\n                                                                                            app.lastName\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                        lineNumber: 265,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-sm text-gray-500\",\n                                                                                        children: app.applicationId\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                        lineNumber: 268,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                lineNumber: 264,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 263,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-sm text-gray-900\",\n                                                                                    children: app.email\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                    lineNumber: 272,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-sm text-gray-500\",\n                                                                                    children: app.phone\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                    lineNumber: 273,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 271,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-sm text-gray-900\",\n                                                                                    children: app.preferredUniversity\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                    lineNumber: 276,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-sm text-gray-500\",\n                                                                                    children: app.firstChoiceProgram\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                    lineNumber: 277,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 275,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800\",\n                                                                                children: app.status\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                lineNumber: 280,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 279,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                                            children: new Date(app.createdAt).toLocaleDateString()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 284,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, app.id, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 262,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 260,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 15\n                                    }, this),\n                                    activeTab === 'newsletter' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium mb-4\",\n                                                children: \"Newsletter Subscribers\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid gap-4\",\n                                                children: newsletters.map((subscriber)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between p-4 border rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_FileText_Mail_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                        className: \"w-5 h-5 text-gray-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 303,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"font-medium\",\n                                                                                children: subscriber.email\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                lineNumber: 305,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm text-gray-500\",\n                                                                                children: [\n                                                                                    \"Subscribed: \",\n                                                                                    new Date(subscriber.subscribedAt).toLocaleDateString()\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                lineNumber: 306,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 304,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 302,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-1 text-xs rounded-full \".concat(subscriber.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'),\n                                                                children: subscriber.isActive ? 'Active' : 'Inactive'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 311,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, subscriber.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 301,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 299,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 297,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 143,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\page.tsx\",\n        lineNumber: 127,\n        columnNumber: 5\n    }, this);\n}\n_s(AdminDashboard, \"VQfbKd7ownrpxI+zvhFsJNibNCc=\");\n_c = AdminDashboard;\nvar _c;\n$RefreshReg$(_c, \"AdminDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/page.tsx\n"));

/***/ })

});