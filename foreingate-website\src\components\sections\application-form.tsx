'use client'

import { motion } from 'framer-motion'
import { useState } from 'react'
import { useInView } from 'react-intersection-observer'
import { ChevronRight, ChevronLeft, User, GraduationCap, FileText, Send, CheckCircle } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'

const steps = [
  { id: 1, title: 'Personal Information', icon: User },
  { id: 2, title: 'Academic Background', icon: GraduationCap },
  { id: 3, title: 'Program Selection', icon: FileText },
  { id: 4, title: 'Review & Submit', icon: Send }
]

export function ApplicationFormSection() {
  const [currentStep, setCurrentStep] = useState(1)
  const [formData, setFormData] = useState({
    // Personal Information
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    dateOfBirth: '',
    nationality: '',
    passportNumber: '',

    // Academic Background
    highSchoolName: '',
    graduationYear: '',
    gpa: '',
    englishProficiency: '',

    // Program Selection
    preferredUniversity: '',
    firstChoiceProgram: '',
    secondChoiceProgram: '',
    intakeYear: '',
    intakeSemester: ''
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitted, setSubmitted] = useState(false)
  const [applicationId, setApplicationId] = useState('')

  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1
  })

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const nextStep = () => {
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1)
    }
  }

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleSubmit = async () => {
    setIsSubmitting(true)

    try {
      const response = await fetch('/api/applications', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      const result = await response.json()

      if (response.ok) {
        setIsSubmitting(false)
        setSubmitted(true)

        // Store application ID for display
        setApplicationId(result.applicationId)
      } else {
        throw new Error(result.error || 'Failed to submit application')
      }
    } catch (error) {
      console.error('Application submission error:', error)
      setIsSubmitting(false)
      alert('Failed to submit application. Please try again or contact us directly.')
    }
  }

  if (submitted) {
    return (
      <section className="section-padding bg-muted/30">
        <div className="container">
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            className="max-w-2xl mx-auto text-center bg-background rounded-xl p-12 shadow-sm border"
          >
            <div className="w-20 h-20 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-6">
              <CheckCircle className="w-10 h-10 text-green-600 dark:text-green-400" />
            </div>
            <h3 className="text-3xl font-bold mb-4">Application Submitted Successfully!</h3>
            <p className="text-muted-foreground mb-6">
              Thank you for your application. Our admissions team will review your application
              and contact you within 24-48 hours with next steps.
            </p>
            <div className="bg-muted/50 rounded-lg p-4 mb-6">
              <p className="text-sm">
                <strong>Application ID:</strong> {applicationId}
              </p>
              <p className="text-sm mt-1">
                Please save this ID for future reference and check your email for confirmation.
              </p>
            </div>
            <Button size="lg">
              Track Application Status
            </Button>
          </motion.div>
        </div>
      </section>
    )
  }

  return (
    <section ref={ref} className="section-padding bg-muted/30" id="application-form">
      <div className="container">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
          className="max-w-4xl mx-auto"
        >
          {/* Section Header */}
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Complete Your{' '}
              <span className="gradient-text">University Application</span>
            </h2>
            <p className="text-xl text-muted-foreground">
              Follow our guided process to submit your application in just a few minutes
            </p>
          </div>

          {/* Progress Steps */}
          <div className="mb-12">
            <div className="flex items-center justify-between max-w-2xl mx-auto">
              {steps.map((step, index) => (
                <div key={step.id} className="flex items-center">
                  <div className={`flex items-center justify-center w-12 h-12 rounded-full border-2 transition-all duration-300 ${
                    currentStep >= step.id
                      ? 'bg-primary border-primary text-primary-foreground'
                      : 'border-muted-foreground/30 text-muted-foreground'
                  }`}>
                    <step.icon className="w-5 h-5" />
                  </div>
                  {index < steps.length - 1 && (
                    <div className={`w-16 h-0.5 mx-2 transition-all duration-300 ${
                      currentStep > step.id ? 'bg-primary' : 'bg-muted-foreground/30'
                    }`} />
                  )}
                </div>
              ))}
            </div>
            <div className="flex justify-between max-w-2xl mx-auto mt-4">
              {steps.map((step) => (
                <div key={step.id} className="text-center flex-1">
                  <div className={`text-sm font-medium transition-colors ${
                    currentStep >= step.id ? 'text-primary' : 'text-muted-foreground'
                  }`}>
                    {step.title}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Form Content */}
          <motion.div
            key={currentStep}
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -50 }}
            transition={{ duration: 0.3 }}
            className="bg-background rounded-xl p-8 shadow-sm border"
          >
            <h3 className="text-2xl font-bold mb-6">{steps[currentStep - 1].title}</h3>

            {/* Step 1: Personal Information */}
            {currentStep === 1 && (
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium mb-2">First Name *</label>
                  <input
                    type="text"
                    name="firstName"
                    value={formData.firstName}
                    onChange={handleInputChange}
                    className="w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                    placeholder="Enter your first name"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">Last Name *</label>
                  <input
                    type="text"
                    name="lastName"
                    value={formData.lastName}
                    onChange={handleInputChange}
                    className="w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                    placeholder="Enter your last name"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">Email Address *</label>
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    className="w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                    placeholder="<EMAIL>"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">Phone Number *</label>
                  <input
                    type="tel"
                    name="phone"
                    value={formData.phone}
                    onChange={handleInputChange}
                    className="w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                    placeholder="+****************"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">Date of Birth *</label>
                  <input
                    type="date"
                    name="dateOfBirth"
                    value={formData.dateOfBirth}
                    onChange={handleInputChange}
                    className="w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">Nationality *</label>
                  <select
                    name="nationality"
                    value={formData.nationality}
                    onChange={handleInputChange}
                    className="w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                  >
                    <option value="">Select your nationality</option>
                    <option value="US">United States</option>
                    <option value="UK">United Kingdom</option>
                    <option value="CA">Canada</option>
                    <option value="AU">Australia</option>
                    <option value="DE">Germany</option>
                    <option value="FR">France</option>
                    <option value="other">Other</option>
                  </select>
                </div>
              </div>
            )}

            {/* Step 2: Academic Background */}
            {currentStep === 2 && (
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium mb-2">High School Name *</label>
                  <input
                    type="text"
                    name="highSchoolName"
                    value={formData.highSchoolName}
                    onChange={handleInputChange}
                    className="w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                    placeholder="Enter your high school name"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">Graduation Year *</label>
                  <select
                    name="graduationYear"
                    value={formData.graduationYear}
                    onChange={handleInputChange}
                    className="w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                  >
                    <option value="">Select graduation year</option>
                    {Array.from({ length: 10 }, (_, i) => 2024 - i).map(year => (
                      <option key={year} value={year}>{year}</option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">GPA / Grade Average *</label>
                  <input
                    type="text"
                    name="gpa"
                    value={formData.gpa}
                    onChange={handleInputChange}
                    className="w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                    placeholder="e.g., 3.5/4.0 or 85%"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">English Proficiency</label>
                  <select
                    name="englishProficiency"
                    value={formData.englishProficiency}
                    onChange={handleInputChange}
                    className="w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                  >
                    <option value="">Select test type</option>
                    <option value="IELTS">IELTS</option>
                    <option value="TOEFL">TOEFL</option>
                    <option value="Native">Native English Speaker</option>
                    <option value="None">No test taken yet</option>
                  </select>
                </div>
              </div>
            )}

            {/* Step 3: Program Selection */}
            {currentStep === 3 && (
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium mb-2">Preferred University *</label>
                  <select
                    name="preferredUniversity"
                    value={formData.preferredUniversity}
                    onChange={handleInputChange}
                    className="w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                  >
                    <option value="">Select preferred university</option>
                    <option value="EMU">Eastern Mediterranean University</option>
                    <option value="NEU">Near East University</option>
                    <option value="CIU">Cyprus International University</option>
                    <option value="UKU">University of Kyrenia</option>
                  </select>
                </div>
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium mb-2">First Choice Program *</label>
                    <select
                      name="firstChoiceProgram"
                      value={formData.firstChoiceProgram}
                      onChange={handleInputChange}
                      className="w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                    >
                      <option value="">Select program</option>
                      <option value="Computer Engineering">Computer Engineering</option>
                      <option value="Business Administration">Business Administration</option>
                      <option value="Medicine">Medicine</option>
                      <option value="Architecture">Architecture</option>
                      <option value="Psychology">Psychology</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2">Second Choice Program</label>
                    <select
                      name="secondChoiceProgram"
                      value={formData.secondChoiceProgram}
                      onChange={handleInputChange}
                      className="w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                    >
                      <option value="">Select program</option>
                      <option value="Computer Engineering">Computer Engineering</option>
                      <option value="Business Administration">Business Administration</option>
                      <option value="Medicine">Medicine</option>
                      <option value="Architecture">Architecture</option>
                      <option value="Psychology">Psychology</option>
                    </select>
                  </div>
                </div>
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium mb-2">Intake Year *</label>
                    <select
                      name="intakeYear"
                      value={formData.intakeYear}
                      onChange={handleInputChange}
                      className="w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                    >
                      <option value="">Select year</option>
                      <option value="2024">2024</option>
                      <option value="2025">2025</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2">Intake Semester *</label>
                    <select
                      name="intakeSemester"
                      value={formData.intakeSemester}
                      onChange={handleInputChange}
                      className="w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                    >
                      <option value="">Select semester</option>
                      <option value="Fall">Fall (September)</option>
                      <option value="Spring">Spring (February)</option>
                      <option value="Summer">Summer (June)</option>
                    </select>
                  </div>
                </div>
              </div>
            )}

            {/* Step 4: Review & Submit */}
            {currentStep === 4 && (
              <div className="space-y-6">
                <div className="bg-muted/50 rounded-lg p-6">
                  <h4 className="font-semibold mb-4">Application Summary</h4>
                  <div className="grid md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <strong>Name:</strong> {formData.firstName} {formData.lastName}
                    </div>
                    <div>
                      <strong>Email:</strong> {formData.email}
                    </div>
                    <div>
                      <strong>Nationality:</strong> {formData.nationality}
                    </div>
                    <div>
                      <strong>High School:</strong> {formData.highSchoolName}
                    </div>
                    <div>
                      <strong>Preferred University:</strong> {formData.preferredUniversity}
                    </div>
                    <div>
                      <strong>First Choice Program:</strong> {formData.firstChoiceProgram}
                    </div>
                  </div>
                </div>

                <div className="bg-blue-50 dark:bg-blue-950/20 rounded-lg p-6 border border-blue-200 dark:border-blue-800">
                  <h4 className="font-semibold mb-2 text-blue-800 dark:text-blue-200">Next Steps</h4>
                  <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                    <li>• Our admissions team will review your application within 24-48 hours</li>
                    <li>• You'll receive an email with document requirements and next steps</li>
                    <li>• A dedicated counselor will be assigned to guide you through the process</li>
                    <li>• We'll help you with visa applications and accommodation arrangements</li>
                  </ul>
                </div>
              </div>
            )}

            {/* Navigation Buttons */}
            <div className="flex justify-between mt-8">
              <Button
                variant="outline"
                onClick={prevStep}
                disabled={currentStep === 1}
                className="flex items-center"
              >
                <ChevronLeft className="w-4 h-4 mr-2" />
                Previous
              </Button>

              {currentStep < steps.length ? (
                <Button onClick={nextStep} className="flex items-center">
                  Next
                  <ChevronRight className="w-4 h-4 ml-2" />
                </Button>
              ) : (
                <Button
                  onClick={handleSubmit}
                  disabled={isSubmitting}
                  className="flex items-center"
                  size="lg"
                >
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                      Submitting...
                    </>
                  ) : (
                    <>
                      Submit Application
                      <Send className="w-4 h-4 ml-2" />
                    </>
                  )}
                </Button>
              )}
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  )
}
