// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}

model Application {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Personal Information
  firstName   String
  lastName    String
  email       String
  phone       String?
  dateOfBirth String?
  nationality String?
  passportNumber String?

  // Academic Background
  highSchoolName     String?
  graduationYear     String?
  gpa               String?
  englishProficiency String?

  // Program Selection
  preferredUniversity   String?
  firstChoiceProgram   String?
  secondChoiceProgram  String?
  intakeYear          String?
  intakeSemester      String?

  // Application Status
  status              ApplicationStatus @default(PENDING)
  applicationId       String            @unique
  assignedCounselor   String?
  notes              String?

  @@map("applications")
}

model ContactInquiry {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Contact Information
  name                String
  email               String
  phone               String?
  subject             String
  message             String
  preferredContact    String
  interestedServices  String? // JSON string of array

  // Inquiry Status
  status              InquiryStatus @default(NEW)
  inquiryId           String        @unique
  assignedAgent       String?
  responseNotes       String?
  respondedAt         DateTime?

  @@map("contact_inquiries")
}

model Newsletter {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now())
  email     String   @unique
  isActive  Boolean  @default(true)

  @@map("newsletter_subscribers")
}

enum ApplicationStatus {
  PENDING
  UNDER_REVIEW
  DOCUMENTS_REQUESTED
  DOCUMENTS_RECEIVED
  APPROVED
  REJECTED
  ENROLLED
}

enum InquiryStatus {
  NEW
  IN_PROGRESS
  RESPONDED
  CLOSED
}
