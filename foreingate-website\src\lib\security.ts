import crypto from 'crypto'

// Security utilities for the application

/**
 * Generate a secure random string
 */
export function generateSecureToken(length: number = 32): string {
  return crypto.randomBytes(length).toString('hex')
}

/**
 * Hash a password using PBKDF2
 */
export async function hashPassword(password: string): Promise<string> {
  const salt = crypto.randomBytes(16).toString('hex')
  const hash = crypto.pbkdf2Sync(password, salt, 10000, 64, 'sha512').toString('hex')
  return `${salt}:${hash}`
}

/**
 * Verify a password against its hash
 */
export async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
  const [salt, hash] = hashedPassword.split(':')
  const verifyHash = crypto.pbkdf2Sync(password, salt, 10000, 64, 'sha512').toString('hex')
  return hash === verifyHash
}

/**
 * Encrypt sensitive data
 */
export function encryptData(data: string, key?: string): string {
  const encryptionKey = key || process.env.ENCRYPTION_KEY || 'default-key-change-in-production'
  const algorithm = 'aes-256-gcm'
  const iv = crypto.randomBytes(16)
  
  const cipher = crypto.createCipher(algorithm, encryptionKey)
  let encrypted = cipher.update(data, 'utf8', 'hex')
  encrypted += cipher.final('hex')
  
  return `${iv.toString('hex')}:${encrypted}`
}

/**
 * Decrypt sensitive data
 */
export function decryptData(encryptedData: string, key?: string): string {
  const encryptionKey = key || process.env.ENCRYPTION_KEY || 'default-key-change-in-production'
  const algorithm = 'aes-256-gcm'
  
  const [ivHex, encrypted] = encryptedData.split(':')
  const iv = Buffer.from(ivHex, 'hex')
  
  const decipher = crypto.createDecipher(algorithm, encryptionKey)
  let decrypted = decipher.update(encrypted, 'hex', 'utf8')
  decrypted += decipher.final('utf8')
  
  return decrypted
}

/**
 * Sanitize user input to prevent XSS
 */
export function sanitizeInput(input: string): string {
  return input
    .replace(/[<>]/g, '') // Remove < and >
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, '') // Remove event handlers
    .trim()
}

/**
 * Validate email format
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

/**
 * Validate phone number format
 */
export function isValidPhone(phone: string): boolean {
  const phoneRegex = /^\+?[\d\s\-\(\)]{10,}$/
  return phoneRegex.test(phone)
}

/**
 * Generate CSRF token
 */
export function generateCSRFToken(): string {
  return crypto.randomBytes(32).toString('hex')
}

/**
 * Verify CSRF token
 */
export function verifyCSRFToken(token: string, sessionToken: string): boolean {
  return crypto.timingSafeEqual(
    Buffer.from(token, 'hex'),
    Buffer.from(sessionToken, 'hex')
  )
}

/**
 * Rate limiting helper
 */
export class RateLimiter {
  private requests: Map<string, number[]> = new Map()
  private maxRequests: number
  private windowMs: number

  constructor(maxRequests: number = 100, windowMs: number = 60000) {
    this.maxRequests = maxRequests
    this.windowMs = windowMs
  }

  isAllowed(identifier: string): boolean {
    const now = Date.now()
    const requests = this.requests.get(identifier) || []
    
    // Remove old requests outside the window
    const validRequests = requests.filter(time => now - time < this.windowMs)
    
    if (validRequests.length >= this.maxRequests) {
      return false
    }
    
    validRequests.push(now)
    this.requests.set(identifier, validRequests)
    return true
  }

  getRemainingRequests(identifier: string): number {
    const requests = this.requests.get(identifier) || []
    const now = Date.now()
    const validRequests = requests.filter(time => now - time < this.windowMs)
    return Math.max(0, this.maxRequests - validRequests.length)
  }
}

/**
 * Secure session management
 */
export class SecureSession {
  private static sessions: Map<string, any> = new Map()

  static create(data: any): string {
    const sessionId = generateSecureToken(32)
    const sessionData = {
      ...data,
      createdAt: Date.now(),
      lastAccessed: Date.now()
    }
    this.sessions.set(sessionId, sessionData)
    return sessionId
  }

  static get(sessionId: string): any | null {
    const session = this.sessions.get(sessionId)
    if (!session) return null

    // Check if session is expired (24 hours)
    if (Date.now() - session.lastAccessed > 24 * 60 * 60 * 1000) {
      this.sessions.delete(sessionId)
      return null
    }

    // Update last accessed time
    session.lastAccessed = Date.now()
    return session
  }

  static destroy(sessionId: string): void {
    this.sessions.delete(sessionId)
  }

  static cleanup(): void {
    const now = Date.now()
    for (const [sessionId, session] of this.sessions.entries()) {
      if (now - session.lastAccessed > 24 * 60 * 60 * 1000) {
        this.sessions.delete(sessionId)
      }
    }
  }
}

/**
 * Input validation schemas
 */
export const ValidationSchemas = {
  email: (email: string) => {
    if (!email || email.length > 254) return false
    return isValidEmail(email)
  },
  
  name: (name: string) => {
    if (!name || name.length < 2 || name.length > 100) return false
    return /^[a-zA-Z\s\-'\.]+$/.test(name)
  },
  
  phone: (phone: string) => {
    if (!phone) return true // Optional field
    return isValidPhone(phone)
  },
  
  message: (message: string) => {
    if (!message || message.length < 10 || message.length > 5000) return false
    return true
  },
  
  subject: (subject: string) => {
    if (!subject || subject.length < 5 || subject.length > 200) return false
    return true
  }
}

/**
 * Security audit logger
 */
export class SecurityLogger {
  static log(event: string, details: any, level: 'info' | 'warn' | 'error' = 'info') {
    const logEntry = {
      timestamp: new Date().toISOString(),
      event,
      details,
      level
    }
    
    // In production, send to logging service
    if (process.env.NODE_ENV === 'production') {
      // Send to external logging service
      console.log('[SECURITY]', JSON.stringify(logEntry))
    } else {
      console.log(`[SECURITY ${level.toUpperCase()}]`, event, details)
    }
  }

  static logFailedLogin(ip: string, userAgent: string) {
    this.log('FAILED_LOGIN', { ip, userAgent }, 'warn')
  }

  static logSuspiciousActivity(ip: string, activity: string) {
    this.log('SUSPICIOUS_ACTIVITY', { ip, activity }, 'error')
  }

  static logDataAccess(resource: string, ip: string) {
    this.log('DATA_ACCESS', { resource, ip }, 'info')
  }
}
