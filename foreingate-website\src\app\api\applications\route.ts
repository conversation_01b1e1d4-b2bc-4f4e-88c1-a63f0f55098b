import { NextRequest, NextResponse } from 'next/server'
import { writeFile, readFile, mkdir } from 'fs/promises'
import { existsSync } from 'fs'
import path from 'path'

const DATA_DIR = path.join(process.cwd(), 'data')
const APPLICATIONS_FILE = path.join(DATA_DIR, 'applications.json')

// Ensure data directory exists
async function ensureDataDir() {
  if (!existsSync(DATA_DIR)) {
    await mkdir(DATA_DIR, { recursive: true })
  }
}

// Read applications from file
async function readApplications() {
  try {
    await ensureDataDir()
    if (!existsSync(APPLICATIONS_FILE)) {
      return []
    }
    const data = await readFile(APPLICATIONS_FILE, 'utf-8')
    return JSON.parse(data)
  } catch (error) {
    console.error('Error reading applications:', error)
    return []
  }
}

// Write applications to file
async function writeApplications(applications: any[]) {
  try {
    await ensureDataDir()
    await writeFile(APPLICATIONS_FILE, JSON.stringify(applications, null, 2))
  } catch (error) {
    console.error('Error writing applications:', error)
    throw error
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      firstName,
      lastName,
      email,
      phone,
      dateOfBirth,
      nationality,
      passportNumber,
      highSchoolName,
      graduationYear,
      gpa,
      englishProficiency,
      preferredUniversity,
      firstChoiceProgram,
      secondChoiceProgram,
      intakeYear,
      intakeSemester
    } = body

    // Validate required fields
    if (!firstName || !lastName || !email) {
      return NextResponse.json(
        { error: 'Missing required fields: firstName, lastName, email' },
        { status: 400 }
      )
    }

    // Generate unique application ID
    const applicationId = `FG-${Date.now()}-${Math.random().toString(36).substr(2, 9).toUpperCase()}`

    // Create application object
    const application = {
      id: Math.random().toString(36).substr(2, 9),
      applicationId,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      status: 'PENDING',
      
      // Personal Information
      firstName,
      lastName,
      email,
      phone: phone || null,
      dateOfBirth: dateOfBirth || null,
      nationality: nationality || null,
      passportNumber: passportNumber || null,

      // Academic Background
      highSchoolName: highSchoolName || null,
      graduationYear: graduationYear || null,
      gpa: gpa || null,
      englishProficiency: englishProficiency || null,

      // Program Selection
      preferredUniversity: preferredUniversity || null,
      firstChoiceProgram: firstChoiceProgram || null,
      secondChoiceProgram: secondChoiceProgram || null,
      intakeYear: intakeYear || null,
      intakeSemester: intakeSemester || null,

      // Additional fields
      assignedCounselor: null,
      notes: null
    }

    // Read existing applications
    const applications = await readApplications()
    
    // Add new application
    applications.push(application)
    
    // Write back to file
    await writeApplications(applications)

    // Send confirmation email (if email service is configured)
    try {
      if (process.env.RESEND_API_KEY) {
        const { Resend } = require('resend')
        const resend = new Resend(process.env.RESEND_API_KEY)

        const confirmationEmailHtml = `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center;">
              <h1 style="color: white; margin: 0;">Application Submitted Successfully!</h1>
              <p style="color: white; opacity: 0.9; margin: 10px 0 0 0;">Foreingate Group</p>
            </div>
            
            <div style="padding: 30px; background: #f8f9fa;">
              <p style="color: #333; font-size: 16px; line-height: 1.6;">Dear ${firstName} ${lastName},</p>
              
              <p style="color: #555; line-height: 1.6;">
                Thank you for submitting your university application through Foreingate Group! We have successfully received your application and our admissions team will begin processing it immediately.
              </p>

              <div style="background: #e8f5e8; padding: 20px; border-radius: 8px; border-left: 4px solid #4caf50; margin: 20px 0;">
                <h3 style="color: #2e7d32; margin: 0 0 10px 0;">📋 Application Details</h3>
                <p style="margin: 5px 0; color: #555;"><strong>Application ID:</strong> ${applicationId}</p>
                <p style="margin: 5px 0; color: #555;"><strong>Preferred University:</strong> ${preferredUniversity || 'Not specified'}</p>
                <p style="margin: 5px 0; color: #555;"><strong>First Choice Program:</strong> ${firstChoiceProgram || 'Not specified'}</p>
                <p style="margin: 5px 0; color: #555;"><strong>Intake:</strong> ${intakeSemester || 'Not specified'} ${intakeYear || ''}</p>
                <p style="margin: 5px 0; color: #555;"><strong>Submitted:</strong> ${new Date().toLocaleString()}</p>
              </div>

              <div style="background: #fff3e0; padding: 20px; border-radius: 8px; border-left: 4px solid #ff9800; margin: 20px 0;">
                <h3 style="color: #f57c00; margin: 0 0 10px 0;">📋 Next Steps</h3>
                <ol style="color: #555; margin: 0; padding-left: 20px;">
                  <li>Our admissions team will review your application within 24-48 hours</li>
                  <li>A dedicated counselor will be assigned to your case</li>
                  <li>You'll receive an email with required documents list</li>
                  <li>We'll guide you through the entire admission process</li>
                  <li>Visa support and accommodation assistance will be provided</li>
                </ol>
              </div>

              <div style="background: #e3f2fd; padding: 20px; border-radius: 8px; border-left: 4px solid #2196f3; margin: 20px 0;">
                <h3 style="color: #1976d2; margin: 0 0 10px 0;">📞 Contact Information</h3>
                <p style="color: #555; margin: 0;">
                  <strong>Phone:</strong> +90 ************<br>
                  <strong>WhatsApp:</strong> +90 ************<br>
                  <strong>Email:</strong> <EMAIL>
                </p>
              </div>

              <p style="color: #555; line-height: 1.6;">
                We're excited to help you achieve your educational goals and look forward to welcoming you to our community of successful students!
              </p>

              <p style="color: #555; line-height: 1.6;">
                Best regards,<br>
                <strong>The Foreingate Group Admissions Team</strong><br>
                <em>Your Gateway to International Education</em>
              </p>
            </div>

            <div style="background: #333; padding: 20px; text-align: center;">
              <p style="color: #ccc; margin: 0; font-size: 14px;">
                © 2024 Foreingate Group. All rights reserved.<br>
                Please save your Application ID: <strong>${applicationId}</strong>
              </p>
            </div>
          </div>
        `

        await resend.emails.send({
          from: 'Foreingate Admissions <<EMAIL>>',
          to: [email],
          subject: `Application Received - ${applicationId}`,
          html: confirmationEmailHtml,
        })

        // Send notification to admin
        await resend.emails.send({
          from: 'Foreingate System <<EMAIL>>',
          to: ['<EMAIL>'],
          subject: `New Application: ${firstName} ${lastName} - ${applicationId}`,
          html: `
            <h2>New University Application Received</h2>
            <p><strong>Application ID:</strong> ${applicationId}</p>
            <p><strong>Student:</strong> ${firstName} ${lastName}</p>
            <p><strong>Email:</strong> ${email}</p>
            <p><strong>Phone:</strong> ${phone || 'Not provided'}</p>
            <p><strong>Preferred University:</strong> ${preferredUniversity || 'Not specified'}</p>
            <p><strong>First Choice Program:</strong> ${firstChoiceProgram || 'Not specified'}</p>
            <p><strong>Intake:</strong> ${intakeSemester || 'Not specified'} ${intakeYear || ''}</p>
            <p><strong>Submitted:</strong> ${new Date().toLocaleString()}</p>
            <hr>
            <p>Please assign a counselor and begin the application review process.</p>
          `,
        })
      }
    } catch (emailError) {
      console.error('Email sending failed:', emailError)
      // Don't fail the application if email fails
    }

    return NextResponse.json({
      success: true,
      message: 'Application submitted successfully!',
      applicationId,
      status: 'PENDING'
    })

  } catch (error) {
    console.error('Application submission error:', error)
    return NextResponse.json(
      { error: 'Failed to submit application. Please try again.' },
      { status: 500 }
    )
  }
}

// GET endpoint to retrieve applications (for admin use)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status')
    const search = searchParams.get('search')
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const sortBy = searchParams.get('sortBy') || 'createdAt'
    const sortOrder = searchParams.get('sortOrder') || 'desc'

    let applications = await readApplications()

    // Apply filters
    if (status && status !== 'all') {
      applications = applications.filter((app: any) =>
        app.status.toLowerCase() === status.toLowerCase()
      )
    }

    if (search) {
      applications = applications.filter((app: any) =>
        app.firstName.toLowerCase().includes(search.toLowerCase()) ||
        app.lastName.toLowerCase().includes(search.toLowerCase()) ||
        app.email.toLowerCase().includes(search.toLowerCase()) ||
        app.applicationId.toLowerCase().includes(search.toLowerCase()) ||
        (app.preferredUniversity && app.preferredUniversity.toLowerCase().includes(search.toLowerCase())) ||
        (app.firstChoiceProgram && app.firstChoiceProgram.toLowerCase().includes(search.toLowerCase()))
      )
    }

    // Sort applications
    applications.sort((a: any, b: any) => {
      let aValue = a[sortBy]
      let bValue = b[sortBy]

      if (sortBy === 'createdAt' || sortBy === 'updatedAt') {
        aValue = new Date(aValue).getTime()
        bValue = new Date(bValue).getTime()
      }

      if (sortOrder === 'desc') {
        return bValue > aValue ? 1 : -1
      } else {
        return aValue > bValue ? 1 : -1
      }
    })

    // Pagination
    const total = applications.length
    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit
    const paginatedApplications = applications.slice(startIndex, endIndex)

    // Calculate statistics
    const stats = {
      total: applications.length,
      pending: applications.filter((app: any) => app.status === 'PENDING').length,
      underReview: applications.filter((app: any) => app.status === 'UNDER_REVIEW').length,
      approved: applications.filter((app: any) => app.status === 'APPROVED').length,
      rejected: applications.filter((app: any) => app.status === 'REJECTED').length,
      enrolled: applications.filter((app: any) => app.status === 'ENROLLED').length,
    }

    return NextResponse.json({
      success: true,
      data: paginatedApplications,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: endIndex < total,
        hasPrev: page > 1
      },
      stats
    })
  } catch (error) {
    console.error('Error fetching applications:', error)
    return NextResponse.json(
      { error: 'Failed to fetch applications' },
      { status: 500 }
    )
  }
}
