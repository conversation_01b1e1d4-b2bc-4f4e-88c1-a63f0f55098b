'use client'

import { motion } from 'framer-motion'
import { useInView } from 'react-intersection-observer'
import { Eye, Target, Heart, Lightbulb } from 'lucide-react'

const values = [
  {
    icon: Heart,
    title: 'Student-Centric Approach',
    description: 'Every decision we make is guided by what\'s best for our students. Their success is our success.'
  },
  {
    icon: Lightbulb,
    title: 'Innovation & Excellence',
    description: 'We continuously innovate our services and maintain the highest standards of excellence in everything we do.'
  },
  {
    icon: Target,
    title: 'Integrity & Transparency',
    description: 'We believe in honest communication and transparent processes, building trust through reliability.'
  },
  {
    icon: Eye,
    title: 'Global Perspective',
    description: 'We embrace diversity and bring a global perspective to education, connecting cultures through learning.'
  }
]

export function VisionMissionSection() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1
  })

  return (
    <section ref={ref} className="section-padding bg-muted/30">
      <div className="container">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
          className="text-center max-w-3xl mx-auto mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Our Vision &{' '}
            <span className="gradient-text">Mission</span>
          </h2>
          <p className="text-xl text-muted-foreground">
            Guided by our core values and driven by our commitment to educational excellence
          </p>
        </motion.div>

        {/* Vision & Mission Cards */}
        <div className="grid md:grid-cols-2 gap-8 mb-16">
          {/* Vision Card */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={inView ? { opacity: 1, x: 0 } : {}}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="bg-gradient-to-br from-primary/5 to-primary/10 rounded-2xl p-8 border border-primary/20"
          >
            <div className="flex items-center mb-6">
              <div className="w-12 h-12 bg-primary/20 rounded-lg flex items-center justify-center mr-4">
                <Eye className="w-6 h-6 text-primary" />
              </div>
              <h3 className="text-2xl font-bold">Our Vision</h3>
            </div>
            <p className="text-lg text-muted-foreground leading-relaxed">
              To be the world's leading educational consultancy, recognized for transforming 
              lives through accessible, quality education. We envision a future where every 
              student, regardless of their background, has the opportunity to pursue their 
              academic dreams and contribute meaningfully to global society.
            </p>
          </motion.div>

          {/* Mission Card */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={inView ? { opacity: 1, x: 0 } : {}}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="bg-gradient-to-br from-secondary/5 to-secondary/10 rounded-2xl p-8 border border-secondary/20"
          >
            <div className="flex items-center mb-6">
              <div className="w-12 h-12 bg-secondary/20 rounded-lg flex items-center justify-center mr-4">
                <Target className="w-6 h-6 text-secondary" />
              </div>
              <h3 className="text-2xl font-bold">Our Mission</h3>
            </div>
            <p className="text-lg text-muted-foreground leading-relaxed">
              To provide comprehensive, personalized educational consulting services that 
              empower students to achieve their academic goals. We are committed to 
              simplifying the study abroad process, ensuring every student receives the 
              support they need to succeed in their educational journey.
            </p>
          </motion.div>
        </div>

        {/* Core Values */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="text-center mb-12"
        >
          <h3 className="text-3xl font-bold mb-4">Our Core Values</h3>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            These fundamental principles guide our actions and shape our culture
          </p>
        </motion.div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
          {values.map((value, index) => (
            <motion.div
              key={value.title}
              initial={{ opacity: 0, y: 30 }}
              animate={inView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.6, delay: 0.8 + index * 0.1 }}
              className="bg-background rounded-xl p-6 shadow-sm border hover:shadow-md transition-all duration-300 hover:border-primary/20 group"
            >
              <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4 group-hover:bg-primary/20 transition-colors">
                <value.icon className="w-6 h-6 text-primary" />
              </div>
              <h4 className="text-lg font-semibold mb-3">{value.title}</h4>
              <p className="text-muted-foreground text-sm leading-relaxed">
                {value.description}
              </p>
            </motion.div>
          ))}
        </div>

        {/* Commitment Statement */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 1.2 }}
          className="mt-16 text-center bg-gradient-to-r from-primary/5 to-secondary/5 rounded-2xl p-8 md:p-12"
        >
          <h3 className="text-2xl md:text-3xl font-bold mb-6">Our Commitment to You</h3>
          <p className="text-lg text-muted-foreground max-w-4xl mx-auto leading-relaxed">
            At Foreingate Group, we don't just help you get admitted to a university – we partner 
            with you throughout your entire educational journey. From the initial consultation to 
            graduation day and beyond, we're committed to your success. Our comprehensive support 
            system ensures that you have the resources, guidance, and encouragement needed to 
            thrive in your academic pursuits and achieve your career aspirations.
          </p>
        </motion.div>
      </div>
    </section>
  )
}
