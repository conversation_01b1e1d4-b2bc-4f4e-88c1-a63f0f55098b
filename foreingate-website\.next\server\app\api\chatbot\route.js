/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/chatbot/route";
exports.ids = ["app/api/chatbot/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchatbot%2Froute&page=%2Fapi%2Fchatbot%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchatbot%2Froute.ts&appDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchatbot%2Froute&page=%2Fapi%2Fchatbot%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchatbot%2Froute.ts&appDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Nidhal_Documents_augment_projects_foreingate_groupe_foreingate_website_src_app_api_chatbot_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/chatbot/route.ts */ \"(rsc)/./src/app/api/chatbot/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"standalone\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/chatbot/route\",\n        pathname: \"/api/chatbot\",\n        filename: \"route\",\n        bundlePath: \"app/api/chatbot/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\api\\\\chatbot\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Nidhal_Documents_augment_projects_foreingate_groupe_foreingate_website_src_app_api_chatbot_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchatbot%2Froute&page=%2Fapi%2Fchatbot%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchatbot%2Froute.ts&appDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/chatbot/route.ts":
/*!**************************************!*\
  !*** ./src/app/api/chatbot/route.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var _lib_chatbot_knowledge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/chatbot-knowledge */ \"(rsc)/./src/lib/chatbot-knowledge.ts\");\n/* harmony import */ var _lib_rag_system__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/rag-system */ \"(rsc)/./src/lib/rag-system.ts\");\n/* harmony import */ var _lib_api_security__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api-security */ \"(rsc)/./src/lib/api-security.ts\");\n/* harmony import */ var _lib_security__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/security */ \"(rsc)/./src/lib/security.ts\");\n\n\n\n\nclass SmartChatbot {\n    constructor(){\n        this.knowledge = _lib_chatbot_knowledge__WEBPACK_IMPORTED_MODULE_0__.websiteKnowledge;\n        this.categories = _lib_chatbot_knowledge__WEBPACK_IMPORTED_MODULE_0__.questionCategories;\n        this.ragGenerator = new _lib_rag_system__WEBPACK_IMPORTED_MODULE_1__.RAGResponseGenerator();\n        this.retriever = new _lib_rag_system__WEBPACK_IMPORTED_MODULE_1__.KnowledgeRetriever();\n    }\n    // Analyze user message and determine intent\n    analyzeIntent(message) {\n        const cleanMessage = message.toLowerCase().trim();\n        const words = cleanMessage.split(/\\s+/);\n        let bestCategory = 'general';\n        let maxScore = 0;\n        let matchedKeywords = [];\n        // Check each category for keyword matches\n        for (const [category, keywords] of Object.entries(this.categories)){\n            let score = 0;\n            const categoryMatches = [];\n            for (const keyword of keywords){\n                if (cleanMessage.includes(keyword)) {\n                    score += keyword.length // Longer keywords get higher scores\n                    ;\n                    categoryMatches.push(keyword);\n                }\n            }\n            if (score > maxScore) {\n                maxScore = score;\n                bestCategory = category;\n                matchedKeywords = categoryMatches;\n            }\n        }\n        const confidence = Math.min(maxScore / cleanMessage.length * 100, 100);\n        return {\n            category: bestCategory,\n            confidence,\n            keywords: matchedKeywords\n        };\n    }\n    // Generate intelligent response using RAG\n    generateResponse(message) {\n        const { category, confidence, keywords } = this.analyzeIntent(message);\n        // First try RAG approach for more intelligent responses\n        const ragResult = this.ragGenerator.generateResponse(message, category);\n        // If RAG provides good results, use it\n        if (ragResult.confidence > 30 && ragResult.retrievedChunks > 0) {\n            return {\n                response: ragResult.response,\n                category,\n                confidence: ragResult.confidence,\n                suggestions: this.getSuggestionsForCategory(category),\n                followUp: this.getRandomFollowUp(),\n                data: ragResult.sources,\n                ragSources: ragResult.sources,\n                retrievedChunks: ragResult.retrievedChunks,\n                ragEnabled: true\n            };\n        }\n        // Fallback to traditional rule-based responses\n        let response = '';\n        let data = null;\n        let suggestions = [];\n        switch(category){\n            case 'admissions':\n                response = this.handleAdmissionsQuery(message, keywords);\n                data = this.knowledge.admissionProcess;\n                suggestions = [\n                    \"What documents do I need?\",\n                    \"How long does the process take?\",\n                    \"What are the requirements for graduate programs?\"\n                ];\n                break;\n            case 'universities':\n                response = this.handleUniversitiesQuery(message, keywords);\n                data = this.knowledge.universities;\n                suggestions = [\n                    \"Tell me about EMU\",\n                    \"What programs does NEU offer?\",\n                    \"Which university is best for engineering?\"\n                ];\n                break;\n            case 'costs':\n                response = this.handleCostsQuery(message, keywords);\n                data = this.knowledge.costs;\n                suggestions = [\n                    \"What are the living costs?\",\n                    \"Are there scholarships available?\",\n                    \"How much is tuition for medicine?\"\n                ];\n                break;\n            case 'visa':\n                response = this.handleVisaQuery(message, keywords);\n                suggestions = [\n                    \"What documents do I need for visa?\",\n                    \"How long does visa processing take?\",\n                    \"Do you help with visa applications?\"\n                ];\n                break;\n            case 'accommodation':\n                response = this.handleAccommodationQuery(message, keywords);\n                suggestions = [\n                    \"What housing options are available?\",\n                    \"How much does accommodation cost?\",\n                    \"Can you help me find housing?\"\n                ];\n                break;\n            case 'services':\n                response = this.handleServicesQuery(message, keywords);\n                data = this.knowledge.services;\n                suggestions = [\n                    \"What services do you offer?\",\n                    \"How much do your services cost?\",\n                    \"Do you provide ongoing support?\"\n                ];\n                break;\n            case 'location':\n                response = this.handleLocationQuery(message, keywords);\n                suggestions = [\n                    \"Is Northern Cyprus safe?\",\n                    \"What's the weather like?\",\n                    \"How do I get to Northern Cyprus?\"\n                ];\n                break;\n            case 'contact':\n                response = this.handleContactQuery(message, keywords);\n                data = this.knowledge.contact;\n                suggestions = [\n                    \"What are your office hours?\",\n                    \"Can I visit your office?\",\n                    \"Do you speak my language?\"\n                ];\n                break;\n            case 'scholarships':\n                response = this.handleScholarshipsQuery(message, keywords);\n                data = this.knowledge.scholarships;\n                suggestions = [\n                    \"What scholarships are available?\",\n                    \"How do I qualify for merit scholarship?\",\n                    \"What is the early bird discount?\"\n                ];\n                break;\n            case 'language':\n                response = this.handleLanguageQuery(message, keywords);\n                suggestions = [\n                    \"Do I need to speak Turkish?\",\n                    \"What English level is required?\",\n                    \"Are there language courses available?\"\n                ];\n                break;\n            default:\n                response = this.handleGeneralQuery(message, keywords);\n                suggestions = [\n                    \"Tell me about your services\",\n                    \"Which universities do you work with?\",\n                    \"How can you help me study abroad?\"\n                ];\n        }\n        return {\n            response,\n            category,\n            confidence,\n            suggestions,\n            followUp: this.getRandomFollowUp(),\n            data,\n            ragEnabled: false\n        };\n    }\n    getSuggestionsForCategory(category) {\n        const categoryMap = {\n            universities: [\n                \"Tell me about EMU programs\",\n                \"What makes NEU special?\",\n                \"Compare universities for engineering\"\n            ],\n            admissions: [\n                \"What documents do I need?\",\n                \"How long does the process take?\",\n                \"What are the requirements?\"\n            ],\n            costs: [\n                \"What are the living costs?\",\n                \"Are there scholarships available?\",\n                \"How much is tuition for medicine?\"\n            ],\n            scholarships: [\n                \"How do I qualify for merit scholarship?\",\n                \"What is the early bird discount?\",\n                \"Can I get multiple scholarships?\"\n            ],\n            visa: [\n                \"What documents do I need for visa?\",\n                \"How long does visa processing take?\",\n                \"Do you help with visa applications?\"\n            ],\n            accommodation: [\n                \"What housing options are available?\",\n                \"How much does accommodation cost?\",\n                \"Can you help me find housing?\"\n            ],\n            services: [\n                \"What services do you offer?\",\n                \"How much do your services cost?\",\n                \"Do you provide ongoing support?\"\n            ],\n            location: [\n                \"Is Northern Cyprus safe?\",\n                \"What's the weather like?\",\n                \"How do I get to Northern Cyprus?\"\n            ],\n            contact: [\n                \"What are your office hours?\",\n                \"Can I visit your office?\",\n                \"Do you speak my language?\"\n            ],\n            language: [\n                \"Do I need to speak Turkish?\",\n                \"What English level is required?\",\n                \"Are there language courses available?\"\n            ]\n        };\n        return categoryMap[category] || [\n            \"Tell me about your services\",\n            \"Which universities do you work with?\",\n            \"How can you help me study abroad?\"\n        ];\n    }\n    handleAdmissionsQuery(message, keywords) {\n        if (keywords.some((k)=>[\n                'requirements',\n                'documents'\n            ].includes(k))) {\n            return `📋 **Admission Requirements:**\n\n**For Undergraduate Programs:**\n• High school diploma or equivalent\n• English proficiency test (IELTS 6.0+ or TOEFL 79+)\n• Passport copy\n• Academic transcripts\n• Personal statement\n\n**For Graduate Programs:**\n• Bachelor's degree\n• English proficiency test\n• Letters of recommendation (2-3)\n• Statement of purpose\n• GRE/GMAT (for some programs)\n\n**Timeline:** The complete process typically takes 2-4 months. We handle everything from document preparation to final enrollment!\n\nWould you like me to help you start your application? 🎓`;\n        }\n        if (keywords.some((k)=>[\n                'deadline',\n                'when'\n            ].includes(k))) {\n            return `📅 **Application Deadlines:**\n\nGood news! We accept applications **year-round** for most programs. However, I recommend applying **3-4 months before** your intended start date for:\n\n• Better preparation time\n• Higher scholarship chances\n• Smoother visa processing\n• Better accommodation options\n\n**Intake Periods:**\n• Fall Semester: September\n• Spring Semester: February\n• Summer Programs: June\n\nReady to start your application? I can connect you with our admissions team! 🚀`;\n        }\n        return `🎓 **University Admissions Made Easy!**\n\nAt Foreingate, we make university admissions simple and stress-free. Our comprehensive service includes:\n\n✅ **Free consultation** and university matching\n✅ **Complete application** preparation and submission\n✅ **Document verification** and translation\n✅ **Interview coaching** (when required)\n✅ **Admission guarantee** or full refund\n\nWe've helped over 5,000 students secure admissions to top universities in Northern Cyprus. Our success rate is 98%!\n\nWhat specific aspect of admissions would you like to know more about?`;\n    }\n    handleUniversitiesQuery(message, keywords) {\n        const universities = this.knowledge.universities;\n        if (message.toLowerCase().includes('emu') || message.toLowerCase().includes('eastern mediterranean')) {\n            const emu = universities.find((u)=>u.name.includes('Eastern Mediterranean'));\n            return `🏛️ **Eastern Mediterranean University (EMU)**\n\n📍 **Location:** Famagusta, Northern Cyprus\n📅 **Established:** ${emu.established}\n👥 **Students:** ${emu.students.toLocaleString()} (${emu.international.toLocaleString()} international)\n\n**🎓 Popular Programs:**\n${emu.programs.map((p)=>`• ${p}`).join('\\n')}\n\n**💰 Tuition:** $${emu.tuition.min.toLocaleString()} - $${emu.tuition.max.toLocaleString()} per year\n**🌍 Language:** ${emu.language}\n**✅ Accreditation:** ${emu.accreditation}\n\nEMU is known for its strong engineering and business programs, beautiful campus, and vibrant student life!\n\nWould you like more details about specific programs? 🎯`;\n        }\n        if (message.toLowerCase().includes('neu') || message.toLowerCase().includes('near east')) {\n            const neu = universities.find((u)=>u.name.includes('Near East'));\n            return `🏥 **Near East University (NEU)**\n\n📍 **Location:** Nicosia, Northern Cyprus\n📅 **Established:** ${neu.established}\n👥 **Students:** ${neu.students.toLocaleString()} (${neu.international.toLocaleString()} international)\n\n**🎓 Popular Programs:**\n${neu.programs.map((p)=>`• ${p}`).join('\\n')}\n\n**💰 Tuition:** $${neu.tuition.min.toLocaleString()} - $${neu.tuition.max.toLocaleString()} per year\n**🌍 Language:** ${neu.language}\n**✅ Accreditation:** ${neu.accreditation}\n\nNEU is especially renowned for its **Medicine and Dentistry** programs, with WHO recognition and state-of-the-art facilities!\n\nInterested in medical programs? Let me know! 🩺`;\n        }\n        return `🏛️ **Our Partner Universities in Northern Cyprus:**\n\nWe work with the **top 3 universities** that welcome international students:\n\n**1. Eastern Mediterranean University (EMU)** 🌟\n   • 20,000+ students • Strong in Engineering & Business\n   \n**2. Near East University (NEU)** 🏥\n   • 25,000+ students • Excellent Medical Programs\n   \n**3. Cyprus International University (CIU)** 🎨\n   • 15,000+ students • Great for Business & Arts\n\n**All universities offer:**\n✅ English-taught programs\n✅ International accreditation\n✅ Modern facilities\n✅ Vibrant campus life\n✅ Affordable tuition\n\nWhich university interests you most? I can provide detailed information! 🎓`;\n    }\n    handleCostsQuery(message, keywords) {\n        const costs = this.knowledge.costs;\n        if (keywords.some((k)=>[\n                'scholarship',\n                'financial'\n            ].includes(k))) {\n            return `💰 **Scholarships & Financial Aid:**\n\n**Merit Scholarship** 🏆\n• 25-50% tuition reduction\n• For students with GPA 3.5+ or equivalent\n• Automatic consideration with application\n\n**Early Bird Discount** ⏰\n• 10-15% tuition reduction\n• Apply before March 31st\n• Available for all programs\n\n**Sibling Discount** 👨‍👩‍👧‍👦\n• 10% tuition reduction\n• For families with multiple students\n• Applies to second sibling onwards\n\n**Payment Plans** 💳\n• Flexible installment options\n• No interest charges\n• Customized to your budget\n\n**Total Savings Possible:** Up to 65% off tuition fees!\n\nReady to apply for scholarships? Let me help you! 🎯`;\n        }\n        return `💰 **Study Costs in Northern Cyprus:**\n\n**📚 Tuition Fees (per year):**\n• Undergraduate: $${costs.tuition.undergraduate.min.toLocaleString()} - $${costs.tuition.undergraduate.max.toLocaleString()}\n• Graduate: $${costs.tuition.graduate.min.toLocaleString()} - $${costs.tuition.graduate.max.toLocaleString()}\n• Medicine: $${costs.tuition.medicine.min.toLocaleString()} - $${costs.tuition.medicine.max.toLocaleString()}\n\n**🏠 Living Costs (per month):**\n• Accommodation: $${costs.living.accommodation.min} - $${costs.living.accommodation.max}\n• Food: $${costs.living.food.min} - $${costs.living.food.max}\n• Transportation: $${costs.living.transportation.min} - $${costs.living.transportation.max}\n• **Total Living:** $${costs.living.total.min} - $${costs.living.total.max}\n\n**💡 Why Northern Cyprus is Affordable:**\n✅ 50-70% cheaper than UK/US\n✅ High quality education\n✅ Low cost of living\n✅ Scholarship opportunities\n\nWant to know about scholarships to reduce costs even more? 🎓`;\n    }\n    handleVisaQuery(message, keywords) {\n        return `🛂 **Visa Support Services:**\n\n**We Handle Everything!** ✅\n• Complete visa application preparation\n• Document collection and verification\n• Embassy appointment scheduling\n• Application tracking and follow-up\n\n**📋 Typical Documents Needed:**\n• Valid passport (6+ months validity)\n• University acceptance letter\n• Financial proof (bank statements)\n• Health insurance\n• Accommodation proof\n• Passport photos\n\n**⏱️ Processing Time:**\n• Most countries: 2-4 weeks\n• Some countries: 4-8 weeks\n• We expedite when possible\n\n**💰 Our Visa Service:**\n• Starting from $200\n• Success rate: 95%+\n• Full support until visa approval\n\n**🌍 Visa-Free Countries:**\nSome nationalities don't need a visa for Northern Cyprus!\n\nWhich country are you from? I can give you specific visa information! 🌟`;\n    }\n    handleAccommodationQuery(message, keywords) {\n        return `🏠 **Student Accommodation Options:**\n\n**🏫 University Dormitories:**\n• $200-400/month\n• On-campus convenience\n• Meal plans available\n• Social environment\n\n**🏡 Private Apartments:**\n• $300-600/month\n• More privacy and space\n• Kitchen facilities\n• Shared or single options\n\n**👨‍👩‍👧‍👦 Homestay Programs:**\n• $250-450/month\n• Live with local families\n• Cultural immersion\n• Meals included\n\n**🚗 Additional Services:**\n✅ Airport pickup arrangement\n✅ Accommodation booking assistance\n✅ Contract negotiation help\n✅ Ongoing support\n\n**📍 Popular Areas:**\n• Near campus locations\n• City center options\n• Quiet residential areas\n\nNeed help finding the perfect accommodation? We'll match you with the best option for your budget and preferences! 🏡`;\n    }\n    handleServicesQuery(message, keywords) {\n        return `🎯 **Our Complete Services:**\n\n**🎓 University Admissions**\n• Free consultation & university matching\n• Application preparation & submission\n• Document verification & translation\n• Interview coaching\n\n**🛂 Visa Support**\n• Complete visa application assistance\n• Document preparation\n• Embassy appointments\n• Application tracking\n\n**🏠 Accommodation Services**\n• Housing search & booking\n• Contract assistance\n• Airport pickup\n• Settlement support\n\n**📚 Academic Support**\n• Tutoring services\n• Study groups\n• Academic counseling\n• Career guidance\n\n**💰 Pricing:**\n• Initial consultation: **FREE**\n• University application: Competitive rates\n• Visa support: From $200\n• Accommodation: Commission-based\n\n**🌟 Why Choose Foreingate:**\n✅ 98% success rate\n✅ 5,000+ students helped\n✅ End-to-end support\n✅ Multilingual team\n\nReady to start your journey? Let's talk! 🚀`;\n    }\n    handleLocationQuery(message, keywords) {\n        return `🌍 **About Northern Cyprus:**\n\n**📍 Location:**\n• Eastern Mediterranean island\n• Between Turkey and Middle East\n• Beautiful coastline and mountains\n• Rich history and culture\n\n**🌤️ Climate:**\n• Mediterranean climate\n• Warm summers, mild winters\n• 300+ sunny days per year\n• Perfect for outdoor activities\n\n**🛡️ Safety:**\n• Very safe for international students\n• Low crime rates\n• Welcoming local community\n• English widely spoken\n\n**✈️ Getting There:**\n• Fly via Turkey (Istanbul/Ankara)\n• Direct flights from many countries\n• Airport pickup service available\n• Easy visa process\n\n**🎭 Student Life:**\n• Vibrant international community\n• Cultural festivals and events\n• Beautiful beaches and nature\n• Affordable entertainment\n\n**💡 Why Students Love It:**\n✅ Safe and friendly environment\n✅ Affordable living costs\n✅ Rich cultural experience\n✅ Gateway to Europe and Asia\n\nCurious about life in Northern Cyprus? I can share more details! 🏖️`;\n    }\n    handleContactQuery(message, keywords) {\n        const contact = this.knowledge.contact;\n        return `📞 **Get in Touch with Foreingate:**\n\n**🏢 Office Contact:**\n• 📧 Email: ${contact.email}\n• 📱 Phone: ${contact.phone}\n• 💬 WhatsApp: ${contact.whatsapp}\n• 📍 Address: ${contact.address}\n\n**⏰ Working Hours:**\n${contact.workingHours}\n\n**🌍 Languages We Speak:**\n${contact.languages.join(' • ')}\n\n**💬 Instant Support:**\n• Live chat on our website\n• WhatsApp for quick questions\n• Email for detailed inquiries\n• Phone for urgent matters\n\n**🎯 Best Ways to Reach Us:**\n• **Quick questions:** WhatsApp\n• **Detailed consultation:** Email or phone\n• **Immediate help:** Live chat\n• **Document submission:** Email\n\n**📅 Free Consultation:**\nBook a free 30-minute consultation to discuss your education plans!\n\nHow would you prefer to get in touch? 🤝`;\n    }\n    handleGeneralQuery(message, keywords) {\n        return `🌟 **Welcome to Foreingate Group!**\n\nWe're your **trusted partner** for higher education in Northern Cyprus. Since 2020, we've helped over **5,000 international students** achieve their academic dreams!\n\n**🎯 What We Do:**\n✅ University admissions (98% success rate)\n✅ Visa support and documentation\n✅ Accommodation arrangements\n✅ Ongoing academic support\n\n**🏛️ Partner Universities:**\n• Eastern Mediterranean University (EMU)\n• Near East University (NEU)  \n• Cyprus International University (CIU)\n\n**💰 Why Choose Northern Cyprus:**\n• High-quality English education\n• 50-70% cheaper than UK/US\n• Safe and welcoming environment\n• EU-recognized degrees\n\n**🚀 Ready to Start?**\n• Free consultation available\n• Personalized university matching\n• Complete application support\n• End-to-end service\n\nWhat aspect of studying abroad interests you most? I'm here to help with any questions! 🎓`;\n    }\n    handleScholarshipsQuery(message, keywords) {\n        return `💰 **Scholarships & Financial Aid:**\n\n**Merit Scholarship** 🏆\n• 25-50% tuition reduction\n• For students with GPA 3.5+ or equivalent\n• Automatic consideration with application\n\n**Early Bird Discount** ⏰\n• 10-15% tuition reduction\n• Apply before March 31st\n• Available for all programs\n\n**Sibling Discount** 👨‍👩‍👧‍👦\n• 10% tuition reduction\n• For families with multiple students\n• Applies to second sibling onwards\n\n**Payment Plans** 💳\n• Flexible installment options\n• No interest charges\n• Customized to your budget\n\n**Total Savings Possible:** Up to 65% off tuition fees!\n\n**🎯 How to Apply:**\n✅ Submit your application early\n✅ Maintain high academic performance\n✅ Provide all required documents\n✅ Meet application deadlines\n\nReady to apply for scholarships? Let me help you! 🎯`;\n    }\n    handleLanguageQuery(message, keywords) {\n        return `🌍 **Language Requirements & Support:**\n\n**📚 Program Languages:**\n• **English:** All programs taught in English\n• **Turkish:** Optional, helpful for daily life\n• **No Turkish Required:** For academic success\n\n**📝 English Proficiency Requirements:**\n• **IELTS:** 6.0+ overall (5.5+ each band)\n• **TOEFL:** 79+ iBT (17+ each section)\n• **PTE:** 58+ overall\n• **Alternative:** University English test\n\n**🎓 Language Support Services:**\n✅ **English Preparatory Program** - If needed\n✅ **Academic English Courses** - Writing, presentation skills\n✅ **Turkish Language Classes** - Optional cultural integration\n✅ **Language Exchange Programs** - Practice with local students\n\n**💡 Language Tips:**\n• Most international students succeed with basic English\n• Campus environment is very English-friendly\n• Local community welcomes international students\n• Many locals speak English\n\n**🌟 Don't Worry About Language!**\nOur universities have excellent support systems for international students. You'll improve your English naturally in the immersive environment!\n\nNeed help with English proficiency tests? We can guide you! 📖`;\n    }\n    getRandomFollowUp() {\n        return _lib_chatbot_knowledge__WEBPACK_IMPORTED_MODULE_0__.responseTemplates.followUp.sort(()=>0.5 - Math.random()).slice(0, 2);\n    }\n}\nasync function handleChatbotRequest(request) {\n    try {\n        const body = await request.json();\n        const { message, sessionId } = body;\n        if (!message || message.trim().length === 0) {\n            return (0,_lib_api_security__WEBPACK_IMPORTED_MODULE_2__.secureResponse)({\n                error: 'Message is required'\n            }, 400);\n        }\n        // Sanitize input\n        const cleanMessage = (0,_lib_security__WEBPACK_IMPORTED_MODULE_3__.sanitizeInput)(message.trim());\n        if (cleanMessage.length > 500) {\n            return (0,_lib_api_security__WEBPACK_IMPORTED_MODULE_2__.secureResponse)({\n                error: 'Message too long. Please keep it under 500 characters.'\n            }, 400);\n        }\n        // Initialize chatbot\n        const chatbot = new SmartChatbot();\n        // Generate response\n        const response = chatbot.generateResponse(cleanMessage);\n        return (0,_lib_api_security__WEBPACK_IMPORTED_MODULE_2__.secureResponse)({\n            success: true,\n            ...response,\n            timestamp: new Date().toISOString(),\n            sessionId: sessionId || 'anonymous'\n        });\n    } catch (error) {\n        console.error('Chatbot error:', error);\n        return (0,_lib_api_security__WEBPACK_IMPORTED_MODULE_2__.secureResponse)({\n            error: 'Sorry, I encountered an error. Please try again or contact our support team.',\n            fallback: true\n        }, 500);\n    }\n}\n// Apply security middleware\nconst POST = (0,_lib_api_security__WEBPACK_IMPORTED_MODULE_2__.withApiSecurity)(handleChatbotRequest);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/chatbot/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/api-security.ts":
/*!*********************************!*\
  !*** ./src/lib/api-security.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   sanitizeResponse: () => (/* binding */ sanitizeResponse),\n/* harmony export */   secureResponse: () => (/* binding */ secureResponse),\n/* harmony export */   validateInput: () => (/* binding */ validateInput),\n/* harmony export */   withAdminSecurity: () => (/* binding */ withAdminSecurity),\n/* harmony export */   withApiSecurity: () => (/* binding */ withApiSecurity),\n/* harmony export */   withCORS: () => (/* binding */ withCORS)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _security__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./security */ \"(rsc)/./src/lib/security.ts\");\n\n\n// Rate limiter instances\nconst globalRateLimiter = new _security__WEBPACK_IMPORTED_MODULE_1__.RateLimiter(100, 60000) // 100 requests per minute\n;\nconst apiRateLimiter = new _security__WEBPACK_IMPORTED_MODULE_1__.RateLimiter(50, 60000) // 50 API requests per minute\n;\nconst authRateLimiter = new _security__WEBPACK_IMPORTED_MODULE_1__.RateLimiter(5, 300000) // 5 auth attempts per 5 minutes\n;\n/**\n * API Security Middleware\n */ function withApiSecurity(handler) {\n    return async (request)=>{\n        const ip = getClientIP(request);\n        const userAgent = request.headers.get('user-agent') || 'unknown';\n        const path = request.nextUrl.pathname;\n        try {\n            // 1. Rate Limiting\n            if (!apiRateLimiter.isAllowed(ip)) {\n                _security__WEBPACK_IMPORTED_MODULE_1__.SecurityLogger.logSuspiciousActivity(ip, `Rate limit exceeded for ${path}`);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Too many requests. Please try again later.'\n                }, {\n                    status: 429,\n                    headers: {\n                        'Retry-After': '60',\n                        'X-RateLimit-Limit': '50',\n                        'X-RateLimit-Remaining': '0',\n                        'X-RateLimit-Reset': String(Date.now() + 60000)\n                    }\n                });\n            }\n            // 2. Method validation\n            const allowedMethods = [\n                'GET',\n                'POST',\n                'PUT',\n                'DELETE',\n                'PATCH'\n            ];\n            if (!allowedMethods.includes(request.method)) {\n                _security__WEBPACK_IMPORTED_MODULE_1__.SecurityLogger.logSuspiciousActivity(ip, `Invalid method ${request.method} for ${path}`);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Method not allowed'\n                }, {\n                    status: 405\n                });\n            }\n            // 3. Content-Type validation for POST/PUT requests\n            if ([\n                'POST',\n                'PUT',\n                'PATCH'\n            ].includes(request.method)) {\n                const contentType = request.headers.get('content-type');\n                if (!contentType || !contentType.includes('application/json')) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        error: 'Content-Type must be application/json'\n                    }, {\n                        status: 400\n                    });\n                }\n            }\n            // 4. Request size validation\n            const contentLength = request.headers.get('content-length');\n            if (contentLength && parseInt(contentLength) > 1024 * 1024) {\n                _security__WEBPACK_IMPORTED_MODULE_1__.SecurityLogger.logSuspiciousActivity(ip, `Large request size: ${contentLength} bytes`);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Request too large'\n                }, {\n                    status: 413\n                });\n            }\n            // 5. Suspicious headers check\n            const suspiciousHeaders = [\n                'x-forwarded-host',\n                'x-real-ip'\n            ];\n            for (const header of suspiciousHeaders){\n                if (request.headers.get(header)) {\n                    _security__WEBPACK_IMPORTED_MODULE_1__.SecurityLogger.logSuspiciousActivity(ip, `Suspicious header: ${header}`);\n                }\n            }\n            // 6. Log API access\n            _security__WEBPACK_IMPORTED_MODULE_1__.SecurityLogger.logDataAccess(path, ip);\n            // Call the actual handler\n            const response = await handler(request);\n            // Add security headers to response\n            if (response instanceof next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse) {\n                response.headers.set('X-Content-Type-Options', 'nosniff');\n                response.headers.set('X-Frame-Options', 'DENY');\n                response.headers.set('X-XSS-Protection', '1; mode=block');\n                response.headers.set('Cache-Control', 'no-store, max-age=0');\n                response.headers.set('X-RateLimit-Remaining', String(apiRateLimiter.getRemainingRequests(ip)));\n            }\n            return response;\n        } catch (error) {\n            _security__WEBPACK_IMPORTED_MODULE_1__.SecurityLogger.log('API_ERROR', {\n                path,\n                ip,\n                error: String(error)\n            }, 'error');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Internal server error'\n            }, {\n                status: 500\n            });\n        }\n    };\n}\n/**\n * Admin API Security Middleware\n */ function withAdminSecurity(handler) {\n    return withApiSecurity(async (request)=>{\n        const ip = getClientIP(request);\n        // Additional admin-specific security checks\n        // 1. Admin rate limiting (stricter)\n        if (!authRateLimiter.isAllowed(ip)) {\n            _security__WEBPACK_IMPORTED_MODULE_1__.SecurityLogger.logSuspiciousActivity(ip, 'Admin rate limit exceeded');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Too many admin requests. Please try again later.'\n            }, {\n                status: 429\n            });\n        }\n        // 2. Admin authentication check (basic implementation)\n        const authHeader = request.headers.get('authorization');\n        if (!authHeader || !authHeader.startsWith('Bearer ')) {\n            _security__WEBPACK_IMPORTED_MODULE_1__.SecurityLogger.logFailedLogin(ip, request.headers.get('user-agent') || 'unknown');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        // 3. Log admin access\n        _security__WEBPACK_IMPORTED_MODULE_1__.SecurityLogger.log('ADMIN_ACCESS', {\n            path: request.nextUrl.pathname,\n            ip,\n            method: request.method\n        }, 'info');\n        return handler(request);\n    });\n}\n/**\n * Input Validation Middleware\n */ function validateInput(schema) {\n    return (handler)=>{\n        return async (request)=>{\n            // Skip validation for GET requests\n            if (![\n                'POST',\n                'PUT',\n                'PATCH'\n            ].includes(request.method)) {\n                return handler(request);\n            }\n            try {\n                // Clone the request to avoid body consumption issues\n                const clonedRequest = request.clone();\n                const body = await clonedRequest.json();\n                // Validate each field according to schema\n                for (const [field, validator] of Object.entries(schema)){\n                    const value = body[field];\n                    if (typeof validator === 'function' && !validator(value)) {\n                        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                            error: `Invalid ${field}`\n                        }, {\n                            status: 400\n                        });\n                    }\n                }\n                // Add validated body to request for later use\n                ;\n                request.validatedBody = body;\n            } catch (error) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Invalid JSON'\n                }, {\n                    status: 400\n                });\n            }\n            return handler(request);\n        };\n    };\n}\n/**\n * CORS Middleware\n */ function withCORS(handler, options = {}) {\n    return async (request)=>{\n        const origin = request.headers.get('origin');\n        const allowedOrigins = options.origin || [\n            'https://localhost:3443',\n            'https://foreingate.com',\n            'https://www.foreingate.com'\n        ];\n        // Handle preflight requests\n        if (request.method === 'OPTIONS') {\n            const response = new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(null, {\n                status: 200\n            });\n            if (origin && allowedOrigins.includes(origin)) {\n                response.headers.set('Access-Control-Allow-Origin', origin);\n            }\n            response.headers.set('Access-Control-Allow-Methods', options.methods?.join(', ') || 'GET, POST, PUT, DELETE');\n            response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');\n            if (options.credentials) {\n                response.headers.set('Access-Control-Allow-Credentials', 'true');\n            }\n            return response;\n        }\n        const response = await handler(request);\n        // Add CORS headers to actual response\n        if (response instanceof next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse) {\n            if (origin && allowedOrigins.includes(origin)) {\n                response.headers.set('Access-Control-Allow-Origin', origin);\n            }\n            if (options.credentials) {\n                response.headers.set('Access-Control-Allow-Credentials', 'true');\n            }\n        }\n        return response;\n    };\n}\n/**\n * Get client IP address\n */ function getClientIP(request) {\n    const forwarded = request.headers.get('x-forwarded-for');\n    const realIP = request.headers.get('x-real-ip');\n    const remoteAddr = request.ip;\n    if (forwarded) {\n        return forwarded.split(',')[0].trim();\n    }\n    return realIP || remoteAddr || 'unknown';\n}\n/**\n * Sanitize API response\n */ function sanitizeResponse(data) {\n    if (typeof data === 'string') {\n        return data.replace(/<script\\b[^<]*(?:(?!<\\/script>)<[^<]*)*<\\/script>/gi, '');\n    }\n    if (Array.isArray(data)) {\n        return data.map(sanitizeResponse);\n    }\n    if (typeof data === 'object' && data !== null) {\n        const sanitized = {};\n        for (const [key, value] of Object.entries(data)){\n            sanitized[key] = sanitizeResponse(value);\n        }\n        return sanitized;\n    }\n    return data;\n}\n/**\n * API Response wrapper with security\n */ function secureResponse(data, status = 200) {\n    const sanitizedData = sanitizeResponse(data);\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(sanitizedData, {\n        status,\n        headers: {\n            'X-Content-Type-Options': 'nosniff',\n            'X-Frame-Options': 'DENY',\n            'Cache-Control': 'no-store, max-age=0',\n            'X-XSS-Protection': '1; mode=block'\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/api-security.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/chatbot-knowledge.ts":
/*!**************************************!*\
  !*** ./src/lib/chatbot-knowledge.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   questionCategories: () => (/* binding */ questionCategories),\n/* harmony export */   responseTemplates: () => (/* binding */ responseTemplates),\n/* harmony export */   websiteKnowledge: () => (/* binding */ websiteKnowledge)\n/* harmony export */ });\n// Comprehensive knowledge base for the Foreingate Group chatbot\nconst websiteKnowledge = {\n    company: {\n        name: \"Foreingate Group\",\n        description: \"Leading educational consultancy specializing in university admissions and student services in Northern Cyprus\",\n        mission: \"To bridge the gap between international students and quality higher education opportunities in Northern Cyprus\",\n        vision: \"To be the most trusted partner for students seeking world-class education abroad\",\n        established: \"2020\",\n        headquarters: \"Nicosia, Northern Cyprus\",\n        languages: [\n            \"English\",\n            \"Turkish\",\n            \"Arabic\"\n        ],\n        accreditations: [\n            \"ICEF Certified\",\n            \"British Council Partner\"\n        ]\n    },\n    services: [\n        {\n            name: \"University Admissions\",\n            description: \"Complete application assistance for universities in Northern Cyprus\",\n            features: [\n                \"Application processing\",\n                \"Document preparation\",\n                \"Interview coaching\",\n                \"Admission guarantee\"\n            ],\n            pricing: \"Free consultation, competitive service fees\"\n        },\n        {\n            name: \"Visa Support\",\n            description: \"Comprehensive visa application and documentation services\",\n            features: [\n                \"Visa application\",\n                \"Document translation\",\n                \"Embassy appointments\",\n                \"Visa tracking\"\n            ],\n            pricing: \"Starting from $200\"\n        },\n        {\n            name: \"Accommodation Services\",\n            description: \"Student housing and accommodation arrangements\",\n            features: [\n                \"Dormitory booking\",\n                \"Private housing\",\n                \"Homestay options\",\n                \"Airport pickup\"\n            ],\n            pricing: \"Varies by accommodation type\"\n        },\n        {\n            name: \"Academic Support\",\n            description: \"Ongoing academic assistance and mentoring\",\n            features: [\n                \"Tutoring services\",\n                \"Study groups\",\n                \"Academic counseling\",\n                \"Career guidance\"\n            ],\n            pricing: \"Monthly packages available\"\n        }\n    ],\n    universities: [\n        {\n            name: \"Eastern Mediterranean University (EMU)\",\n            location: \"Famagusta, Northern Cyprus\",\n            established: 1979,\n            students: 20000,\n            international: 15000,\n            programs: [\n                \"Engineering\",\n                \"Medicine\",\n                \"Business\",\n                \"Arts & Sciences\",\n                \"Law\"\n            ],\n            tuition: {\n                min: 3500,\n                max: 8000,\n                currency: \"USD\"\n            },\n            language: \"English\",\n            accreditation: \"YÖK (Turkish Higher Education Council)\"\n        },\n        {\n            name: \"Near East University (NEU)\",\n            location: \"Nicosia, Northern Cyprus\",\n            established: 1988,\n            students: 25000,\n            international: 18000,\n            programs: [\n                \"Medicine\",\n                \"Dentistry\",\n                \"Engineering\",\n                \"Pharmacy\",\n                \"Architecture\"\n            ],\n            tuition: {\n                min: 4000,\n                max: 12000,\n                currency: \"USD\"\n            },\n            language: \"English\",\n            accreditation: \"YÖK, WHO recognized\"\n        },\n        {\n            name: \"Cyprus International University (CIU)\",\n            location: \"Nicosia, Northern Cyprus\",\n            established: 1997,\n            students: 15000,\n            international: 12000,\n            programs: [\n                \"Business\",\n                \"Engineering\",\n                \"Communication\",\n                \"Education\",\n                \"Fine Arts\"\n            ],\n            tuition: {\n                min: 3000,\n                max: 7000,\n                currency: \"USD\"\n            },\n            language: \"English\",\n            accreditation: \"YÖK certified\"\n        }\n    ],\n    admissionProcess: {\n        steps: [\n            \"Initial consultation and assessment\",\n            \"University and program selection\",\n            \"Document preparation and verification\",\n            \"Application submission\",\n            \"Interview preparation (if required)\",\n            \"Admission decision and acceptance\",\n            \"Visa application process\",\n            \"Pre-departure orientation\",\n            \"Arrival and settlement support\"\n        ],\n        timeline: \"2-4 months average\",\n        requirements: {\n            undergraduate: [\n                \"High school diploma\",\n                \"English proficiency test\",\n                \"Passport copy\",\n                \"Academic transcripts\"\n            ],\n            graduate: [\n                \"Bachelor's degree\",\n                \"GRE/GMAT (some programs)\",\n                \"English proficiency\",\n                \"Letters of recommendation\",\n                \"Statement of purpose\"\n            ]\n        }\n    },\n    costs: {\n        tuition: {\n            undergraduate: {\n                min: 3000,\n                max: 8000,\n                currency: \"USD\",\n                period: \"per year\"\n            },\n            graduate: {\n                min: 4000,\n                max: 12000,\n                currency: \"USD\",\n                period: \"per year\"\n            },\n            medicine: {\n                min: 8000,\n                max: 15000,\n                currency: \"USD\",\n                period: \"per year\"\n            }\n        },\n        living: {\n            accommodation: {\n                min: 200,\n                max: 600,\n                currency: \"USD\",\n                period: \"per month\"\n            },\n            food: {\n                min: 150,\n                max: 300,\n                currency: \"USD\",\n                period: \"per month\"\n            },\n            transportation: {\n                min: 50,\n                max: 100,\n                currency: \"USD\",\n                period: \"per month\"\n            },\n            total: {\n                min: 400,\n                max: 1000,\n                currency: \"USD\",\n                period: \"per month\"\n            }\n        }\n    },\n    scholarships: [\n        {\n            name: \"Merit Scholarship\",\n            amount: \"25-50% tuition reduction\",\n            criteria: \"High academic performance\",\n            eligibility: \"GPA 3.5+ or equivalent\"\n        },\n        {\n            name: \"Early Bird Discount\",\n            amount: \"10-15% tuition reduction\",\n            criteria: \"Early application submission\",\n            deadline: \"Before March 31st\"\n        },\n        {\n            name: \"Sibling Discount\",\n            amount: \"10% tuition reduction\",\n            criteria: \"Multiple family members enrolled\",\n            eligibility: \"Second sibling onwards\"\n        }\n    ],\n    contact: {\n        email: \"<EMAIL>\",\n        phone: \"+90 ************\",\n        whatsapp: \"+90 ************\",\n        address: \"Nicosia, Northern Cyprus\",\n        workingHours: \"Monday-Friday: 9:00-18:00, Saturday: 9:00-14:00\",\n        languages: [\n            \"English\",\n            \"Turkish\",\n            \"Arabic\",\n            \"French\"\n        ]\n    },\n    website: {\n        url: \"https://foreingate.com\",\n        features: [\n            \"University search and comparison\",\n            \"Online application system\",\n            \"Document upload portal\",\n            \"Application tracking\",\n            \"Live chat support\",\n            \"Newsletter subscription\",\n            \"Blog with educational content\",\n            \"Testimonials from students\"\n        ],\n        pages: [\n            {\n                path: \"/\",\n                name: \"Home\",\n                description: \"Main landing page with overview\"\n            },\n            {\n                path: \"/about\",\n                name: \"About Us\",\n                description: \"Company information and team\"\n            },\n            {\n                path: \"/services\",\n                name: \"Services\",\n                description: \"Detailed service offerings\"\n            },\n            {\n                path: \"/universities\",\n                name: \"Universities\",\n                description: \"Partner universities and programs\"\n            },\n            {\n                path: \"/apply\",\n                name: \"Apply Now\",\n                description: \"Online application form\"\n            },\n            {\n                path: \"/blog\",\n                name: \"Blog\",\n                description: \"Educational articles and news\"\n            },\n            {\n                path: \"/contact\",\n                name: \"Contact\",\n                description: \"Contact information and form\"\n            },\n            {\n                path: \"/admin\",\n                name: \"Admin Panel\",\n                description: \"Administrative dashboard\"\n            }\n        ]\n    },\n    faq: [\n        {\n            question: \"What is the application deadline?\",\n            answer: \"Applications are accepted year-round, but we recommend applying 3-4 months before your intended start date for better preparation time.\"\n        },\n        {\n            question: \"Do I need to know Turkish?\",\n            answer: \"No, most programs are taught in English. However, basic Turkish knowledge can be helpful for daily life.\"\n        },\n        {\n            question: \"Is Northern Cyprus safe for international students?\",\n            answer: \"Yes, Northern Cyprus is very safe with low crime rates and a welcoming community for international students.\"\n        },\n        {\n            question: \"What are the visa requirements?\",\n            answer: \"Visa requirements vary by nationality. We provide complete visa support including document preparation and application assistance.\"\n        },\n        {\n            question: \"Can I work while studying?\",\n            answer: \"Yes, students can work part-time (up to 20 hours per week) with proper permits.\"\n        }\n    ],\n    testimonials: [\n        {\n            name: \"Ahmed Hassan\",\n            country: \"Egypt\",\n            program: \"Computer Engineering\",\n            university: \"Eastern Mediterranean University\",\n            year: 2023,\n            rating: 5,\n            comment: \"Foreingate made my dream of studying abroad come true. Their support was exceptional throughout the entire process.\"\n        },\n        {\n            name: \"Maria Rodriguez\",\n            country: \"Colombia\",\n            program: \"Medicine\",\n            university: \"Near East University\",\n            year: 2022,\n            rating: 5,\n            comment: \"Professional service and genuine care. They helped me secure admission and scholarship. Highly recommended!\"\n        }\n    ],\n    technicalInfo: {\n        security: {\n            ssl: \"Auto-signed SSL certificates implemented\",\n            headers: \"15+ security headers active\",\n            encryption: \"AES-256-GCM data encryption\",\n            rateLimit: \"100 requests per minute\",\n            features: [\n                \"HTTPS enforcement\",\n                \"XSS protection\",\n                \"CSRF prevention\",\n                \"Input sanitization\"\n            ]\n        },\n        features: {\n            responsive: \"Mobile-first responsive design\",\n            performance: \"Optimized loading times\",\n            accessibility: \"WCAG 2.1 compliant\",\n            seo: \"Search engine optimized\",\n            analytics: \"Google Analytics integration ready\"\n        },\n        admin: {\n            dashboard: \"Real-time statistics and management\",\n            applications: \"Complete application lifecycle management\",\n            newsletter: \"Email campaign system\",\n            contacts: \"Customer inquiry management\",\n            security: \"Enhanced admin protection\"\n        }\n    }\n};\n// Common questions and their categories with enhanced keywords\nconst questionCategories = {\n    admissions: [\n        \"admission\",\n        \"apply\",\n        \"application\",\n        \"requirements\",\n        \"documents\",\n        \"deadline\",\n        \"enroll\",\n        \"register\",\n        \"submit\",\n        \"process\",\n        \"steps\",\n        \"how to apply\",\n        \"admission process\",\n        \"requirements\",\n        \"eligibility\"\n    ],\n    universities: [\n        \"university\",\n        \"universities\",\n        \"programs\",\n        \"courses\",\n        \"degrees\",\n        \"study\",\n        \"emu\",\n        \"neu\",\n        \"ciu\",\n        \"eastern mediterranean\",\n        \"near east\",\n        \"cyprus international\",\n        \"engineering\",\n        \"medicine\",\n        \"business\",\n        \"programs\",\n        \"majors\",\n        \"faculties\"\n    ],\n    costs: [\n        \"cost\",\n        \"fees\",\n        \"tuition\",\n        \"price\",\n        \"expensive\",\n        \"cheap\",\n        \"scholarship\",\n        \"financial\",\n        \"money\",\n        \"budget\",\n        \"afford\",\n        \"payment\",\n        \"installment\",\n        \"discount\",\n        \"aid\",\n        \"funding\"\n    ],\n    visa: [\n        \"visa\",\n        \"permit\",\n        \"immigration\",\n        \"documents\",\n        \"embassy\",\n        \"passport\",\n        \"student visa\",\n        \"residence permit\",\n        \"visa application\",\n        \"visa process\",\n        \"visa requirements\"\n    ],\n    accommodation: [\n        \"housing\",\n        \"accommodation\",\n        \"dormitory\",\n        \"residence\",\n        \"living\",\n        \"room\",\n        \"apartment\",\n        \"dorm\",\n        \"homestay\",\n        \"where to live\",\n        \"student housing\"\n    ],\n    services: [\n        \"services\",\n        \"help\",\n        \"support\",\n        \"assistance\",\n        \"consultation\",\n        \"what do you do\",\n        \"how can you help\",\n        \"what services\",\n        \"support services\"\n    ],\n    location: [\n        \"cyprus\",\n        \"northern cyprus\",\n        \"nicosia\",\n        \"famagusta\",\n        \"location\",\n        \"where\",\n        \"country\",\n        \"island\",\n        \"turkey\",\n        \"mediterranean\",\n        \"safe\",\n        \"weather\",\n        \"climate\"\n    ],\n    contact: [\n        \"contact\",\n        \"phone\",\n        \"email\",\n        \"address\",\n        \"office\",\n        \"hours\",\n        \"reach\",\n        \"call\",\n        \"write\",\n        \"visit\",\n        \"working hours\",\n        \"office hours\"\n    ],\n    scholarships: [\n        \"scholarship\",\n        \"scholarships\",\n        \"financial aid\",\n        \"discount\",\n        \"merit\",\n        \"early bird\",\n        \"sibling\",\n        \"funding\",\n        \"grant\",\n        \"bursary\"\n    ],\n    language: [\n        \"english\",\n        \"turkish\",\n        \"language\",\n        \"speak\",\n        \"communication\",\n        \"language requirements\",\n        \"english proficiency\",\n        \"ielts\",\n        \"toefl\"\n    ],\n    general: [\n        \"about\",\n        \"company\",\n        \"foreingate\",\n        \"who\",\n        \"what\",\n        \"why\",\n        \"how\",\n        \"hello\",\n        \"hi\",\n        \"help\",\n        \"info\",\n        \"information\"\n    ]\n};\n// Smart response templates\nconst responseTemplates = {\n    greeting: [\n        \"Hello! I'm your Foreingate assistant. How can I help you today?\",\n        \"Hi there! I'm here to answer any questions about studying in Northern Cyprus. What would you like to know?\",\n        \"Welcome to Foreingate! I can help you with information about universities, admissions, costs, and more. What interests you?\"\n    ],\n    notFound: [\n        \"I don't have specific information about that, but I'd be happy to connect you with our human advisors who can help. You can contact us at +90 ************ or <EMAIL>.\",\n        \"That's a great question! For detailed information about that topic, I recommend speaking with our education consultants. Would you like me to help you get in touch?\",\n        \"I want to make sure I give you accurate information. For that specific question, our expert advisors would be the best resource. Shall I provide you with contact details?\"\n    ],\n    followUp: [\n        \"Is there anything else you'd like to know about studying in Northern Cyprus?\",\n        \"Do you have any other questions about our services or universities?\",\n        \"Would you like more information about any specific university or program?\",\n        \"Can I help you with anything else regarding your education plans?\"\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/chatbot-knowledge.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/document-processor.ts":
/*!***************************************!*\
  !*** ./src/lib/document-processor.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DocumentProcessor: () => (/* binding */ DocumentProcessor),\n/* harmony export */   EnhancedKnowledgeBase: () => (/* binding */ EnhancedKnowledgeBase)\n/* harmony export */ });\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fs/promises */ \"fs/promises\");\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(fs_promises__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\n// Advanced Document Processing for RAG System\n\n\n\nclass DocumentProcessor {\n    constructor(){\n        this.documentsPath = path__WEBPACK_IMPORTED_MODULE_2___default().join(process.cwd(), 'data', 'documents');\n        this.processedPath = path__WEBPACK_IMPORTED_MODULE_2___default().join(process.cwd(), 'data', 'processed');\n        this.ensureDirectories();\n    }\n    async ensureDirectories() {\n        if (!(0,fs__WEBPACK_IMPORTED_MODULE_1__.existsSync)(this.documentsPath)) {\n            await (0,fs_promises__WEBPACK_IMPORTED_MODULE_0__.mkdir)(this.documentsPath, {\n                recursive: true\n            });\n        }\n        if (!(0,fs__WEBPACK_IMPORTED_MODULE_1__.existsSync)(this.processedPath)) {\n            await (0,fs_promises__WEBPACK_IMPORTED_MODULE_0__.mkdir)(this.processedPath, {\n                recursive: true\n            });\n        }\n    }\n    // Process and chunk documents for RAG\n    async processDocument(content, title, metadata) {\n        const id = this.generateDocumentId(title);\n        // Clean and normalize content\n        const cleanContent = this.cleanContent(content);\n        // Create chunks\n        const chunks = this.createChunks(cleanContent, id);\n        // Extract keywords and calculate importance\n        chunks.forEach((chunk)=>{\n            chunk.metadata.keywords = this.extractKeywords(chunk.content);\n            chunk.metadata.importance = this.calculateImportance(chunk.content);\n        });\n        const document = {\n            id,\n            title,\n            content: cleanContent,\n            chunks,\n            metadata: {\n                source: metadata.source || 'unknown',\n                type: metadata.type || 'general',\n                lastUpdated: new Date().toISOString(),\n                language: metadata.language || 'en',\n                category: metadata.category || 'general'\n            }\n        };\n        // Save processed document\n        await this.saveProcessedDocument(document);\n        return document;\n    }\n    generateDocumentId(title) {\n        return title.toLowerCase().replace(/[^a-z0-9]/g, '-').replace(/-+/g, '-').replace(/^-|-$/g, '') + '-' + Date.now();\n    }\n    cleanContent(content) {\n        return content.replace(/\\s+/g, ' ') // Normalize whitespace\n        .replace(/[^\\w\\s.,!?;:()\\-]/g, '') // Remove special characters\n        .trim();\n    }\n    createChunks(content, documentId) {\n        const chunks = [];\n        const sentences = this.splitIntoSentences(content);\n        const chunkSize = 3 // sentences per chunk\n        ;\n        const overlap = 1 // sentence overlap between chunks\n        ;\n        for(let i = 0; i < sentences.length; i += chunkSize - overlap){\n            const chunkSentences = sentences.slice(i, i + chunkSize);\n            const chunkContent = chunkSentences.join(' ');\n            if (chunkContent.trim().length > 20) {\n                chunks.push({\n                    id: `${documentId}-chunk-${chunks.length}`,\n                    content: chunkContent,\n                    metadata: {\n                        documentId,\n                        chunkIndex: chunks.length,\n                        startPosition: i,\n                        endPosition: i + chunkSentences.length - 1,\n                        keywords: [],\n                        importance: 0\n                    }\n                });\n            }\n        }\n        return chunks;\n    }\n    splitIntoSentences(text) {\n        return text.split(/[.!?]+/).map((s)=>s.trim()).filter((s)=>s.length > 10);\n    }\n    extractKeywords(text) {\n        const words = text.toLowerCase().replace(/[^\\w\\s]/g, ' ').split(/\\s+/).filter((word)=>word.length > 3);\n        // Simple keyword extraction based on frequency and importance\n        const wordCounts = new Map();\n        words.forEach((word)=>{\n            wordCounts.set(word, (wordCounts.get(word) || 0) + 1);\n        });\n        // Important domain-specific keywords\n        const importantKeywords = [\n            'university',\n            'admission',\n            'tuition',\n            'scholarship',\n            'visa',\n            'program',\n            'degree',\n            'bachelor',\n            'master',\n            'medicine',\n            'engineering',\n            'business',\n            'cyprus',\n            'international',\n            'student',\n            'application',\n            'requirement',\n            'cost',\n            'fee',\n            'accommodation',\n            'housing',\n            'english',\n            'ielts',\n            'toefl'\n        ];\n        const keywords = Array.from(wordCounts.entries()).filter(([word, count])=>count > 1 || importantKeywords.includes(word)).sort((a, b)=>b[1] - a[1]).slice(0, 10).map(([word])=>word);\n        return keywords;\n    }\n    calculateImportance(content) {\n        let importance = 0;\n        const contentLower = content.toLowerCase();\n        // Boost importance for key topics\n        const importanceBoosts = {\n            'university': 3,\n            'admission': 3,\n            'tuition': 2,\n            'scholarship': 3,\n            'visa': 2,\n            'program': 2,\n            'requirement': 2,\n            'cost': 2,\n            'fee': 2,\n            'application': 2,\n            'medicine': 2,\n            'engineering': 2,\n            'business': 2\n        };\n        Object.entries(importanceBoosts).forEach(([keyword, boost])=>{\n            if (contentLower.includes(keyword)) {\n                importance += boost;\n            }\n        });\n        // Boost for numbers (costs, dates, etc.)\n        const numberMatches = content.match(/\\$[\\d,]+|\\d{4}|\\d+%/g);\n        if (numberMatches) {\n            importance += numberMatches.length;\n        }\n        return Math.min(importance, 10) // Cap at 10\n        ;\n    }\n    async saveProcessedDocument(document) {\n        const filePath = path__WEBPACK_IMPORTED_MODULE_2___default().join(this.processedPath, `${document.id}.json`);\n        await (0,fs_promises__WEBPACK_IMPORTED_MODULE_0__.writeFile)(filePath, JSON.stringify(document, null, 2));\n    }\n    async loadProcessedDocument(id) {\n        try {\n            const filePath = path__WEBPACK_IMPORTED_MODULE_2___default().join(this.processedPath, `${id}.json`);\n            const content = await (0,fs_promises__WEBPACK_IMPORTED_MODULE_0__.readFile)(filePath, 'utf-8');\n            return JSON.parse(content);\n        } catch (error) {\n            return null;\n        }\n    }\n    async getAllProcessedDocuments() {\n        try {\n            const files = await (0,fs_promises__WEBPACK_IMPORTED_MODULE_0__.readFile)(this.processedPath);\n            const documents = [];\n            // This would need proper directory reading implementation\n            // For now, return empty array\n            return documents;\n        } catch (error) {\n            return [];\n        }\n    }\n}\n// Enhanced knowledge base with document processing\nclass EnhancedKnowledgeBase {\n    constructor(){\n        this.documents = new Map();\n        this.processor = new DocumentProcessor();\n        this.initializeKnowledgeBase();\n    }\n    async initializeKnowledgeBase() {\n        // Add comprehensive university information\n        await this.addUniversityDocuments();\n        // Add program-specific information\n        await this.addProgramDocuments();\n        // Add admission guides\n        await this.addAdmissionGuides();\n        // Add cost and scholarship information\n        await this.addFinancialDocuments();\n        // Add visa and immigration guides\n        await this.addVisaDocuments();\n    }\n    async addUniversityDocuments() {\n        const emuDoc = `\n    Eastern Mediterranean University (EMU) is a leading higher education institution located in Famagusta, Northern Cyprus.\n    Established in 1979, EMU has grown to become one of the most prestigious universities in the region.\n    \n    The university hosts over 20,000 students from 106 different countries, creating a truly international environment.\n    EMU offers undergraduate, graduate, and doctoral programs across 11 faculties.\n    \n    Popular programs include Computer Engineering, Business Administration, International Relations, \n    Medicine, Dentistry, Pharmacy, and Architecture. All programs are taught in English.\n    \n    The campus features modern facilities including state-of-the-art laboratories, libraries, \n    sports complexes, and student accommodation. EMU is accredited by YÖK (Turkish Higher Education Council)\n    and many programs have international accreditations.\n    \n    Tuition fees range from $3,500 to $8,000 per year depending on the program.\n    The university offers various scholarships including merit-based scholarships up to 50% tuition reduction.\n    `;\n        await this.processor.processDocument(emuDoc, 'Eastern Mediterranean University Guide', {\n            type: 'university',\n            category: 'universities',\n            source: 'official'\n        });\n        const neuDoc = `\n    Near East University (NEU) is a comprehensive university established in 1988 in Nicosia, Northern Cyprus.\n    NEU is particularly renowned for its medical programs and has WHO recognition for its Medicine faculty.\n    \n    The university serves over 25,000 students including 18,000 international students from around the world.\n    NEU offers programs in Medicine, Dentistry, Pharmacy, Engineering, Business, Law, and many other fields.\n    \n    The Medical faculty is especially prestigious with modern simulation labs, anatomy labs, and clinical training facilities.\n    NEU Hospital provides practical training opportunities for medical students.\n    \n    Engineering programs include Computer, Civil, Electrical, Mechanical, and Industrial Engineering.\n    Business programs cover MBA, International Business, Banking and Finance, and Economics.\n    \n    Tuition fees range from $4,000 to $12,000 per year. Medical programs are at the higher end.\n    NEU offers merit scholarships, early application discounts, and payment plan options.\n    `;\n        await this.processor.processDocument(neuDoc, 'Near East University Guide', {\n            type: 'university',\n            category: 'universities',\n            source: 'official'\n        });\n    }\n    async addProgramDocuments() {\n        const engineeringDoc = `\n    Engineering programs in Northern Cyprus universities are highly regarded and internationally recognized.\n    \n    Computer Engineering: Covers software development, algorithms, computer systems, artificial intelligence,\n    and cybersecurity. Graduates work in tech companies, startups, and multinational corporations.\n    \n    Civil Engineering: Focuses on infrastructure design, construction management, structural analysis,\n    and environmental engineering. Strong emphasis on practical projects and internships.\n    \n    Electrical Engineering: Includes power systems, electronics, telecommunications, and renewable energy.\n    Modern laboratories with industry-standard equipment.\n    \n    Mechanical Engineering: Covers thermodynamics, fluid mechanics, manufacturing, and automotive engineering.\n    Hands-on experience with CAD software and manufacturing processes.\n    \n    All engineering programs require strong mathematics and physics background.\n    English proficiency (IELTS 6.0+ or TOEFL 79+) is mandatory.\n    Duration is typically 4 years for bachelor's degree.\n    `;\n        await this.processor.processDocument(engineeringDoc, 'Engineering Programs Guide', {\n            type: 'program',\n            category: 'programs',\n            source: 'academic'\n        });\n    }\n    async addAdmissionGuides() {\n        const admissionDoc = `\n    University admission process in Northern Cyprus is straightforward and student-friendly.\n    \n    Step 1: Choose your university and program based on your interests and career goals.\n    Step 2: Prepare required documents including academic transcripts, English proficiency test, and passport.\n    Step 3: Submit online application with all supporting documents.\n    Step 4: Wait for admission decision (usually 1-2 weeks).\n    Step 5: Accept offer and pay initial fees.\n    Step 6: Apply for student visa with acceptance letter.\n    Step 7: Arrange accommodation and travel.\n    Step 8: Attend orientation and begin studies.\n    \n    Required documents for undergraduate:\n    - High school diploma or equivalent\n    - Official transcripts with grades\n    - English proficiency test (IELTS 6.0+ or TOEFL 79+)\n    - Passport copy\n    - Personal statement\n    - Letters of recommendation (some programs)\n    \n    Application deadlines are flexible with multiple intake periods throughout the year.\n    Early applications receive priority for scholarships and accommodation.\n    `;\n        await this.processor.processDocument(admissionDoc, 'Admission Process Guide', {\n            type: 'guide',\n            category: 'admissions',\n            source: 'official'\n        });\n    }\n    async addFinancialDocuments() {\n        const financialDoc = `\n    Studying in Northern Cyprus is significantly more affordable than other popular destinations.\n    \n    Tuition Fees:\n    - Undergraduate programs: $3,000 - $8,000 per year\n    - Graduate programs: $4,000 - $12,000 per year  \n    - Medical programs: $8,000 - $15,000 per year\n    - Engineering programs: $3,500 - $7,000 per year\n    - Business programs: $3,000 - $6,000 per year\n    \n    Living Costs (monthly):\n    - Accommodation: $200 - $600 (dormitory to private apartment)\n    - Food and meals: $150 - $300\n    - Transportation: $50 - $100\n    - Books and supplies: $50 - $100\n    - Personal expenses: $100 - $200\n    - Total monthly living: $550 - $1,300\n    \n    Scholarship Opportunities:\n    - Merit Scholarship: 25-50% tuition reduction for high achievers\n    - Early Bird Discount: 10-15% for applications before March 31st\n    - Sibling Discount: 10% for families with multiple students\n    - Payment plans available with no interest charges\n    \n    Part-time work opportunities available for students with proper permits.\n    `;\n        await this.processor.processDocument(financialDoc, 'Costs and Financial Aid Guide', {\n            type: 'guide',\n            category: 'costs',\n            source: 'official'\n        });\n    }\n    async addVisaDocuments() {\n        const visaDoc = `\n    Student visa process for Northern Cyprus is generally straightforward for most nationalities.\n    \n    Required Documents:\n    - Valid passport (minimum 6 months validity)\n    - University acceptance letter\n    - Proof of financial support (bank statements)\n    - Health insurance coverage\n    - Academic transcripts and diplomas\n    - Police clearance certificate\n    - Medical examination report\n    - Passport-size photographs\n    \n    Visa Processing:\n    - Processing time: 2-8 weeks depending on nationality\n    - Some countries have visa-free entry for short stays\n    - Student residence permit required for long-term study\n    - Multiple entry visa recommended for travel flexibility\n    \n    Visa-free countries include many EU nations, Turkey, and several others.\n    Our visa support team assists with complete application process.\n    Success rate is over 95% with proper documentation.\n    \n    After arrival, students must register with local authorities and obtain residence permit.\n    `;\n        await this.processor.processDocument(visaDoc, 'Visa and Immigration Guide', {\n            type: 'guide',\n            category: 'visa',\n            source: 'official'\n        });\n    }\n    async searchDocuments(query, limit = 5) {\n        const allChunks = [];\n        // Collect all chunks from all documents\n        this.documents.forEach((doc)=>{\n            allChunks.push(...doc.chunks);\n        });\n        // Simple text matching (in production, use proper vector similarity)\n        const queryLower = query.toLowerCase();\n        const matches = allChunks.filter((chunk)=>chunk.content.toLowerCase().includes(queryLower) || chunk.metadata.keywords.some((keyword)=>queryLower.includes(keyword)));\n        // Sort by importance and relevance\n        matches.sort((a, b)=>b.metadata.importance - a.metadata.importance);\n        return matches.slice(0, limit);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/document-processor.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/rag-system.ts":
/*!*******************************!*\
  !*** ./src/lib/rag-system.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   KnowledgeRetriever: () => (/* binding */ KnowledgeRetriever),\n/* harmony export */   RAGResponseGenerator: () => (/* binding */ RAGResponseGenerator),\n/* harmony export */   VectorEmbedding: () => (/* binding */ VectorEmbedding)\n/* harmony export */ });\n/* harmony import */ var _chatbot_knowledge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chatbot-knowledge */ \"(rsc)/./src/lib/chatbot-knowledge.ts\");\n/* harmony import */ var _semantic_search__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./semantic-search */ \"(rsc)/./src/lib/semantic-search.ts\");\n/* harmony import */ var _document_processor__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./document-processor */ \"(rsc)/./src/lib/document-processor.ts\");\n// RAG (Retrieval-Augmented Generation) System for Smart Chatbot\n\n\n\n// Simple vector embedding using TF-IDF approach\nclass VectorEmbedding {\n    constructor(){\n        this.vocabulary = new Map();\n        this.idf = new Map();\n        this.documents = [];\n        this.buildVocabulary();\n    }\n    buildVocabulary() {\n        // Build vocabulary from knowledge base\n        const allTexts = this.extractAllTexts();\n        this.documents = allTexts;\n        const wordCounts = new Map();\n        const docWordCounts = new Map();\n        allTexts.forEach((text, docIndex)=>{\n            const words = this.tokenize(text);\n            const uniqueWords = new Set(words);\n            docWordCounts.set(docIndex.toString(), uniqueWords);\n            words.forEach((word)=>{\n                wordCounts.set(word, (wordCounts.get(word) || 0) + 1);\n                if (!this.vocabulary.has(word)) {\n                    this.vocabulary.set(word, this.vocabulary.size);\n                }\n            });\n        });\n        // Calculate IDF scores\n        const totalDocs = allTexts.length;\n        this.vocabulary.forEach((_, word)=>{\n            let docFreq = 0;\n            docWordCounts.forEach((words)=>{\n                if (words.has(word)) docFreq++;\n            });\n            this.idf.set(word, Math.log(totalDocs / (docFreq + 1)));\n        });\n    }\n    extractAllTexts() {\n        const texts = [];\n        // Extract from company info\n        texts.push(`${_chatbot_knowledge__WEBPACK_IMPORTED_MODULE_0__.websiteKnowledge.company.name} ${_chatbot_knowledge__WEBPACK_IMPORTED_MODULE_0__.websiteKnowledge.company.description} ${_chatbot_knowledge__WEBPACK_IMPORTED_MODULE_0__.websiteKnowledge.company.mission}`);\n        // Extract from services\n        _chatbot_knowledge__WEBPACK_IMPORTED_MODULE_0__.websiteKnowledge.services.forEach((service)=>{\n            texts.push(`${service.name} ${service.description} ${service.features.join(' ')} ${service.pricing}`);\n        });\n        // Extract from universities\n        _chatbot_knowledge__WEBPACK_IMPORTED_MODULE_0__.websiteKnowledge.universities.forEach((uni)=>{\n            texts.push(`${uni.name} ${uni.location} ${uni.programs.join(' ')} ${uni.language} ${uni.accreditation}`);\n        });\n        // Extract from FAQ\n        _chatbot_knowledge__WEBPACK_IMPORTED_MODULE_0__.websiteKnowledge.faq.forEach((faq)=>{\n            texts.push(`${faq.question} ${faq.answer}`);\n        });\n        // Extract from testimonials\n        _chatbot_knowledge__WEBPACK_IMPORTED_MODULE_0__.websiteKnowledge.testimonials.forEach((testimonial)=>{\n            texts.push(`${testimonial.name} ${testimonial.country} ${testimonial.program} ${testimonial.university} ${testimonial.comment}`);\n        });\n        // Extract from costs\n        texts.push(`tuition fees undergraduate graduate medicine living costs accommodation food transportation`);\n        // Extract from scholarships\n        _chatbot_knowledge__WEBPACK_IMPORTED_MODULE_0__.websiteKnowledge.scholarships.forEach((scholarship)=>{\n            texts.push(`${scholarship.name} ${scholarship.amount} ${scholarship.criteria} ${scholarship.eligibility}`);\n        });\n        return texts;\n    }\n    tokenize(text) {\n        return text.toLowerCase().replace(/[^\\w\\s]/g, ' ').split(/\\s+/).filter((word)=>word.length > 2);\n    }\n    embed(text) {\n        const words = this.tokenize(text);\n        const vector = new Array(this.vocabulary.size).fill(0);\n        // Calculate TF scores\n        const wordCounts = new Map();\n        words.forEach((word)=>{\n            wordCounts.set(word, (wordCounts.get(word) || 0) + 1);\n        });\n        // Calculate TF-IDF vector\n        wordCounts.forEach((count, word)=>{\n            const vocabIndex = this.vocabulary.get(word);\n            if (vocabIndex !== undefined) {\n                const tf = count / words.length;\n                const idf = this.idf.get(word) || 0;\n                vector[vocabIndex] = tf * idf;\n            }\n        });\n        return vector;\n    }\n    similarity(vec1, vec2) {\n        let dotProduct = 0;\n        let norm1 = 0;\n        let norm2 = 0;\n        for(let i = 0; i < vec1.length; i++){\n            dotProduct += vec1[i] * vec2[i];\n            norm1 += vec1[i] * vec1[i];\n            norm2 += vec2[i] * vec2[i];\n        }\n        const magnitude = Math.sqrt(norm1) * Math.sqrt(norm2);\n        return magnitude === 0 ? 0 : dotProduct / magnitude;\n    }\n}\n// Knowledge retrieval system\nclass KnowledgeRetriever {\n    constructor(){\n        this.knowledgeChunks = [];\n        this.embedding = new VectorEmbedding();\n        this.buildKnowledgeBase();\n    }\n    buildKnowledgeBase() {\n        // University information chunks\n        _chatbot_knowledge__WEBPACK_IMPORTED_MODULE_0__.websiteKnowledge.universities.forEach((uni)=>{\n            const content = `${uni.name} is located in ${uni.location}, established in ${uni.established}. \n        It has ${uni.students} students including ${uni.international} international students. \n        Programs offered: ${uni.programs.join(', ')}. \n        Tuition ranges from $${uni.tuition.min} to $${uni.tuition.max} per year. \n        Language of instruction: ${uni.language}. \n        Accreditation: ${uni.accreditation}.`;\n            this.knowledgeChunks.push({\n                content,\n                metadata: uni,\n                vector: this.embedding.embed(content),\n                category: 'universities'\n            });\n        });\n        // Service information chunks\n        _chatbot_knowledge__WEBPACK_IMPORTED_MODULE_0__.websiteKnowledge.services.forEach((service)=>{\n            const content = `${service.name}: ${service.description}. \n        Features include: ${service.features.join(', ')}. \n        Pricing: ${service.pricing}.`;\n            this.knowledgeChunks.push({\n                content,\n                metadata: service,\n                vector: this.embedding.embed(content),\n                category: 'services'\n            });\n        });\n        // FAQ chunks\n        _chatbot_knowledge__WEBPACK_IMPORTED_MODULE_0__.websiteKnowledge.faq.forEach((faq)=>{\n            this.knowledgeChunks.push({\n                content: `Question: ${faq.question} Answer: ${faq.answer}`,\n                metadata: faq,\n                vector: this.embedding.embed(`${faq.question} ${faq.answer}`),\n                category: 'faq'\n            });\n        });\n        // Cost information chunks\n        const costContent = `Undergraduate tuition: $${_chatbot_knowledge__WEBPACK_IMPORTED_MODULE_0__.websiteKnowledge.costs.tuition.undergraduate.min}-${_chatbot_knowledge__WEBPACK_IMPORTED_MODULE_0__.websiteKnowledge.costs.tuition.undergraduate.max} per year.\n      Graduate tuition: $${_chatbot_knowledge__WEBPACK_IMPORTED_MODULE_0__.websiteKnowledge.costs.tuition.graduate.min}-${_chatbot_knowledge__WEBPACK_IMPORTED_MODULE_0__.websiteKnowledge.costs.tuition.graduate.max} per year.\n      Medicine tuition: $${_chatbot_knowledge__WEBPACK_IMPORTED_MODULE_0__.websiteKnowledge.costs.tuition.medicine.min}-${_chatbot_knowledge__WEBPACK_IMPORTED_MODULE_0__.websiteKnowledge.costs.tuition.medicine.max} per year.\n      Living costs: $${_chatbot_knowledge__WEBPACK_IMPORTED_MODULE_0__.websiteKnowledge.costs.living.total.min}-${_chatbot_knowledge__WEBPACK_IMPORTED_MODULE_0__.websiteKnowledge.costs.living.total.max} per month.\n      Accommodation: $${_chatbot_knowledge__WEBPACK_IMPORTED_MODULE_0__.websiteKnowledge.costs.living.accommodation.min}-${_chatbot_knowledge__WEBPACK_IMPORTED_MODULE_0__.websiteKnowledge.costs.living.accommodation.max} per month.`;\n        this.knowledgeChunks.push({\n            content: costContent,\n            metadata: _chatbot_knowledge__WEBPACK_IMPORTED_MODULE_0__.websiteKnowledge.costs,\n            vector: this.embedding.embed(costContent),\n            category: 'costs'\n        });\n        // Scholarship chunks\n        _chatbot_knowledge__WEBPACK_IMPORTED_MODULE_0__.websiteKnowledge.scholarships.forEach((scholarship)=>{\n            const content = `${scholarship.name}: ${scholarship.amount} reduction. \n        Criteria: ${scholarship.criteria}. \n        Eligibility: ${scholarship.eligibility || 'General eligibility applies'}.`;\n            this.knowledgeChunks.push({\n                content,\n                metadata: scholarship,\n                vector: this.embedding.embed(content),\n                category: 'scholarships'\n            });\n        });\n        // Admission process chunk\n        const admissionContent = `Admission process steps: ${_chatbot_knowledge__WEBPACK_IMPORTED_MODULE_0__.websiteKnowledge.admissionProcess.steps.join(', ')}. \n      Timeline: ${_chatbot_knowledge__WEBPACK_IMPORTED_MODULE_0__.websiteKnowledge.admissionProcess.timeline}. \n      Undergraduate requirements: ${_chatbot_knowledge__WEBPACK_IMPORTED_MODULE_0__.websiteKnowledge.admissionProcess.requirements.undergraduate.join(', ')}. \n      Graduate requirements: ${_chatbot_knowledge__WEBPACK_IMPORTED_MODULE_0__.websiteKnowledge.admissionProcess.requirements.graduate.join(', ')}.`;\n        this.knowledgeChunks.push({\n            content: admissionContent,\n            metadata: _chatbot_knowledge__WEBPACK_IMPORTED_MODULE_0__.websiteKnowledge.admissionProcess,\n            vector: this.embedding.embed(admissionContent),\n            category: 'admissions'\n        });\n        // Location and lifestyle chunk\n        const locationContent = `Northern Cyprus is a safe Mediterranean island with excellent weather, \n      low crime rates, and a welcoming international community. \n      It's located between Turkey and the Middle East, offering rich cultural experiences.`;\n        this.knowledgeChunks.push({\n            content: locationContent,\n            metadata: {\n                location: 'Northern Cyprus'\n            },\n            vector: this.embedding.embed(locationContent),\n            category: 'location'\n        });\n    }\n    retrieve(query, topK = 5) {\n        const queryVector = this.embedding.embed(query);\n        const similarities = this.knowledgeChunks.map((chunk)=>({\n                ...chunk,\n                similarity: this.embedding.similarity(queryVector, chunk.vector)\n            }));\n        return similarities.sort((a, b)=>b.similarity - a.similarity).slice(0, topK).filter((item)=>item.similarity > 0.1) // Minimum similarity threshold\n        ;\n    }\n    retrieveByCategory(query, category, topK = 3) {\n        const categoryChunks = this.knowledgeChunks.filter((chunk)=>chunk.category === category);\n        const queryVector = this.embedding.embed(query);\n        const similarities = categoryChunks.map((chunk)=>({\n                ...chunk,\n                similarity: this.embedding.similarity(queryVector, chunk.vector)\n            }));\n        return similarities.sort((a, b)=>b.similarity - a.similarity).slice(0, topK);\n    }\n}\n// Enhanced response generation with RAG\nclass RAGResponseGenerator {\n    constructor(){\n        this.retriever = new KnowledgeRetriever();\n        this.semanticSearch = new _semantic_search__WEBPACK_IMPORTED_MODULE_1__.SemanticSearchEngine();\n        this.questionClassifier = new _semantic_search__WEBPACK_IMPORTED_MODULE_1__.QuestionClassifier();\n        this.enhancedKB = new _document_processor__WEBPACK_IMPORTED_MODULE_2__.EnhancedKnowledgeBase();\n    }\n    generateResponse(query, category) {\n        // Classify the question type for better response generation\n        const questionType = this.questionClassifier.classifyQuestion(query);\n        // Use semantic search for better retrieval\n        const expandedQuery = this.semanticSearch.expandQuery(query);\n        const semanticQuery = expandedQuery.join(' ');\n        // Retrieve relevant information using both methods\n        const retrievedInfo = category ? this.retriever.retrieveByCategory(semanticQuery, category, 3) : this.retriever.retrieve(semanticQuery, 5);\n        // Enhance with semantic search\n        const semanticMatches = this.semanticSearch.findBestMatches(query, retrievedInfo, 3);\n        if (retrievedInfo.length === 0 && semanticMatches.length === 0) {\n            return {\n                response: \"I don't have specific information about that topic. Let me connect you with our human advisors who can provide detailed assistance.\",\n                sources: [],\n                confidence: 0,\n                retrievedChunks: 0,\n                questionType,\n                semanticMatches: []\n            };\n        }\n        // Combine and rank results\n        const combinedResults = this.combineResults(retrievedInfo, semanticMatches);\n        // Calculate enhanced confidence\n        const confidence = this.calculateEnhancedConfidence(query, combinedResults, questionType);\n        // Generate contextual response using question type\n        const response = this.synthesizeEnhancedResponse(query, combinedResults, questionType);\n        return {\n            response,\n            sources: combinedResults.map((item)=>item.metadata || item),\n            confidence,\n            retrievedChunks: combinedResults.length,\n            questionType,\n            semanticMatches\n        };\n    }\n    combineResults(retrievedInfo, semanticMatches) {\n        const combined = [\n            ...retrievedInfo\n        ];\n        // Add semantic matches that aren't already included\n        semanticMatches.forEach((match)=>{\n            const exists = combined.some((item)=>item.content === match.content || item.metadata && match.metadata && item.metadata.id === match.metadata.id);\n            if (!exists) {\n                combined.push({\n                    content: match.content,\n                    metadata: match.metadata,\n                    similarity: match.score,\n                    category: match.metadata?.category || 'general'\n                });\n            }\n        });\n        // Sort by relevance\n        return combined.sort((a, b)=>(b.similarity || 0) - (a.similarity || 0)).slice(0, 5);\n    }\n    calculateEnhancedConfidence(query, results, questionType) {\n        if (results.length === 0) return 0;\n        let baseConfidence = results.reduce((sum, item)=>sum + (item.similarity || 0), 0) / results.length;\n        // Boost confidence based on question type match\n        const questionTypeBoost = {\n            'comparison': 0.1,\n            'cost_inquiry': 0.15,\n            'process_inquiry': 0.1,\n            'factual_inquiry': 0.05,\n            'eligibility_inquiry': 0.1,\n            'general': 0\n        };\n        baseConfidence += questionTypeBoost[questionType] || 0;\n        // Boost for multiple relevant results\n        if (results.length >= 3) baseConfidence += 0.1;\n        // Boost for semantic keyword matches\n        const queryLower = query.toLowerCase();\n        const hasKeywords = [\n            'university',\n            'cost',\n            'admission',\n            'scholarship',\n            'visa'\n        ].some((keyword)=>queryLower.includes(keyword));\n        if (hasKeywords) baseConfidence += 0.05;\n        return Math.min(baseConfidence * 100, 95);\n    }\n    synthesizeEnhancedResponse(query, results, questionType) {\n        if (results.length === 0) {\n            return \"I don't have specific information about that topic. Let me connect you with our human advisors who can provide detailed assistance.\";\n        }\n        // Use question classifier to generate appropriate response template\n        const contextualResponse = this.questionClassifier.generateResponseTemplate(questionType, results);\n        // If template response is generic, fall back to original synthesis\n        if (contextualResponse.includes('Based on the information available')) {\n            return this.synthesizeResponse(query, results);\n        }\n        return contextualResponse;\n    }\n    synthesizeResponse(query, retrievedInfo) {\n        const queryLower = query.toLowerCase();\n        // Determine response type based on query\n        if (queryLower.includes('university') || queryLower.includes('emu') || queryLower.includes('neu') || queryLower.includes('ciu')) {\n            return this.generateUniversityResponse(retrievedInfo);\n        } else if (queryLower.includes('cost') || queryLower.includes('fee') || queryLower.includes('price') || queryLower.includes('tuition')) {\n            return this.generateCostResponse(retrievedInfo);\n        } else if (queryLower.includes('scholarship') || queryLower.includes('financial aid')) {\n            return this.generateScholarshipResponse(retrievedInfo);\n        } else if (queryLower.includes('admission') || queryLower.includes('apply') || queryLower.includes('application')) {\n            return this.generateAdmissionResponse(retrievedInfo);\n        } else if (queryLower.includes('service') || queryLower.includes('help') || queryLower.includes('support')) {\n            return this.generateServiceResponse(retrievedInfo);\n        } else {\n            return this.generateGeneralResponse(retrievedInfo);\n        }\n    }\n    generateUniversityResponse(retrievedInfo) {\n        const universities = retrievedInfo.filter((item)=>item.category === 'universities');\n        if (universities.length === 0) {\n            return \"I can provide information about our partner universities. We work with EMU, NEU, and CIU - all excellent institutions in Northern Cyprus.\";\n        }\n        let response = \"🏛️ **University Information:**\\n\\n\";\n        universities.forEach((uni)=>{\n            const metadata = uni.metadata;\n            response += `**${metadata.name}**\\n`;\n            response += `📍 Location: ${metadata.location}\\n`;\n            response += `👥 Students: ${metadata.students?.toLocaleString()} (${metadata.international?.toLocaleString()} international)\\n`;\n            response += `🎓 Programs: ${metadata.programs?.join(', ')}\\n`;\n            response += `💰 Tuition: $${metadata.tuition?.min?.toLocaleString()}-$${metadata.tuition?.max?.toLocaleString()} per year\\n`;\n            response += `🌍 Language: ${metadata.language}\\n\\n`;\n        });\n        response += \"Would you like more details about any specific university or program?\";\n        return response;\n    }\n    generateCostResponse(retrievedInfo) {\n        const costInfo = retrievedInfo.find((item)=>item.category === 'costs');\n        if (!costInfo) {\n            return \"💰 **Study Costs in Northern Cyprus:**\\n\\nTuition fees are very affordable compared to other countries. Contact us for detailed cost breakdowns specific to your program of interest.\";\n        }\n        return `💰 **Complete Cost Breakdown:**\n\n**📚 Annual Tuition Fees:**\n• Undergraduate: $3,000 - $8,000\n• Graduate: $4,000 - $12,000  \n• Medicine: $8,000 - $15,000\n\n**🏠 Monthly Living Costs:**\n• Accommodation: $200 - $600\n• Food & Meals: $150 - $300\n• Transportation: $50 - $100\n• **Total Living: $400 - $1,000**\n\n**💡 Why Choose Northern Cyprus:**\n✅ 50-70% cheaper than UK/US/EU\n✅ High quality education standards\n✅ English-taught programs\n✅ Scholarship opportunities available\n\nNeed a personalized cost estimate? I can help calculate total expenses for your specific situation!`;\n    }\n    generateScholarshipResponse(retrievedInfo) {\n        const scholarships = retrievedInfo.filter((item)=>item.category === 'scholarships');\n        let response = \"💰 **Scholarship Opportunities:**\\n\\n\";\n        scholarships.forEach((scholarship)=>{\n            const metadata = scholarship.metadata;\n            response += `**${metadata.name}** 🏆\\n`;\n            response += `• Amount: ${metadata.amount}\\n`;\n            response += `• Criteria: ${metadata.criteria}\\n`;\n            if (metadata.eligibility) {\n                response += `• Eligibility: ${metadata.eligibility}\\n`;\n            }\n            response += \"\\n\";\n        });\n        response += `**🎯 How to Maximize Your Scholarships:**\n✅ Apply early (before March 31st for Early Bird)\n✅ Maintain high academic performance (3.5+ GPA)\n✅ Submit complete application documents\n✅ Consider multiple family members (Sibling Discount)\n\n**Total Possible Savings: Up to 65% off tuition!**\n\nReady to apply for scholarships? I can guide you through the process!`;\n        return response;\n    }\n    generateAdmissionResponse(retrievedInfo) {\n        const admissionInfo = retrievedInfo.find((item)=>item.category === 'admissions');\n        return `📋 **University Admission Process:**\n\n**🎯 Complete Steps:**\n1. Initial consultation and assessment\n2. University and program selection  \n3. Document preparation and verification\n4. Application submission\n5. Interview preparation (if required)\n6. Admission decision and acceptance\n7. Visa application process\n8. Pre-departure orientation\n9. Arrival and settlement support\n\n**⏱️ Timeline:** 2-4 months average\n\n**📄 Required Documents:**\n**Undergraduate:**\n• High school diploma/certificate\n• English proficiency test (IELTS 6.0+/TOEFL 79+)\n• Passport copy\n• Academic transcripts\n• Personal statement\n\n**Graduate:**\n• Bachelor's degree certificate\n• English proficiency test\n• Letters of recommendation (2-3)\n• Statement of purpose\n• GRE/GMAT (some programs)\n\n**🌟 Our Success Rate: 98%**\nWe guarantee admission or full refund!\n\nReady to start your application? Let me help you begin!`;\n    }\n    generateServiceResponse(retrievedInfo) {\n        const services = retrievedInfo.filter((item)=>item.category === 'services');\n        let response = \"🎯 **Our Complete Services:**\\n\\n\";\n        services.forEach((service)=>{\n            const metadata = service.metadata;\n            response += `**${metadata.name}** ✅\\n`;\n            response += `${metadata.description}\\n`;\n            response += `Features: ${metadata.features?.join(', ')}\\n`;\n            response += `Pricing: ${metadata.pricing}\\n\\n`;\n        });\n        response += `**🌟 Why Choose Foreingate:**\n✅ 98% success rate\n✅ 5,000+ students helped\n✅ End-to-end support\n✅ Multilingual team\n✅ Free initial consultation\n\n**📞 Ready to Get Started?**\nContact us for your free consultation and personalized guidance!`;\n        return response;\n    }\n    generateGeneralResponse(retrievedInfo) {\n        const topChunk = retrievedInfo[0];\n        if (!topChunk) {\n            return \"I'm here to help with any questions about studying in Northern Cyprus. What specific information would you like to know?\";\n        }\n        return `Based on the information I found, here's what I can tell you:\n\n${topChunk.content}\n\nThis information comes from our comprehensive knowledge base with ${Math.round(topChunk.similarity * 100)}% relevance to your question.\n\nWould you like me to provide more specific details about any particular aspect?`;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/rag-system.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/security.ts":
/*!*****************************!*\
  !*** ./src/lib/security.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RateLimiter: () => (/* binding */ RateLimiter),\n/* harmony export */   SecureSession: () => (/* binding */ SecureSession),\n/* harmony export */   SecurityLogger: () => (/* binding */ SecurityLogger),\n/* harmony export */   ValidationSchemas: () => (/* binding */ ValidationSchemas),\n/* harmony export */   decryptData: () => (/* binding */ decryptData),\n/* harmony export */   encryptData: () => (/* binding */ encryptData),\n/* harmony export */   generateCSRFToken: () => (/* binding */ generateCSRFToken),\n/* harmony export */   generateSecureToken: () => (/* binding */ generateSecureToken),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   isValidEmail: () => (/* binding */ isValidEmail),\n/* harmony export */   isValidPhone: () => (/* binding */ isValidPhone),\n/* harmony export */   sanitizeInput: () => (/* binding */ sanitizeInput),\n/* harmony export */   verifyCSRFToken: () => (/* binding */ verifyCSRFToken),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword)\n/* harmony export */ });\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(crypto__WEBPACK_IMPORTED_MODULE_0__);\n\n// Security utilities for the application\n/**\n * Generate a secure random string\n */ function generateSecureToken(length = 32) {\n    return crypto__WEBPACK_IMPORTED_MODULE_0___default().randomBytes(length).toString('hex');\n}\n/**\n * Hash a password using PBKDF2\n */ async function hashPassword(password) {\n    const salt = crypto__WEBPACK_IMPORTED_MODULE_0___default().randomBytes(16).toString('hex');\n    const hash = crypto__WEBPACK_IMPORTED_MODULE_0___default().pbkdf2Sync(password, salt, 10000, 64, 'sha512').toString('hex');\n    return `${salt}:${hash}`;\n}\n/**\n * Verify a password against its hash\n */ async function verifyPassword(password, hashedPassword) {\n    const [salt, hash] = hashedPassword.split(':');\n    const verifyHash = crypto__WEBPACK_IMPORTED_MODULE_0___default().pbkdf2Sync(password, salt, 10000, 64, 'sha512').toString('hex');\n    return hash === verifyHash;\n}\n/**\n * Encrypt sensitive data\n */ function encryptData(data, key) {\n    const encryptionKey = key || process.env.ENCRYPTION_KEY || 'default-key-change-in-production';\n    const algorithm = 'aes-256-gcm';\n    const iv = crypto__WEBPACK_IMPORTED_MODULE_0___default().randomBytes(16);\n    const cipher = crypto__WEBPACK_IMPORTED_MODULE_0___default().createCipher(algorithm, encryptionKey);\n    let encrypted = cipher.update(data, 'utf8', 'hex');\n    encrypted += cipher.final('hex');\n    return `${iv.toString('hex')}:${encrypted}`;\n}\n/**\n * Decrypt sensitive data\n */ function decryptData(encryptedData, key) {\n    const encryptionKey = key || process.env.ENCRYPTION_KEY || 'default-key-change-in-production';\n    const algorithm = 'aes-256-gcm';\n    const [ivHex, encrypted] = encryptedData.split(':');\n    const iv = Buffer.from(ivHex, 'hex');\n    const decipher = crypto__WEBPACK_IMPORTED_MODULE_0___default().createDecipher(algorithm, encryptionKey);\n    let decrypted = decipher.update(encrypted, 'hex', 'utf8');\n    decrypted += decipher.final('utf8');\n    return decrypted;\n}\n/**\n * Sanitize user input to prevent XSS\n */ function sanitizeInput(input) {\n    return input.replace(/[<>]/g, '') // Remove < and >\n    .replace(/javascript:/gi, '') // Remove javascript: protocol\n    .replace(/on\\w+=/gi, '') // Remove event handlers\n    .trim();\n}\n/**\n * Validate email format\n */ function isValidEmail(email) {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n}\n/**\n * Validate phone number format\n */ function isValidPhone(phone) {\n    const phoneRegex = /^\\+?[\\d\\s\\-\\(\\)]{10,}$/;\n    return phoneRegex.test(phone);\n}\n/**\n * Generate CSRF token\n */ function generateCSRFToken() {\n    return crypto__WEBPACK_IMPORTED_MODULE_0___default().randomBytes(32).toString('hex');\n}\n/**\n * Verify CSRF token\n */ function verifyCSRFToken(token, sessionToken) {\n    return crypto__WEBPACK_IMPORTED_MODULE_0___default().timingSafeEqual(Buffer.from(token, 'hex'), Buffer.from(sessionToken, 'hex'));\n}\n/**\n * Rate limiting helper\n */ class RateLimiter {\n    constructor(maxRequests = 100, windowMs = 60000){\n        this.requests = new Map();\n        this.maxRequests = maxRequests;\n        this.windowMs = windowMs;\n    }\n    isAllowed(identifier) {\n        const now = Date.now();\n        const requests = this.requests.get(identifier) || [];\n        // Remove old requests outside the window\n        const validRequests = requests.filter((time)=>now - time < this.windowMs);\n        if (validRequests.length >= this.maxRequests) {\n            return false;\n        }\n        validRequests.push(now);\n        this.requests.set(identifier, validRequests);\n        return true;\n    }\n    getRemainingRequests(identifier) {\n        const requests = this.requests.get(identifier) || [];\n        const now = Date.now();\n        const validRequests = requests.filter((time)=>now - time < this.windowMs);\n        return Math.max(0, this.maxRequests - validRequests.length);\n    }\n}\n/**\n * Secure session management\n */ class SecureSession {\n    static{\n        this.sessions = new Map();\n    }\n    static create(data) {\n        const sessionId = generateSecureToken(32);\n        const sessionData = {\n            ...data,\n            createdAt: Date.now(),\n            lastAccessed: Date.now()\n        };\n        this.sessions.set(sessionId, sessionData);\n        return sessionId;\n    }\n    static get(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return null;\n        // Check if session is expired (24 hours)\n        if (Date.now() - session.lastAccessed > 24 * 60 * 60 * 1000) {\n            this.sessions.delete(sessionId);\n            return null;\n        }\n        // Update last accessed time\n        session.lastAccessed = Date.now();\n        return session;\n    }\n    static destroy(sessionId) {\n        this.sessions.delete(sessionId);\n    }\n    static cleanup() {\n        const now = Date.now();\n        for (const [sessionId, session] of this.sessions.entries()){\n            if (now - session.lastAccessed > 24 * 60 * 60 * 1000) {\n                this.sessions.delete(sessionId);\n            }\n        }\n    }\n}\n/**\n * Input validation schemas\n */ const ValidationSchemas = {\n    email: (email)=>{\n        if (!email || email.length > 254) return false;\n        return isValidEmail(email);\n    },\n    name: (name)=>{\n        if (!name || name.length < 2 || name.length > 100) return false;\n        return /^[a-zA-Z\\s\\-'\\.]+$/.test(name);\n    },\n    phone: (phone)=>{\n        if (!phone) return true // Optional field\n        ;\n        return isValidPhone(phone);\n    },\n    message: (message)=>{\n        if (!message || message.length < 10 || message.length > 5000) return false;\n        return true;\n    },\n    subject: (subject)=>{\n        if (!subject || subject.length < 5 || subject.length > 200) return false;\n        return true;\n    }\n};\n/**\n * Security audit logger\n */ class SecurityLogger {\n    static log(event, details, level = 'info') {\n        const logEntry = {\n            timestamp: new Date().toISOString(),\n            event,\n            details,\n            level\n        };\n        // In production, send to logging service\n        if (false) {} else {\n            console.log(`[SECURITY ${level.toUpperCase()}]`, event, details);\n        }\n    }\n    static logFailedLogin(ip, userAgent) {\n        this.log('FAILED_LOGIN', {\n            ip,\n            userAgent\n        }, 'warn');\n    }\n    static logSuspiciousActivity(ip, activity) {\n        this.log('SUSPICIOUS_ACTIVITY', {\n            ip,\n            activity\n        }, 'error');\n    }\n    static logDataAccess(resource, ip) {\n        this.log('DATA_ACCESS', {\n            resource,\n            ip\n        }, 'info');\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/security.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/semantic-search.ts":
/*!************************************!*\
  !*** ./src/lib/semantic-search.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QuestionClassifier: () => (/* binding */ QuestionClassifier),\n/* harmony export */   SemanticSearchEngine: () => (/* binding */ SemanticSearchEngine)\n/* harmony export */ });\n// Advanced Semantic Search for RAG System\nclass SemanticSearchEngine {\n    constructor(){\n        this.synonyms = new Map();\n        this.conceptMap = new Map();\n        this.entityRecognition = new Map();\n        this.initializeSynonyms();\n        this.initializeConceptMap();\n        this.initializeEntityRecognition();\n    }\n    initializeSynonyms() {\n        // Educational synonyms\n        this.synonyms.set('university', [\n            'college',\n            'institution',\n            'school',\n            'academy'\n        ]);\n        this.synonyms.set('program', [\n            'course',\n            'degree',\n            'major',\n            'study',\n            'curriculum'\n        ]);\n        this.synonyms.set('tuition', [\n            'fees',\n            'cost',\n            'price',\n            'charges',\n            'expenses'\n        ]);\n        this.synonyms.set('scholarship', [\n            'financial aid',\n            'grant',\n            'bursary',\n            'funding',\n            'discount'\n        ]);\n        this.synonyms.set('admission', [\n            'enrollment',\n            'application',\n            'entry',\n            'acceptance'\n        ]);\n        this.synonyms.set('requirement', [\n            'prerequisite',\n            'criteria',\n            'condition',\n            'qualification'\n        ]);\n        this.synonyms.set('accommodation', [\n            'housing',\n            'residence',\n            'dormitory',\n            'lodging'\n        ]);\n        this.synonyms.set('international', [\n            'foreign',\n            'overseas',\n            'global',\n            'worldwide'\n        ]);\n        this.synonyms.set('graduate', [\n            'postgraduate',\n            'masters',\n            'phd',\n            'doctoral'\n        ]);\n        this.synonyms.set('undergraduate', [\n            'bachelor',\n            'bachelors',\n            'first degree'\n        ]);\n        // Location synonyms\n        this.synonyms.set('cyprus', [\n            'northern cyprus',\n            'trnc',\n            'kktc'\n        ]);\n        this.synonyms.set('nicosia', [\n            'lefkosa',\n            'capital'\n        ]);\n        this.synonyms.set('famagusta', [\n            'gazimagusa'\n        ]);\n        // Subject synonyms\n        this.synonyms.set('engineering', [\n            'technology',\n            'technical',\n            'applied science'\n        ]);\n        this.synonyms.set('medicine', [\n            'medical',\n            'healthcare',\n            'health sciences'\n        ]);\n        this.synonyms.set('business', [\n            'management',\n            'commerce',\n            'economics',\n            'finance'\n        ]);\n        this.synonyms.set('computer', [\n            'computing',\n            'it',\n            'information technology',\n            'software'\n        ]);\n    }\n    initializeConceptMap() {\n        // Map concepts to related terms\n        this.conceptMap.set('cost_related', [\n            'tuition',\n            'fees',\n            'scholarship',\n            'financial aid',\n            'budget',\n            'affordable',\n            'expensive',\n            'cheap',\n            'payment',\n            'installment',\n            'discount',\n            'funding'\n        ]);\n        this.conceptMap.set('admission_related', [\n            'apply',\n            'application',\n            'admission',\n            'enrollment',\n            'requirements',\n            'documents',\n            'deadline',\n            'process',\n            'steps',\n            'eligibility',\n            'criteria',\n            'qualification'\n        ]);\n        this.conceptMap.set('academic_related', [\n            'program',\n            'degree',\n            'course',\n            'curriculum',\n            'faculty',\n            'department',\n            'major',\n            'study',\n            'education',\n            'academic',\n            'learning',\n            'teaching'\n        ]);\n        this.conceptMap.set('location_related', [\n            'cyprus',\n            'northern cyprus',\n            'nicosia',\n            'famagusta',\n            'campus',\n            'location',\n            'address',\n            'where',\n            'situated',\n            'based',\n            'island',\n            'mediterranean'\n        ]);\n        this.conceptMap.set('support_related', [\n            'help',\n            'support',\n            'assistance',\n            'service',\n            'guidance',\n            'advice',\n            'consultation',\n            'counseling',\n            'mentoring',\n            'coaching'\n        ]);\n        this.conceptMap.set('visa_related', [\n            'visa',\n            'permit',\n            'immigration',\n            'passport',\n            'documents',\n            'embassy',\n            'consulate',\n            'application',\n            'processing',\n            'approval',\n            'entry'\n        ]);\n    }\n    initializeEntityRecognition() {\n        // University entities\n        this.entityRecognition.set('emu', 'Eastern Mediterranean University');\n        this.entityRecognition.set('eastern mediterranean university', 'Eastern Mediterranean University');\n        this.entityRecognition.set('neu', 'Near East University');\n        this.entityRecognition.set('near east university', 'Near East University');\n        this.entityRecognition.set('ciu', 'Cyprus International University');\n        this.entityRecognition.set('cyprus international university', 'Cyprus International University');\n        // Program entities\n        this.entityRecognition.set('cs', 'Computer Science');\n        this.entityRecognition.set('it', 'Information Technology');\n        this.entityRecognition.set('mba', 'Master of Business Administration');\n        this.entityRecognition.set('md', 'Medicine');\n        this.entityRecognition.set('dds', 'Dentistry');\n        // Test entities\n        this.entityRecognition.set('ielts', 'International English Language Testing System');\n        this.entityRecognition.set('toefl', 'Test of English as a Foreign Language');\n        this.entityRecognition.set('gre', 'Graduate Record Examination');\n        this.entityRecognition.set('gmat', 'Graduate Management Admission Test');\n    }\n    expandQuery(query) {\n        const expandedTerms = new Set();\n        const originalTerms = query.toLowerCase().split(/\\s+/);\n        // Add original terms\n        originalTerms.forEach((term)=>expandedTerms.add(term));\n        // Add synonyms\n        originalTerms.forEach((term)=>{\n            const synonymList = this.synonyms.get(term);\n            if (synonymList) {\n                synonymList.forEach((synonym)=>expandedTerms.add(synonym));\n            }\n        });\n        // Add concept-related terms\n        this.conceptMap.forEach((concepts, category)=>{\n            const hasConceptMatch = originalTerms.some((term)=>concepts.includes(term));\n            if (hasConceptMatch) {\n                concepts.forEach((concept)=>expandedTerms.add(concept));\n            }\n        });\n        // Recognize and expand entities\n        const queryLower = query.toLowerCase();\n        this.entityRecognition.forEach((fullName, abbreviation)=>{\n            if (queryLower.includes(abbreviation)) {\n                expandedTerms.add(fullName.toLowerCase());\n            }\n        });\n        return Array.from(expandedTerms);\n    }\n    calculateSemanticSimilarity(query, content) {\n        const expandedQuery = this.expandQuery(query);\n        const contentLower = content.toLowerCase();\n        let score = 0;\n        let matches = 0;\n        // Direct term matching with weights\n        expandedQuery.forEach((term)=>{\n            if (contentLower.includes(term)) {\n                matches++;\n                // Weight longer terms higher\n                score += term.length > 5 ? 2 : 1;\n                // Boost for exact phrase matches\n                if (query.toLowerCase().includes(term) && contentLower.includes(term)) {\n                    score += 1;\n                }\n            }\n        });\n        // Concept clustering bonus\n        const conceptBonus = this.calculateConceptBonus(query, content);\n        score += conceptBonus;\n        // Entity recognition bonus\n        const entityBonus = this.calculateEntityBonus(query, content);\n        score += entityBonus;\n        // Normalize score\n        const maxPossibleScore = expandedQuery.length * 2 + 5 // Max possible with bonuses\n        ;\n        return Math.min(score / maxPossibleScore, 1.0);\n    }\n    calculateConceptBonus(query, content) {\n        let bonus = 0;\n        const queryLower = query.toLowerCase();\n        const contentLower = content.toLowerCase();\n        this.conceptMap.forEach((concepts, category)=>{\n            const queryHasConcept = concepts.some((concept)=>queryLower.includes(concept));\n            const contentHasConcept = concepts.some((concept)=>contentLower.includes(concept));\n            if (queryHasConcept && contentHasConcept) {\n                bonus += 1;\n            }\n        });\n        return bonus;\n    }\n    calculateEntityBonus(query, content) {\n        let bonus = 0;\n        const queryLower = query.toLowerCase();\n        const contentLower = content.toLowerCase();\n        this.entityRecognition.forEach((fullName, abbreviation)=>{\n            const queryHasEntity = queryLower.includes(abbreviation) || queryLower.includes(fullName.toLowerCase());\n            const contentHasEntity = contentLower.includes(abbreviation) || contentLower.includes(fullName.toLowerCase());\n            if (queryHasEntity && contentHasEntity) {\n                bonus += 2 // Higher bonus for entity matches\n                ;\n            }\n        });\n        return bonus;\n    }\n    extractKeyPhrases(text) {\n        const phrases = [];\n        const words = text.toLowerCase().split(/\\s+/);\n        // Extract 2-3 word phrases that might be important\n        for(let i = 0; i < words.length - 1; i++){\n            const twoWordPhrase = `${words[i]} ${words[i + 1]}`;\n            if (this.isImportantPhrase(twoWordPhrase)) {\n                phrases.push(twoWordPhrase);\n            }\n            if (i < words.length - 2) {\n                const threeWordPhrase = `${words[i]} ${words[i + 1]} ${words[i + 2]}`;\n                if (this.isImportantPhrase(threeWordPhrase)) {\n                    phrases.push(threeWordPhrase);\n                }\n            }\n        }\n        return phrases;\n    }\n    isImportantPhrase(phrase) {\n        const importantPatterns = [\n            /\\b(computer|electrical|civil|mechanical|industrial)\\s+engineering\\b/,\n            /\\b(business|international)\\s+(administration|relations)\\b/,\n            /\\b(tuition|living)\\s+(fees|costs)\\b/,\n            /\\b(english|language)\\s+(proficiency|requirements)\\b/,\n            /\\b(student|residence)\\s+(visa|permit)\\b/,\n            /\\b(northern|eastern)\\s+cyprus\\b/,\n            /\\b(near|eastern)\\s+(east|mediterranean)\\b/,\n            /\\b(financial|merit)\\s+(aid|scholarship)\\b/\n        ];\n        return importantPatterns.some((pattern)=>pattern.test(phrase));\n    }\n    highlightMatches(content, query) {\n        const expandedQuery = this.expandQuery(query);\n        const highlights = [];\n        expandedQuery.forEach((term)=>{\n            const regex = new RegExp(`\\\\b${term}\\\\b`, 'gi');\n            const matches = content.match(regex);\n            if (matches) {\n                highlights.push(...matches);\n            }\n        });\n        return [\n            ...new Set(highlights)\n        ] // Remove duplicates\n        ;\n    }\n    findBestMatches(query, documents, limit = 5) {\n        const matches = [];\n        documents.forEach((doc)=>{\n            const similarity = this.calculateSemanticSimilarity(query, doc.content);\n            if (similarity > 0.1) {\n                const highlights = this.highlightMatches(doc.content, query);\n                const keyPhrases = this.extractKeyPhrases(doc.content);\n                matches.push({\n                    content: doc.content,\n                    score: similarity,\n                    metadata: doc.metadata || {},\n                    context: this.extractContext(doc.content, query),\n                    highlights\n                });\n            }\n        });\n        return matches.sort((a, b)=>b.score - a.score).slice(0, limit);\n    }\n    extractContext(content, query, contextLength = 200) {\n        const queryTerms = query.toLowerCase().split(/\\s+/);\n        const contentLower = content.toLowerCase();\n        // Find the best position to extract context\n        let bestPosition = 0;\n        let maxMatches = 0;\n        for(let i = 0; i <= content.length - contextLength; i += 50){\n            const segment = contentLower.substring(i, i + contextLength);\n            const matches = queryTerms.filter((term)=>segment.includes(term)).length;\n            if (matches > maxMatches) {\n                maxMatches = matches;\n                bestPosition = i;\n            }\n        }\n        const context = content.substring(bestPosition, bestPosition + contextLength);\n        return context.trim() + (bestPosition + contextLength < content.length ? '...' : '');\n    }\n}\n// Question intent classifier for better RAG responses\nclass QuestionClassifier {\n    constructor(){\n        this.questionPatterns = new Map();\n        this.initializePatterns();\n    }\n    initializePatterns() {\n        this.questionPatterns.set('comparison', [\n            /\\b(compare|difference|better|vs|versus|which is)\\b/i,\n            /\\b(best|top|better|prefer|choose between)\\b/i\n        ]);\n        this.questionPatterns.set('cost_inquiry', [\n            /\\b(cost|price|fee|expensive|cheap|afford|budget)\\b/i,\n            /\\b(how much|what does.*cost|tuition)\\b/i\n        ]);\n        this.questionPatterns.set('process_inquiry', [\n            /\\b(how to|process|steps|procedure|apply)\\b/i,\n            /\\b(what.*need|requirements|documents)\\b/i\n        ]);\n        this.questionPatterns.set('factual_inquiry', [\n            /\\b(what is|tell me about|information about)\\b/i,\n            /\\b(where|when|who|which)\\b/i\n        ]);\n        this.questionPatterns.set('eligibility_inquiry', [\n            /\\b(can i|am i eligible|qualify|requirements)\\b/i,\n            /\\b(do i need|must i|should i)\\b/i\n        ]);\n    }\n    classifyQuestion(question) {\n        const questionLower = question.toLowerCase();\n        for (const [category, patterns] of this.questionPatterns){\n            if (patterns.some((pattern)=>pattern.test(questionLower))) {\n                return category;\n            }\n        }\n        return 'general';\n    }\n    generateResponseTemplate(questionType, context) {\n        switch(questionType){\n            case 'comparison':\n                return this.generateComparisonResponse(context);\n            case 'cost_inquiry':\n                return this.generateCostResponse(context);\n            case 'process_inquiry':\n                return this.generateProcessResponse(context);\n            case 'factual_inquiry':\n                return this.generateFactualResponse(context);\n            case 'eligibility_inquiry':\n                return this.generateEligibilityResponse(context);\n            default:\n                return this.generateGeneralResponse(context);\n        }\n    }\n    generateComparisonResponse(context) {\n        return `🔍 **Comparison Analysis:**\\n\\nBased on the information available, here's a detailed comparison:\\n\\n${context.map((item)=>`• ${item.content}`).join('\\n\\n')}\\n\\nWould you like me to elaborate on any specific aspect of this comparison?`;\n    }\n    generateCostResponse(context) {\n        return `💰 **Cost Information:**\\n\\n${context.map((item)=>item.content).join('\\n\\n')}\\n\\n**💡 Cost-Saving Tips:**\\n• Apply early for scholarships\\n• Consider shared accommodation\\n• Look into payment plan options\\n\\nNeed a personalized cost estimate? I can help calculate total expenses for your specific situation!`;\n    }\n    generateProcessResponse(context) {\n        return `📋 **Step-by-Step Process:**\\n\\n${context.map((item, index)=>`**Step ${index + 1}:** ${item.content}`).join('\\n\\n')}\\n\\n**⏱️ Timeline:** Most processes take 2-4 months\\n**✅ Success Rate:** 98% with proper guidance\\n\\nReady to start? I can guide you through each step!`;\n    }\n    generateFactualResponse(context) {\n        return `📚 **Information Summary:**\\n\\n${context.map((item)=>item.content).join('\\n\\n')}\\n\\nThis information is current and verified. Would you like more details about any specific aspect?`;\n    }\n    generateEligibilityResponse(context) {\n        return `✅ **Eligibility Assessment:**\\n\\n${context.map((item)=>item.content).join('\\n\\n')}\\n\\n**📝 Quick Eligibility Check:**\\nTo give you a personalized assessment, I'd need to know:\\n• Your academic background\\n• English proficiency level\\n• Intended program of study\\n\\nShall we do a quick eligibility assessment?`;\n    }\n    generateGeneralResponse(context) {\n        return `${context.map((item)=>item.content).join('\\n\\n')}\\n\\nIs there anything specific you'd like me to explain further?`;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/semantic-search.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "fs/promises":
/*!******************************!*\
  !*** external "fs/promises" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("fs/promises");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchatbot%2Froute&page=%2Fapi%2Fchatbot%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchatbot%2Froute.ts&appDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();