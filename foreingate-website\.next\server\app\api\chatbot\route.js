/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/chatbot/route";
exports.ids = ["app/api/chatbot/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchatbot%2Froute&page=%2Fapi%2Fchatbot%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchatbot%2Froute.ts&appDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchatbot%2Froute&page=%2Fapi%2Fchatbot%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchatbot%2Froute.ts&appDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Nidhal_Documents_augment_projects_foreingate_groupe_foreingate_website_src_app_api_chatbot_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/chatbot/route.ts */ \"(rsc)/./src/app/api/chatbot/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"standalone\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/chatbot/route\",\n        pathname: \"/api/chatbot\",\n        filename: \"route\",\n        bundlePath: \"app/api/chatbot/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\api\\\\chatbot\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Nidhal_Documents_augment_projects_foreingate_groupe_foreingate_website_src_app_api_chatbot_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchatbot%2Froute&page=%2Fapi%2Fchatbot%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchatbot%2Froute.ts&appDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/chatbot/route.ts":
/*!**************************************!*\
  !*** ./src/app/api/chatbot/route.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var _lib_chatbot_knowledge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/chatbot-knowledge */ \"(rsc)/./src/lib/chatbot-knowledge.ts\");\n/* harmony import */ var _lib_api_security__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/api-security */ \"(rsc)/./src/lib/api-security.ts\");\n/* harmony import */ var _lib_security__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/security */ \"(rsc)/./src/lib/security.ts\");\n\n\n\nclass SmartChatbot {\n    // Analyze user message and determine intent\n    analyzeIntent(message) {\n        const cleanMessage = message.toLowerCase().trim();\n        const words = cleanMessage.split(/\\s+/);\n        let bestCategory = 'general';\n        let maxScore = 0;\n        let matchedKeywords = [];\n        // Check each category for keyword matches\n        for (const [category, keywords] of Object.entries(this.categories)){\n            let score = 0;\n            const categoryMatches = [];\n            for (const keyword of keywords){\n                if (cleanMessage.includes(keyword)) {\n                    score += keyword.length // Longer keywords get higher scores\n                    ;\n                    categoryMatches.push(keyword);\n                }\n            }\n            if (score > maxScore) {\n                maxScore = score;\n                bestCategory = category;\n                matchedKeywords = categoryMatches;\n            }\n        }\n        const confidence = Math.min(maxScore / cleanMessage.length * 100, 100);\n        return {\n            category: bestCategory,\n            confidence,\n            keywords: matchedKeywords\n        };\n    }\n    // Generate intelligent response based on intent\n    generateResponse(message) {\n        const { category, confidence, keywords } = this.analyzeIntent(message);\n        let response = '';\n        let data = null;\n        let suggestions = [];\n        switch(category){\n            case 'admissions':\n                response = this.handleAdmissionsQuery(message, keywords);\n                data = this.knowledge.admissionProcess;\n                suggestions = [\n                    \"What documents do I need?\",\n                    \"How long does the process take?\",\n                    \"What are the requirements for graduate programs?\"\n                ];\n                break;\n            case 'universities':\n                response = this.handleUniversitiesQuery(message, keywords);\n                data = this.knowledge.universities;\n                suggestions = [\n                    \"Tell me about EMU\",\n                    \"What programs does NEU offer?\",\n                    \"Which university is best for engineering?\"\n                ];\n                break;\n            case 'costs':\n                response = this.handleCostsQuery(message, keywords);\n                data = this.knowledge.costs;\n                suggestions = [\n                    \"What are the living costs?\",\n                    \"Are there scholarships available?\",\n                    \"How much is tuition for medicine?\"\n                ];\n                break;\n            case 'visa':\n                response = this.handleVisaQuery(message, keywords);\n                suggestions = [\n                    \"What documents do I need for visa?\",\n                    \"How long does visa processing take?\",\n                    \"Do you help with visa applications?\"\n                ];\n                break;\n            case 'accommodation':\n                response = this.handleAccommodationQuery(message, keywords);\n                suggestions = [\n                    \"What housing options are available?\",\n                    \"How much does accommodation cost?\",\n                    \"Can you help me find housing?\"\n                ];\n                break;\n            case 'services':\n                response = this.handleServicesQuery(message, keywords);\n                data = this.knowledge.services;\n                suggestions = [\n                    \"What services do you offer?\",\n                    \"How much do your services cost?\",\n                    \"Do you provide ongoing support?\"\n                ];\n                break;\n            case 'location':\n                response = this.handleLocationQuery(message, keywords);\n                suggestions = [\n                    \"Is Northern Cyprus safe?\",\n                    \"What's the weather like?\",\n                    \"How do I get to Northern Cyprus?\"\n                ];\n                break;\n            case 'contact':\n                response = this.handleContactQuery(message, keywords);\n                data = this.knowledge.contact;\n                suggestions = [\n                    \"What are your office hours?\",\n                    \"Can I visit your office?\",\n                    \"Do you speak my language?\"\n                ];\n                break;\n            case 'scholarships':\n                response = this.handleScholarshipsQuery(message, keywords);\n                data = this.knowledge.scholarships;\n                suggestions = [\n                    \"What scholarships are available?\",\n                    \"How do I qualify for merit scholarship?\",\n                    \"What is the early bird discount?\"\n                ];\n                break;\n            case 'language':\n                response = this.handleLanguageQuery(message, keywords);\n                suggestions = [\n                    \"Do I need to speak Turkish?\",\n                    \"What English level is required?\",\n                    \"Are there language courses available?\"\n                ];\n                break;\n            default:\n                response = this.handleGeneralQuery(message, keywords);\n                suggestions = [\n                    \"Tell me about your services\",\n                    \"Which universities do you work with?\",\n                    \"How can you help me study abroad?\"\n                ];\n        }\n        return {\n            response,\n            category,\n            confidence,\n            suggestions,\n            followUp: this.getRandomFollowUp(),\n            data\n        };\n    }\n    handleAdmissionsQuery(message, keywords) {\n        if (keywords.some((k)=>[\n                'requirements',\n                'documents'\n            ].includes(k))) {\n            return `📋 **Admission Requirements:**\n\n**For Undergraduate Programs:**\n• High school diploma or equivalent\n• English proficiency test (IELTS 6.0+ or TOEFL 79+)\n• Passport copy\n• Academic transcripts\n• Personal statement\n\n**For Graduate Programs:**\n• Bachelor's degree\n• English proficiency test\n• Letters of recommendation (2-3)\n• Statement of purpose\n• GRE/GMAT (for some programs)\n\n**Timeline:** The complete process typically takes 2-4 months. We handle everything from document preparation to final enrollment!\n\nWould you like me to help you start your application? 🎓`;\n        }\n        if (keywords.some((k)=>[\n                'deadline',\n                'when'\n            ].includes(k))) {\n            return `📅 **Application Deadlines:**\n\nGood news! We accept applications **year-round** for most programs. However, I recommend applying **3-4 months before** your intended start date for:\n\n• Better preparation time\n• Higher scholarship chances\n• Smoother visa processing\n• Better accommodation options\n\n**Intake Periods:**\n• Fall Semester: September\n• Spring Semester: February\n• Summer Programs: June\n\nReady to start your application? I can connect you with our admissions team! 🚀`;\n        }\n        return `🎓 **University Admissions Made Easy!**\n\nAt Foreingate, we make university admissions simple and stress-free. Our comprehensive service includes:\n\n✅ **Free consultation** and university matching\n✅ **Complete application** preparation and submission\n✅ **Document verification** and translation\n✅ **Interview coaching** (when required)\n✅ **Admission guarantee** or full refund\n\nWe've helped over 5,000 students secure admissions to top universities in Northern Cyprus. Our success rate is 98%!\n\nWhat specific aspect of admissions would you like to know more about?`;\n    }\n    handleUniversitiesQuery(message, keywords) {\n        const universities = this.knowledge.universities;\n        if (message.toLowerCase().includes('emu') || message.toLowerCase().includes('eastern mediterranean')) {\n            const emu = universities.find((u)=>u.name.includes('Eastern Mediterranean'));\n            return `🏛️ **Eastern Mediterranean University (EMU)**\n\n📍 **Location:** Famagusta, Northern Cyprus\n📅 **Established:** ${emu.established}\n👥 **Students:** ${emu.students.toLocaleString()} (${emu.international.toLocaleString()} international)\n\n**🎓 Popular Programs:**\n${emu.programs.map((p)=>`• ${p}`).join('\\n')}\n\n**💰 Tuition:** $${emu.tuition.min.toLocaleString()} - $${emu.tuition.max.toLocaleString()} per year\n**🌍 Language:** ${emu.language}\n**✅ Accreditation:** ${emu.accreditation}\n\nEMU is known for its strong engineering and business programs, beautiful campus, and vibrant student life!\n\nWould you like more details about specific programs? 🎯`;\n        }\n        if (message.toLowerCase().includes('neu') || message.toLowerCase().includes('near east')) {\n            const neu = universities.find((u)=>u.name.includes('Near East'));\n            return `🏥 **Near East University (NEU)**\n\n📍 **Location:** Nicosia, Northern Cyprus\n📅 **Established:** ${neu.established}\n👥 **Students:** ${neu.students.toLocaleString()} (${neu.international.toLocaleString()} international)\n\n**🎓 Popular Programs:**\n${neu.programs.map((p)=>`• ${p}`).join('\\n')}\n\n**💰 Tuition:** $${neu.tuition.min.toLocaleString()} - $${neu.tuition.max.toLocaleString()} per year\n**🌍 Language:** ${neu.language}\n**✅ Accreditation:** ${neu.accreditation}\n\nNEU is especially renowned for its **Medicine and Dentistry** programs, with WHO recognition and state-of-the-art facilities!\n\nInterested in medical programs? Let me know! 🩺`;\n        }\n        return `🏛️ **Our Partner Universities in Northern Cyprus:**\n\nWe work with the **top 3 universities** that welcome international students:\n\n**1. Eastern Mediterranean University (EMU)** 🌟\n   • 20,000+ students • Strong in Engineering & Business\n   \n**2. Near East University (NEU)** 🏥\n   • 25,000+ students • Excellent Medical Programs\n   \n**3. Cyprus International University (CIU)** 🎨\n   • 15,000+ students • Great for Business & Arts\n\n**All universities offer:**\n✅ English-taught programs\n✅ International accreditation\n✅ Modern facilities\n✅ Vibrant campus life\n✅ Affordable tuition\n\nWhich university interests you most? I can provide detailed information! 🎓`;\n    }\n    handleCostsQuery(message, keywords) {\n        const costs = this.knowledge.costs;\n        if (keywords.some((k)=>[\n                'scholarship',\n                'financial'\n            ].includes(k))) {\n            return `💰 **Scholarships & Financial Aid:**\n\n**Merit Scholarship** 🏆\n• 25-50% tuition reduction\n• For students with GPA 3.5+ or equivalent\n• Automatic consideration with application\n\n**Early Bird Discount** ⏰\n• 10-15% tuition reduction\n• Apply before March 31st\n• Available for all programs\n\n**Sibling Discount** 👨‍👩‍👧‍👦\n• 10% tuition reduction\n• For families with multiple students\n• Applies to second sibling onwards\n\n**Payment Plans** 💳\n• Flexible installment options\n• No interest charges\n• Customized to your budget\n\n**Total Savings Possible:** Up to 65% off tuition fees!\n\nReady to apply for scholarships? Let me help you! 🎯`;\n        }\n        return `💰 **Study Costs in Northern Cyprus:**\n\n**📚 Tuition Fees (per year):**\n• Undergraduate: $${costs.tuition.undergraduate.min.toLocaleString()} - $${costs.tuition.undergraduate.max.toLocaleString()}\n• Graduate: $${costs.tuition.graduate.min.toLocaleString()} - $${costs.tuition.graduate.max.toLocaleString()}\n• Medicine: $${costs.tuition.medicine.min.toLocaleString()} - $${costs.tuition.medicine.max.toLocaleString()}\n\n**🏠 Living Costs (per month):**\n• Accommodation: $${costs.living.accommodation.min} - $${costs.living.accommodation.max}\n• Food: $${costs.living.food.min} - $${costs.living.food.max}\n• Transportation: $${costs.living.transportation.min} - $${costs.living.transportation.max}\n• **Total Living:** $${costs.living.total.min} - $${costs.living.total.max}\n\n**💡 Why Northern Cyprus is Affordable:**\n✅ 50-70% cheaper than UK/US\n✅ High quality education\n✅ Low cost of living\n✅ Scholarship opportunities\n\nWant to know about scholarships to reduce costs even more? 🎓`;\n    }\n    handleVisaQuery(message, keywords) {\n        return `🛂 **Visa Support Services:**\n\n**We Handle Everything!** ✅\n• Complete visa application preparation\n• Document collection and verification\n• Embassy appointment scheduling\n• Application tracking and follow-up\n\n**📋 Typical Documents Needed:**\n• Valid passport (6+ months validity)\n• University acceptance letter\n• Financial proof (bank statements)\n• Health insurance\n• Accommodation proof\n• Passport photos\n\n**⏱️ Processing Time:**\n• Most countries: 2-4 weeks\n• Some countries: 4-8 weeks\n• We expedite when possible\n\n**💰 Our Visa Service:**\n• Starting from $200\n• Success rate: 95%+\n• Full support until visa approval\n\n**🌍 Visa-Free Countries:**\nSome nationalities don't need a visa for Northern Cyprus!\n\nWhich country are you from? I can give you specific visa information! 🌟`;\n    }\n    handleAccommodationQuery(message, keywords) {\n        return `🏠 **Student Accommodation Options:**\n\n**🏫 University Dormitories:**\n• $200-400/month\n• On-campus convenience\n• Meal plans available\n• Social environment\n\n**🏡 Private Apartments:**\n• $300-600/month\n• More privacy and space\n• Kitchen facilities\n• Shared or single options\n\n**👨‍👩‍👧‍👦 Homestay Programs:**\n• $250-450/month\n• Live with local families\n• Cultural immersion\n• Meals included\n\n**🚗 Additional Services:**\n✅ Airport pickup arrangement\n✅ Accommodation booking assistance\n✅ Contract negotiation help\n✅ Ongoing support\n\n**📍 Popular Areas:**\n• Near campus locations\n• City center options\n• Quiet residential areas\n\nNeed help finding the perfect accommodation? We'll match you with the best option for your budget and preferences! 🏡`;\n    }\n    handleServicesQuery(message, keywords) {\n        return `🎯 **Our Complete Services:**\n\n**🎓 University Admissions**\n• Free consultation & university matching\n• Application preparation & submission\n• Document verification & translation\n• Interview coaching\n\n**🛂 Visa Support**\n• Complete visa application assistance\n• Document preparation\n• Embassy appointments\n• Application tracking\n\n**🏠 Accommodation Services**\n• Housing search & booking\n• Contract assistance\n• Airport pickup\n• Settlement support\n\n**📚 Academic Support**\n• Tutoring services\n• Study groups\n• Academic counseling\n• Career guidance\n\n**💰 Pricing:**\n• Initial consultation: **FREE**\n• University application: Competitive rates\n• Visa support: From $200\n• Accommodation: Commission-based\n\n**🌟 Why Choose Foreingate:**\n✅ 98% success rate\n✅ 5,000+ students helped\n✅ End-to-end support\n✅ Multilingual team\n\nReady to start your journey? Let's talk! 🚀`;\n    }\n    handleLocationQuery(message, keywords) {\n        return `🌍 **About Northern Cyprus:**\n\n**📍 Location:**\n• Eastern Mediterranean island\n• Between Turkey and Middle East\n• Beautiful coastline and mountains\n• Rich history and culture\n\n**🌤️ Climate:**\n• Mediterranean climate\n• Warm summers, mild winters\n• 300+ sunny days per year\n• Perfect for outdoor activities\n\n**🛡️ Safety:**\n• Very safe for international students\n• Low crime rates\n• Welcoming local community\n• English widely spoken\n\n**✈️ Getting There:**\n• Fly via Turkey (Istanbul/Ankara)\n• Direct flights from many countries\n• Airport pickup service available\n• Easy visa process\n\n**🎭 Student Life:**\n• Vibrant international community\n• Cultural festivals and events\n• Beautiful beaches and nature\n• Affordable entertainment\n\n**💡 Why Students Love It:**\n✅ Safe and friendly environment\n✅ Affordable living costs\n✅ Rich cultural experience\n✅ Gateway to Europe and Asia\n\nCurious about life in Northern Cyprus? I can share more details! 🏖️`;\n    }\n    handleContactQuery(message, keywords) {\n        const contact = this.knowledge.contact;\n        return `📞 **Get in Touch with Foreingate:**\n\n**🏢 Office Contact:**\n• 📧 Email: ${contact.email}\n• 📱 Phone: ${contact.phone}\n• 💬 WhatsApp: ${contact.whatsapp}\n• 📍 Address: ${contact.address}\n\n**⏰ Working Hours:**\n${contact.workingHours}\n\n**🌍 Languages We Speak:**\n${contact.languages.join(' • ')}\n\n**💬 Instant Support:**\n• Live chat on our website\n• WhatsApp for quick questions\n• Email for detailed inquiries\n• Phone for urgent matters\n\n**🎯 Best Ways to Reach Us:**\n• **Quick questions:** WhatsApp\n• **Detailed consultation:** Email or phone\n• **Immediate help:** Live chat\n• **Document submission:** Email\n\n**📅 Free Consultation:**\nBook a free 30-minute consultation to discuss your education plans!\n\nHow would you prefer to get in touch? 🤝`;\n    }\n    handleGeneralQuery(message, keywords) {\n        return `🌟 **Welcome to Foreingate Group!**\n\nWe're your **trusted partner** for higher education in Northern Cyprus. Since 2020, we've helped over **5,000 international students** achieve their academic dreams!\n\n**🎯 What We Do:**\n✅ University admissions (98% success rate)\n✅ Visa support and documentation\n✅ Accommodation arrangements\n✅ Ongoing academic support\n\n**🏛️ Partner Universities:**\n• Eastern Mediterranean University (EMU)\n• Near East University (NEU)  \n• Cyprus International University (CIU)\n\n**💰 Why Choose Northern Cyprus:**\n• High-quality English education\n• 50-70% cheaper than UK/US\n• Safe and welcoming environment\n• EU-recognized degrees\n\n**🚀 Ready to Start?**\n• Free consultation available\n• Personalized university matching\n• Complete application support\n• End-to-end service\n\nWhat aspect of studying abroad interests you most? I'm here to help with any questions! 🎓`;\n    }\n    handleScholarshipsQuery(message, keywords) {\n        return `💰 **Scholarships & Financial Aid:**\n\n**Merit Scholarship** 🏆\n• 25-50% tuition reduction\n• For students with GPA 3.5+ or equivalent\n• Automatic consideration with application\n\n**Early Bird Discount** ⏰\n• 10-15% tuition reduction\n• Apply before March 31st\n• Available for all programs\n\n**Sibling Discount** 👨‍👩‍👧‍👦\n• 10% tuition reduction\n• For families with multiple students\n• Applies to second sibling onwards\n\n**Payment Plans** 💳\n• Flexible installment options\n• No interest charges\n• Customized to your budget\n\n**Total Savings Possible:** Up to 65% off tuition fees!\n\n**🎯 How to Apply:**\n✅ Submit your application early\n✅ Maintain high academic performance\n✅ Provide all required documents\n✅ Meet application deadlines\n\nReady to apply for scholarships? Let me help you! 🎯`;\n    }\n    handleLanguageQuery(message, keywords) {\n        return `🌍 **Language Requirements & Support:**\n\n**📚 Program Languages:**\n• **English:** All programs taught in English\n• **Turkish:** Optional, helpful for daily life\n• **No Turkish Required:** For academic success\n\n**📝 English Proficiency Requirements:**\n• **IELTS:** 6.0+ overall (5.5+ each band)\n• **TOEFL:** 79+ iBT (17+ each section)\n• **PTE:** 58+ overall\n• **Alternative:** University English test\n\n**🎓 Language Support Services:**\n✅ **English Preparatory Program** - If needed\n✅ **Academic English Courses** - Writing, presentation skills\n✅ **Turkish Language Classes** - Optional cultural integration\n✅ **Language Exchange Programs** - Practice with local students\n\n**💡 Language Tips:**\n• Most international students succeed with basic English\n• Campus environment is very English-friendly\n• Local community welcomes international students\n• Many locals speak English\n\n**🌟 Don't Worry About Language!**\nOur universities have excellent support systems for international students. You'll improve your English naturally in the immersive environment!\n\nNeed help with English proficiency tests? We can guide you! 📖`;\n    }\n    getRandomFollowUp() {\n        return _lib_chatbot_knowledge__WEBPACK_IMPORTED_MODULE_0__.responseTemplates.followUp.sort(()=>0.5 - Math.random()).slice(0, 2);\n    }\n    constructor(){\n        this.knowledge = _lib_chatbot_knowledge__WEBPACK_IMPORTED_MODULE_0__.websiteKnowledge;\n        this.categories = _lib_chatbot_knowledge__WEBPACK_IMPORTED_MODULE_0__.questionCategories;\n    }\n}\nasync function handleChatbotRequest(request) {\n    try {\n        const body = await request.json();\n        const { message, sessionId } = body;\n        if (!message || message.trim().length === 0) {\n            return (0,_lib_api_security__WEBPACK_IMPORTED_MODULE_1__.secureResponse)({\n                error: 'Message is required'\n            }, 400);\n        }\n        // Sanitize input\n        const cleanMessage = (0,_lib_security__WEBPACK_IMPORTED_MODULE_2__.sanitizeInput)(message.trim());\n        if (cleanMessage.length > 500) {\n            return (0,_lib_api_security__WEBPACK_IMPORTED_MODULE_1__.secureResponse)({\n                error: 'Message too long. Please keep it under 500 characters.'\n            }, 400);\n        }\n        // Initialize chatbot\n        const chatbot = new SmartChatbot();\n        // Generate response\n        const response = chatbot.generateResponse(cleanMessage);\n        return (0,_lib_api_security__WEBPACK_IMPORTED_MODULE_1__.secureResponse)({\n            success: true,\n            ...response,\n            timestamp: new Date().toISOString(),\n            sessionId: sessionId || 'anonymous'\n        });\n    } catch (error) {\n        console.error('Chatbot error:', error);\n        return (0,_lib_api_security__WEBPACK_IMPORTED_MODULE_1__.secureResponse)({\n            error: 'Sorry, I encountered an error. Please try again or contact our support team.',\n            fallback: true\n        }, 500);\n    }\n}\n// Apply security middleware\nconst POST = (0,_lib_api_security__WEBPACK_IMPORTED_MODULE_1__.withApiSecurity)(handleChatbotRequest);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/chatbot/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/api-security.ts":
/*!*********************************!*\
  !*** ./src/lib/api-security.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   sanitizeResponse: () => (/* binding */ sanitizeResponse),\n/* harmony export */   secureResponse: () => (/* binding */ secureResponse),\n/* harmony export */   validateInput: () => (/* binding */ validateInput),\n/* harmony export */   withAdminSecurity: () => (/* binding */ withAdminSecurity),\n/* harmony export */   withApiSecurity: () => (/* binding */ withApiSecurity),\n/* harmony export */   withCORS: () => (/* binding */ withCORS)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _security__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./security */ \"(rsc)/./src/lib/security.ts\");\n\n\n// Rate limiter instances\nconst globalRateLimiter = new _security__WEBPACK_IMPORTED_MODULE_1__.RateLimiter(100, 60000) // 100 requests per minute\n;\nconst apiRateLimiter = new _security__WEBPACK_IMPORTED_MODULE_1__.RateLimiter(50, 60000) // 50 API requests per minute\n;\nconst authRateLimiter = new _security__WEBPACK_IMPORTED_MODULE_1__.RateLimiter(5, 300000) // 5 auth attempts per 5 minutes\n;\n/**\n * API Security Middleware\n */ function withApiSecurity(handler) {\n    return async (request)=>{\n        const ip = getClientIP(request);\n        const userAgent = request.headers.get('user-agent') || 'unknown';\n        const path = request.nextUrl.pathname;\n        try {\n            // 1. Rate Limiting\n            if (!apiRateLimiter.isAllowed(ip)) {\n                _security__WEBPACK_IMPORTED_MODULE_1__.SecurityLogger.logSuspiciousActivity(ip, `Rate limit exceeded for ${path}`);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Too many requests. Please try again later.'\n                }, {\n                    status: 429,\n                    headers: {\n                        'Retry-After': '60',\n                        'X-RateLimit-Limit': '50',\n                        'X-RateLimit-Remaining': '0',\n                        'X-RateLimit-Reset': String(Date.now() + 60000)\n                    }\n                });\n            }\n            // 2. Method validation\n            const allowedMethods = [\n                'GET',\n                'POST',\n                'PUT',\n                'DELETE',\n                'PATCH'\n            ];\n            if (!allowedMethods.includes(request.method)) {\n                _security__WEBPACK_IMPORTED_MODULE_1__.SecurityLogger.logSuspiciousActivity(ip, `Invalid method ${request.method} for ${path}`);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Method not allowed'\n                }, {\n                    status: 405\n                });\n            }\n            // 3. Content-Type validation for POST/PUT requests\n            if ([\n                'POST',\n                'PUT',\n                'PATCH'\n            ].includes(request.method)) {\n                const contentType = request.headers.get('content-type');\n                if (!contentType || !contentType.includes('application/json')) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        error: 'Content-Type must be application/json'\n                    }, {\n                        status: 400\n                    });\n                }\n            }\n            // 4. Request size validation\n            const contentLength = request.headers.get('content-length');\n            if (contentLength && parseInt(contentLength) > 1024 * 1024) {\n                _security__WEBPACK_IMPORTED_MODULE_1__.SecurityLogger.logSuspiciousActivity(ip, `Large request size: ${contentLength} bytes`);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Request too large'\n                }, {\n                    status: 413\n                });\n            }\n            // 5. Suspicious headers check\n            const suspiciousHeaders = [\n                'x-forwarded-host',\n                'x-real-ip'\n            ];\n            for (const header of suspiciousHeaders){\n                if (request.headers.get(header)) {\n                    _security__WEBPACK_IMPORTED_MODULE_1__.SecurityLogger.logSuspiciousActivity(ip, `Suspicious header: ${header}`);\n                }\n            }\n            // 6. Log API access\n            _security__WEBPACK_IMPORTED_MODULE_1__.SecurityLogger.logDataAccess(path, ip);\n            // Call the actual handler\n            const response = await handler(request);\n            // Add security headers to response\n            if (response instanceof next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse) {\n                response.headers.set('X-Content-Type-Options', 'nosniff');\n                response.headers.set('X-Frame-Options', 'DENY');\n                response.headers.set('X-XSS-Protection', '1; mode=block');\n                response.headers.set('Cache-Control', 'no-store, max-age=0');\n                response.headers.set('X-RateLimit-Remaining', String(apiRateLimiter.getRemainingRequests(ip)));\n            }\n            return response;\n        } catch (error) {\n            _security__WEBPACK_IMPORTED_MODULE_1__.SecurityLogger.log('API_ERROR', {\n                path,\n                ip,\n                error: String(error)\n            }, 'error');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Internal server error'\n            }, {\n                status: 500\n            });\n        }\n    };\n}\n/**\n * Admin API Security Middleware\n */ function withAdminSecurity(handler) {\n    return withApiSecurity(async (request)=>{\n        const ip = getClientIP(request);\n        // Additional admin-specific security checks\n        // 1. Admin rate limiting (stricter)\n        if (!authRateLimiter.isAllowed(ip)) {\n            _security__WEBPACK_IMPORTED_MODULE_1__.SecurityLogger.logSuspiciousActivity(ip, 'Admin rate limit exceeded');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Too many admin requests. Please try again later.'\n            }, {\n                status: 429\n            });\n        }\n        // 2. Admin authentication check (basic implementation)\n        const authHeader = request.headers.get('authorization');\n        if (!authHeader || !authHeader.startsWith('Bearer ')) {\n            _security__WEBPACK_IMPORTED_MODULE_1__.SecurityLogger.logFailedLogin(ip, request.headers.get('user-agent') || 'unknown');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        // 3. Log admin access\n        _security__WEBPACK_IMPORTED_MODULE_1__.SecurityLogger.log('ADMIN_ACCESS', {\n            path: request.nextUrl.pathname,\n            ip,\n            method: request.method\n        }, 'info');\n        return handler(request);\n    });\n}\n/**\n * Input Validation Middleware\n */ function validateInput(schema) {\n    return (handler)=>{\n        return async (request)=>{\n            // Skip validation for GET requests\n            if (![\n                'POST',\n                'PUT',\n                'PATCH'\n            ].includes(request.method)) {\n                return handler(request);\n            }\n            try {\n                // Clone the request to avoid body consumption issues\n                const clonedRequest = request.clone();\n                const body = await clonedRequest.json();\n                // Validate each field according to schema\n                for (const [field, validator] of Object.entries(schema)){\n                    const value = body[field];\n                    if (typeof validator === 'function' && !validator(value)) {\n                        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                            error: `Invalid ${field}`\n                        }, {\n                            status: 400\n                        });\n                    }\n                }\n                // Add validated body to request for later use\n                ;\n                request.validatedBody = body;\n            } catch (error) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Invalid JSON'\n                }, {\n                    status: 400\n                });\n            }\n            return handler(request);\n        };\n    };\n}\n/**\n * CORS Middleware\n */ function withCORS(handler, options = {}) {\n    return async (request)=>{\n        const origin = request.headers.get('origin');\n        const allowedOrigins = options.origin || [\n            'https://localhost:3443',\n            'https://foreingate.com',\n            'https://www.foreingate.com'\n        ];\n        // Handle preflight requests\n        if (request.method === 'OPTIONS') {\n            const response = new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(null, {\n                status: 200\n            });\n            if (origin && allowedOrigins.includes(origin)) {\n                response.headers.set('Access-Control-Allow-Origin', origin);\n            }\n            response.headers.set('Access-Control-Allow-Methods', options.methods?.join(', ') || 'GET, POST, PUT, DELETE');\n            response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');\n            if (options.credentials) {\n                response.headers.set('Access-Control-Allow-Credentials', 'true');\n            }\n            return response;\n        }\n        const response = await handler(request);\n        // Add CORS headers to actual response\n        if (response instanceof next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse) {\n            if (origin && allowedOrigins.includes(origin)) {\n                response.headers.set('Access-Control-Allow-Origin', origin);\n            }\n            if (options.credentials) {\n                response.headers.set('Access-Control-Allow-Credentials', 'true');\n            }\n        }\n        return response;\n    };\n}\n/**\n * Get client IP address\n */ function getClientIP(request) {\n    const forwarded = request.headers.get('x-forwarded-for');\n    const realIP = request.headers.get('x-real-ip');\n    const remoteAddr = request.ip;\n    if (forwarded) {\n        return forwarded.split(',')[0].trim();\n    }\n    return realIP || remoteAddr || 'unknown';\n}\n/**\n * Sanitize API response\n */ function sanitizeResponse(data) {\n    if (typeof data === 'string') {\n        return data.replace(/<script\\b[^<]*(?:(?!<\\/script>)<[^<]*)*<\\/script>/gi, '');\n    }\n    if (Array.isArray(data)) {\n        return data.map(sanitizeResponse);\n    }\n    if (typeof data === 'object' && data !== null) {\n        const sanitized = {};\n        for (const [key, value] of Object.entries(data)){\n            sanitized[key] = sanitizeResponse(value);\n        }\n        return sanitized;\n    }\n    return data;\n}\n/**\n * API Response wrapper with security\n */ function secureResponse(data, status = 200) {\n    const sanitizedData = sanitizeResponse(data);\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(sanitizedData, {\n        status,\n        headers: {\n            'X-Content-Type-Options': 'nosniff',\n            'X-Frame-Options': 'DENY',\n            'Cache-Control': 'no-store, max-age=0',\n            'X-XSS-Protection': '1; mode=block'\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/api-security.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/chatbot-knowledge.ts":
/*!**************************************!*\
  !*** ./src/lib/chatbot-knowledge.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   questionCategories: () => (/* binding */ questionCategories),\n/* harmony export */   responseTemplates: () => (/* binding */ responseTemplates),\n/* harmony export */   websiteKnowledge: () => (/* binding */ websiteKnowledge)\n/* harmony export */ });\n// Comprehensive knowledge base for the Foreingate Group chatbot\nconst websiteKnowledge = {\n    company: {\n        name: \"Foreingate Group\",\n        description: \"Leading educational consultancy specializing in university admissions and student services in Northern Cyprus\",\n        mission: \"To bridge the gap between international students and quality higher education opportunities in Northern Cyprus\",\n        vision: \"To be the most trusted partner for students seeking world-class education abroad\",\n        established: \"2020\",\n        headquarters: \"Nicosia, Northern Cyprus\",\n        languages: [\n            \"English\",\n            \"Turkish\",\n            \"Arabic\"\n        ],\n        accreditations: [\n            \"ICEF Certified\",\n            \"British Council Partner\"\n        ]\n    },\n    services: [\n        {\n            name: \"University Admissions\",\n            description: \"Complete application assistance for universities in Northern Cyprus\",\n            features: [\n                \"Application processing\",\n                \"Document preparation\",\n                \"Interview coaching\",\n                \"Admission guarantee\"\n            ],\n            pricing: \"Free consultation, competitive service fees\"\n        },\n        {\n            name: \"Visa Support\",\n            description: \"Comprehensive visa application and documentation services\",\n            features: [\n                \"Visa application\",\n                \"Document translation\",\n                \"Embassy appointments\",\n                \"Visa tracking\"\n            ],\n            pricing: \"Starting from $200\"\n        },\n        {\n            name: \"Accommodation Services\",\n            description: \"Student housing and accommodation arrangements\",\n            features: [\n                \"Dormitory booking\",\n                \"Private housing\",\n                \"Homestay options\",\n                \"Airport pickup\"\n            ],\n            pricing: \"Varies by accommodation type\"\n        },\n        {\n            name: \"Academic Support\",\n            description: \"Ongoing academic assistance and mentoring\",\n            features: [\n                \"Tutoring services\",\n                \"Study groups\",\n                \"Academic counseling\",\n                \"Career guidance\"\n            ],\n            pricing: \"Monthly packages available\"\n        }\n    ],\n    universities: [\n        {\n            name: \"Eastern Mediterranean University (EMU)\",\n            location: \"Famagusta, Northern Cyprus\",\n            established: 1979,\n            students: 20000,\n            international: 15000,\n            programs: [\n                \"Engineering\",\n                \"Medicine\",\n                \"Business\",\n                \"Arts & Sciences\",\n                \"Law\"\n            ],\n            tuition: {\n                min: 3500,\n                max: 8000,\n                currency: \"USD\"\n            },\n            language: \"English\",\n            accreditation: \"YÖK (Turkish Higher Education Council)\"\n        },\n        {\n            name: \"Near East University (NEU)\",\n            location: \"Nicosia, Northern Cyprus\",\n            established: 1988,\n            students: 25000,\n            international: 18000,\n            programs: [\n                \"Medicine\",\n                \"Dentistry\",\n                \"Engineering\",\n                \"Pharmacy\",\n                \"Architecture\"\n            ],\n            tuition: {\n                min: 4000,\n                max: 12000,\n                currency: \"USD\"\n            },\n            language: \"English\",\n            accreditation: \"YÖK, WHO recognized\"\n        },\n        {\n            name: \"Cyprus International University (CIU)\",\n            location: \"Nicosia, Northern Cyprus\",\n            established: 1997,\n            students: 15000,\n            international: 12000,\n            programs: [\n                \"Business\",\n                \"Engineering\",\n                \"Communication\",\n                \"Education\",\n                \"Fine Arts\"\n            ],\n            tuition: {\n                min: 3000,\n                max: 7000,\n                currency: \"USD\"\n            },\n            language: \"English\",\n            accreditation: \"YÖK certified\"\n        }\n    ],\n    admissionProcess: {\n        steps: [\n            \"Initial consultation and assessment\",\n            \"University and program selection\",\n            \"Document preparation and verification\",\n            \"Application submission\",\n            \"Interview preparation (if required)\",\n            \"Admission decision and acceptance\",\n            \"Visa application process\",\n            \"Pre-departure orientation\",\n            \"Arrival and settlement support\"\n        ],\n        timeline: \"2-4 months average\",\n        requirements: {\n            undergraduate: [\n                \"High school diploma\",\n                \"English proficiency test\",\n                \"Passport copy\",\n                \"Academic transcripts\"\n            ],\n            graduate: [\n                \"Bachelor's degree\",\n                \"GRE/GMAT (some programs)\",\n                \"English proficiency\",\n                \"Letters of recommendation\",\n                \"Statement of purpose\"\n            ]\n        }\n    },\n    costs: {\n        tuition: {\n            undergraduate: {\n                min: 3000,\n                max: 8000,\n                currency: \"USD\",\n                period: \"per year\"\n            },\n            graduate: {\n                min: 4000,\n                max: 12000,\n                currency: \"USD\",\n                period: \"per year\"\n            },\n            medicine: {\n                min: 8000,\n                max: 15000,\n                currency: \"USD\",\n                period: \"per year\"\n            }\n        },\n        living: {\n            accommodation: {\n                min: 200,\n                max: 600,\n                currency: \"USD\",\n                period: \"per month\"\n            },\n            food: {\n                min: 150,\n                max: 300,\n                currency: \"USD\",\n                period: \"per month\"\n            },\n            transportation: {\n                min: 50,\n                max: 100,\n                currency: \"USD\",\n                period: \"per month\"\n            },\n            total: {\n                min: 400,\n                max: 1000,\n                currency: \"USD\",\n                period: \"per month\"\n            }\n        }\n    },\n    scholarships: [\n        {\n            name: \"Merit Scholarship\",\n            amount: \"25-50% tuition reduction\",\n            criteria: \"High academic performance\",\n            eligibility: \"GPA 3.5+ or equivalent\"\n        },\n        {\n            name: \"Early Bird Discount\",\n            amount: \"10-15% tuition reduction\",\n            criteria: \"Early application submission\",\n            deadline: \"Before March 31st\"\n        },\n        {\n            name: \"Sibling Discount\",\n            amount: \"10% tuition reduction\",\n            criteria: \"Multiple family members enrolled\",\n            eligibility: \"Second sibling onwards\"\n        }\n    ],\n    contact: {\n        email: \"<EMAIL>\",\n        phone: \"+90 ************\",\n        whatsapp: \"+90 ************\",\n        address: \"Nicosia, Northern Cyprus\",\n        workingHours: \"Monday-Friday: 9:00-18:00, Saturday: 9:00-14:00\",\n        languages: [\n            \"English\",\n            \"Turkish\",\n            \"Arabic\",\n            \"French\"\n        ]\n    },\n    website: {\n        url: \"https://foreingate.com\",\n        features: [\n            \"University search and comparison\",\n            \"Online application system\",\n            \"Document upload portal\",\n            \"Application tracking\",\n            \"Live chat support\",\n            \"Newsletter subscription\",\n            \"Blog with educational content\",\n            \"Testimonials from students\"\n        ],\n        pages: [\n            {\n                path: \"/\",\n                name: \"Home\",\n                description: \"Main landing page with overview\"\n            },\n            {\n                path: \"/about\",\n                name: \"About Us\",\n                description: \"Company information and team\"\n            },\n            {\n                path: \"/services\",\n                name: \"Services\",\n                description: \"Detailed service offerings\"\n            },\n            {\n                path: \"/universities\",\n                name: \"Universities\",\n                description: \"Partner universities and programs\"\n            },\n            {\n                path: \"/apply\",\n                name: \"Apply Now\",\n                description: \"Online application form\"\n            },\n            {\n                path: \"/blog\",\n                name: \"Blog\",\n                description: \"Educational articles and news\"\n            },\n            {\n                path: \"/contact\",\n                name: \"Contact\",\n                description: \"Contact information and form\"\n            },\n            {\n                path: \"/admin\",\n                name: \"Admin Panel\",\n                description: \"Administrative dashboard\"\n            }\n        ]\n    },\n    faq: [\n        {\n            question: \"What is the application deadline?\",\n            answer: \"Applications are accepted year-round, but we recommend applying 3-4 months before your intended start date for better preparation time.\"\n        },\n        {\n            question: \"Do I need to know Turkish?\",\n            answer: \"No, most programs are taught in English. However, basic Turkish knowledge can be helpful for daily life.\"\n        },\n        {\n            question: \"Is Northern Cyprus safe for international students?\",\n            answer: \"Yes, Northern Cyprus is very safe with low crime rates and a welcoming community for international students.\"\n        },\n        {\n            question: \"What are the visa requirements?\",\n            answer: \"Visa requirements vary by nationality. We provide complete visa support including document preparation and application assistance.\"\n        },\n        {\n            question: \"Can I work while studying?\",\n            answer: \"Yes, students can work part-time (up to 20 hours per week) with proper permits.\"\n        }\n    ],\n    testimonials: [\n        {\n            name: \"Ahmed Hassan\",\n            country: \"Egypt\",\n            program: \"Computer Engineering\",\n            university: \"Eastern Mediterranean University\",\n            year: 2023,\n            rating: 5,\n            comment: \"Foreingate made my dream of studying abroad come true. Their support was exceptional throughout the entire process.\"\n        },\n        {\n            name: \"Maria Rodriguez\",\n            country: \"Colombia\",\n            program: \"Medicine\",\n            university: \"Near East University\",\n            year: 2022,\n            rating: 5,\n            comment: \"Professional service and genuine care. They helped me secure admission and scholarship. Highly recommended!\"\n        }\n    ],\n    technicalInfo: {\n        security: {\n            ssl: \"Auto-signed SSL certificates implemented\",\n            headers: \"15+ security headers active\",\n            encryption: \"AES-256-GCM data encryption\",\n            rateLimit: \"100 requests per minute\",\n            features: [\n                \"HTTPS enforcement\",\n                \"XSS protection\",\n                \"CSRF prevention\",\n                \"Input sanitization\"\n            ]\n        },\n        features: {\n            responsive: \"Mobile-first responsive design\",\n            performance: \"Optimized loading times\",\n            accessibility: \"WCAG 2.1 compliant\",\n            seo: \"Search engine optimized\",\n            analytics: \"Google Analytics integration ready\"\n        },\n        admin: {\n            dashboard: \"Real-time statistics and management\",\n            applications: \"Complete application lifecycle management\",\n            newsletter: \"Email campaign system\",\n            contacts: \"Customer inquiry management\",\n            security: \"Enhanced admin protection\"\n        }\n    }\n};\n// Common questions and their categories with enhanced keywords\nconst questionCategories = {\n    admissions: [\n        \"admission\",\n        \"apply\",\n        \"application\",\n        \"requirements\",\n        \"documents\",\n        \"deadline\",\n        \"enroll\",\n        \"register\",\n        \"submit\",\n        \"process\",\n        \"steps\",\n        \"how to apply\",\n        \"admission process\",\n        \"requirements\",\n        \"eligibility\"\n    ],\n    universities: [\n        \"university\",\n        \"universities\",\n        \"programs\",\n        \"courses\",\n        \"degrees\",\n        \"study\",\n        \"emu\",\n        \"neu\",\n        \"ciu\",\n        \"eastern mediterranean\",\n        \"near east\",\n        \"cyprus international\",\n        \"engineering\",\n        \"medicine\",\n        \"business\",\n        \"programs\",\n        \"majors\",\n        \"faculties\"\n    ],\n    costs: [\n        \"cost\",\n        \"fees\",\n        \"tuition\",\n        \"price\",\n        \"expensive\",\n        \"cheap\",\n        \"scholarship\",\n        \"financial\",\n        \"money\",\n        \"budget\",\n        \"afford\",\n        \"payment\",\n        \"installment\",\n        \"discount\",\n        \"aid\",\n        \"funding\"\n    ],\n    visa: [\n        \"visa\",\n        \"permit\",\n        \"immigration\",\n        \"documents\",\n        \"embassy\",\n        \"passport\",\n        \"student visa\",\n        \"residence permit\",\n        \"visa application\",\n        \"visa process\",\n        \"visa requirements\"\n    ],\n    accommodation: [\n        \"housing\",\n        \"accommodation\",\n        \"dormitory\",\n        \"residence\",\n        \"living\",\n        \"room\",\n        \"apartment\",\n        \"dorm\",\n        \"homestay\",\n        \"where to live\",\n        \"student housing\"\n    ],\n    services: [\n        \"services\",\n        \"help\",\n        \"support\",\n        \"assistance\",\n        \"consultation\",\n        \"what do you do\",\n        \"how can you help\",\n        \"what services\",\n        \"support services\"\n    ],\n    location: [\n        \"cyprus\",\n        \"northern cyprus\",\n        \"nicosia\",\n        \"famagusta\",\n        \"location\",\n        \"where\",\n        \"country\",\n        \"island\",\n        \"turkey\",\n        \"mediterranean\",\n        \"safe\",\n        \"weather\",\n        \"climate\"\n    ],\n    contact: [\n        \"contact\",\n        \"phone\",\n        \"email\",\n        \"address\",\n        \"office\",\n        \"hours\",\n        \"reach\",\n        \"call\",\n        \"write\",\n        \"visit\",\n        \"working hours\",\n        \"office hours\"\n    ],\n    scholarships: [\n        \"scholarship\",\n        \"scholarships\",\n        \"financial aid\",\n        \"discount\",\n        \"merit\",\n        \"early bird\",\n        \"sibling\",\n        \"funding\",\n        \"grant\",\n        \"bursary\"\n    ],\n    language: [\n        \"english\",\n        \"turkish\",\n        \"language\",\n        \"speak\",\n        \"communication\",\n        \"language requirements\",\n        \"english proficiency\",\n        \"ielts\",\n        \"toefl\"\n    ],\n    general: [\n        \"about\",\n        \"company\",\n        \"foreingate\",\n        \"who\",\n        \"what\",\n        \"why\",\n        \"how\",\n        \"hello\",\n        \"hi\",\n        \"help\",\n        \"info\",\n        \"information\"\n    ]\n};\n// Smart response templates\nconst responseTemplates = {\n    greeting: [\n        \"Hello! I'm your Foreingate assistant. How can I help you today?\",\n        \"Hi there! I'm here to answer any questions about studying in Northern Cyprus. What would you like to know?\",\n        \"Welcome to Foreingate! I can help you with information about universities, admissions, costs, and more. What interests you?\"\n    ],\n    notFound: [\n        \"I don't have specific information about that, but I'd be happy to connect you with our human advisors who can help. You can contact us at +90 ************ or <EMAIL>.\",\n        \"That's a great question! For detailed information about that topic, I recommend speaking with our education consultants. Would you like me to help you get in touch?\",\n        \"I want to make sure I give you accurate information. For that specific question, our expert advisors would be the best resource. Shall I provide you with contact details?\"\n    ],\n    followUp: [\n        \"Is there anything else you'd like to know about studying in Northern Cyprus?\",\n        \"Do you have any other questions about our services or universities?\",\n        \"Would you like more information about any specific university or program?\",\n        \"Can I help you with anything else regarding your education plans?\"\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/chatbot-knowledge.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/security.ts":
/*!*****************************!*\
  !*** ./src/lib/security.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RateLimiter: () => (/* binding */ RateLimiter),\n/* harmony export */   SecureSession: () => (/* binding */ SecureSession),\n/* harmony export */   SecurityLogger: () => (/* binding */ SecurityLogger),\n/* harmony export */   ValidationSchemas: () => (/* binding */ ValidationSchemas),\n/* harmony export */   decryptData: () => (/* binding */ decryptData),\n/* harmony export */   encryptData: () => (/* binding */ encryptData),\n/* harmony export */   generateCSRFToken: () => (/* binding */ generateCSRFToken),\n/* harmony export */   generateSecureToken: () => (/* binding */ generateSecureToken),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   isValidEmail: () => (/* binding */ isValidEmail),\n/* harmony export */   isValidPhone: () => (/* binding */ isValidPhone),\n/* harmony export */   sanitizeInput: () => (/* binding */ sanitizeInput),\n/* harmony export */   verifyCSRFToken: () => (/* binding */ verifyCSRFToken),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword)\n/* harmony export */ });\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(crypto__WEBPACK_IMPORTED_MODULE_0__);\n\n// Security utilities for the application\n/**\n * Generate a secure random string\n */ function generateSecureToken(length = 32) {\n    return crypto__WEBPACK_IMPORTED_MODULE_0___default().randomBytes(length).toString('hex');\n}\n/**\n * Hash a password using PBKDF2\n */ async function hashPassword(password) {\n    const salt = crypto__WEBPACK_IMPORTED_MODULE_0___default().randomBytes(16).toString('hex');\n    const hash = crypto__WEBPACK_IMPORTED_MODULE_0___default().pbkdf2Sync(password, salt, 10000, 64, 'sha512').toString('hex');\n    return `${salt}:${hash}`;\n}\n/**\n * Verify a password against its hash\n */ async function verifyPassword(password, hashedPassword) {\n    const [salt, hash] = hashedPassword.split(':');\n    const verifyHash = crypto__WEBPACK_IMPORTED_MODULE_0___default().pbkdf2Sync(password, salt, 10000, 64, 'sha512').toString('hex');\n    return hash === verifyHash;\n}\n/**\n * Encrypt sensitive data\n */ function encryptData(data, key) {\n    const encryptionKey = key || process.env.ENCRYPTION_KEY || 'default-key-change-in-production';\n    const algorithm = 'aes-256-gcm';\n    const iv = crypto__WEBPACK_IMPORTED_MODULE_0___default().randomBytes(16);\n    const cipher = crypto__WEBPACK_IMPORTED_MODULE_0___default().createCipher(algorithm, encryptionKey);\n    let encrypted = cipher.update(data, 'utf8', 'hex');\n    encrypted += cipher.final('hex');\n    return `${iv.toString('hex')}:${encrypted}`;\n}\n/**\n * Decrypt sensitive data\n */ function decryptData(encryptedData, key) {\n    const encryptionKey = key || process.env.ENCRYPTION_KEY || 'default-key-change-in-production';\n    const algorithm = 'aes-256-gcm';\n    const [ivHex, encrypted] = encryptedData.split(':');\n    const iv = Buffer.from(ivHex, 'hex');\n    const decipher = crypto__WEBPACK_IMPORTED_MODULE_0___default().createDecipher(algorithm, encryptionKey);\n    let decrypted = decipher.update(encrypted, 'hex', 'utf8');\n    decrypted += decipher.final('utf8');\n    return decrypted;\n}\n/**\n * Sanitize user input to prevent XSS\n */ function sanitizeInput(input) {\n    return input.replace(/[<>]/g, '') // Remove < and >\n    .replace(/javascript:/gi, '') // Remove javascript: protocol\n    .replace(/on\\w+=/gi, '') // Remove event handlers\n    .trim();\n}\n/**\n * Validate email format\n */ function isValidEmail(email) {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n}\n/**\n * Validate phone number format\n */ function isValidPhone(phone) {\n    const phoneRegex = /^\\+?[\\d\\s\\-\\(\\)]{10,}$/;\n    return phoneRegex.test(phone);\n}\n/**\n * Generate CSRF token\n */ function generateCSRFToken() {\n    return crypto__WEBPACK_IMPORTED_MODULE_0___default().randomBytes(32).toString('hex');\n}\n/**\n * Verify CSRF token\n */ function verifyCSRFToken(token, sessionToken) {\n    return crypto__WEBPACK_IMPORTED_MODULE_0___default().timingSafeEqual(Buffer.from(token, 'hex'), Buffer.from(sessionToken, 'hex'));\n}\n/**\n * Rate limiting helper\n */ class RateLimiter {\n    constructor(maxRequests = 100, windowMs = 60000){\n        this.requests = new Map();\n        this.maxRequests = maxRequests;\n        this.windowMs = windowMs;\n    }\n    isAllowed(identifier) {\n        const now = Date.now();\n        const requests = this.requests.get(identifier) || [];\n        // Remove old requests outside the window\n        const validRequests = requests.filter((time)=>now - time < this.windowMs);\n        if (validRequests.length >= this.maxRequests) {\n            return false;\n        }\n        validRequests.push(now);\n        this.requests.set(identifier, validRequests);\n        return true;\n    }\n    getRemainingRequests(identifier) {\n        const requests = this.requests.get(identifier) || [];\n        const now = Date.now();\n        const validRequests = requests.filter((time)=>now - time < this.windowMs);\n        return Math.max(0, this.maxRequests - validRequests.length);\n    }\n}\n/**\n * Secure session management\n */ class SecureSession {\n    static{\n        this.sessions = new Map();\n    }\n    static create(data) {\n        const sessionId = generateSecureToken(32);\n        const sessionData = {\n            ...data,\n            createdAt: Date.now(),\n            lastAccessed: Date.now()\n        };\n        this.sessions.set(sessionId, sessionData);\n        return sessionId;\n    }\n    static get(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return null;\n        // Check if session is expired (24 hours)\n        if (Date.now() - session.lastAccessed > 24 * 60 * 60 * 1000) {\n            this.sessions.delete(sessionId);\n            return null;\n        }\n        // Update last accessed time\n        session.lastAccessed = Date.now();\n        return session;\n    }\n    static destroy(sessionId) {\n        this.sessions.delete(sessionId);\n    }\n    static cleanup() {\n        const now = Date.now();\n        for (const [sessionId, session] of this.sessions.entries()){\n            if (now - session.lastAccessed > 24 * 60 * 60 * 1000) {\n                this.sessions.delete(sessionId);\n            }\n        }\n    }\n}\n/**\n * Input validation schemas\n */ const ValidationSchemas = {\n    email: (email)=>{\n        if (!email || email.length > 254) return false;\n        return isValidEmail(email);\n    },\n    name: (name)=>{\n        if (!name || name.length < 2 || name.length > 100) return false;\n        return /^[a-zA-Z\\s\\-'\\.]+$/.test(name);\n    },\n    phone: (phone)=>{\n        if (!phone) return true // Optional field\n        ;\n        return isValidPhone(phone);\n    },\n    message: (message)=>{\n        if (!message || message.length < 10 || message.length > 5000) return false;\n        return true;\n    },\n    subject: (subject)=>{\n        if (!subject || subject.length < 5 || subject.length > 200) return false;\n        return true;\n    }\n};\n/**\n * Security audit logger\n */ class SecurityLogger {\n    static log(event, details, level = 'info') {\n        const logEntry = {\n            timestamp: new Date().toISOString(),\n            event,\n            details,\n            level\n        };\n        // In production, send to logging service\n        if (false) {} else {\n            console.log(`[SECURITY ${level.toUpperCase()}]`, event, details);\n        }\n    }\n    static logFailedLogin(ip, userAgent) {\n        this.log('FAILED_LOGIN', {\n            ip,\n            userAgent\n        }, 'warn');\n    }\n    static logSuspiciousActivity(ip, activity) {\n        this.log('SUSPICIOUS_ACTIVITY', {\n            ip,\n            activity\n        }, 'error');\n    }\n    static logDataAccess(resource, ip) {\n        this.log('DATA_ACCESS', {\n            resource,\n            ip\n        }, 'info');\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/security.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchatbot%2Froute&page=%2Fapi%2Fchatbot%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchatbot%2Froute.ts&appDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();