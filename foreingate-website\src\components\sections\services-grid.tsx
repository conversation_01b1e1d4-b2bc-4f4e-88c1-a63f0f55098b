'use client'

import { motion } from 'framer-motion'
import Link from 'next/link'
import { useInView } from 'react-intersection-observer'
import { 
  GraduationCap, 
  FileText, 
  Home, 
  Languages, 
  Plane, 
  BookOpen,
  ArrowRight,
  CheckCircle,
  DollarSign
} from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { formatCurrency } from '@/lib/utils'

const services = [
  {
    id: 'admissions',
    icon: GraduationCap,
    title: 'University Admissions',
    description: 'Complete assistance with university applications, document preparation, and admission guidance from our expert counselors.',
    features: [
      'University selection based on your profile',
      'Application document preparation',
      'Interview preparation and coaching',
      'Scholarship application assistance',
      'Follow-up with universities until acceptance'
    ],
    process: [
      'Initial consultation and profile assessment',
      'University and program selection',
      'Document preparation and review',
      'Application submission and tracking',
      'Interview preparation and follow-up'
    ],
    pricing: {
      basic: 500,
      premium: 1000,
      currency: 'USD'
    },
    featured: true,
    image: '/images/services/admissions.jpg'
  },
  {
    id: 'visa',
    icon: FileText,
    title: 'Visa Support',
    description: 'Professional visa application assistance and documentation support for student visas with 98% success rate.',
    features: [
      'Complete visa application guidance',
      'Document checklist and preparation',
      'Embassy appointment scheduling',
      'Interview preparation and coaching',
      'Status tracking and regular updates'
    ],
    process: [
      'Visa requirements assessment',
      'Document collection and verification',
      'Application form completion',
      'Embassy submission and tracking',
      'Follow-up until visa approval'
    ],
    pricing: {
      basic: 300,
      premium: 600,
      currency: 'USD'
    },
    featured: true,
    image: '/images/services/visa.jpg'
  },
  {
    id: 'housing',
    icon: Home,
    title: 'Student Housing',
    description: 'Find comfortable and affordable accommodation options near your university with our housing assistance service.',
    features: [
      'University dormitory booking',
      'Private housing options',
      'Roommate matching service',
      'Housing inspection and verification',
      'Lease agreement assistance'
    ],
    process: [
      'Housing preference assessment',
      'Available options presentation',
      'Property visits and inspections',
      'Booking and reservation',
      'Move-in assistance and support'
    ],
    pricing: {
      basic: 200,
      premium: 400,
      currency: 'USD'
    },
    featured: false,
    image: '/images/services/housing.jpg'
  },
  {
    id: 'translation',
    icon: Languages,
    title: 'Document Translation',
    description: 'Professional translation services for all your academic and legal documents with certified translators.',
    features: [
      'Certified document translation',
      'Academic transcript translation',
      'Legal document translation',
      'Notarization services',
      'Fast turnaround time'
    ],
    process: [
      'Document assessment and quote',
      'Professional translation',
      'Quality review and verification',
      'Certification and notarization',
      'Delivery and follow-up'
    ],
    pricing: {
      basic: 50,
      premium: 100,
      currency: 'USD'
    },
    featured: false,
    image: '/images/services/translation.jpg'
  },
  {
    id: 'pickup',
    icon: Plane,
    title: 'Airport Pickup',
    description: 'Safe and reliable transportation from the airport to your accommodation with our 24/7 pickup service.',
    features: [
      '24/7 airport pickup service',
      'Meet and greet at arrival',
      'Luggage assistance',
      'Direct transport to accommodation',
      'Local orientation during journey'
    ],
    process: [
      'Flight details confirmation',
      'Driver assignment and briefing',
      'Airport arrival and passenger pickup',
      'Safe transport to destination',
      'Arrival confirmation and support'
    ],
    pricing: {
      basic: 75,
      premium: 150,
      currency: 'USD'
    },
    featured: false,
    image: '/images/services/pickup.jpg'
  },
  {
    id: 'academic',
    icon: BookOpen,
    title: 'Academic Support',
    description: 'Ongoing academic support to help you succeed in your studies with tutoring and counseling services.',
    features: [
      'Subject-specific tutoring',
      'Study group coordination',
      'Academic counseling',
      'Exam preparation assistance',
      'Progress monitoring and feedback'
    ],
    process: [
      'Academic needs assessment',
      'Tutor matching and scheduling',
      'Regular tutoring sessions',
      'Progress tracking and evaluation',
      'Ongoing support and adjustments'
    ],
    pricing: {
      basic: 100,
      premium: 200,
      currency: 'USD'
    },
    featured: false,
    image: '/images/services/academic.jpg'
  }
]

export function ServicesGridSection() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1
  })

  return (
    <section ref={ref} className="section-padding">
      <div className="container">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
          className="text-center max-w-3xl mx-auto mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Our Comprehensive{' '}
            <span className="gradient-text">Service Portfolio</span>
          </h2>
          <p className="text-xl text-muted-foreground">
            Everything you need for a successful educational journey, from application to graduation
          </p>
        </motion.div>

        {/* Services Grid */}
        <div className="grid lg:grid-cols-2 gap-8">
          {services.map((service, index) => (
            <motion.div
              key={service.id}
              initial={{ opacity: 0, y: 30 }}
              animate={inView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              className={`bg-background rounded-2xl overflow-hidden shadow-sm border hover:shadow-lg transition-all duration-300 group ${
                service.featured ? 'ring-2 ring-primary/20' : ''
              }`}
            >
              {/* Service Image */}
              <div className="relative h-48 overflow-hidden">
                <img
                  src={service.image}
                  alt={service.title}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  onError={(e) => {
                    e.currentTarget.src = `data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='400' height='200' viewBox='0 0 400 200'%3E%3Crect width='400' height='200' fill='%23f3f4f6'/%3E%3Ctext x='50%25' y='50%25' dominant-baseline='middle' text-anchor='middle' font-family='Arial, sans-serif' font-size='16' fill='%236b7280'%3E${service.title}%3C/text%3E%3C/svg%3E`
                  }}
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
                
                {/* Featured Badge */}
                {service.featured && (
                  <div className="absolute top-4 left-4 bg-primary text-primary-foreground px-3 py-1 rounded-full text-sm font-medium">
                    Most Popular
                  </div>
                )}

                {/* Icon */}
                <div className="absolute top-4 right-4 w-12 h-12 bg-white/90 dark:bg-gray-800/90 rounded-lg flex items-center justify-center">
                  <service.icon className="w-6 h-6 text-primary" />
                </div>
              </div>

              {/* Service Content */}
              <div className="p-6">
                <h3 className="text-2xl font-semibold mb-3 group-hover:text-primary transition-colors">
                  {service.title}
                </h3>
                <p className="text-muted-foreground mb-4 leading-relaxed">
                  {service.description}
                </p>

                {/* Key Features */}
                <div className="mb-6">
                  <h4 className="font-medium mb-3">What's Included:</h4>
                  <ul className="space-y-2">
                    {service.features.slice(0, 3).map((feature) => (
                      <li key={feature} className="flex items-start text-sm text-muted-foreground">
                        <CheckCircle className="w-4 h-4 text-primary mr-2 mt-0.5 flex-shrink-0" />
                        {feature}
                      </li>
                    ))}
                    {service.features.length > 3 && (
                      <li className="text-sm text-primary font-medium">
                        +{service.features.length - 3} more features
                      </li>
                    )}
                  </ul>
                </div>

                {/* Pricing */}
                <div className="flex items-center justify-between mb-4 p-3 bg-muted/50 rounded-lg">
                  <div className="flex items-center">
                    <DollarSign className="w-4 h-4 text-primary mr-1" />
                    <span className="text-sm text-muted-foreground">Starting from</span>
                  </div>
                  <div className="text-right">
                    <div className="font-semibold text-primary">
                      {formatCurrency(service.pricing.basic, service.pricing.currency)}
                    </div>
                    <div className="text-xs text-muted-foreground">Basic Package</div>
                  </div>
                </div>

                {/* CTA */}
                <div className="flex gap-3">
                  <Button className="flex-1 group" asChild>
                    <Link href={`/services/${service.id}`}>
                      Learn More
                      <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                    </Link>
                  </Button>
                  <Button variant="outline" asChild>
                    <Link href="/contact">
                      Get Quote
                    </Link>
                  </Button>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Bottom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="text-center mt-16 bg-gradient-to-r from-primary/5 to-secondary/5 rounded-2xl p-8 md:p-12"
        >
          <h3 className="text-2xl md:text-3xl font-bold mb-4">
            Need a Custom Package?
          </h3>
          <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
            We understand that every student's needs are unique. Contact us to create a 
            personalized service package that fits your specific requirements and budget.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" asChild>
              <Link href="/contact">
                Get Custom Quote
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
            <Button size="lg" variant="outline" asChild>
              <Link href="/apply">
                Start Application
              </Link>
            </Button>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
