/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/contact/route";
exports.ids = ["app/api/contact/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcontact%2Froute&page=%2Fapi%2Fcontact%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcontact%2Froute.ts&appDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcontact%2Froute&page=%2Fapi%2Fcontact%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcontact%2Froute.ts&appDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Nidhal_Documents_augment_projects_foreingate_groupe_foreingate_website_src_app_api_contact_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/contact/route.ts */ \"(rsc)/./src/app/api/contact/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/contact/route\",\n        pathname: \"/api/contact\",\n        filename: \"route\",\n        bundlePath: \"app/api/contact/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\api\\\\contact\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Nidhal_Documents_augment_projects_foreingate_groupe_foreingate_website_src_app_api_contact_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcontact%2Froute&page=%2Fapi%2Fcontact%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcontact%2Froute.ts&appDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/contact/route.ts":
/*!**************************************!*\
  !*** ./src/app/api/contact/route.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fs/promises */ \"fs/promises\");\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fs_promises__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var resend__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! resend */ \"(rsc)/../node_modules/resend/dist/index.mjs\");\n\n\n\n\n\nconst resend = new resend__WEBPACK_IMPORTED_MODULE_4__.Resend(process.env.RESEND_API_KEY);\nconst DATA_DIR = path__WEBPACK_IMPORTED_MODULE_3___default().join(process.cwd(), 'data');\nconst CONTACT_FILE = path__WEBPACK_IMPORTED_MODULE_3___default().join(DATA_DIR, 'contact-inquiries.json');\n// Ensure data directory exists\nasync function ensureDataDir() {\n    if (!(0,fs__WEBPACK_IMPORTED_MODULE_2__.existsSync)(DATA_DIR)) {\n        await (0,fs_promises__WEBPACK_IMPORTED_MODULE_1__.mkdir)(DATA_DIR, {\n            recursive: true\n        });\n    }\n}\n// Read contact inquiries from file\nasync function readContactInquiries() {\n    try {\n        await ensureDataDir();\n        if (!(0,fs__WEBPACK_IMPORTED_MODULE_2__.existsSync)(CONTACT_FILE)) {\n            return [];\n        }\n        const data = await (0,fs_promises__WEBPACK_IMPORTED_MODULE_1__.readFile)(CONTACT_FILE, 'utf-8');\n        return JSON.parse(data);\n    } catch (error) {\n        console.error('Error reading contact inquiries:', error);\n        return [];\n    }\n}\n// Write contact inquiries to file\nasync function writeContactInquiries(inquiries) {\n    try {\n        await ensureDataDir();\n        await (0,fs_promises__WEBPACK_IMPORTED_MODULE_1__.writeFile)(CONTACT_FILE, JSON.stringify(inquiries, null, 2));\n    } catch (error) {\n        console.error('Error writing contact inquiries:', error);\n        throw error;\n    }\n}\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { name, email, phone, subject, message, preferredContact, interestedServices } = body;\n        // Validate required fields\n        if (!name || !email || !subject || !message) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Missing required fields'\n            }, {\n                status: 400\n            });\n        }\n        // Email template for admin notification\n        const adminEmailHtml = `\n      <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n        <div style=\"background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center;\">\n          <h1 style=\"color: white; margin: 0;\">New Contact Form Submission</h1>\n          <p style=\"color: white; opacity: 0.9; margin: 10px 0 0 0;\">Foreingate Group Website</p>\n        </div>\n        \n        <div style=\"padding: 30px; background: #f8f9fa;\">\n          <h2 style=\"color: #333; border-bottom: 2px solid #667eea; padding-bottom: 10px;\">Contact Information</h2>\n          <table style=\"width: 100%; border-collapse: collapse; margin: 20px 0;\">\n            <tr>\n              <td style=\"padding: 10px; font-weight: bold; color: #555;\">Name:</td>\n              <td style=\"padding: 10px; color: #333;\">${name}</td>\n            </tr>\n            <tr style=\"background: #f1f3f4;\">\n              <td style=\"padding: 10px; font-weight: bold; color: #555;\">Email:</td>\n              <td style=\"padding: 10px; color: #333;\">${email}</td>\n            </tr>\n            <tr>\n              <td style=\"padding: 10px; font-weight: bold; color: #555;\">Phone:</td>\n              <td style=\"padding: 10px; color: #333;\">${phone || 'Not provided'}</td>\n            </tr>\n            <tr style=\"background: #f1f3f4;\">\n              <td style=\"padding: 10px; font-weight: bold; color: #555;\">Subject:</td>\n              <td style=\"padding: 10px; color: #333;\">${subject}</td>\n            </tr>\n            <tr>\n              <td style=\"padding: 10px; font-weight: bold; color: #555;\">Preferred Contact:</td>\n              <td style=\"padding: 10px; color: #333;\">${preferredContact}</td>\n            </tr>\n          </table>\n\n          ${interestedServices && interestedServices.length > 0 ? `\n            <h3 style=\"color: #333; margin-top: 30px;\">Interested Services:</h3>\n            <ul style=\"background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #667eea;\">\n              ${interestedServices.map((service)=>`<li style=\"margin: 5px 0; color: #555;\">${service}</li>`).join('')}\n            </ul>\n          ` : ''}\n\n          <h3 style=\"color: #333; margin-top: 30px;\">Message:</h3>\n          <div style=\"background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #667eea; white-space: pre-wrap; color: #555; line-height: 1.6;\">\n            ${message}\n          </div>\n\n          <div style=\"margin-top: 30px; padding: 20px; background: #e3f2fd; border-radius: 8px; text-align: center;\">\n            <p style=\"margin: 0; color: #1976d2; font-weight: bold;\">⏰ Submitted: ${new Date().toLocaleString()}</p>\n            <p style=\"margin: 10px 0 0 0; color: #666; font-size: 14px;\">Please respond within 24 hours</p>\n          </div>\n        </div>\n      </div>\n    `;\n        // Email template for user confirmation\n        const userEmailHtml = `\n      <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n        <div style=\"background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center;\">\n          <h1 style=\"color: white; margin: 0;\">Thank You for Contacting Us!</h1>\n          <p style=\"color: white; opacity: 0.9; margin: 10px 0 0 0;\">Foreingate Group</p>\n        </div>\n        \n        <div style=\"padding: 30px; background: #f8f9fa;\">\n          <p style=\"color: #333; font-size: 16px; line-height: 1.6;\">Dear ${name},</p>\n          \n          <p style=\"color: #555; line-height: 1.6;\">\n            Thank you for reaching out to Foreingate Group! We have received your inquiry and our expert team will review your message carefully.\n          </p>\n\n          <div style=\"background: #e8f5e8; padding: 20px; border-radius: 8px; border-left: 4px solid #4caf50; margin: 20px 0;\">\n            <h3 style=\"color: #2e7d32; margin: 0 0 10px 0;\">✅ What happens next?</h3>\n            <ul style=\"color: #555; margin: 0; padding-left: 20px;\">\n              <li>Our admissions team will review your inquiry within 24 hours</li>\n              <li>A dedicated counselor will be assigned to your case</li>\n              <li>You'll receive a personalized response via ${preferredContact}</li>\n              <li>We'll provide detailed information about your areas of interest</li>\n            </ul>\n          </div>\n\n          <div style=\"background: white; padding: 20px; border-radius: 8px; border: 1px solid #e0e0e0; margin: 20px 0;\">\n            <h3 style=\"color: #333; margin: 0 0 15px 0;\">Your Inquiry Summary:</h3>\n            <p style=\"margin: 5px 0; color: #555;\"><strong>Subject:</strong> ${subject}</p>\n            ${interestedServices && interestedServices.length > 0 ? `\n              <p style=\"margin: 5px 0; color: #555;\"><strong>Services of Interest:</strong> ${interestedServices.join(', ')}</p>\n            ` : ''}\n            <p style=\"margin: 5px 0; color: #555;\"><strong>Submitted:</strong> ${new Date().toLocaleString()}</p>\n          </div>\n\n          <div style=\"background: #fff3e0; padding: 20px; border-radius: 8px; border-left: 4px solid #ff9800; margin: 20px 0;\">\n            <h3 style=\"color: #f57c00; margin: 0 0 10px 0;\">📞 Need Immediate Assistance?</h3>\n            <p style=\"color: #555; margin: 0;\">\n              <strong>Phone:</strong> +90 ************<br>\n              <strong>WhatsApp:</strong> +90 ************<br>\n              <strong>Email:</strong> <EMAIL>\n            </p>\n          </div>\n\n          <p style=\"color: #555; line-height: 1.6;\">\n            We're excited to help you achieve your educational goals and look forward to speaking with you soon!\n          </p>\n\n          <p style=\"color: #555; line-height: 1.6;\">\n            Best regards,<br>\n            <strong>The Foreingate Group Team</strong><br>\n            <em>Your Gateway to International Education</em>\n          </p>\n        </div>\n\n        <div style=\"background: #333; padding: 20px; text-align: center;\">\n          <p style=\"color: #ccc; margin: 0; font-size: 14px;\">\n            © 2024 Foreingate Group. All rights reserved.<br>\n            This is an automated confirmation email.\n          </p>\n        </div>\n      </div>\n    `;\n        // Send email to admin\n        const adminEmail = await resend.emails.send({\n            from: 'Foreingate Website <<EMAIL>>',\n            to: [\n                '<EMAIL>'\n            ],\n            subject: `New Contact Form: ${subject}`,\n            html: adminEmailHtml\n        });\n        // Send confirmation email to user\n        const userEmail = await resend.emails.send({\n            from: 'Foreingate Group <<EMAIL>>',\n            to: [\n                email\n            ],\n            subject: 'Thank you for contacting Foreingate Group',\n            html: userEmailHtml\n        });\n        // Generate a unique inquiry ID\n        const inquiryId = `FG-${Date.now()}-${Math.random().toString(36).substr(2, 9).toUpperCase()}`;\n        // Store contact inquiry in database\n        try {\n            const contactInquiry = {\n                id: Math.random().toString(36).substr(2, 9),\n                inquiryId,\n                createdAt: new Date().toISOString(),\n                updatedAt: new Date().toISOString(),\n                name,\n                email,\n                phone: phone || null,\n                subject,\n                message,\n                preferredContact,\n                interestedServices: JSON.stringify(interestedServices || []),\n                status: 'NEW',\n                assignedAgent: null,\n                responseNotes: null,\n                respondedAt: null\n            };\n            const inquiries = await readContactInquiries();\n            inquiries.push(contactInquiry);\n            await writeContactInquiries(inquiries);\n        } catch (storageError) {\n            console.error('Failed to store contact inquiry:', storageError);\n        // Don't fail the API if storage fails\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'Your message has been sent successfully!',\n            inquiryId,\n            adminEmailId: adminEmail.data?.id,\n            userEmailId: userEmail.data?.id\n        });\n    } catch (error) {\n        console.error('Contact form error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to send message. Please try again.'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/contact/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "async_hooks":
/*!******************************!*\
  !*** external "async_hooks" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("async_hooks");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "fs/promises":
/*!******************************!*\
  !*** external "fs/promises" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("fs/promises");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "prettier/plugins/html":
/*!****************************************!*\
  !*** external "prettier/plugins/html" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = import("prettier/plugins/html");;

/***/ }),

/***/ "prettier/standalone":
/*!**************************************!*\
  !*** external "prettier/standalone" ***!
  \**************************************/
/***/ ((module) => {

"use strict";
module.exports = import("prettier/standalone");;

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/resend"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcontact%2Froute&page=%2Fapi%2Fcontact%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcontact%2Froute.ts&appDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();