/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/contact/route";
exports.ids = ["app/api/contact/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcontact%2Froute&page=%2Fapi%2Fcontact%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcontact%2Froute.ts&appDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcontact%2Froute&page=%2Fapi%2Fcontact%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcontact%2Froute.ts&appDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Nidhal_Documents_augment_projects_foreingate_groupe_foreingate_website_src_app_api_contact_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/contact/route.ts */ \"(rsc)/./src/app/api/contact/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"standalone\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/contact/route\",\n        pathname: \"/api/contact\",\n        filename: \"route\",\n        bundlePath: \"app/api/contact/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\api\\\\contact\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Nidhal_Documents_augment_projects_foreingate_groupe_foreingate_website_src_app_api_contact_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcontact%2Froute&page=%2Fapi%2Fcontact%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcontact%2Froute.ts&appDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/contact/route.ts":
/*!**************************************!*\
  !*** ./src/app/api/contact/route.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fs/promises */ \"fs/promises\");\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(fs_promises__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var resend__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! resend */ \"(rsc)/../node_modules/resend/dist/index.mjs\");\n/* harmony import */ var _lib_api_security__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api-security */ \"(rsc)/./src/lib/api-security.ts\");\n/* harmony import */ var _lib_security__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/security */ \"(rsc)/./src/lib/security.ts\");\n\n\n\n\n\n\nconst resend = new resend__WEBPACK_IMPORTED_MODULE_3__.Resend(process.env.RESEND_API_KEY);\nconst DATA_DIR = path__WEBPACK_IMPORTED_MODULE_2___default().join(process.cwd(), 'data');\nconst CONTACT_FILE = path__WEBPACK_IMPORTED_MODULE_2___default().join(DATA_DIR, 'contact-inquiries.json');\n// Ensure data directory exists\nasync function ensureDataDir() {\n    if (!(0,fs__WEBPACK_IMPORTED_MODULE_1__.existsSync)(DATA_DIR)) {\n        await (0,fs_promises__WEBPACK_IMPORTED_MODULE_0__.mkdir)(DATA_DIR, {\n            recursive: true\n        });\n    }\n}\n// Read contact inquiries from file\nasync function readContactInquiries() {\n    try {\n        await ensureDataDir();\n        if (!(0,fs__WEBPACK_IMPORTED_MODULE_1__.existsSync)(CONTACT_FILE)) {\n            return [];\n        }\n        const data = await (0,fs_promises__WEBPACK_IMPORTED_MODULE_0__.readFile)(CONTACT_FILE, 'utf-8');\n        return JSON.parse(data);\n    } catch (error) {\n        console.error('Error reading contact inquiries:', error);\n        return [];\n    }\n}\n// Write contact inquiries to file\nasync function writeContactInquiries(inquiries) {\n    try {\n        await ensureDataDir();\n        await (0,fs_promises__WEBPACK_IMPORTED_MODULE_0__.writeFile)(CONTACT_FILE, JSON.stringify(inquiries, null, 2));\n    } catch (error) {\n        console.error('Error writing contact inquiries:', error);\n        throw error;\n    }\n}\n// Validation schema for contact form\nconst contactValidationSchema = {\n    name: _lib_security__WEBPACK_IMPORTED_MODULE_5__.ValidationSchemas.name,\n    email: _lib_security__WEBPACK_IMPORTED_MODULE_5__.ValidationSchemas.email,\n    phone: _lib_security__WEBPACK_IMPORTED_MODULE_5__.ValidationSchemas.phone,\n    subject: _lib_security__WEBPACK_IMPORTED_MODULE_5__.ValidationSchemas.subject,\n    message: _lib_security__WEBPACK_IMPORTED_MODULE_5__.ValidationSchemas.message\n};\nasync function handleContactSubmission(request) {\n    try {\n        // Use validated body if available, otherwise parse JSON\n        const body = request.validatedBody || await request.json();\n        let { name, email, phone, subject, message, preferredContact, interestedServices } = body;\n        // Sanitize inputs\n        name = (0,_lib_security__WEBPACK_IMPORTED_MODULE_5__.sanitizeInput)(name);\n        email = (0,_lib_security__WEBPACK_IMPORTED_MODULE_5__.sanitizeInput)(email);\n        phone = phone ? (0,_lib_security__WEBPACK_IMPORTED_MODULE_5__.sanitizeInput)(phone) : null;\n        subject = (0,_lib_security__WEBPACK_IMPORTED_MODULE_5__.sanitizeInput)(subject);\n        message = (0,_lib_security__WEBPACK_IMPORTED_MODULE_5__.sanitizeInput)(message);\n        // Validate required fields\n        if (!name || !email || !subject || !message) {\n            return (0,_lib_api_security__WEBPACK_IMPORTED_MODULE_4__.secureResponse)({\n                error: 'Missing required fields'\n            }, 400);\n        }\n        // Additional validation using security schemas\n        if (!_lib_security__WEBPACK_IMPORTED_MODULE_5__.ValidationSchemas.name(name)) {\n            return (0,_lib_api_security__WEBPACK_IMPORTED_MODULE_4__.secureResponse)({\n                error: 'Invalid name format'\n            }, 400);\n        }\n        if (!_lib_security__WEBPACK_IMPORTED_MODULE_5__.ValidationSchemas.email(email)) {\n            return (0,_lib_api_security__WEBPACK_IMPORTED_MODULE_4__.secureResponse)({\n                error: 'Invalid email format'\n            }, 400);\n        }\n        if (!_lib_security__WEBPACK_IMPORTED_MODULE_5__.ValidationSchemas.subject(subject)) {\n            return (0,_lib_api_security__WEBPACK_IMPORTED_MODULE_4__.secureResponse)({\n                error: 'Invalid subject format'\n            }, 400);\n        }\n        if (!_lib_security__WEBPACK_IMPORTED_MODULE_5__.ValidationSchemas.message(message)) {\n            return (0,_lib_api_security__WEBPACK_IMPORTED_MODULE_4__.secureResponse)({\n                error: 'Invalid message format'\n            }, 400);\n        }\n        if (phone && !_lib_security__WEBPACK_IMPORTED_MODULE_5__.ValidationSchemas.phone(phone)) {\n            return (0,_lib_api_security__WEBPACK_IMPORTED_MODULE_4__.secureResponse)({\n                error: 'Invalid phone format'\n            }, 400);\n        }\n        // Email template for admin notification\n        const adminEmailHtml = `\n      <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n        <div style=\"background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center;\">\n          <h1 style=\"color: white; margin: 0;\">New Contact Form Submission</h1>\n          <p style=\"color: white; opacity: 0.9; margin: 10px 0 0 0;\">Foreingate Group Website</p>\n        </div>\n        \n        <div style=\"padding: 30px; background: #f8f9fa;\">\n          <h2 style=\"color: #333; border-bottom: 2px solid #667eea; padding-bottom: 10px;\">Contact Information</h2>\n          <table style=\"width: 100%; border-collapse: collapse; margin: 20px 0;\">\n            <tr>\n              <td style=\"padding: 10px; font-weight: bold; color: #555;\">Name:</td>\n              <td style=\"padding: 10px; color: #333;\">${name}</td>\n            </tr>\n            <tr style=\"background: #f1f3f4;\">\n              <td style=\"padding: 10px; font-weight: bold; color: #555;\">Email:</td>\n              <td style=\"padding: 10px; color: #333;\">${email}</td>\n            </tr>\n            <tr>\n              <td style=\"padding: 10px; font-weight: bold; color: #555;\">Phone:</td>\n              <td style=\"padding: 10px; color: #333;\">${phone || 'Not provided'}</td>\n            </tr>\n            <tr style=\"background: #f1f3f4;\">\n              <td style=\"padding: 10px; font-weight: bold; color: #555;\">Subject:</td>\n              <td style=\"padding: 10px; color: #333;\">${subject}</td>\n            </tr>\n            <tr>\n              <td style=\"padding: 10px; font-weight: bold; color: #555;\">Preferred Contact:</td>\n              <td style=\"padding: 10px; color: #333;\">${preferredContact}</td>\n            </tr>\n          </table>\n\n          ${interestedServices && interestedServices.length > 0 ? `\n            <h3 style=\"color: #333; margin-top: 30px;\">Interested Services:</h3>\n            <ul style=\"background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #667eea;\">\n              ${interestedServices.map((service)=>`<li style=\"margin: 5px 0; color: #555;\">${service}</li>`).join('')}\n            </ul>\n          ` : ''}\n\n          <h3 style=\"color: #333; margin-top: 30px;\">Message:</h3>\n          <div style=\"background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #667eea; white-space: pre-wrap; color: #555; line-height: 1.6;\">\n            ${message}\n          </div>\n\n          <div style=\"margin-top: 30px; padding: 20px; background: #e3f2fd; border-radius: 8px; text-align: center;\">\n            <p style=\"margin: 0; color: #1976d2; font-weight: bold;\">⏰ Submitted: ${new Date().toLocaleString()}</p>\n            <p style=\"margin: 10px 0 0 0; color: #666; font-size: 14px;\">Please respond within 24 hours</p>\n          </div>\n        </div>\n      </div>\n    `;\n        // Email template for user confirmation\n        const userEmailHtml = `\n      <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n        <div style=\"background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center;\">\n          <h1 style=\"color: white; margin: 0;\">Thank You for Contacting Us!</h1>\n          <p style=\"color: white; opacity: 0.9; margin: 10px 0 0 0;\">Foreingate Group</p>\n        </div>\n        \n        <div style=\"padding: 30px; background: #f8f9fa;\">\n          <p style=\"color: #333; font-size: 16px; line-height: 1.6;\">Dear ${name},</p>\n          \n          <p style=\"color: #555; line-height: 1.6;\">\n            Thank you for reaching out to Foreingate Group! We have received your inquiry and our expert team will review your message carefully.\n          </p>\n\n          <div style=\"background: #e8f5e8; padding: 20px; border-radius: 8px; border-left: 4px solid #4caf50; margin: 20px 0;\">\n            <h3 style=\"color: #2e7d32; margin: 0 0 10px 0;\">✅ What happens next?</h3>\n            <ul style=\"color: #555; margin: 0; padding-left: 20px;\">\n              <li>Our admissions team will review your inquiry within 24 hours</li>\n              <li>A dedicated counselor will be assigned to your case</li>\n              <li>You'll receive a personalized response via ${preferredContact}</li>\n              <li>We'll provide detailed information about your areas of interest</li>\n            </ul>\n          </div>\n\n          <div style=\"background: white; padding: 20px; border-radius: 8px; border: 1px solid #e0e0e0; margin: 20px 0;\">\n            <h3 style=\"color: #333; margin: 0 0 15px 0;\">Your Inquiry Summary:</h3>\n            <p style=\"margin: 5px 0; color: #555;\"><strong>Subject:</strong> ${subject}</p>\n            ${interestedServices && interestedServices.length > 0 ? `\n              <p style=\"margin: 5px 0; color: #555;\"><strong>Services of Interest:</strong> ${interestedServices.join(', ')}</p>\n            ` : ''}\n            <p style=\"margin: 5px 0; color: #555;\"><strong>Submitted:</strong> ${new Date().toLocaleString()}</p>\n          </div>\n\n          <div style=\"background: #fff3e0; padding: 20px; border-radius: 8px; border-left: 4px solid #ff9800; margin: 20px 0;\">\n            <h3 style=\"color: #f57c00; margin: 0 0 10px 0;\">📞 Need Immediate Assistance?</h3>\n            <p style=\"color: #555; margin: 0;\">\n              <strong>Phone:</strong> +90 ************<br>\n              <strong>WhatsApp:</strong> +90 ************<br>\n              <strong>Email:</strong> <EMAIL>\n            </p>\n          </div>\n\n          <p style=\"color: #555; line-height: 1.6;\">\n            We're excited to help you achieve your educational goals and look forward to speaking with you soon!\n          </p>\n\n          <p style=\"color: #555; line-height: 1.6;\">\n            Best regards,<br>\n            <strong>The Foreingate Group Team</strong><br>\n            <em>Your Gateway to International Education</em>\n          </p>\n        </div>\n\n        <div style=\"background: #333; padding: 20px; text-align: center;\">\n          <p style=\"color: #ccc; margin: 0; font-size: 14px;\">\n            © 2024 Foreingate Group. All rights reserved.<br>\n            This is an automated confirmation email.\n          </p>\n        </div>\n      </div>\n    `;\n        // Send email to admin\n        const adminEmail = await resend.emails.send({\n            from: 'Foreingate Website <<EMAIL>>',\n            to: [\n                '<EMAIL>'\n            ],\n            subject: `New Contact Form: ${subject}`,\n            html: adminEmailHtml\n        });\n        // Send confirmation email to user\n        const userEmail = await resend.emails.send({\n            from: 'Foreingate Group <<EMAIL>>',\n            to: [\n                email\n            ],\n            subject: 'Thank you for contacting Foreingate Group',\n            html: userEmailHtml\n        });\n        // Generate a unique inquiry ID\n        const inquiryId = `FG-${Date.now()}-${Math.random().toString(36).substr(2, 9).toUpperCase()}`;\n        // Store contact inquiry in database\n        try {\n            const contactInquiry = {\n                id: Math.random().toString(36).substr(2, 9),\n                inquiryId,\n                createdAt: new Date().toISOString(),\n                updatedAt: new Date().toISOString(),\n                name,\n                email,\n                phone: phone || null,\n                subject,\n                message,\n                preferredContact,\n                interestedServices: JSON.stringify(interestedServices || []),\n                status: 'NEW',\n                assignedAgent: null,\n                responseNotes: null,\n                respondedAt: null\n            };\n            const inquiries = await readContactInquiries();\n            inquiries.push(contactInquiry);\n            await writeContactInquiries(inquiries);\n        } catch (storageError) {\n            console.error('Failed to store contact inquiry:', storageError);\n        // Don't fail the API if storage fails\n        }\n        return (0,_lib_api_security__WEBPACK_IMPORTED_MODULE_4__.secureResponse)({\n            success: true,\n            message: 'Your message has been sent successfully!',\n            inquiryId,\n            adminEmailId: adminEmail.data?.id,\n            userEmailId: userEmail.data?.id\n        });\n    } catch (error) {\n        console.error('Contact form error:', error);\n        return (0,_lib_api_security__WEBPACK_IMPORTED_MODULE_4__.secureResponse)({\n            error: 'Failed to send message. Please try again.'\n        }, 500);\n    }\n}\n// Apply security middleware to the POST handler\nconst POST = (0,_lib_api_security__WEBPACK_IMPORTED_MODULE_4__.withApiSecurity)((0,_lib_api_security__WEBPACK_IMPORTED_MODULE_4__.validateInput)(contactValidationSchema)(handleContactSubmission));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/contact/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/api-security.ts":
/*!*********************************!*\
  !*** ./src/lib/api-security.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   sanitizeResponse: () => (/* binding */ sanitizeResponse),\n/* harmony export */   secureResponse: () => (/* binding */ secureResponse),\n/* harmony export */   validateInput: () => (/* binding */ validateInput),\n/* harmony export */   withAdminSecurity: () => (/* binding */ withAdminSecurity),\n/* harmony export */   withApiSecurity: () => (/* binding */ withApiSecurity),\n/* harmony export */   withCORS: () => (/* binding */ withCORS)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _security__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./security */ \"(rsc)/./src/lib/security.ts\");\n\n\n// Rate limiter instances\nconst globalRateLimiter = new _security__WEBPACK_IMPORTED_MODULE_1__.RateLimiter(100, 60000) // 100 requests per minute\n;\nconst apiRateLimiter = new _security__WEBPACK_IMPORTED_MODULE_1__.RateLimiter(50, 60000) // 50 API requests per minute\n;\nconst authRateLimiter = new _security__WEBPACK_IMPORTED_MODULE_1__.RateLimiter(5, 300000) // 5 auth attempts per 5 minutes\n;\n/**\n * API Security Middleware\n */ function withApiSecurity(handler) {\n    return async (request)=>{\n        const ip = getClientIP(request);\n        const userAgent = request.headers.get('user-agent') || 'unknown';\n        const path = request.nextUrl.pathname;\n        try {\n            // 1. Rate Limiting\n            if (!apiRateLimiter.isAllowed(ip)) {\n                _security__WEBPACK_IMPORTED_MODULE_1__.SecurityLogger.logSuspiciousActivity(ip, `Rate limit exceeded for ${path}`);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Too many requests. Please try again later.'\n                }, {\n                    status: 429,\n                    headers: {\n                        'Retry-After': '60',\n                        'X-RateLimit-Limit': '50',\n                        'X-RateLimit-Remaining': '0',\n                        'X-RateLimit-Reset': String(Date.now() + 60000)\n                    }\n                });\n            }\n            // 2. Method validation\n            const allowedMethods = [\n                'GET',\n                'POST',\n                'PUT',\n                'DELETE',\n                'PATCH'\n            ];\n            if (!allowedMethods.includes(request.method)) {\n                _security__WEBPACK_IMPORTED_MODULE_1__.SecurityLogger.logSuspiciousActivity(ip, `Invalid method ${request.method} for ${path}`);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Method not allowed'\n                }, {\n                    status: 405\n                });\n            }\n            // 3. Content-Type validation for POST/PUT requests\n            if ([\n                'POST',\n                'PUT',\n                'PATCH'\n            ].includes(request.method)) {\n                const contentType = request.headers.get('content-type');\n                if (!contentType || !contentType.includes('application/json')) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        error: 'Content-Type must be application/json'\n                    }, {\n                        status: 400\n                    });\n                }\n            }\n            // 4. Request size validation\n            const contentLength = request.headers.get('content-length');\n            if (contentLength && parseInt(contentLength) > 1024 * 1024) {\n                _security__WEBPACK_IMPORTED_MODULE_1__.SecurityLogger.logSuspiciousActivity(ip, `Large request size: ${contentLength} bytes`);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Request too large'\n                }, {\n                    status: 413\n                });\n            }\n            // 5. Suspicious headers check\n            const suspiciousHeaders = [\n                'x-forwarded-host',\n                'x-real-ip'\n            ];\n            for (const header of suspiciousHeaders){\n                if (request.headers.get(header)) {\n                    _security__WEBPACK_IMPORTED_MODULE_1__.SecurityLogger.logSuspiciousActivity(ip, `Suspicious header: ${header}`);\n                }\n            }\n            // 6. Log API access\n            _security__WEBPACK_IMPORTED_MODULE_1__.SecurityLogger.logDataAccess(path, ip);\n            // Call the actual handler\n            const response = await handler(request);\n            // Add security headers to response\n            if (response instanceof next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse) {\n                response.headers.set('X-Content-Type-Options', 'nosniff');\n                response.headers.set('X-Frame-Options', 'DENY');\n                response.headers.set('X-XSS-Protection', '1; mode=block');\n                response.headers.set('Cache-Control', 'no-store, max-age=0');\n                response.headers.set('X-RateLimit-Remaining', String(apiRateLimiter.getRemainingRequests(ip)));\n            }\n            return response;\n        } catch (error) {\n            _security__WEBPACK_IMPORTED_MODULE_1__.SecurityLogger.log('API_ERROR', {\n                path,\n                ip,\n                error: String(error)\n            }, 'error');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Internal server error'\n            }, {\n                status: 500\n            });\n        }\n    };\n}\n/**\n * Admin API Security Middleware\n */ function withAdminSecurity(handler) {\n    return withApiSecurity(async (request)=>{\n        const ip = getClientIP(request);\n        // Additional admin-specific security checks\n        // 1. Admin rate limiting (stricter)\n        if (!authRateLimiter.isAllowed(ip)) {\n            _security__WEBPACK_IMPORTED_MODULE_1__.SecurityLogger.logSuspiciousActivity(ip, 'Admin rate limit exceeded');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Too many admin requests. Please try again later.'\n            }, {\n                status: 429\n            });\n        }\n        // 2. Admin authentication check (basic implementation)\n        const authHeader = request.headers.get('authorization');\n        if (!authHeader || !authHeader.startsWith('Bearer ')) {\n            _security__WEBPACK_IMPORTED_MODULE_1__.SecurityLogger.logFailedLogin(ip, request.headers.get('user-agent') || 'unknown');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        // 3. Log admin access\n        _security__WEBPACK_IMPORTED_MODULE_1__.SecurityLogger.log('ADMIN_ACCESS', {\n            path: request.nextUrl.pathname,\n            ip,\n            method: request.method\n        }, 'info');\n        return handler(request);\n    });\n}\n/**\n * Input Validation Middleware\n */ function validateInput(schema) {\n    return (handler)=>{\n        return async (request)=>{\n            // Skip validation for GET requests\n            if (![\n                'POST',\n                'PUT',\n                'PATCH'\n            ].includes(request.method)) {\n                return handler(request);\n            }\n            try {\n                // Clone the request to avoid body consumption issues\n                const clonedRequest = request.clone();\n                const body = await clonedRequest.json();\n                // Validate each field according to schema\n                for (const [field, validator] of Object.entries(schema)){\n                    const value = body[field];\n                    if (typeof validator === 'function' && !validator(value)) {\n                        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                            error: `Invalid ${field}`\n                        }, {\n                            status: 400\n                        });\n                    }\n                }\n                // Add validated body to request for later use\n                ;\n                request.validatedBody = body;\n            } catch (error) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Invalid JSON'\n                }, {\n                    status: 400\n                });\n            }\n            return handler(request);\n        };\n    };\n}\n/**\n * CORS Middleware\n */ function withCORS(handler, options = {}) {\n    return async (request)=>{\n        const origin = request.headers.get('origin');\n        const allowedOrigins = options.origin || [\n            'https://localhost:3443',\n            'https://foreingate.com',\n            'https://www.foreingate.com'\n        ];\n        // Handle preflight requests\n        if (request.method === 'OPTIONS') {\n            const response = new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(null, {\n                status: 200\n            });\n            if (origin && allowedOrigins.includes(origin)) {\n                response.headers.set('Access-Control-Allow-Origin', origin);\n            }\n            response.headers.set('Access-Control-Allow-Methods', options.methods?.join(', ') || 'GET, POST, PUT, DELETE');\n            response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');\n            if (options.credentials) {\n                response.headers.set('Access-Control-Allow-Credentials', 'true');\n            }\n            return response;\n        }\n        const response = await handler(request);\n        // Add CORS headers to actual response\n        if (response instanceof next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse) {\n            if (origin && allowedOrigins.includes(origin)) {\n                response.headers.set('Access-Control-Allow-Origin', origin);\n            }\n            if (options.credentials) {\n                response.headers.set('Access-Control-Allow-Credentials', 'true');\n            }\n        }\n        return response;\n    };\n}\n/**\n * Get client IP address\n */ function getClientIP(request) {\n    const forwarded = request.headers.get('x-forwarded-for');\n    const realIP = request.headers.get('x-real-ip');\n    const remoteAddr = request.ip;\n    if (forwarded) {\n        return forwarded.split(',')[0].trim();\n    }\n    return realIP || remoteAddr || 'unknown';\n}\n/**\n * Sanitize API response\n */ function sanitizeResponse(data) {\n    if (typeof data === 'string') {\n        return data.replace(/<script\\b[^<]*(?:(?!<\\/script>)<[^<]*)*<\\/script>/gi, '');\n    }\n    if (Array.isArray(data)) {\n        return data.map(sanitizeResponse);\n    }\n    if (typeof data === 'object' && data !== null) {\n        const sanitized = {};\n        for (const [key, value] of Object.entries(data)){\n            sanitized[key] = sanitizeResponse(value);\n        }\n        return sanitized;\n    }\n    return data;\n}\n/**\n * API Response wrapper with security\n */ function secureResponse(data, status = 200) {\n    const sanitizedData = sanitizeResponse(data);\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(sanitizedData, {\n        status,\n        headers: {\n            'X-Content-Type-Options': 'nosniff',\n            'X-Frame-Options': 'DENY',\n            'Cache-Control': 'no-store, max-age=0',\n            'X-XSS-Protection': '1; mode=block'\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/api-security.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/security.ts":
/*!*****************************!*\
  !*** ./src/lib/security.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RateLimiter: () => (/* binding */ RateLimiter),\n/* harmony export */   SecureSession: () => (/* binding */ SecureSession),\n/* harmony export */   SecurityLogger: () => (/* binding */ SecurityLogger),\n/* harmony export */   ValidationSchemas: () => (/* binding */ ValidationSchemas),\n/* harmony export */   decryptData: () => (/* binding */ decryptData),\n/* harmony export */   encryptData: () => (/* binding */ encryptData),\n/* harmony export */   generateCSRFToken: () => (/* binding */ generateCSRFToken),\n/* harmony export */   generateSecureToken: () => (/* binding */ generateSecureToken),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   isValidEmail: () => (/* binding */ isValidEmail),\n/* harmony export */   isValidPhone: () => (/* binding */ isValidPhone),\n/* harmony export */   sanitizeInput: () => (/* binding */ sanitizeInput),\n/* harmony export */   verifyCSRFToken: () => (/* binding */ verifyCSRFToken),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword)\n/* harmony export */ });\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(crypto__WEBPACK_IMPORTED_MODULE_0__);\n\n// Security utilities for the application\n/**\n * Generate a secure random string\n */ function generateSecureToken(length = 32) {\n    return crypto__WEBPACK_IMPORTED_MODULE_0___default().randomBytes(length).toString('hex');\n}\n/**\n * Hash a password using PBKDF2\n */ async function hashPassword(password) {\n    const salt = crypto__WEBPACK_IMPORTED_MODULE_0___default().randomBytes(16).toString('hex');\n    const hash = crypto__WEBPACK_IMPORTED_MODULE_0___default().pbkdf2Sync(password, salt, 10000, 64, 'sha512').toString('hex');\n    return `${salt}:${hash}`;\n}\n/**\n * Verify a password against its hash\n */ async function verifyPassword(password, hashedPassword) {\n    const [salt, hash] = hashedPassword.split(':');\n    const verifyHash = crypto__WEBPACK_IMPORTED_MODULE_0___default().pbkdf2Sync(password, salt, 10000, 64, 'sha512').toString('hex');\n    return hash === verifyHash;\n}\n/**\n * Encrypt sensitive data\n */ function encryptData(data, key) {\n    const encryptionKey = key || process.env.ENCRYPTION_KEY || 'default-key-change-in-production';\n    const algorithm = 'aes-256-gcm';\n    const iv = crypto__WEBPACK_IMPORTED_MODULE_0___default().randomBytes(16);\n    const cipher = crypto__WEBPACK_IMPORTED_MODULE_0___default().createCipher(algorithm, encryptionKey);\n    let encrypted = cipher.update(data, 'utf8', 'hex');\n    encrypted += cipher.final('hex');\n    return `${iv.toString('hex')}:${encrypted}`;\n}\n/**\n * Decrypt sensitive data\n */ function decryptData(encryptedData, key) {\n    const encryptionKey = key || process.env.ENCRYPTION_KEY || 'default-key-change-in-production';\n    const algorithm = 'aes-256-gcm';\n    const [ivHex, encrypted] = encryptedData.split(':');\n    const iv = Buffer.from(ivHex, 'hex');\n    const decipher = crypto__WEBPACK_IMPORTED_MODULE_0___default().createDecipher(algorithm, encryptionKey);\n    let decrypted = decipher.update(encrypted, 'hex', 'utf8');\n    decrypted += decipher.final('utf8');\n    return decrypted;\n}\n/**\n * Sanitize user input to prevent XSS\n */ function sanitizeInput(input) {\n    return input.replace(/[<>]/g, '') // Remove < and >\n    .replace(/javascript:/gi, '') // Remove javascript: protocol\n    .replace(/on\\w+=/gi, '') // Remove event handlers\n    .trim();\n}\n/**\n * Validate email format\n */ function isValidEmail(email) {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n}\n/**\n * Validate phone number format\n */ function isValidPhone(phone) {\n    const phoneRegex = /^\\+?[\\d\\s\\-\\(\\)]{10,}$/;\n    return phoneRegex.test(phone);\n}\n/**\n * Generate CSRF token\n */ function generateCSRFToken() {\n    return crypto__WEBPACK_IMPORTED_MODULE_0___default().randomBytes(32).toString('hex');\n}\n/**\n * Verify CSRF token\n */ function verifyCSRFToken(token, sessionToken) {\n    return crypto__WEBPACK_IMPORTED_MODULE_0___default().timingSafeEqual(Buffer.from(token, 'hex'), Buffer.from(sessionToken, 'hex'));\n}\n/**\n * Rate limiting helper\n */ class RateLimiter {\n    constructor(maxRequests = 100, windowMs = 60000){\n        this.requests = new Map();\n        this.maxRequests = maxRequests;\n        this.windowMs = windowMs;\n    }\n    isAllowed(identifier) {\n        const now = Date.now();\n        const requests = this.requests.get(identifier) || [];\n        // Remove old requests outside the window\n        const validRequests = requests.filter((time)=>now - time < this.windowMs);\n        if (validRequests.length >= this.maxRequests) {\n            return false;\n        }\n        validRequests.push(now);\n        this.requests.set(identifier, validRequests);\n        return true;\n    }\n    getRemainingRequests(identifier) {\n        const requests = this.requests.get(identifier) || [];\n        const now = Date.now();\n        const validRequests = requests.filter((time)=>now - time < this.windowMs);\n        return Math.max(0, this.maxRequests - validRequests.length);\n    }\n}\n/**\n * Secure session management\n */ class SecureSession {\n    static{\n        this.sessions = new Map();\n    }\n    static create(data) {\n        const sessionId = generateSecureToken(32);\n        const sessionData = {\n            ...data,\n            createdAt: Date.now(),\n            lastAccessed: Date.now()\n        };\n        this.sessions.set(sessionId, sessionData);\n        return sessionId;\n    }\n    static get(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return null;\n        // Check if session is expired (24 hours)\n        if (Date.now() - session.lastAccessed > 24 * 60 * 60 * 1000) {\n            this.sessions.delete(sessionId);\n            return null;\n        }\n        // Update last accessed time\n        session.lastAccessed = Date.now();\n        return session;\n    }\n    static destroy(sessionId) {\n        this.sessions.delete(sessionId);\n    }\n    static cleanup() {\n        const now = Date.now();\n        for (const [sessionId, session] of this.sessions.entries()){\n            if (now - session.lastAccessed > 24 * 60 * 60 * 1000) {\n                this.sessions.delete(sessionId);\n            }\n        }\n    }\n}\n/**\n * Input validation schemas\n */ const ValidationSchemas = {\n    email: (email)=>{\n        if (!email || email.length > 254) return false;\n        return isValidEmail(email);\n    },\n    name: (name)=>{\n        if (!name || name.length < 2 || name.length > 100) return false;\n        return /^[a-zA-Z\\s\\-'\\.]+$/.test(name);\n    },\n    phone: (phone)=>{\n        if (!phone) return true // Optional field\n        ;\n        return isValidPhone(phone);\n    },\n    message: (message)=>{\n        if (!message || message.length < 10 || message.length > 5000) return false;\n        return true;\n    },\n    subject: (subject)=>{\n        if (!subject || subject.length < 5 || subject.length > 200) return false;\n        return true;\n    }\n};\n/**\n * Security audit logger\n */ class SecurityLogger {\n    static log(event, details, level = 'info') {\n        const logEntry = {\n            timestamp: new Date().toISOString(),\n            event,\n            details,\n            level\n        };\n        // In production, send to logging service\n        if (false) {} else {\n            console.log(`[SECURITY ${level.toUpperCase()}]`, event, details);\n        }\n    }\n    static logFailedLogin(ip, userAgent) {\n        this.log('FAILED_LOGIN', {\n            ip,\n            userAgent\n        }, 'warn');\n    }\n    static logSuspiciousActivity(ip, activity) {\n        this.log('SUSPICIOUS_ACTIVITY', {\n            ip,\n            activity\n        }, 'error');\n    }\n    static logDataAccess(resource, ip) {\n        this.log('DATA_ACCESS', {\n            resource,\n            ip\n        }, 'info');\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/security.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "async_hooks":
/*!******************************!*\
  !*** external "async_hooks" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("async_hooks");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "fs/promises":
/*!******************************!*\
  !*** external "fs/promises" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("fs/promises");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "prettier/plugins/html":
/*!****************************************!*\
  !*** external "prettier/plugins/html" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = import("prettier/plugins/html");;

/***/ }),

/***/ "prettier/standalone":
/*!**************************************!*\
  !*** external "prettier/standalone" ***!
  \**************************************/
/***/ ((module) => {

"use strict";
module.exports = import("prettier/standalone");;

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/resend"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcontact%2Froute&page=%2Fapi%2Fcontact%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcontact%2Froute.ts&appDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();