"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"ea310ccf5901\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXE5pZGhhbFxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxmb3JlaW5nYXRlX2dyb3VwZVxcZm9yZWluZ2F0ZS13ZWJzaXRlXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJlYTMxMGNjZjU5MDFcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/layout/navigation.tsx":
/*!**********************************************!*\
  !*** ./src/components/layout/navigation.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Navigation: () => (/* binding */ Navigation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/../node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/../node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Mail_Menu_Phone_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Mail,Menu,Phone,X!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Mail_Menu_Phone_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Mail,Menu,Phone,X!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Mail_Menu_Phone_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Mail,Menu,Phone,X!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Mail_Menu_Phone_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Mail,Menu,Phone,X!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Mail_Menu_Phone_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Mail,Menu,Phone,X!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Mail_Menu_Phone_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Mail,Menu,Phone,X!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_theme_toggle__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/theme-toggle */ \"(app-pages-browser)/./src/components/ui/theme-toggle.tsx\");\n/* harmony import */ var _components_ui_search__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/search */ \"(app-pages-browser)/./src/components/ui/search.tsx\");\n/* harmony import */ var _components_ui_language_switcher__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/language-switcher */ \"(app-pages-browser)/./src/components/ui/language-switcher.tsx\");\n/* harmony import */ var _hooks_use_translation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/use-translation */ \"(app-pages-browser)/./src/hooks/use-translation.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Navigation auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n// Navigation items will be translated\nconst getNavigationItems = (t)=>[\n        {\n            name: t.nav.home,\n            href: '/'\n        },\n        {\n            name: t.nav.about,\n            href: '/about'\n        },\n        {\n            name: t.nav.services,\n            href: '/services',\n            submenu: [\n                {\n                    name: 'University Admissions',\n                    href: '/services/admissions'\n                },\n                {\n                    name: 'Visa Support',\n                    href: '/services/visa'\n                },\n                {\n                    name: 'Student Housing',\n                    href: '/services/housing'\n                },\n                {\n                    name: 'Document Translation',\n                    href: '/services/translation'\n                },\n                {\n                    name: 'Airport Pickup',\n                    href: '/services/pickup'\n                },\n                {\n                    name: 'Academic Support',\n                    href: '/services/academic'\n                }\n            ]\n        },\n        {\n            name: t.nav.universities,\n            href: '/universities'\n        },\n        {\n            name: t.nav.programs,\n            href: '/programs'\n        },\n        {\n            name: t.nav.blog,\n            href: '/blog'\n        },\n        {\n            name: t.nav.contact,\n            href: '/contact'\n        }\n    ];\nfunction Navigation() {\n    _s();\n    const [isOpen, setIsOpen] = react__WEBPACK_IMPORTED_MODULE_1__.useState(false);\n    const [activeSubmenu, setActiveSubmenu] = react__WEBPACK_IMPORTED_MODULE_1__.useState(null);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const { t } = (0,_hooks_use_translation__WEBPACK_IMPORTED_MODULE_8__.useTranslation)();\n    const navigationItems = getNavigationItems(t);\n    const toggleMenu = ()=>setIsOpen(!isOpen);\n    const closeMenu = ()=>setIsOpen(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden lg:block border-b bg-muted/50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex h-10 items-center justify-between text-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Mail_Menu_Phone_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-3 w-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                                lineNumber: 56,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"+90 ************\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                                lineNumber: 57,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Mail_Menu_Phone_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-3 w-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                                lineNumber: 60,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"<EMAIL>\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                                lineNumber: 61,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                        lineNumber: 59,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_language_switcher__WEBPACK_IMPORTED_MODULE_7__.LanguageSwitcher, {\n                                        variant: \"minimal\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                        lineNumber: 65,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_theme_toggle__WEBPACK_IMPORTED_MODULE_5__.ThemeToggle, {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                lineNumber: 51,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex h-16 items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-8 w-8 rounded-lg bg-primary flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-primary-foreground font-bold text-lg\",\n                                        children: \"F\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-bold text-xl\",\n                                    children: \"Foreingate Group\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"hidden lg:flex items-center space-x-8\",\n                            children: navigationItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    onMouseEnter: ()=>item.submenu && setActiveSubmenu(item.name),\n                                    onMouseLeave: ()=>setActiveSubmenu(null),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: item.href,\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.cn)(\"flex items-center space-x-1 text-sm font-medium transition-colors hover:text-primary\", pathname === item.href ? \"text-primary\" : \"text-muted-foreground\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: item.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                                    lineNumber: 99,\n                                                    columnNumber: 19\n                                                }, this),\n                                                item.submenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Mail_Menu_Phone_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                                    lineNumber: 100,\n                                                    columnNumber: 36\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 17\n                                        }, this),\n                                        item.submenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.AnimatePresence, {\n                                            children: activeSubmenu === item.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                                                initial: {\n                                                    opacity: 0,\n                                                    y: 10\n                                                },\n                                                animate: {\n                                                    opacity: 1,\n                                                    y: 0\n                                                },\n                                                exit: {\n                                                    opacity: 0,\n                                                    y: 10\n                                                },\n                                                transition: {\n                                                    duration: 0.2\n                                                },\n                                                className: \"absolute top-full left-0 mt-2 w-64 rounded-md border bg-popover p-2 shadow-lg\",\n                                                children: item.submenu.map((subItem)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: subItem.href,\n                                                        className: \"block rounded-sm px-3 py-2 text-sm hover:bg-accent hover:text-accent-foreground\",\n                                                        children: subItem.name\n                                                    }, subItem.name, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                                        lineNumber: 115,\n                                                        columnNumber: 27\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                                lineNumber: 107,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, item.name, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden lg:flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_search__WEBPACK_IMPORTED_MODULE_6__.HeaderSearch, {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/apply\",\n                                        children: \"Apply Now\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/contact\",\n                                        children: \"Get Started\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            variant: \"ghost\",\n                            size: \"icon\",\n                            className: \"lg:hidden\",\n                            onClick: toggleMenu,\n                            children: isOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Mail_Menu_Phone_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 23\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Mail_Menu_Phone_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                className: \"h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 51\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                            lineNumber: 143,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                    lineNumber: 74,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                lineNumber: 73,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.AnimatePresence, {\n                children: isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        height: 0\n                    },\n                    animate: {\n                        opacity: 1,\n                        height: 'auto'\n                    },\n                    exit: {\n                        opacity: 0,\n                        height: 0\n                    },\n                    transition: {\n                        duration: 0.3\n                    },\n                    className: \"lg:hidden border-t bg-background\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container mx-auto px-4 py-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"flex flex-col space-y-4\",\n                            children: [\n                                navigationItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: item.href,\n                                                onClick: closeMenu,\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.cn)(\"block py-2 text-sm font-medium transition-colors hover:text-primary\", pathname === item.href ? \"text-primary\" : \"text-muted-foreground\"),\n                                                children: item.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 21\n                                            }, this),\n                                            item.submenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-4 mt-2 space-y-2\",\n                                                children: item.submenu.map((subItem)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: subItem.href,\n                                                        onClick: closeMenu,\n                                                        className: \"block py-1 text-sm text-muted-foreground hover:text-primary\",\n                                                        children: subItem.name\n                                                    }, subItem.name, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                                        lineNumber: 181,\n                                                        columnNumber: 27\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, item.name, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 19\n                                    }, this)),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col space-y-2 pt-4 border-t\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"outline\",\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/apply\",\n                                                onClick: closeMenu,\n                                                children: \"Apply Now\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/contact\",\n                                                onClick: closeMenu,\n                                                children: \"Get Started\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between pt-4 border-t\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Mail_Menu_Phone_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                                    lineNumber: 204,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    className: \"bg-transparent text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"en\",\n                                                            children: \"English\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                                            lineNumber: 206,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"ar\",\n                                                            children: \"العربية\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                                            lineNumber: 207,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                                    lineNumber: 205,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_theme_toggle__WEBPACK_IMPORTED_MODULE_5__.ThemeToggle, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                    lineNumber: 157,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                lineNumber: 155,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, this);\n}\n_s(Navigation, \"nSv3u0XRZdfKSVv0A/3dNKuG7Wc=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname,\n        _hooks_use_translation__WEBPACK_IMPORTED_MODULE_8__.useTranslation\n    ];\n});\n_c = Navigation;\nvar _c;\n$RefreshReg$(_c, \"Navigation\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layout/navigation.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/language-switcher.tsx":
/*!*************************************************!*\
  !*** ./src/components/ui/language-switcher.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LanguageSwitcher: () => (/* binding */ LanguageSwitcher)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/../node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/../node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_Globe_Search_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,Globe,Search!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_Globe_Search_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,Globe,Search!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_Globe_Search_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,Globe,Search!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_Globe_Search_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,Globe,Search!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _hooks_use_translation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/use-translation */ \"(app-pages-browser)/./src/hooks/use-translation.ts\");\n/* harmony import */ var _lib_i18n__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/i18n */ \"(app-pages-browser)/./src/lib/i18n.ts\");\n/* harmony import */ var _button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* __next_internal_client_entry_do_not_use__ LanguageSwitcher auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction LanguageSwitcher(param) {\n    let { variant = 'default', showFlag = true, showNativeName = true, className = '' } = param;\n    _s();\n    const { locale, setLocale, t } = (0,_hooks_use_translation__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const dropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Close dropdown when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LanguageSwitcher.useEffect\": ()=>{\n            function handleClickOutside(event) {\n                if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n                    setIsOpen(false);\n                    setSearchQuery('');\n                }\n            }\n            document.addEventListener('mousedown', handleClickOutside);\n            return ({\n                \"LanguageSwitcher.useEffect\": ()=>document.removeEventListener('mousedown', handleClickOutside)\n            })[\"LanguageSwitcher.useEffect\"];\n        }\n    }[\"LanguageSwitcher.useEffect\"], []);\n    // Filter languages based on search query\n    const filteredLocales = _lib_i18n__WEBPACK_IMPORTED_MODULE_3__.locales.filter((loc)=>{\n        const lang = _lib_i18n__WEBPACK_IMPORTED_MODULE_3__.languageNames[loc];\n        const query = searchQuery.toLowerCase();\n        return lang.native.toLowerCase().includes(query) || lang.english.toLowerCase().includes(query) || loc.toLowerCase().includes(query);\n    });\n    // Popular languages (shown first)\n    const popularLocales = [\n        'en',\n        'tr',\n        'ar',\n        'fr',\n        'es',\n        'de',\n        'ru',\n        'zh',\n        'ja',\n        'ko'\n    ];\n    const otherLocales = filteredLocales.filter((loc)=>!popularLocales.includes(loc));\n    const sortedPopularLocales = popularLocales.filter((loc)=>filteredLocales.includes(loc));\n    const handleLanguageChange = (newLocale)=>{\n        setLocale(newLocale);\n        setIsOpen(false);\n        setSearchQuery('');\n    };\n    const currentLanguage = _lib_i18n__WEBPACK_IMPORTED_MODULE_3__.languageNames[locale];\n    if (variant === 'minimal') {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative \".concat(className),\n            ref: dropdownRef,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                    variant: \"ghost\",\n                    size: \"sm\",\n                    onClick: ()=>setIsOpen(!isOpen),\n                    className: \"p-2\",\n                    children: [\n                        showFlag && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-lg\",\n                            children: currentLanguage.flag\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 24\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"ml-1 text-sm font-medium\",\n                            children: locale.toUpperCase()\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.AnimatePresence, {\n                    children: isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: -10\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        exit: {\n                            opacity: 0,\n                            y: -10\n                        },\n                        className: \"absolute top-full right-0 mt-2 w-64 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50 max-h-80 overflow-hidden\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LanguageList, {\n                            locales: filteredLocales,\n                            currentLocale: locale,\n                            onSelect: handleLanguageChange,\n                            showFlag: showFlag,\n                            showNativeName: showNativeName,\n                            searchQuery: searchQuery,\n                            onSearchChange: setSearchQuery\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n            lineNumber: 68,\n            columnNumber: 7\n        }, this);\n    }\n    if (variant === 'compact') {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative \".concat(className),\n            ref: dropdownRef,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                    variant: \"outline\",\n                    size: \"sm\",\n                    onClick: ()=>setIsOpen(!isOpen),\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        showFlag && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: currentLanguage.flag\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 24\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm\",\n                            children: showNativeName ? currentLanguage.native : currentLanguage.english\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_Globe_Search_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"w-4 h-4 transition-transform \".concat(isOpen ? 'rotate-180' : '')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                    lineNumber: 106,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.AnimatePresence, {\n                    children: isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: -10\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        exit: {\n                            opacity: 0,\n                            y: -10\n                        },\n                        className: \"absolute top-full right-0 mt-2 w-80 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50 max-h-96 overflow-hidden\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LanguageList, {\n                            locales: filteredLocales,\n                            currentLocale: locale,\n                            onSelect: handleLanguageChange,\n                            showFlag: showFlag,\n                            showNativeName: showNativeName,\n                            searchQuery: searchQuery,\n                            onSearchChange: setSearchQuery,\n                            showSearch: true,\n                            popularLocales: sortedPopularLocales,\n                            otherLocales: otherLocales\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                    lineNumber: 119,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n            lineNumber: 105,\n            columnNumber: 7\n        }, this);\n    }\n    // Default variant\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative \".concat(className),\n        ref: dropdownRef,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                variant: \"outline\",\n                onClick: ()=>setIsOpen(!isOpen),\n                className: \"flex items-center space-x-2 min-w-[140px] justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_Globe_Search_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 11\n                            }, this),\n                            showFlag && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: currentLanguage.flag\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 24\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm\",\n                                children: showNativeName ? currentLanguage.native : currentLanguage.english\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_Globe_Search_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        className: \"w-4 h-4 transition-transform \".concat(isOpen ? 'rotate-180' : '')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                lineNumber: 149,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.AnimatePresence, {\n                children: isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: -10\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    exit: {\n                        opacity: 0,\n                        y: -10\n                    },\n                    className: \"absolute top-full right-0 mt-2 w-96 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50 max-h-[500px] overflow-hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LanguageList, {\n                        locales: filteredLocales,\n                        currentLocale: locale,\n                        onSelect: handleLanguageChange,\n                        showFlag: showFlag,\n                        showNativeName: showNativeName,\n                        searchQuery: searchQuery,\n                        onSearchChange: setSearchQuery,\n                        showSearch: true,\n                        popularLocales: sortedPopularLocales,\n                        otherLocales: otherLocales\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                    lineNumber: 166,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                lineNumber: 164,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n        lineNumber: 148,\n        columnNumber: 5\n    }, this);\n}\n_s(LanguageSwitcher, \"tRpYha2bw8y87HtsCPyjX4Llcc8=\", false, function() {\n    return [\n        _hooks_use_translation__WEBPACK_IMPORTED_MODULE_2__.useTranslation\n    ];\n});\n_c = LanguageSwitcher;\nfunction LanguageList(param) {\n    let { locales, currentLocale, onSelect, showFlag, showNativeName, searchQuery, onSearchChange, showSearch = false, popularLocales = [], otherLocales = [] } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col\",\n        children: [\n            showSearch && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-3 border-b border-gray-200 dark:border-gray-700\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_Globe_Search_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                            lineNumber: 221,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                            type: \"text\",\n                            placeholder: \"Search languages...\",\n                            value: searchQuery,\n                            onChange: (e)=>onSearchChange(e.target.value),\n                            className: \"pl-10 text-sm\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                            lineNumber: 222,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                    lineNumber: 220,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                lineNumber: 219,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"overflow-y-auto max-h-80\",\n                children: [\n                    popularLocales.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wide bg-gray-50 dark:bg-gray-700\",\n                                children: \"Popular Languages\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 13\n                            }, this),\n                            popularLocales.map((loc)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LanguageItem, {\n                                    locale: loc,\n                                    isSelected: loc === currentLocale,\n                                    onSelect: onSelect,\n                                    showFlag: showFlag,\n                                    showNativeName: showNativeName\n                                }, loc, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 15\n                                }, this))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 11\n                    }, this),\n                    otherLocales.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            popularLocales.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wide bg-gray-50 dark:bg-gray-700\",\n                                children: \"All Languages\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 15\n                            }, this),\n                            otherLocales.map((loc)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LanguageItem, {\n                                    locale: loc,\n                                    isSelected: loc === currentLocale,\n                                    onSelect: onSelect,\n                                    showFlag: showFlag,\n                                    showNativeName: showNativeName\n                                }, loc, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 15\n                                }, this))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                        lineNumber: 253,\n                        columnNumber: 11\n                    }, this),\n                    popularLocales.length === 0 && otherLocales.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 text-center text-gray-500\",\n                        children: \"No languages found\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                        lineNumber: 273,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                lineNumber: 233,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n        lineNumber: 217,\n        columnNumber: 5\n    }, this);\n}\n_c1 = LanguageList;\nfunction LanguageItem(param) {\n    let { locale, isSelected, onSelect, showFlag, showNativeName } = param;\n    const language = _lib_i18n__WEBPACK_IMPORTED_MODULE_3__.languageNames[locale];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.button, {\n        whileHover: {\n            backgroundColor: 'rgba(0, 0, 0, 0.05)'\n        },\n        onClick: ()=>onSelect(locale),\n        className: \"w-full px-3 py-2 text-left flex items-center justify-between hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors \".concat(isSelected ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400' : ''),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-3\",\n                children: [\n                    showFlag && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-lg\",\n                        children: language.flag\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                        lineNumber: 308,\n                        columnNumber: 22\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-medium\",\n                                children: showNativeName ? language.native : language.english\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                                lineNumber: 310,\n                                columnNumber: 11\n                            }, this),\n                            showNativeName && language.native !== language.english && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs text-gray-500\",\n                                children: language.english\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                                lineNumber: 314,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                        lineNumber: 309,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                lineNumber: 307,\n                columnNumber: 7\n            }, this),\n            isSelected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_Globe_Search_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                lineNumber: 318,\n                columnNumber: 22\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n        lineNumber: 300,\n        columnNumber: 5\n    }, this);\n}\n_c2 = LanguageItem;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"LanguageSwitcher\");\n$RefreshReg$(_c1, \"LanguageList\");\n$RefreshReg$(_c2, \"LanguageItem\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/language-switcher.tsx\n"));

/***/ })

});