import { NextRequest, NextResponse } from 'next/server'
import { readFile, writeFile, mkdir } from 'fs/promises'
import { existsSync } from 'fs'
import path from 'path'

const DATA_DIR = path.join(process.cwd(), 'data')
const PROGRAMS_FILE = path.join(DATA_DIR, 'programs.json')

// Ensure data directory exists
async function ensureDataDir() {
  if (!existsSync(DATA_DIR)) {
    await mkdir(DATA_DIR, { recursive: true })
  }
}

// Read programs from file
async function readPrograms() {
  try {
    await ensureDataDir()
    if (!existsSync(PROGRAMS_FILE)) {
      // Create initial data if file doesn't exist
      const initialData = [
        {
          id: '1',
          name: 'Computer Engineering',
          slug: 'computer-engineering',
          universityId: '1',
          universityName: 'Eastern Mediterranean University',
          degree: 'Bachelor',
          field: 'Engineering',
          duration: '4 years',
          language: 'English',
          tuition: 4500,
          currency: 'USD',
          description: 'Comprehensive computer engineering program covering software development, hardware design, and emerging technologies.',
          requirements: [
            'High school diploma with mathematics and physics',
            'IELTS 6.0 or TOEFL 79',
            'SAT or equivalent entrance exam'
          ],
          courses: [
            'Programming Fundamentals',
            'Data Structures and Algorithms',
            'Computer Architecture',
            'Software Engineering',
            'Database Systems',
            'Artificial Intelligence'
          ],
          careerOpportunities: [
            'Software Developer',
            'Systems Engineer',
            'Data Scientist',
            'Cybersecurity Specialist',
            'AI/ML Engineer'
          ],
          accreditation: 'ABET',
          intakes: ['Fall', 'Spring'],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: '2',
          name: 'Business Administration',
          slug: 'business-administration',
          universityId: '2',
          universityName: 'Near East University',
          degree: 'Bachelor',
          field: 'Business',
          duration: '4 years',
          language: 'English',
          tuition: 3800,
          currency: 'USD',
          description: 'Comprehensive business program preparing students for leadership roles in global markets.',
          requirements: [
            'High school diploma',
            'IELTS 6.0 or TOEFL 79',
            'Mathematics background preferred'
          ],
          courses: [
            'Principles of Management',
            'Marketing Management',
            'Financial Accounting',
            'Business Statistics',
            'International Business',
            'Strategic Management'
          ],
          careerOpportunities: [
            'Business Manager',
            'Marketing Specialist',
            'Financial Analyst',
            'Consultant',
            'Entrepreneur'
          ],
          accreditation: 'AACSB',
          intakes: ['Fall', 'Spring', 'Summer'],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: '3',
          name: 'Medicine',
          slug: 'medicine',
          universityId: '2',
          universityName: 'Near East University',
          degree: 'Doctor',
          field: 'Medicine',
          duration: '6 years',
          language: 'English',
          tuition: 12000,
          currency: 'USD',
          description: 'Comprehensive medical education program with hands-on clinical experience and modern facilities.',
          requirements: [
            'High school diploma with biology, chemistry, physics',
            'IELTS 6.5 or TOEFL 85',
            'Medical entrance exam',
            'Interview'
          ],
          courses: [
            'Anatomy',
            'Physiology',
            'Biochemistry',
            'Pathology',
            'Pharmacology',
            'Clinical Medicine'
          ],
          careerOpportunities: [
            'General Practitioner',
            'Specialist Doctor',
            'Surgeon',
            'Researcher',
            'Medical Consultant'
          ],
          accreditation: 'WHO/WFME',
          intakes: ['Fall'],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: '4',
          name: 'Architecture',
          slug: 'architecture',
          universityId: '1',
          universityName: 'Eastern Mediterranean University',
          degree: 'Bachelor',
          field: 'Architecture',
          duration: '5 years',
          language: 'English',
          tuition: 5200,
          currency: 'USD',
          description: 'Creative and technical architecture program combining design theory with practical application.',
          requirements: [
            'High school diploma',
            'IELTS 6.0 or TOEFL 79',
            'Portfolio submission',
            'Aptitude test'
          ],
          courses: [
            'Architectural Design',
            'Building Technology',
            'Urban Planning',
            'Structural Systems',
            'Environmental Design',
            'Digital Design'
          ],
          careerOpportunities: [
            'Architect',
            'Urban Planner',
            'Interior Designer',
            'Project Manager',
            'Design Consultant'
          ],
          accreditation: 'NAAB',
          intakes: ['Fall'],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      ]
      await writeFile(PROGRAMS_FILE, JSON.stringify(initialData, null, 2))
      return initialData
    }
    const data = await readFile(PROGRAMS_FILE, 'utf-8')
    return JSON.parse(data)
  } catch (error) {
    console.error('Error reading programs:', error)
    return []
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const degrees = searchParams.get('degrees')?.split(',')
    const fields = searchParams.get('fields')?.split(',')
    const languages = searchParams.get('languages')?.split(',')
    const universities = searchParams.get('universities')?.split(',')
    const search = searchParams.get('search')
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')

    let programs = await readPrograms()

    // Apply filters
    if (degrees && degrees.length > 0) {
      programs = programs.filter((program: any) => 
        degrees.includes(program.degree)
      )
    }

    if (fields && fields.length > 0) {
      programs = programs.filter((program: any) => 
        fields.includes(program.field)
      )
    }

    if (languages && languages.length > 0) {
      programs = programs.filter((program: any) => 
        languages.includes(program.language)
      )
    }

    if (universities && universities.length > 0) {
      programs = programs.filter((program: any) => 
        universities.includes(program.universityId) || 
        universities.includes(program.universityName)
      )
    }

    if (search) {
      programs = programs.filter((program: any) =>
        program.name.toLowerCase().includes(search.toLowerCase()) ||
        program.description.toLowerCase().includes(search.toLowerCase()) ||
        program.field.toLowerCase().includes(search.toLowerCase())
      )
    }

    // Pagination
    const total = programs.length
    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit
    const paginatedPrograms = programs.slice(startIndex, endIndex)

    return NextResponse.json({
      success: true,
      data: paginatedPrograms,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: endIndex < total,
        hasPrev: page > 1
      }
    })

  } catch (error) {
    console.error('Programs API error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch programs' },
      { status: 500 }
    )
  }
}
