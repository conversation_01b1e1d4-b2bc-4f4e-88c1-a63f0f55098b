'use client'

import { useState, useEffect, ReactNode } from 'react'
import { Locale, defaultLocale, isValidLocale, getLocaleDirection } from '@/lib/i18n'
import { TranslationContext, getTranslations } from '@/hooks/use-translation'

interface TranslationProviderProps {
  children: ReactNode
  initialLocale?: Locale
}

export function TranslationProvider({ 
  children, 
  initialLocale = defaultLocale 
}: TranslationProviderProps) {
  const [locale, setLocaleState] = useState<Locale>(initialLocale)
  const [isClient, setIsClient] = useState(false)

  // Hydration fix
  useEffect(() => {
    setIsClient(true)
    
    // Load locale from localStorage or browser preference
    const savedLocale = localStorage.getItem('locale')
    const browserLocale = navigator.language.split('-')[0]
    
    let preferredLocale = defaultLocale
    
    if (savedLocale && isValidLocale(savedLocale)) {
      preferredLocale = savedLocale as Locale
    } else if (isValidLocale(browserLocale)) {
      preferredLocale = browserLocale as Locale
    }
    
    if (preferredLocale !== locale) {
      setLocaleState(preferredLocale)
    }
  }, [locale])

  const setLocale = (newLocale: Locale) => {
    if (isValidLocale(newLocale)) {
      setLocaleState(newLocale)
      localStorage.setItem('locale', newLocale)
      
      // Update document attributes
      document.documentElement.lang = newLocale
      document.documentElement.dir = getLocaleDirection(newLocale)
      
      // Update page title if needed
      const currentTitle = document.title
      if (currentTitle.includes('Foreingate')) {
        // You can add locale-specific title updates here
      }
    }
  }

  // Update document attributes when locale changes
  useEffect(() => {
    if (isClient) {
      document.documentElement.lang = locale
      document.documentElement.dir = getLocaleDirection(locale)
    }
  }, [locale, isClient])

  const translations = getTranslations(locale)
  const isRTL = getLocaleDirection(locale) === 'rtl'

  const contextValue = {
    locale,
    setLocale,
    t: translations,
    isRTL
  }

  return (
    <TranslationContext.Provider value={contextValue}>
      {children}
    </TranslationContext.Provider>
  )
}
