'use client'

import * as React from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { motion, AnimatePresence } from 'framer-motion'
import { Menu, X, ChevronDown, Globe, Phone, Mail } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { ThemeToggle } from '@/components/ui/theme-toggle'
import { HeaderSearch } from '@/components/ui/search'
import { LanguageSwitcher } from '@/components/ui/language-switcher'
import { useTranslation } from '@/hooks/use-translation'
import { cn } from '@/lib/utils'

// Navigation items will be translated
const getNavigationItems = (t: any) => [
  { name: t.nav.home, href: '/' },
  { name: t.nav.about, href: '/about' },
  {
    name: t.nav.services,
    href: '/services',
    submenu: [
      { name: 'University Admissions', href: '/services/admissions' },
      { name: 'Visa Support', href: '/services/visa' },
      { name: 'Student Housing', href: '/services/housing' },
      { name: 'Document Translation', href: '/services/translation' },
      { name: 'Airport Pickup', href: '/services/pickup' },
      { name: 'Academic Support', href: '/services/academic' },
    ]
  },
  { name: t.nav.universities, href: '/universities' },
  { name: t.nav.programs, href: '/programs' },
  { name: t.nav.blog, href: '/blog' },
  { name: t.nav.contact, href: '/contact' },
]

export function Navigation() {
  const [isOpen, setIsOpen] = React.useState(false)
  const [activeSubmenu, setActiveSubmenu] = React.useState<string | null>(null)
  const pathname = usePathname()
  const { t } = useTranslation()

  const navigationItems = getNavigationItems(t)

  const toggleMenu = () => setIsOpen(!isOpen)
  const closeMenu = () => setIsOpen(false)

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      {/* Top Bar */}
      <div className="hidden lg:block border-b bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="flex h-10 items-center justify-between text-sm">
            <div className="flex items-center space-x-6">
              <div className="flex items-center space-x-2">
                <Phone className="h-3 w-3" />
                <span>+90 ************</span>
              </div>
              <div className="flex items-center space-x-2">
                <Mail className="h-3 w-3" />
                <span><EMAIL></span>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <LanguageSwitcher variant="minimal" />
              <ThemeToggle />
            </div>
          </div>
        </div>
      </div>

      {/* Main Navigation */}
      <div className="container mx-auto px-4">
        <div className="flex h-16 items-center justify-between">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2">
            <div className="h-8 w-8 rounded-lg bg-primary flex items-center justify-center">
              <span className="text-primary-foreground font-bold text-lg">F</span>
            </div>
            <span className="font-bold text-xl">Foreingate Group</span>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center space-x-8">
            {navigationItems.map((item) => (
              <div
                key={item.name}
                className="relative"
                onMouseEnter={() => item.submenu && setActiveSubmenu(item.name)}
                onMouseLeave={() => setActiveSubmenu(null)}
              >
                <Link
                  href={item.href}
                  className={cn(
                    "flex items-center space-x-1 text-sm font-medium transition-colors hover:text-primary",
                    pathname === item.href ? "text-primary" : "text-muted-foreground"
                  )}
                >
                  <span>{item.name}</span>
                  {item.submenu && <ChevronDown className="h-3 w-3" />}
                </Link>

                {/* Submenu */}
                {item.submenu && (
                  <AnimatePresence>
                    {activeSubmenu === item.name && (
                      <motion.div
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: 10 }}
                        transition={{ duration: 0.2 }}
                        className="absolute top-full left-0 mt-2 w-64 rounded-md border bg-popover p-2 shadow-lg"
                      >
                        {item.submenu.map((subItem) => (
                          <Link
                            key={subItem.name}
                            href={subItem.href}
                            className="block rounded-sm px-3 py-2 text-sm hover:bg-accent hover:text-accent-foreground"
                          >
                            {subItem.name}
                          </Link>
                        ))}
                      </motion.div>
                    )}
                  </AnimatePresence>
                )}
              </div>
            ))}
          </nav>

          {/* Search and CTA Buttons */}
          <div className="hidden lg:flex items-center space-x-4">
            <HeaderSearch />
            <Button variant="outline" asChild>
              <Link href="/apply">{t.nav.applyNow}</Link>
            </Button>
            <Button asChild>
              <Link href="/contact">{t.nav.getStarted}</Link>
            </Button>
          </div>

          {/* Mobile Menu Button */}
          <Button
            variant="ghost"
            size="icon"
            className="lg:hidden"
            onClick={toggleMenu}
          >
            {isOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
          </Button>
        </div>
      </div>

      {/* Mobile Navigation */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
            className="lg:hidden border-t bg-background"
          >
            <div className="container mx-auto px-4 py-4">
              <nav className="flex flex-col space-y-4">
                {navigationItems.map((item) => (
                  <div key={item.name}>
                    <Link
                      href={item.href}
                      onClick={closeMenu}
                      className={cn(
                        "block py-2 text-sm font-medium transition-colors hover:text-primary",
                        pathname === item.href ? "text-primary" : "text-muted-foreground"
                      )}
                    >
                      {item.name}
                    </Link>
                    {item.submenu && (
                      <div className="ml-4 mt-2 space-y-2">
                        {item.submenu.map((subItem) => (
                          <Link
                            key={subItem.name}
                            href={subItem.href}
                            onClick={closeMenu}
                            className="block py-1 text-sm text-muted-foreground hover:text-primary"
                          >
                            {subItem.name}
                          </Link>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
                <div className="flex flex-col space-y-2 pt-4 border-t">
                  <Button variant="outline" asChild>
                    <Link href="/apply" onClick={closeMenu}>{t.nav.applyNow}</Link>
                  </Button>
                  <Button asChild>
                    <Link href="/contact" onClick={closeMenu}>{t.nav.getStarted}</Link>
                  </Button>
                </div>
                <div className="flex items-center justify-between pt-4 border-t">
                  <LanguageSwitcher variant="compact" />
                  <ThemeToggle />
                </div>
              </nav>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </header>
  )
}
