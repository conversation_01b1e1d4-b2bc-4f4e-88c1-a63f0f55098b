import { Translations } from '../lib/translations'

export const tr: Translations = {
  nav: {
    home: 'Ana Sayfa',
    about: '<PERSON><PERSON><PERSON><PERSON>m<PERSON><PERSON>',
    services: 'Hizmetler',
    universities: 'Üniversiteler',
    programs: 'Programlar',
    blog: 'Blog',
    contact: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    applyNow: 'Başvu<PERSON>',
    getStarted: 'Ba<PERSON><PERSON>',
    language: 'Dil'
  },
  
  hero: {
    title: '<PERSON><PERSON><PERSON>\'ta Dünya Standartlarında Eğitime Açılan Kapınız',
    subtitle: 'Güvenle Yurtdışında Eğitim',
    description: '<PERSON><PERSON><PERSON>brı<PERSON>\'taki en iyi üniversitelerde kaliteli eğitim arayan uluslararası öğrenciler için uzman rehberlik. Başvurudan mezuniyete kadar her adımda yanınızdayız.',
    ctaPrimary: 'Yolculuğuna Başla',
    ctaSecondary: 'Üniversiteleri Keşfet',
    trustBadge: 'Dünya Çapında 10.000+ Öğrenci Tarafından Güvenilir',
    studentsServed: 'Hizmet Verilen Öğrenci',
    successRate: 'Başarı Oranı',
    yearsExperience: 'Yıl Deneyim'
  },
  
  about: {
    title: 'Foreingate Group Hakkında',
    subtitle: 'Güvenilir Eğitim Ortağınız',
    description: 'Uluslararası öğrencilerin Kuzey Kıbrıs\'taki akademik hayallerini gerçekleştirmelerine yardımcı olan önde gelen eğitim danışmanlığıyız.',
    mission: 'Öğrencilerin uluslararası akademik yolculuklarında başarılı olmalarını sağlayan kapsamlı eğitim rehberliği ve destek hizmetleri sunmak.',
    vision: 'Dünya çapındaki öğrencileri Kuzey Kıbrıs\'taki kaliteli eğitim fırsatlarıyla buluşturan en güvenilir köprü olmak.',
    values: 'Mükemmellik, Dürüstlük, İnovasyon ve Öğrenci Başarısı',
    whyChooseUs: 'Neden Bizi Seçmelisiniz',
    experience: '15+ Yıl Deneyim',
    expertise: 'Uzman Rehberlik',
    support: '7/24 Destek',
    success: '%98 Başarı Oranı'
  },
  
  services: {
    title: 'Hizmetlerimiz',
    subtitle: 'Eğitim Yolculuğunuz İçin Kapsamlı Destek',
    universitySelection: 'Üniversite Seçimi',
    universitySelectionDesc: 'Hedefleriniz ve tercihlerinize göre doğru üniversite ve programı seçmek için uzman rehberlik.',
    admissionGuidance: 'Kabul Rehberliği',
    admissionGuidanceDesc: 'Belge hazırlığından başvuru sürecine kadar tam destek.',
    visaSupport: 'Vize Desteği',
    visaSupportDesc: 'Vize başvuruları ve göçmenlik prosedürleri için profesyonel yardım.',
    accommodationHelp: 'Konaklama Yardımı',
    accommodationHelpDesc: 'Konaklama hizmetlerimizle üniversitenizin yakınında uygun barınma seçenekleri bulun.',
    scholarshipAssistance: 'Burs Yardımı',
    scholarshipAssistanceDesc: 'Burs ve mali yardım fırsatlarını belirleyin ve başvurun.',
    ongoingSupport: 'Sürekli Destek',
    ongoingSupportDesc: 'Gelişinizden mezuniyetinize kadar eğitiminiz boyunca sürekli destek.'
  },
  
  universities: {
    title: 'Partner Üniversiteler',
    subtitle: 'Kuzey Kıbrıs\'ın En İyi Üniversiteleri',
    emu: 'Doğu Akdeniz Üniversitesi',
    neu: 'Yakın Doğu Üniversitesi',
    ciu: 'Kıbrıs Uluslararası Üniversitesi',
    programs: 'Program',
    students: 'Öğrenci',
    established: 'Kuruluş',
    accreditation: 'Akreditasyon',
    tuitionFrom: 'Öğrenim ücreti',
    learnMore: 'Daha Fazla Bilgi',
    applyNow: 'Başvur'
  },
  
  programs: {
    title: 'Eğitim Programları',
    subtitle: 'İlgi Alanlarınıza Uygun Çeşitli Akademik Programlar',
    engineering: 'Mühendislik',
    medicine: 'Tıp',
    business: 'İşletme',
    arts: 'Sanat ve Beşeri Bilimler',
    sciences: 'Fen Bilimleri',
    law: 'Hukuk',
    architecture: 'Mimarlık',
    education: 'Eğitim',
    duration: 'Süre',
    language: 'Dil',
    degree: 'Derece',
    bachelor: 'Lisans',
    master: 'Yüksek Lisans',
    doctorate: 'Doktora'
  },
  
  testimonials: {
    title: 'Öğrenci Başarı Hikayeleri',
    subtitle: 'Başarılı Öğrencilerimizden Dinleyin',
    readMore: 'Devamını Oku',
    showLess: 'Daha Az Göster',
    verified: 'Doğrulanmış Öğrenci',
    graduate: 'Mezun',
    currentStudent: 'Mevcut Öğrenci'
  },
  
  contact: {
    title: 'İletişim',
    subtitle: 'Eğitim Uzmanlarımızla İletişime Geçin',
    getInTouch: 'İletişime Geç',
    name: 'Ad Soyad',
    email: 'E-posta Adresi',
    phone: 'Telefon Numarası',
    message: 'Mesaj',
    subject: 'Konu',
    send: 'Mesaj Gönder',
    sending: 'Gönderiliyor...',
    sent: 'Mesaj Başarıyla Gönderildi!',
    error: 'Mesaj gönderilemedi. Lütfen tekrar deneyin.',
    required: 'Bu alan zorunludur',
    invalidEmail: 'Lütfen geçerli bir e-posta adresi girin',
    office: 'Ofis Saatleri',
    hours: 'Pazartesi - Cuma: 09:00 - 18:00',
    emergency: '7/24 Acil Durum Desteği Mevcuttur'
  },
  
  footer: {
    description: 'Kuzey Kıbrıs\'ta uluslararası eğitim için güvenilir ortağınız. Başvurudan mezuniyete uzman rehberlik.',
    quickLinks: 'Hızlı Bağlantılar',
    services: 'Hizmetler',
    contact: 'İletişim Bilgileri',
    followUs: 'Bizi Takip Edin',
    newsletter: 'Bülten',
    newsletterDesc: 'Üniversiteler, programlar ve burslar hakkında en son güncellemeleri almak için abone olun.',
    subscribe: 'Abone Ol',
    subscribing: 'Abone Oluyor...',
    subscribed: 'Başarıyla Abone Oldunuz!',
    privacy: 'Gizlilik Politikası',
    terms: 'Hizmet Şartları',
    cookies: 'Çerez Politikası',
    sitemap: 'Site Haritası',
    allRightsReserved: 'Tüm hakları saklıdır.'
  },
  
  common: {
    loading: 'Yükleniyor...',
    error: 'Hata',
    success: 'Başarılı',
    warning: 'Uyarı',
    info: 'Bilgi',
    close: 'Kapat',
    cancel: 'İptal',
    confirm: 'Onayla',
    save: 'Kaydet',
    edit: 'Düzenle',
    delete: 'Sil',
    search: 'Ara',
    filter: 'Filtrele',
    sort: 'Sırala',
    next: 'Sonraki',
    previous: 'Önceki',
    page: 'Sayfa',
    of: '/',
    showing: 'Gösteriliyor',
    results: 'sonuç',
    noResults: 'Sonuç bulunamadı',
    tryAgain: 'Tekrar Dene',
    learnMore: 'Daha Fazla Bilgi',
    readMore: 'Devamını Oku',
    showMore: 'Daha Fazla Göster',
    showLess: 'Daha Az Göster',
    viewAll: 'Tümünü Görüntüle',
    backToTop: 'Başa Dön'
  },
  
  chatbot: {
    title: 'Eğitim Asistanı',
    placeholder: 'Üniversiteler, programlar, maliyetler hakkında sor...',
    send: 'Gönder',
    thinking: 'Düşünüyor...',
    error: 'Üzgünüm, bir hatayla karşılaştım. Lütfen tekrar deneyin.',
    retry: 'Tekrar Dene',
    clear: 'Sohbeti Temizle',
    minimize: 'Küçült',
    maximize: 'Büyüt',
    close: 'Kapat',
    greeting: 'Merhaba! Eğitim yolculuğunuzda size yardımcı olmak için buradayım. Ne öğrenmek istiyorsunuz?',
    suggestions: 'Önerilen Sorular',
    typing: 'Yazıyor...',
    offline: 'Çevrimdışı',
    online: 'Çevrimiçi'
  },
  
  forms: {
    firstName: 'Ad',
    lastName: 'Soyad',
    fullName: 'Ad Soyad',
    email: 'E-posta Adresi',
    phone: 'Telefon Numarası',
    country: 'Ülke',
    city: 'Şehir',
    address: 'Adres',
    zipCode: 'Posta Kodu',
    dateOfBirth: 'Doğum Tarihi',
    gender: 'Cinsiyet',
    male: 'Erkek',
    female: 'Kadın',
    other: 'Diğer',
    preferNotToSay: 'Belirtmek istemiyorum',
    nationality: 'Uyruk',
    passportNumber: 'Pasaport Numarası',
    education: 'Eğitim Seviyesi',
    highSchool: 'Lise',
    bachelor: 'Lisans Derecesi',
    master: 'Yüksek Lisans Derecesi',
    doctorate: 'Doktora',
    workExperience: 'İş Deneyimi',
    englishLevel: 'İngilizce Seviyesi',
    beginner: 'Başlangıç',
    intermediate: 'Orta',
    advanced: 'İleri',
    native: 'Ana Dil',
    interestedProgram: 'İlgilenilen Program',
    interestedUniversity: 'İlgilenilen Üniversite',
    startDate: 'Tercih Edilen Başlangıç Tarihi',
    additionalInfo: 'Ek Bilgiler',
    agreeTerms: 'Hizmet Şartlarını kabul ediyorum',
    agreePrivacy: 'Gizlilik Politikasını kabul ediyorum',
    agreeMarketing: 'Pazarlama iletişimi almayı kabul ediyorum',
    submit: 'Gönder',
    submitting: 'Gönderiliyor...',
    submitted: 'Başarıyla Gönderildi!',
    required: 'Zorunlu alan',
    invalid: 'Geçersiz format',
    tooShort: 'Çok kısa',
    tooLong: 'Çok uzun',
    passwordMismatch: 'Şifreler eşleşmiyor'
  },
  
  costs: {
    title: 'Maliyetler ve Burslar',
    subtitle: 'Mali Destek Seçenekleri ile Uygun Fiyatlı Eğitim',
    tuitionFees: 'Öğrenim Ücretleri',
    livingCosts: 'Yaşam Maliyetleri',
    totalCost: 'Toplam Maliyet',
    scholarships: 'Burslar',
    financialAid: 'Mali Yardım',
    paymentPlans: 'Ödeme Planları',
    currency: 'USD',
    perYear: 'yıllık',
    perMonth: 'aylık',
    accommodation: 'Konaklama',
    food: 'Yemek ve Beslenme',
    transportation: 'Ulaşım',
    books: 'Kitap ve Malzemeler',
    personal: 'Kişisel Harcamalar',
    insurance: 'Sağlık Sigortası',
    visa: 'Vize ve Göçmenlik',
    other: 'Diğer Harcamalar',
    meritScholarship: 'Başarı Bursu',
    needBasedAid: 'İhtiyaç Temelli Yardım',
    earlyBird: 'Erken Başvuru İndirimi',
    siblingDiscount: 'Kardeş İndirimi',
    calculate: 'Maliyetleri Hesapla',
    getQuote: 'Teklif Al'
  }
}
