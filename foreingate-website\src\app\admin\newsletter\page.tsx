'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  Search, Download, Edit, Trash2, Send, Mail, 
  UserCheck, UserX, ChevronLeft, ChevronRight,
  MessageSquare, Users, TrendingUp, Calendar
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'

interface Subscriber {
  id: string
  email: string
  subscribedAt: string
  isActive: boolean
  source: string
}

export default function NewsletterAdmin() {
  const [subscribers, setSubscribers] = useState<Subscriber[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedSubscribers, setSelectedSubscribers] = useState<string[]>([])
  const [filters, setFilters] = useState({
    status: 'all',
    search: '',
    page: 1,
    limit: 10,
    sortBy: 'subscribedAt',
    sortOrder: 'desc'
  })
  const [stats, setStats] = useState<any>({})
  const [pagination, setPagination] = useState<any>({})
  const [showCampaignModal, setShowCampaignModal] = useState(false)
  const [campaignData, setCampaignData] = useState({
    subject: '',
    content: '',
    targetAudience: 'active',
    testEmail: ''
  })
  const [bulkAction, setBulkAction] = useState('')
  const [sendingCampaign, setSendingCampaign] = useState(false)

  useEffect(() => {
    fetchSubscribers()
  }, [filters])

  const fetchSubscribers = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        status: filters.status,
        search: filters.search,
        page: filters.page.toString(),
        limit: filters.limit.toString(),
        sortBy: filters.sortBy,
        sortOrder: filters.sortOrder
      })

      const response = await fetch(`/api/newsletter?${params}`)
      const result = await response.json()

      if (result.success) {
        setSubscribers(result.data)
        setStats(result.stats)
        setPagination(result.pagination)
      }
    } catch (error) {
      console.error('Error fetching subscribers:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleBulkAction = async () => {
    if (!bulkAction || selectedSubscribers.length === 0) return

    try {
      const response = await fetch('/api/admin/bulk-operations', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          operation: bulkAction,
          type: 'newsletter',
          ids: selectedSubscribers
        })
      })

      if (response.ok) {
        setSelectedSubscribers([])
        setBulkAction('')
        fetchSubscribers()
      }
    } catch (error) {
      console.error('Error performing bulk action:', error)
    }
  }

  const handleSendCampaign = async (isTest = false) => {
    try {
      setSendingCampaign(true)
      const payload = {
        ...campaignData,
        testEmail: isTest ? campaignData.testEmail : undefined
      }

      const response = await fetch('/api/admin/newsletter-campaign', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
      })

      const result = await response.json()

      if (result.success) {
        alert(result.message)
        if (!isTest) {
          setShowCampaignModal(false)
          setCampaignData({ subject: '', content: '', targetAudience: 'active', testEmail: '' })
        }
      } else {
        alert('Error: ' + result.error)
      }
    } catch (error) {
      console.error('Error sending campaign:', error)
      alert('Failed to send campaign')
    } finally {
      setSendingCampaign(false)
    }
  }

  const exportToCSV = () => {
    const headers = ['Email', 'Status', 'Source', 'Subscribed Date']
    const csvContent = [
      headers.join(','),
      ...subscribers.map(sub => [
        sub.email,
        sub.isActive ? 'Active' : 'Inactive',
        sub.source,
        new Date(sub.subscribedAt).toLocaleDateString()
      ].join(','))
    ].join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `newsletter-subscribers-${new Date().toISOString().split('T')[0]}.csv`
    a.click()
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Newsletter Management</h1>
              <p className="text-gray-600">Manage subscribers and send campaigns</p>
            </div>
            <div className="flex space-x-3">
              <Button onClick={exportToCSV} variant="outline">
                <Download className="w-4 h-4 mr-2" />
                Export CSV
              </Button>
              <Button onClick={() => setShowCampaignModal(true)}>
                <Send className="w-4 h-4 mr-2" />
                Send Campaign
              </Button>
              <Button onClick={fetchSubscribers}>
                Refresh
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-6 mb-8">
          {[
            { title: 'Total', value: stats.total, color: 'text-blue-600', icon: Users },
            { title: 'Active', value: stats.active, color: 'text-green-600', icon: UserCheck },
            { title: 'Inactive', value: stats.inactive, color: 'text-red-600', icon: UserX },
            { title: 'This Week', value: stats.thisWeek, color: 'text-purple-600', icon: TrendingUp },
            { title: 'This Month', value: stats.thisMonth, color: 'text-orange-600', icon: Calendar }
          ].map((stat, index) => (
            <motion.div
              key={stat.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="bg-white rounded-lg shadow p-6"
            >
              <div className="flex items-center">
                <div className="p-2 rounded-lg bg-gray-100">
                  <stat.icon className={`w-5 h-5 ${stat.color}`} />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                  <p className={`text-2xl font-bold ${stat.color}`}>{stat.value || 0}</p>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Filters and Search */}
        <div className="bg-white rounded-lg shadow mb-6">
          <div className="p-6 border-b border-gray-200">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <Input
                  placeholder="Search subscribers..."
                  value={filters.search}
                  onChange={(e) => setFilters({ ...filters, search: e.target.value, page: 1 })}
                  className="w-full"
                />
              </div>
              <Select value={filters.status} onValueChange={(value) => setFilters({ ...filters, status: value, page: 1 })}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Subscribers</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Bulk Actions */}
          {selectedSubscribers.length > 0 && (
            <div className="p-4 bg-blue-50 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <span className="text-sm text-blue-700">
                  {selectedSubscribers.length} subscriber(s) selected
                </span>
                <div className="flex items-center space-x-2">
                  <Select value={bulkAction} onValueChange={setBulkAction}>
                    <SelectTrigger className="w-48">
                      <SelectValue placeholder="Bulk actions" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="activate">Activate</SelectItem>
                      <SelectItem value="deactivate">Deactivate</SelectItem>
                      <SelectItem value="delete">Delete</SelectItem>
                    </SelectContent>
                  </Select>
                  <Button onClick={handleBulkAction} disabled={!bulkAction}>
                    Apply
                  </Button>
                </div>
              </div>
            </div>
          )}

          {/* Subscribers Table */}
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left">
                    <Checkbox
                      checked={selectedSubscribers.length === subscribers.length}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          setSelectedSubscribers(subscribers.map(sub => sub.id))
                        } else {
                          setSelectedSubscribers([])
                        }
                      }}
                    />
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Email
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Source
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Subscribed
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {subscribers.map((subscriber) => (
                  <tr key={subscriber.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <Checkbox
                        checked={selectedSubscribers.includes(subscriber.id)}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            setSelectedSubscribers([...selectedSubscribers, subscriber.id])
                          } else {
                            setSelectedSubscribers(selectedSubscribers.filter(id => id !== subscriber.id))
                          }
                        }}
                      />
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{subscriber.email}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <Badge className={subscriber.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
                        {subscriber.isActive ? 'Active' : 'Inactive'}
                      </Badge>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {subscriber.source}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(subscriber.subscribedAt).toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => window.open(`mailto:${subscriber.email}`)}
                        >
                          <Mail className="w-4 h-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          <div className="px-6 py-4 border-t border-gray-200">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-700">
                Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
                {Math.min(pagination.page * pagination.limit, pagination.total)} of{' '}
                {pagination.total} results
              </div>
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setFilters({ ...filters, page: filters.page - 1 })}
                  disabled={!pagination.hasPrev}
                >
                  <ChevronLeft className="w-4 h-4" />
                  Previous
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setFilters({ ...filters, page: filters.page + 1 })}
                  disabled={!pagination.hasNext}
                >
                  Next
                  <ChevronRight className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Campaign Modal */}
      {showCampaignModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl mx-4">
            <h2 className="text-2xl font-bold mb-4">Send Newsletter Campaign</h2>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Subject</label>
                <Input
                  value={campaignData.subject}
                  onChange={(e) => setCampaignData({ ...campaignData, subject: e.target.value })}
                  placeholder="Enter email subject"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Content</label>
                <Textarea
                  value={campaignData.content}
                  onChange={(e) => setCampaignData({ ...campaignData, content: e.target.value })}
                  placeholder="Enter email content"
                  rows={8}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Target Audience</label>
                <Select 
                  value={campaignData.targetAudience} 
                  onValueChange={(value) => setCampaignData({ ...campaignData, targetAudience: value })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Active Subscribers</SelectItem>
                    <SelectItem value="active">Active Subscribers</SelectItem>
                    <SelectItem value="recent">Recent Subscribers (30 days)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Test Email (Optional)</label>
                <Input
                  value={campaignData.testEmail}
                  onChange={(e) => setCampaignData({ ...campaignData, testEmail: e.target.value })}
                  placeholder="Enter test email address"
                />
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <Button variant="outline" onClick={() => setShowCampaignModal(false)}>
                Cancel
              </Button>
              {campaignData.testEmail && (
                <Button 
                  variant="outline" 
                  onClick={() => handleSendCampaign(true)}
                  disabled={sendingCampaign || !campaignData.subject || !campaignData.content}
                >
                  Send Test
                </Button>
              )}
              <Button 
                onClick={() => handleSendCampaign(false)}
                disabled={sendingCampaign || !campaignData.subject || !campaignData.content}
              >
                {sendingCampaign ? 'Sending...' : 'Send Campaign'}
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
