globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/contact/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/layout/navigation.tsx":{"*":{"id":"(ssr)/./src/components/layout/navigation.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/providers/theme-provider.tsx":{"*":{"id":"(ssr)/./src/components/providers/theme-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/company-story.tsx":{"*":{"id":"(ssr)/./src/components/sections/company-story.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/team-section.tsx":{"*":{"id":"(ssr)/./src/components/sections/team-section.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/vision-mission.tsx":{"*":{"id":"(ssr)/./src/components/sections/vision-mission.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/why-choose-us.tsx":{"*":{"id":"(ssr)/./src/components/sections/why-choose-us.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ui/whatsapp-widget.tsx":{"*":{"id":"(ssr)/./src/components/ui/whatsapp-widget.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/universities-grid.tsx":{"*":{"id":"(ssr)/./src/components/sections/universities-grid.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/universities-hero.tsx":{"*":{"id":"(ssr)/./src/components/sections/universities-hero.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/university-filters.tsx":{"*":{"id":"(ssr)/./src/components/sections/university-filters.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/contact-form.tsx":{"*":{"id":"(ssr)/./src/components/sections/contact-form.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/contact-hero.tsx":{"*":{"id":"(ssr)/./src/components/sections/contact-hero.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/contact-info.tsx":{"*":{"id":"(ssr)/./src/components/sections/contact-info.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/application-form.tsx":{"*":{"id":"(ssr)/./src/components/sections/application-form.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/application-hero.tsx":{"*":{"id":"(ssr)/./src/components/sections/application-hero.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/placeholder-section.tsx":{"*":{"id":"(ssr)/./src/components/sections/placeholder-section.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/service-detail-hero.tsx":{"*":{"id":"(ssr)/./src/components/sections/service-detail-hero.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/faq-categories.tsx":{"*":{"id":"(ssr)/./src/components/sections/faq-categories.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/faq-hero.tsx":{"*":{"id":"(ssr)/./src/components/sections/faq-hero.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/testimonials-grid.tsx":{"*":{"id":"(ssr)/./src/components/sections/testimonials-grid.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/testimonials-hero.tsx":{"*":{"id":"(ssr)/./src/components/sections/testimonials-hero.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/hero-section.tsx":{"*":{"id":"(ssr)/./src/components/sections/hero-section.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/partner-universities.tsx":{"*":{"id":"(ssr)/./src/components/sections/partner-universities.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/services-overview.tsx":{"*":{"id":"(ssr)/./src/components/sections/services-overview.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/testimonials-slider.tsx":{"*":{"id":"(ssr)/./src/components/sections/testimonials-slider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/programs-grid.tsx":{"*":{"id":"(ssr)/./src/components/sections/programs-grid.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/programs-hero.tsx":{"*":{"id":"(ssr)/./src/components/sections/programs-hero.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/service-process.tsx":{"*":{"id":"(ssr)/./src/components/sections/service-process.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/services-grid.tsx":{"*":{"id":"(ssr)/./src/components/sections/services-grid.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/services-hero.tsx":{"*":{"id":"(ssr)/./src/components/sections/services-hero.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/page.tsx":{"*":{"id":"(ssr)/./src/app/admin/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Documents\\augment-projects\\foreingate_groupe\\foreingate-website\\node_modules\\next\\dist\\client\\app-dir\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\foreingate_groupe\\foreingate-website\\node_modules\\next\\dist\\esm\\client\\app-dir\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\foreingate_groupe\\foreingate-website\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\foreingate_groupe\\foreingate-website\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\foreingate_groupe\\foreingate-website\\src\\app\\globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\foreingate_groupe\\foreingate-website\\src\\components\\layout\\navigation.tsx":{"id":"(app-pages-browser)/./src/components/layout/navigation.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\foreingate_groupe\\foreingate-website\\src\\components\\providers\\theme-provider.tsx":{"id":"(app-pages-browser)/./src/components/providers/theme-provider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\foreingate_groupe\\foreingate-website\\src\\components\\sections\\company-story.tsx":{"id":"(app-pages-browser)/./src/components/sections/company-story.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\foreingate_groupe\\foreingate-website\\src\\components\\sections\\team-section.tsx":{"id":"(app-pages-browser)/./src/components/sections/team-section.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\foreingate_groupe\\foreingate-website\\src\\components\\sections\\vision-mission.tsx":{"id":"(app-pages-browser)/./src/components/sections/vision-mission.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\foreingate_groupe\\foreingate-website\\src\\components\\sections\\why-choose-us.tsx":{"id":"(app-pages-browser)/./src/components/sections/why-choose-us.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\foreingate_groupe\\foreingate-website\\src\\components\\ui\\whatsapp-widget.tsx":{"id":"(app-pages-browser)/./src/components/ui/whatsapp-widget.tsx","name":"*","chunks":["app/contact/page","static/chunks/app/contact/page.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\foreingate_groupe\\foreingate-website\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\foreingate_groupe\\foreingate-website\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\foreingate_groupe\\foreingate-website\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\foreingate_groupe\\foreingate-website\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\foreingate_groupe\\foreingate-website\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\foreingate_groupe\\foreingate-website\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\foreingate_groupe\\foreingate-website\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\foreingate_groupe\\foreingate-website\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\foreingate_groupe\\foreingate-website\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\foreingate_groupe\\foreingate-website\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\foreingate_groupe\\foreingate-website\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\foreingate_groupe\\foreingate-website\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\foreingate_groupe\\foreingate-website\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\foreingate_groupe\\foreingate-website\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\foreingate_groupe\\foreingate-website\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\foreingate_groupe\\foreingate-website\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\foreingate_groupe\\foreingate-website\\src\\components\\sections\\universities-grid.tsx":{"id":"(app-pages-browser)/./src/components/sections/universities-grid.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\foreingate_groupe\\foreingate-website\\src\\components\\sections\\universities-hero.tsx":{"id":"(app-pages-browser)/./src/components/sections/universities-hero.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\foreingate_groupe\\foreingate-website\\src\\components\\sections\\university-filters.tsx":{"id":"(app-pages-browser)/./src/components/sections/university-filters.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\foreingate_groupe\\foreingate-website\\src\\components\\sections\\contact-form.tsx":{"id":"(app-pages-browser)/./src/components/sections/contact-form.tsx","name":"*","chunks":["app/contact/page","static/chunks/app/contact/page.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\foreingate_groupe\\foreingate-website\\src\\components\\sections\\contact-hero.tsx":{"id":"(app-pages-browser)/./src/components/sections/contact-hero.tsx","name":"*","chunks":["app/contact/page","static/chunks/app/contact/page.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\foreingate_groupe\\foreingate-website\\src\\components\\sections\\contact-info.tsx":{"id":"(app-pages-browser)/./src/components/sections/contact-info.tsx","name":"*","chunks":["app/contact/page","static/chunks/app/contact/page.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\foreingate_groupe\\foreingate-website\\src\\components\\sections\\application-form.tsx":{"id":"(app-pages-browser)/./src/components/sections/application-form.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\foreingate_groupe\\foreingate-website\\src\\components\\sections\\application-hero.tsx":{"id":"(app-pages-browser)/./src/components/sections/application-hero.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\foreingate_groupe\\foreingate-website\\src\\components\\sections\\placeholder-section.tsx":{"id":"(app-pages-browser)/./src/components/sections/placeholder-section.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\foreingate_groupe\\foreingate-website\\src\\components\\sections\\service-detail-hero.tsx":{"id":"(app-pages-browser)/./src/components/sections/service-detail-hero.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\foreingate_groupe\\foreingate-website\\src\\components\\sections\\faq-categories.tsx":{"id":"(app-pages-browser)/./src/components/sections/faq-categories.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\foreingate_groupe\\foreingate-website\\src\\components\\sections\\faq-hero.tsx":{"id":"(app-pages-browser)/./src/components/sections/faq-hero.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\foreingate_groupe\\foreingate-website\\src\\components\\sections\\testimonials-grid.tsx":{"id":"(app-pages-browser)/./src/components/sections/testimonials-grid.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\foreingate_groupe\\foreingate-website\\src\\components\\sections\\testimonials-hero.tsx":{"id":"(app-pages-browser)/./src/components/sections/testimonials-hero.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\foreingate_groupe\\foreingate-website\\src\\components\\sections\\hero-section.tsx":{"id":"(app-pages-browser)/./src/components/sections/hero-section.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\foreingate_groupe\\foreingate-website\\src\\components\\sections\\partner-universities.tsx":{"id":"(app-pages-browser)/./src/components/sections/partner-universities.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\foreingate_groupe\\foreingate-website\\src\\components\\sections\\services-overview.tsx":{"id":"(app-pages-browser)/./src/components/sections/services-overview.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\foreingate_groupe\\foreingate-website\\src\\components\\sections\\testimonials-slider.tsx":{"id":"(app-pages-browser)/./src/components/sections/testimonials-slider.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\foreingate_groupe\\foreingate-website\\src\\components\\sections\\programs-grid.tsx":{"id":"(app-pages-browser)/./src/components/sections/programs-grid.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\foreingate_groupe\\foreingate-website\\src\\components\\sections\\programs-hero.tsx":{"id":"(app-pages-browser)/./src/components/sections/programs-hero.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\foreingate_groupe\\foreingate-website\\src\\components\\sections\\service-process.tsx":{"id":"(app-pages-browser)/./src/components/sections/service-process.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\foreingate_groupe\\foreingate-website\\src\\components\\sections\\services-grid.tsx":{"id":"(app-pages-browser)/./src/components/sections/services-grid.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\foreingate_groupe\\foreingate-website\\src\\components\\sections\\services-hero.tsx":{"id":"(app-pages-browser)/./src/components/sections/services-hero.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\foreingate_groupe\\foreingate-website\\src\\app\\admin\\page.tsx":{"id":"(app-pages-browser)/./src/app/admin/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Documents\\augment-projects\\foreingate_groupe\\foreingate-website\\src\\":[],"C:\\Users\\<USER>\\Documents\\augment-projects\\foreingate_groupe\\foreingate-website\\src\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"C:\\Users\\<USER>\\Documents\\augment-projects\\foreingate_groupe\\foreingate-website\\src\\app\\page":[],"C:\\Users\\<USER>\\Documents\\augment-projects\\foreingate_groupe\\foreingate-website\\src\\app\\contact\\page":[]},"rscModuleMapping":{"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/globals.css":{"*":{"id":"(rsc)/./src/app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/layout/navigation.tsx":{"*":{"id":"(rsc)/./src/components/layout/navigation.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/providers/theme-provider.tsx":{"*":{"id":"(rsc)/./src/components/providers/theme-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/company-story.tsx":{"*":{"id":"(rsc)/./src/components/sections/company-story.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/team-section.tsx":{"*":{"id":"(rsc)/./src/components/sections/team-section.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/vision-mission.tsx":{"*":{"id":"(rsc)/./src/components/sections/vision-mission.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/why-choose-us.tsx":{"*":{"id":"(rsc)/./src/components/sections/why-choose-us.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ui/whatsapp-widget.tsx":{"*":{"id":"(rsc)/./src/components/ui/whatsapp-widget.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/universities-grid.tsx":{"*":{"id":"(rsc)/./src/components/sections/universities-grid.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/universities-hero.tsx":{"*":{"id":"(rsc)/./src/components/sections/universities-hero.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/university-filters.tsx":{"*":{"id":"(rsc)/./src/components/sections/university-filters.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/contact-form.tsx":{"*":{"id":"(rsc)/./src/components/sections/contact-form.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/contact-hero.tsx":{"*":{"id":"(rsc)/./src/components/sections/contact-hero.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/contact-info.tsx":{"*":{"id":"(rsc)/./src/components/sections/contact-info.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/application-form.tsx":{"*":{"id":"(rsc)/./src/components/sections/application-form.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/application-hero.tsx":{"*":{"id":"(rsc)/./src/components/sections/application-hero.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/placeholder-section.tsx":{"*":{"id":"(rsc)/./src/components/sections/placeholder-section.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/service-detail-hero.tsx":{"*":{"id":"(rsc)/./src/components/sections/service-detail-hero.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/faq-categories.tsx":{"*":{"id":"(rsc)/./src/components/sections/faq-categories.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/faq-hero.tsx":{"*":{"id":"(rsc)/./src/components/sections/faq-hero.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/testimonials-grid.tsx":{"*":{"id":"(rsc)/./src/components/sections/testimonials-grid.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/testimonials-hero.tsx":{"*":{"id":"(rsc)/./src/components/sections/testimonials-hero.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/hero-section.tsx":{"*":{"id":"(rsc)/./src/components/sections/hero-section.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/partner-universities.tsx":{"*":{"id":"(rsc)/./src/components/sections/partner-universities.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/services-overview.tsx":{"*":{"id":"(rsc)/./src/components/sections/services-overview.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/testimonials-slider.tsx":{"*":{"id":"(rsc)/./src/components/sections/testimonials-slider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/programs-grid.tsx":{"*":{"id":"(rsc)/./src/components/sections/programs-grid.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/programs-hero.tsx":{"*":{"id":"(rsc)/./src/components/sections/programs-hero.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/service-process.tsx":{"*":{"id":"(rsc)/./src/components/sections/service-process.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/services-grid.tsx":{"*":{"id":"(rsc)/./src/components/sections/services-grid.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/services-hero.tsx":{"*":{"id":"(rsc)/./src/components/sections/services-hero.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/page.tsx":{"*":{"id":"(rsc)/./src/app/admin/page.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}