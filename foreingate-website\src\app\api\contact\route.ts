import { NextRequest, NextResponse } from 'next/server'
import { writeFile, readFile, mkdir } from 'fs/promises'
import { existsSync } from 'fs'
import path from 'path'
import { Resend } from 'resend'

const resend = new Resend(process.env.RESEND_API_KEY)
const DATA_DIR = path.join(process.cwd(), 'data')
const CONTACT_FILE = path.join(DATA_DIR, 'contact-inquiries.json')

// Ensure data directory exists
async function ensureDataDir() {
  if (!existsSync(DATA_DIR)) {
    await mkdir(DATA_DIR, { recursive: true })
  }
}

// Read contact inquiries from file
async function readContactInquiries() {
  try {
    await ensureDataDir()
    if (!existsSync(CONTACT_FILE)) {
      return []
    }
    const data = await readFile(CONTACT_FILE, 'utf-8')
    return JSON.parse(data)
  } catch (error) {
    console.error('Error reading contact inquiries:', error)
    return []
  }
}

// Write contact inquiries to file
async function writeContactInquiries(inquiries: any[]) {
  try {
    await ensureDataDir()
    await writeFile(CONTACT_FILE, JSON.stringify(inquiries, null, 2))
  } catch (error) {
    console.error('Error writing contact inquiries:', error)
    throw error
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      name,
      email,
      phone,
      subject,
      message,
      preferredContact,
      interestedServices
    } = body

    // Validate required fields
    if (!name || !email || !subject || !message) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Email template for admin notification
    const adminEmailHtml = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center;">
          <h1 style="color: white; margin: 0;">New Contact Form Submission</h1>
          <p style="color: white; opacity: 0.9; margin: 10px 0 0 0;">Foreingate Group Website</p>
        </div>
        
        <div style="padding: 30px; background: #f8f9fa;">
          <h2 style="color: #333; border-bottom: 2px solid #667eea; padding-bottom: 10px;">Contact Information</h2>
          <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
            <tr>
              <td style="padding: 10px; font-weight: bold; color: #555;">Name:</td>
              <td style="padding: 10px; color: #333;">${name}</td>
            </tr>
            <tr style="background: #f1f3f4;">
              <td style="padding: 10px; font-weight: bold; color: #555;">Email:</td>
              <td style="padding: 10px; color: #333;">${email}</td>
            </tr>
            <tr>
              <td style="padding: 10px; font-weight: bold; color: #555;">Phone:</td>
              <td style="padding: 10px; color: #333;">${phone || 'Not provided'}</td>
            </tr>
            <tr style="background: #f1f3f4;">
              <td style="padding: 10px; font-weight: bold; color: #555;">Subject:</td>
              <td style="padding: 10px; color: #333;">${subject}</td>
            </tr>
            <tr>
              <td style="padding: 10px; font-weight: bold; color: #555;">Preferred Contact:</td>
              <td style="padding: 10px; color: #333;">${preferredContact}</td>
            </tr>
          </table>

          ${interestedServices && interestedServices.length > 0 ? `
            <h3 style="color: #333; margin-top: 30px;">Interested Services:</h3>
            <ul style="background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #667eea;">
              ${interestedServices.map((service: string) => `<li style="margin: 5px 0; color: #555;">${service}</li>`).join('')}
            </ul>
          ` : ''}

          <h3 style="color: #333; margin-top: 30px;">Message:</h3>
          <div style="background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #667eea; white-space: pre-wrap; color: #555; line-height: 1.6;">
            ${message}
          </div>

          <div style="margin-top: 30px; padding: 20px; background: #e3f2fd; border-radius: 8px; text-align: center;">
            <p style="margin: 0; color: #1976d2; font-weight: bold;">⏰ Submitted: ${new Date().toLocaleString()}</p>
            <p style="margin: 10px 0 0 0; color: #666; font-size: 14px;">Please respond within 24 hours</p>
          </div>
        </div>
      </div>
    `

    // Email template for user confirmation
    const userEmailHtml = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center;">
          <h1 style="color: white; margin: 0;">Thank You for Contacting Us!</h1>
          <p style="color: white; opacity: 0.9; margin: 10px 0 0 0;">Foreingate Group</p>
        </div>
        
        <div style="padding: 30px; background: #f8f9fa;">
          <p style="color: #333; font-size: 16px; line-height: 1.6;">Dear ${name},</p>
          
          <p style="color: #555; line-height: 1.6;">
            Thank you for reaching out to Foreingate Group! We have received your inquiry and our expert team will review your message carefully.
          </p>

          <div style="background: #e8f5e8; padding: 20px; border-radius: 8px; border-left: 4px solid #4caf50; margin: 20px 0;">
            <h3 style="color: #2e7d32; margin: 0 0 10px 0;">✅ What happens next?</h3>
            <ul style="color: #555; margin: 0; padding-left: 20px;">
              <li>Our admissions team will review your inquiry within 24 hours</li>
              <li>A dedicated counselor will be assigned to your case</li>
              <li>You'll receive a personalized response via ${preferredContact}</li>
              <li>We'll provide detailed information about your areas of interest</li>
            </ul>
          </div>

          <div style="background: white; padding: 20px; border-radius: 8px; border: 1px solid #e0e0e0; margin: 20px 0;">
            <h3 style="color: #333; margin: 0 0 15px 0;">Your Inquiry Summary:</h3>
            <p style="margin: 5px 0; color: #555;"><strong>Subject:</strong> ${subject}</p>
            ${interestedServices && interestedServices.length > 0 ? `
              <p style="margin: 5px 0; color: #555;"><strong>Services of Interest:</strong> ${interestedServices.join(', ')}</p>
            ` : ''}
            <p style="margin: 5px 0; color: #555;"><strong>Submitted:</strong> ${new Date().toLocaleString()}</p>
          </div>

          <div style="background: #fff3e0; padding: 20px; border-radius: 8px; border-left: 4px solid #ff9800; margin: 20px 0;">
            <h3 style="color: #f57c00; margin: 0 0 10px 0;">📞 Need Immediate Assistance?</h3>
            <p style="color: #555; margin: 0;">
              <strong>Phone:</strong> +90 ************<br>
              <strong>WhatsApp:</strong> +90 ************<br>
              <strong>Email:</strong> <EMAIL>
            </p>
          </div>

          <p style="color: #555; line-height: 1.6;">
            We're excited to help you achieve your educational goals and look forward to speaking with you soon!
          </p>

          <p style="color: #555; line-height: 1.6;">
            Best regards,<br>
            <strong>The Foreingate Group Team</strong><br>
            <em>Your Gateway to International Education</em>
          </p>
        </div>

        <div style="background: #333; padding: 20px; text-align: center;">
          <p style="color: #ccc; margin: 0; font-size: 14px;">
            © 2024 Foreingate Group. All rights reserved.<br>
            This is an automated confirmation email.
          </p>
        </div>
      </div>
    `

    // Send email to admin
    const adminEmail = await resend.emails.send({
      from: 'Foreingate Website <<EMAIL>>',
      to: ['<EMAIL>'], // Replace with actual admin email
      subject: `New Contact Form: ${subject}`,
      html: adminEmailHtml,
    })

    // Send confirmation email to user
    const userEmail = await resend.emails.send({
      from: 'Foreingate Group <<EMAIL>>',
      to: [email],
      subject: 'Thank you for contacting Foreingate Group',
      html: userEmailHtml,
    })

    // Generate a unique inquiry ID
    const inquiryId = `FG-${Date.now()}-${Math.random().toString(36).substr(2, 9).toUpperCase()}`

    // Store contact inquiry in database
    try {
      const contactInquiry = {
        id: Math.random().toString(36).substr(2, 9),
        inquiryId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        name,
        email,
        phone: phone || null,
        subject,
        message,
        preferredContact,
        interestedServices: JSON.stringify(interestedServices || []),
        status: 'NEW',
        assignedAgent: null,
        responseNotes: null,
        respondedAt: null
      }

      const inquiries = await readContactInquiries()
      inquiries.push(contactInquiry)
      await writeContactInquiries(inquiries)
    } catch (storageError) {
      console.error('Failed to store contact inquiry:', storageError)
      // Don't fail the API if storage fails
    }

    return NextResponse.json({
      success: true,
      message: 'Your message has been sent successfully!',
      inquiryId,
      adminEmailId: adminEmail.data?.id,
      userEmailId: userEmail.data?.id
    })

  } catch (error) {
    console.error('Contact form error:', error)
    return NextResponse.json(
      { error: 'Failed to send message. Please try again.' },
      { status: 500 }
    )
  }
}
