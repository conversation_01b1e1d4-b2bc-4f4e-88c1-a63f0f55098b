// Advanced Document Processing for RAG System
import { readFile, writeFile, mkdir } from 'fs/promises'
import { existsSync } from 'fs'
import path from 'path'

export interface ProcessedDocument {
  id: string
  title: string
  content: string
  chunks: DocumentChunk[]
  metadata: {
    source: string
    type: 'university' | 'program' | 'faq' | 'policy' | 'guide' | 'general'
    lastUpdated: string
    language: string
    category: string
  }
}

export interface DocumentChunk {
  id: string
  content: string
  embedding?: number[]
  metadata: {
    documentId: string
    chunkIndex: number
    startPosition: number
    endPosition: number
    keywords: string[]
    importance: number
  }
}

export class DocumentProcessor {
  private documentsPath = path.join(process.cwd(), 'data', 'documents')
  private processedPath = path.join(process.cwd(), 'data', 'processed')

  constructor() {
    this.ensureDirectories()
  }

  private async ensureDirectories() {
    try {
      if (!existsSync(this.documentsPath)) {
        await mkdir(this.documentsPath, { recursive: true })
      }
      if (!existsSync(this.processedPath)) {
        await mkdir(this.processedPath, { recursive: true })
      }
    } catch (error) {
      console.warn('Could not create directories for document processing:', error)
    }
  }

  // Process and chunk documents for RAG
  public async processDocument(
    content: string, 
    title: string, 
    metadata: Partial<ProcessedDocument['metadata']>
  ): Promise<ProcessedDocument> {
    const id = this.generateDocumentId(title)
    
    // Clean and normalize content
    const cleanContent = this.cleanContent(content)
    
    // Create chunks
    const chunks = this.createChunks(cleanContent, id)
    
    // Extract keywords and calculate importance
    chunks.forEach(chunk => {
      chunk.metadata.keywords = this.extractKeywords(chunk.content)
      chunk.metadata.importance = this.calculateImportance(chunk.content)
    })

    const document: ProcessedDocument = {
      id,
      title,
      content: cleanContent,
      chunks,
      metadata: {
        source: metadata.source || 'unknown',
        type: metadata.type || 'general',
        lastUpdated: new Date().toISOString(),
        language: metadata.language || 'en',
        category: metadata.category || 'general'
      }
    }

    // Save processed document
    await this.saveProcessedDocument(document)
    
    return document
  }

  private generateDocumentId(title: string): string {
    return title.toLowerCase()
      .replace(/[^a-z0-9]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '') + '-' + Date.now()
  }

  private cleanContent(content: string): string {
    return content
      .replace(/\s+/g, ' ') // Normalize whitespace
      .replace(/[^\w\s.,!?;:()\-]/g, '') // Remove special characters
      .trim()
  }

  private createChunks(content: string, documentId: string): DocumentChunk[] {
    const chunks: DocumentChunk[] = []
    const sentences = this.splitIntoSentences(content)
    const chunkSize = 3 // sentences per chunk
    const overlap = 1 // sentence overlap between chunks

    for (let i = 0; i < sentences.length; i += chunkSize - overlap) {
      const chunkSentences = sentences.slice(i, i + chunkSize)
      const chunkContent = chunkSentences.join(' ')
      
      if (chunkContent.trim().length > 20) { // Minimum chunk size
        chunks.push({
          id: `${documentId}-chunk-${chunks.length}`,
          content: chunkContent,
          metadata: {
            documentId,
            chunkIndex: chunks.length,
            startPosition: i,
            endPosition: i + chunkSentences.length - 1,
            keywords: [],
            importance: 0
          }
        })
      }
    }

    return chunks
  }

  private splitIntoSentences(text: string): string[] {
    return text
      .split(/[.!?]+/)
      .map(s => s.trim())
      .filter(s => s.length > 10)
  }

  private extractKeywords(text: string): string[] {
    const words = text.toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 3)

    // Simple keyword extraction based on frequency and importance
    const wordCounts = new Map<string, number>()
    words.forEach(word => {
      wordCounts.set(word, (wordCounts.get(word) || 0) + 1)
    })

    // Important domain-specific keywords
    const importantKeywords = [
      'university', 'admission', 'tuition', 'scholarship', 'visa', 'program',
      'degree', 'bachelor', 'master', 'medicine', 'engineering', 'business',
      'cyprus', 'international', 'student', 'application', 'requirement',
      'cost', 'fee', 'accommodation', 'housing', 'english', 'ielts', 'toefl'
    ]

    const keywords = Array.from(wordCounts.entries())
      .filter(([word, count]) => count > 1 || importantKeywords.includes(word))
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .map(([word]) => word)

    return keywords
  }

  private calculateImportance(content: string): number {
    let importance = 0
    const contentLower = content.toLowerCase()

    // Boost importance for key topics
    const importanceBoosts = {
      'university': 3,
      'admission': 3,
      'tuition': 2,
      'scholarship': 3,
      'visa': 2,
      'program': 2,
      'requirement': 2,
      'cost': 2,
      'fee': 2,
      'application': 2,
      'medicine': 2,
      'engineering': 2,
      'business': 2
    }

    Object.entries(importanceBoosts).forEach(([keyword, boost]) => {
      if (contentLower.includes(keyword)) {
        importance += boost
      }
    })

    // Boost for numbers (costs, dates, etc.)
    const numberMatches = content.match(/\$[\d,]+|\d{4}|\d+%/g)
    if (numberMatches) {
      importance += numberMatches.length
    }

    return Math.min(importance, 10) // Cap at 10
  }

  private async saveProcessedDocument(document: ProcessedDocument): Promise<void> {
    try {
      await this.ensureDirectories()
      const filePath = path.join(this.processedPath, `${document.id}.json`)
      await writeFile(filePath, JSON.stringify(document, null, 2))
    } catch (error) {
      console.warn('Could not save processed document:', error)
    }
  }

  public async loadProcessedDocument(id: string): Promise<ProcessedDocument | null> {
    try {
      const filePath = path.join(this.processedPath, `${id}.json`)
      const content = await readFile(filePath, 'utf-8')
      return JSON.parse(content)
    } catch (error) {
      return null
    }
  }

  public async getAllProcessedDocuments(): Promise<ProcessedDocument[]> {
    try {
      const files = await readFile(this.processedPath)
      const documents: ProcessedDocument[] = []
      
      // This would need proper directory reading implementation
      // For now, return empty array
      return documents
    } catch (error) {
      return []
    }
  }
}

// Enhanced knowledge base with document processing
export class EnhancedKnowledgeBase {
  private processor: DocumentProcessor
  private documents: Map<string, ProcessedDocument> = new Map()

  constructor() {
    this.processor = new DocumentProcessor()
    this.initializeKnowledgeBase()
  }

  private async initializeKnowledgeBase() {
    // Add comprehensive university information
    await this.addUniversityDocuments()
    
    // Add program-specific information
    await this.addProgramDocuments()
    
    // Add admission guides
    await this.addAdmissionGuides()
    
    // Add cost and scholarship information
    await this.addFinancialDocuments()
    
    // Add visa and immigration guides
    await this.addVisaDocuments()
  }

  private async addUniversityDocuments() {
    const emuDoc = `
    Eastern Mediterranean University (EMU) is a leading higher education institution located in Famagusta, Northern Cyprus.
    Established in 1979, EMU has grown to become one of the most prestigious universities in the region.
    
    The university hosts over 20,000 students from 106 different countries, creating a truly international environment.
    EMU offers undergraduate, graduate, and doctoral programs across 11 faculties.
    
    Popular programs include Computer Engineering, Business Administration, International Relations, 
    Medicine, Dentistry, Pharmacy, and Architecture. All programs are taught in English.
    
    The campus features modern facilities including state-of-the-art laboratories, libraries, 
    sports complexes, and student accommodation. EMU is accredited by YÖK (Turkish Higher Education Council)
    and many programs have international accreditations.
    
    Tuition fees range from $3,500 to $8,000 per year depending on the program.
    The university offers various scholarships including merit-based scholarships up to 50% tuition reduction.
    `

    await this.processor.processDocument(emuDoc, 'Eastern Mediterranean University Guide', {
      type: 'university',
      category: 'universities',
      source: 'official'
    })

    const neuDoc = `
    Near East University (NEU) is a comprehensive university established in 1988 in Nicosia, Northern Cyprus.
    NEU is particularly renowned for its medical programs and has WHO recognition for its Medicine faculty.
    
    The university serves over 25,000 students including 18,000 international students from around the world.
    NEU offers programs in Medicine, Dentistry, Pharmacy, Engineering, Business, Law, and many other fields.
    
    The Medical faculty is especially prestigious with modern simulation labs, anatomy labs, and clinical training facilities.
    NEU Hospital provides practical training opportunities for medical students.
    
    Engineering programs include Computer, Civil, Electrical, Mechanical, and Industrial Engineering.
    Business programs cover MBA, International Business, Banking and Finance, and Economics.
    
    Tuition fees range from $4,000 to $12,000 per year. Medical programs are at the higher end.
    NEU offers merit scholarships, early application discounts, and payment plan options.
    `

    await this.processor.processDocument(neuDoc, 'Near East University Guide', {
      type: 'university',
      category: 'universities',
      source: 'official'
    })
  }

  private async addProgramDocuments() {
    const engineeringDoc = `
    Engineering programs in Northern Cyprus universities are highly regarded and internationally recognized.
    
    Computer Engineering: Covers software development, algorithms, computer systems, artificial intelligence,
    and cybersecurity. Graduates work in tech companies, startups, and multinational corporations.
    
    Civil Engineering: Focuses on infrastructure design, construction management, structural analysis,
    and environmental engineering. Strong emphasis on practical projects and internships.
    
    Electrical Engineering: Includes power systems, electronics, telecommunications, and renewable energy.
    Modern laboratories with industry-standard equipment.
    
    Mechanical Engineering: Covers thermodynamics, fluid mechanics, manufacturing, and automotive engineering.
    Hands-on experience with CAD software and manufacturing processes.
    
    All engineering programs require strong mathematics and physics background.
    English proficiency (IELTS 6.0+ or TOEFL 79+) is mandatory.
    Duration is typically 4 years for bachelor's degree.
    `

    await this.processor.processDocument(engineeringDoc, 'Engineering Programs Guide', {
      type: 'program',
      category: 'programs',
      source: 'academic'
    })
  }

  private async addAdmissionGuides() {
    const admissionDoc = `
    University admission process in Northern Cyprus is straightforward and student-friendly.
    
    Step 1: Choose your university and program based on your interests and career goals.
    Step 2: Prepare required documents including academic transcripts, English proficiency test, and passport.
    Step 3: Submit online application with all supporting documents.
    Step 4: Wait for admission decision (usually 1-2 weeks).
    Step 5: Accept offer and pay initial fees.
    Step 6: Apply for student visa with acceptance letter.
    Step 7: Arrange accommodation and travel.
    Step 8: Attend orientation and begin studies.
    
    Required documents for undergraduate:
    - High school diploma or equivalent
    - Official transcripts with grades
    - English proficiency test (IELTS 6.0+ or TOEFL 79+)
    - Passport copy
    - Personal statement
    - Letters of recommendation (some programs)
    
    Application deadlines are flexible with multiple intake periods throughout the year.
    Early applications receive priority for scholarships and accommodation.
    `

    await this.processor.processDocument(admissionDoc, 'Admission Process Guide', {
      type: 'guide',
      category: 'admissions',
      source: 'official'
    })
  }

  private async addFinancialDocuments() {
    const financialDoc = `
    Studying in Northern Cyprus is significantly more affordable than other popular destinations.
    
    Tuition Fees:
    - Undergraduate programs: $3,000 - $8,000 per year
    - Graduate programs: $4,000 - $12,000 per year  
    - Medical programs: $8,000 - $15,000 per year
    - Engineering programs: $3,500 - $7,000 per year
    - Business programs: $3,000 - $6,000 per year
    
    Living Costs (monthly):
    - Accommodation: $200 - $600 (dormitory to private apartment)
    - Food and meals: $150 - $300
    - Transportation: $50 - $100
    - Books and supplies: $50 - $100
    - Personal expenses: $100 - $200
    - Total monthly living: $550 - $1,300
    
    Scholarship Opportunities:
    - Merit Scholarship: 25-50% tuition reduction for high achievers
    - Early Bird Discount: 10-15% for applications before March 31st
    - Sibling Discount: 10% for families with multiple students
    - Payment plans available with no interest charges
    
    Part-time work opportunities available for students with proper permits.
    `

    await this.processor.processDocument(financialDoc, 'Costs and Financial Aid Guide', {
      type: 'guide',
      category: 'costs',
      source: 'official'
    })
  }

  private async addVisaDocuments() {
    const visaDoc = `
    Student visa process for Northern Cyprus is generally straightforward for most nationalities.
    
    Required Documents:
    - Valid passport (minimum 6 months validity)
    - University acceptance letter
    - Proof of financial support (bank statements)
    - Health insurance coverage
    - Academic transcripts and diplomas
    - Police clearance certificate
    - Medical examination report
    - Passport-size photographs
    
    Visa Processing:
    - Processing time: 2-8 weeks depending on nationality
    - Some countries have visa-free entry for short stays
    - Student residence permit required for long-term study
    - Multiple entry visa recommended for travel flexibility
    
    Visa-free countries include many EU nations, Turkey, and several others.
    Our visa support team assists with complete application process.
    Success rate is over 95% with proper documentation.
    
    After arrival, students must register with local authorities and obtain residence permit.
    `

    await this.processor.processDocument(visaDoc, 'Visa and Immigration Guide', {
      type: 'guide',
      category: 'visa',
      source: 'official'
    })
  }

  public async searchDocuments(query: string, limit: number = 5): Promise<DocumentChunk[]> {
    const allChunks: DocumentChunk[] = []
    
    // Collect all chunks from all documents
    this.documents.forEach(doc => {
      allChunks.push(...doc.chunks)
    })

    // Simple text matching (in production, use proper vector similarity)
    const queryLower = query.toLowerCase()
    const matches = allChunks.filter(chunk => 
      chunk.content.toLowerCase().includes(queryLower) ||
      chunk.metadata.keywords.some(keyword => queryLower.includes(keyword))
    )

    // Sort by importance and relevance
    matches.sort((a, b) => b.metadata.importance - a.metadata.importance)
    
    return matches.slice(0, limit)
  }
}
