import { Translations } from '../lib/translations'

export const en: Translations = {
  nav: {
    home: 'Home',
    about: 'About',
    services: 'Services',
    universities: 'Universities',
    programs: 'Programs',
    blog: 'Blog',
    contact: 'Contact',
    applyNow: 'Apply Now',
    getStarted: 'Get Started',
    language: 'Language'
  },
  
  hero: {
    title: 'Your Gateway to World-Class Education in Northern Cyprus',
    subtitle: 'Study Abroad with Confidence',
    description: 'Expert guidance for international students seeking quality education at top universities in Northern Cyprus. From application to graduation, we\'re with you every step of the way.',
    ctaPrimary: 'Start Your Journey',
    ctaSecondary: 'Explore Universities',
    trustBadge: 'Trusted by 10,000+ Students Worldwide',
    studentsServed: 'Students Served',
    successRate: 'Success Rate',
    yearsExperience: 'Years Experience'
  },
  
  about: {
    title: 'About Foreingate Group',
    subtitle: 'Your Trusted Education Partner',
    description: 'We are a leading educational consultancy specializing in helping international students achieve their academic dreams in Northern Cyprus.',
    mission: 'To provide comprehensive educational guidance and support services that empower students to succeed in their international academic journey.',
    vision: 'To be the most trusted bridge connecting students worldwide with quality education opportunities in Northern Cyprus.',
    values: 'Excellence, Integrity, Innovation, and Student Success',
    whyChooseUs: 'Why Choose Us',
    experience: '15+ Years Experience',
    expertise: 'Expert Guidance',
    support: '24/7 Support',
    success: '98% Success Rate'
  },
  
  services: {
    title: 'Our Services',
    subtitle: 'Comprehensive Support for Your Educational Journey',
    universitySelection: 'University Selection',
    universitySelectionDesc: 'Expert guidance to choose the right university and program based on your goals and preferences.',
    admissionGuidance: 'Admission Guidance',
    admissionGuidanceDesc: 'Complete support through the application process, from document preparation to submission.',
    visaSupport: 'Visa Support',
    visaSupportDesc: 'Professional assistance with visa applications and immigration procedures.',
    accommodationHelp: 'Accommodation Help',
    accommodationHelpDesc: 'Find suitable housing options near your university with our accommodation services.',
    scholarshipAssistance: 'Scholarship Assistance',
    scholarshipAssistanceDesc: 'Identify and apply for scholarships and financial aid opportunities.',
    ongoingSupport: 'Ongoing Support',
    ongoingSupportDesc: 'Continuous support throughout your studies, from arrival to graduation.'
  },
  
  universities: {
    title: 'Partner Universities',
    subtitle: 'Top-Ranked Universities in Northern Cyprus',
    emu: 'Eastern Mediterranean University',
    neu: 'Near East University',
    ciu: 'Cyprus International University',
    programs: 'Programs',
    students: 'Students',
    established: 'Established',
    accreditation: 'Accreditation',
    tuitionFrom: 'Tuition from',
    learnMore: 'Learn More',
    applyNow: 'Apply Now'
  },
  
  programs: {
    title: 'Study Programs',
    subtitle: 'Diverse Academic Programs to Match Your Interests',
    engineering: 'Engineering',
    medicine: 'Medicine',
    business: 'Business',
    arts: 'Arts & Humanities',
    sciences: 'Sciences',
    law: 'Law',
    architecture: 'Architecture',
    education: 'Education',
    duration: 'Duration',
    language: 'Language',
    degree: 'Degree',
    bachelor: 'Bachelor\'s',
    master: 'Master\'s',
    doctorate: 'Doctorate'
  },
  
  testimonials: {
    title: 'Student Success Stories',
    subtitle: 'Hear from Our Successful Students',
    readMore: 'Read More',
    showLess: 'Show Less',
    verified: 'Verified Student',
    graduate: 'Graduate',
    currentStudent: 'Current Student'
  },
  
  contact: {
    title: 'Contact Us',
    subtitle: 'Get in Touch with Our Education Experts',
    getInTouch: 'Get in Touch',
    name: 'Full Name',
    email: 'Email Address',
    phone: 'Phone Number',
    message: 'Message',
    subject: 'Subject',
    send: 'Send Message',
    sending: 'Sending...',
    sent: 'Message Sent Successfully!',
    error: 'Failed to send message. Please try again.',
    required: 'This field is required',
    invalidEmail: 'Please enter a valid email address',
    office: 'Office Hours',
    hours: 'Monday - Friday: 9:00 AM - 6:00 PM',
    emergency: '24/7 Emergency Support Available'
  },
  
  footer: {
    description: 'Your trusted partner for international education in Northern Cyprus. Expert guidance from application to graduation.',
    quickLinks: 'Quick Links',
    services: 'Services',
    contact: 'Contact Info',
    followUs: 'Follow Us',
    newsletter: 'Newsletter',
    newsletterDesc: 'Subscribe to get the latest updates on universities, programs, and scholarships.',
    subscribe: 'Subscribe',
    subscribing: 'Subscribing...',
    subscribed: 'Subscribed Successfully!',
    privacy: 'Privacy Policy',
    terms: 'Terms of Service',
    cookies: 'Cookie Policy',
    sitemap: 'Sitemap',
    allRightsReserved: 'All rights reserved.'
  },
  
  common: {
    loading: 'Loading...',
    error: 'Error',
    success: 'Success',
    warning: 'Warning',
    info: 'Information',
    close: 'Close',
    cancel: 'Cancel',
    confirm: 'Confirm',
    save: 'Save',
    edit: 'Edit',
    delete: 'Delete',
    search: 'Search',
    filter: 'Filter',
    sort: 'Sort',
    next: 'Next',
    previous: 'Previous',
    page: 'Page',
    of: 'of',
    showing: 'Showing',
    results: 'results',
    noResults: 'No results found',
    tryAgain: 'Try Again',
    learnMore: 'Learn More',
    readMore: 'Read More',
    showMore: 'Show More',
    showLess: 'Show Less',
    viewAll: 'View All',
    backToTop: 'Back to Top'
  },
  
  chatbot: {
    title: 'Education Assistant',
    placeholder: 'Ask me about universities, programs, costs...',
    send: 'Send',
    thinking: 'Thinking...',
    error: 'Sorry, I encountered an error. Please try again.',
    retry: 'Retry',
    clear: 'Clear Chat',
    minimize: 'Minimize',
    maximize: 'Maximize',
    close: 'Close',
    greeting: 'Hello! I\'m here to help you with your education journey. What would you like to know?',
    suggestions: 'Suggested Questions',
    typing: 'Typing...',
    offline: 'Offline',
    online: 'Online'
  },
  
  forms: {
    firstName: 'First Name',
    lastName: 'Last Name',
    fullName: 'Full Name',
    email: 'Email Address',
    phone: 'Phone Number',
    country: 'Country',
    city: 'City',
    address: 'Address',
    zipCode: 'ZIP Code',
    dateOfBirth: 'Date of Birth',
    gender: 'Gender',
    male: 'Male',
    female: 'Female',
    other: 'Other',
    preferNotToSay: 'Prefer not to say',
    nationality: 'Nationality',
    passportNumber: 'Passport Number',
    education: 'Education Level',
    highSchool: 'High School',
    bachelor: 'Bachelor\'s Degree',
    master: 'Master\'s Degree',
    doctorate: 'Doctorate',
    workExperience: 'Work Experience',
    englishLevel: 'English Level',
    beginner: 'Beginner',
    intermediate: 'Intermediate',
    advanced: 'Advanced',
    native: 'Native',
    interestedProgram: 'Interested Program',
    interestedUniversity: 'Interested University',
    startDate: 'Preferred Start Date',
    additionalInfo: 'Additional Information',
    agreeTerms: 'I agree to the Terms of Service',
    agreePrivacy: 'I agree to the Privacy Policy',
    agreeMarketing: 'I agree to receive marketing communications',
    submit: 'Submit',
    submitting: 'Submitting...',
    submitted: 'Submitted Successfully!',
    required: 'Required field',
    invalid: 'Invalid format',
    tooShort: 'Too short',
    tooLong: 'Too long',
    passwordMismatch: 'Passwords do not match'
  },
  
  costs: {
    title: 'Costs & Scholarships',
    subtitle: 'Affordable Education with Financial Support Options',
    tuitionFees: 'Tuition Fees',
    livingCosts: 'Living Costs',
    totalCost: 'Total Cost',
    scholarships: 'Scholarships',
    financialAid: 'Financial Aid',
    paymentPlans: 'Payment Plans',
    currency: 'USD',
    perYear: 'per year',
    perMonth: 'per month',
    accommodation: 'Accommodation',
    food: 'Food & Meals',
    transportation: 'Transportation',
    books: 'Books & Supplies',
    personal: 'Personal Expenses',
    insurance: 'Health Insurance',
    visa: 'Visa & Immigration',
    other: 'Other Expenses',
    meritScholarship: 'Merit Scholarship',
    needBasedAid: 'Need-Based Aid',
    earlyBird: 'Early Bird Discount',
    siblingDiscount: 'Sibling Discount',
    calculate: 'Calculate Costs',
    getQuote: 'Get Quote'
  }
}
