import { NextRequest, NextResponse } from 'next/server'
import { readFile, writeFile, mkdir } from 'fs/promises'
import { existsSync } from 'fs'
import path from 'path'

const DATA_DIR = path.join(process.cwd(), 'data')
const UNIVERSITIES_FILE = path.join(DATA_DIR, 'universities.json')

// Ensure data directory exists
async function ensureDataDir() {
  if (!existsSync(DATA_DIR)) {
    await mkdir(DATA_DIR, { recursive: true })
  }
}

// Read universities from file
async function readUniversities() {
  try {
    await ensureDataDir()
    if (!existsSync(UNIVERSITIES_FILE)) {
      // Create initial data if file doesn't exist
      const initialData = [
        {
          id: '1',
          name: 'Eastern Mediterranean University',
          slug: 'eastern-mediterranean-university',
          logo: '/images/universities/emu-logo.png',
          location: 'Famagusta, Northern Cyprus',
          tuitionFrom: 3500,
          currency: 'USD',
          description: 'Eastern Mediterranean University (EMU) is a leading higher education institution in Northern Cyprus, offering world-class education with international recognition.',
          images: [
            '/images/universities/emu-campus-1.jpg',
            '/images/universities/emu-campus-2.jpg',
            '/images/universities/emu-campus-3.jpg'
          ],
          establishedYear: 1979,
          studentCount: 20000,
          internationalStudents: 15000,
          accreditations: ['YÖK', 'YÖDAK', 'ABET'],
          rankings: {
            national: 1,
            regional: 5
          },
          programs: [],
          requirements: [
            'High school diploma or equivalent',
            'English proficiency test (IELTS 6.0 or TOEFL 79)',
            'Academic transcripts',
            'Passport copy',
            'Health insurance'
          ],
          facilities: [
            'Modern laboratories',
            'Library with 200,000+ books',
            'Sports facilities',
            'Student dormitories',
            'Medical center'
          ],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: '2',
          name: 'Near East University',
          slug: 'near-east-university',
          logo: '/images/universities/neu-logo.png',
          location: 'Nicosia, Northern Cyprus',
          tuitionFrom: 4000,
          currency: 'USD',
          description: 'Near East University is a comprehensive university offering innovative programs with state-of-the-art facilities and international partnerships.',
          images: [
            '/images/universities/neu-campus-1.jpg',
            '/images/universities/neu-campus-2.jpg'
          ],
          establishedYear: 1988,
          studentCount: 25000,
          internationalStudents: 18000,
          accreditations: ['YÖK', 'YÖDAK'],
          rankings: {
            national: 2,
            regional: 8
          },
          programs: [],
          requirements: [
            'High school diploma or equivalent',
            'English proficiency test (IELTS 6.0 or TOEFL 79)',
            'Academic transcripts',
            'Passport copy'
          ],
          facilities: [
            'Hospital and medical school',
            'Engineering laboratories',
            'Business incubator',
            'Sports complex',
            'Student housing'
          ],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: '3',
          name: 'Cyprus International University',
          slug: 'cyprus-international-university',
          logo: '/images/universities/ciu-logo.png',
          location: 'Nicosia, Northern Cyprus',
          tuitionFrom: 3800,
          currency: 'USD',
          description: 'Cyprus International University provides quality education with a focus on international standards and multicultural learning environment.',
          images: [
            '/images/universities/ciu-campus-1.jpg',
            '/images/universities/ciu-campus-2.jpg'
          ],
          establishedYear: 1997,
          studentCount: 15000,
          internationalStudents: 12000,
          accreditations: ['YÖK', 'YÖDAK'],
          rankings: {
            national: 3,
            regional: 12
          },
          programs: [],
          requirements: [
            'High school diploma or equivalent',
            'English proficiency test (IELTS 6.0 or TOEFL 79)',
            'Academic transcripts',
            'Passport copy'
          ],
          facilities: [
            'Modern classrooms',
            'Computer laboratories',
            'Library',
            'Student center',
            'Cafeteria'
          ],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      ]
      await writeFile(UNIVERSITIES_FILE, JSON.stringify(initialData, null, 2))
      return initialData
    }
    const data = await readFile(UNIVERSITIES_FILE, 'utf-8')
    return JSON.parse(data)
  } catch (error) {
    console.error('Error reading universities:', error)
    return []
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const location = searchParams.get('location')
    const minTuition = searchParams.get('minTuition')
    const maxTuition = searchParams.get('maxTuition')
    const search = searchParams.get('search')

    let universities = await readUniversities()

    // Apply filters
    if (location && location !== 'All Locations') {
      universities = universities.filter((uni: any) => 
        uni.location.toLowerCase().includes(location.toLowerCase())
      )
    }

    if (minTuition) {
      universities = universities.filter((uni: any) => 
        uni.tuitionFrom >= parseInt(minTuition)
      )
    }

    if (maxTuition) {
      universities = universities.filter((uni: any) => 
        uni.tuitionFrom <= parseInt(maxTuition)
      )
    }

    if (search) {
      universities = universities.filter((uni: any) =>
        uni.name.toLowerCase().includes(search.toLowerCase()) ||
        uni.description.toLowerCase().includes(search.toLowerCase())
      )
    }

    return NextResponse.json({
      success: true,
      data: universities,
      total: universities.length
    })

  } catch (error) {
    console.error('Universities API error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch universities' },
      { status: 500 }
    )
  }
}
