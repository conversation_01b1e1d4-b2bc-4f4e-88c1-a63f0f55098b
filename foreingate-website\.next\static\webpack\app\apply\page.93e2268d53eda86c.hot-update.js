/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/apply/page",{

/***/ "(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/chevron-left.js":
/*!*******************************************************************!*\
  !*** ../node_modules/lucide-react/dist/esm/icons/chevron-left.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ ChevronLeft)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"m15 18-6-6 6-6\",\n            key: \"1wnfg3\"\n        }\n    ]\n];\nconst ChevronLeft = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"chevron-left\", __iconNode);\n //# sourceMappingURL=chevron-left.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2ljb25zL2NoZXZyb24tbGVmdC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFHYSxtQkFBdUI7SUFBQztRQUFDLE1BQVE7UUFBQTtZQUFFLEdBQUcsZ0JBQWtCO1lBQUEsSUFBSyxTQUFTO1FBQUEsQ0FBQztLQUFDO0NBQUE7QUFhL0Usa0JBQWMsa0VBQWlCLGlCQUFnQixDQUFVIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXE5pZGhhbFxcRG9jdW1lbnRzXFxzcmNcXGljb25zXFxjaGV2cm9uLWxlZnQudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGNyZWF0ZUx1Y2lkZUljb24gZnJvbSAnLi4vY3JlYXRlTHVjaWRlSWNvbic7XG5pbXBvcnQgeyBJY29uTm9kZSB9IGZyb20gJy4uL3R5cGVzJztcblxuZXhwb3J0IGNvbnN0IF9faWNvbk5vZGU6IEljb25Ob2RlID0gW1sncGF0aCcsIHsgZDogJ20xNSAxOC02LTYgNi02Jywga2V5OiAnMXduZmczJyB9XV07XG5cbi8qKlxuICogQGNvbXBvbmVudCBAbmFtZSBDaGV2cm9uTGVmdFxuICogQGRlc2NyaXB0aW9uIEx1Y2lkZSBTVkcgaWNvbiBjb21wb25lbnQsIHJlbmRlcnMgU1ZHIEVsZW1lbnQgd2l0aCBjaGlsZHJlbi5cbiAqXG4gKiBAcHJldmlldyAhW2ltZ10oZGF0YTppbWFnZS9zdmcreG1sO2Jhc2U2NCxQSE4yWnlBZ2VHMXNibk05SW1oMGRIQTZMeTkzZDNjdWR6TXViM0puTHpJd01EQXZjM1puSWdvZ0lIZHBaSFJvUFNJeU5DSUtJQ0JvWldsbmFIUTlJakkwSWdvZ0lIWnBaWGRDYjNnOUlqQWdNQ0F5TkNBeU5DSUtJQ0JtYVd4c1BTSnViMjVsSWdvZ0lITjBjbTlyWlQwaUl6QXdNQ0lnYzNSNWJHVTlJbUpoWTJ0bmNtOTFibVF0WTI5c2IzSTZJQ05tWm1ZN0lHSnZjbVJsY2kxeVlXUnBkWE02SURKd2VDSUtJQ0J6ZEhKdmEyVXRkMmxrZEdnOUlqSWlDaUFnYzNSeWIydGxMV3hwYm1WallYQTlJbkp2ZFc1a0lnb2dJSE4wY205clpTMXNhVzVsYW05cGJqMGljbTkxYm1RaUNqNEtJQ0E4Y0dGMGFDQmtQU0p0TVRVZ01UZ3ROaTAySURZdE5pSWdMejRLUEM5emRtYytDZz09KSAtIGh0dHBzOi8vbHVjaWRlLmRldi9pY29ucy9jaGV2cm9uLWxlZnRcbiAqIEBzZWUgaHR0cHM6Ly9sdWNpZGUuZGV2L2d1aWRlL3BhY2thZ2VzL2x1Y2lkZS1yZWFjdCAtIERvY3VtZW50YXRpb25cbiAqXG4gKiBAcGFyYW0ge09iamVjdH0gcHJvcHMgLSBMdWNpZGUgaWNvbnMgcHJvcHMgYW5kIGFueSB2YWxpZCBTVkcgYXR0cmlidXRlXG4gKiBAcmV0dXJucyB7SlNYLkVsZW1lbnR9IEpTWCBFbGVtZW50XG4gKlxuICovXG5jb25zdCBDaGV2cm9uTGVmdCA9IGNyZWF0ZUx1Y2lkZUljb24oJ2NoZXZyb24tbGVmdCcsIF9faWNvbk5vZGUpO1xuXG5leHBvcnQgZGVmYXVsdCBDaGV2cm9uTGVmdDtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/chevron-left.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/chevron-right.js":
/*!********************************************************************!*\
  !*** ../node_modules/lucide-react/dist/esm/icons/chevron-right.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ ChevronRight)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"m9 18 6-6-6-6\",\n            key: \"mthhwq\"\n        }\n    ]\n];\nconst ChevronRight = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"chevron-right\", __iconNode);\n //# sourceMappingURL=chevron-right.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2ljb25zL2NoZXZyb24tcmlnaHQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBR2EsbUJBQXVCO0lBQUM7UUFBQyxNQUFRO1FBQUE7WUFBRSxHQUFHLGVBQWlCO1lBQUEsSUFBSyxTQUFTO1FBQUEsQ0FBQztLQUFDO0NBQUE7QUFhOUUsbUJBQWUsa0VBQWlCLGtCQUFpQixDQUFVIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXE5pZGhhbFxcRG9jdW1lbnRzXFxzcmNcXGljb25zXFxjaGV2cm9uLXJpZ2h0LnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBjcmVhdGVMdWNpZGVJY29uIGZyb20gJy4uL2NyZWF0ZUx1Y2lkZUljb24nO1xuaW1wb3J0IHsgSWNvbk5vZGUgfSBmcm9tICcuLi90eXBlcyc7XG5cbmV4cG9ydCBjb25zdCBfX2ljb25Ob2RlOiBJY29uTm9kZSA9IFtbJ3BhdGgnLCB7IGQ6ICdtOSAxOCA2LTYtNi02Jywga2V5OiAnbXRoaHdxJyB9XV07XG5cbi8qKlxuICogQGNvbXBvbmVudCBAbmFtZSBDaGV2cm9uUmlnaHRcbiAqIEBkZXNjcmlwdGlvbiBMdWNpZGUgU1ZHIGljb24gY29tcG9uZW50LCByZW5kZXJzIFNWRyBFbGVtZW50IHdpdGggY2hpbGRyZW4uXG4gKlxuICogQHByZXZpZXcgIVtpbWddKGRhdGE6aW1hZ2Uvc3ZnK3htbDtiYXNlNjQsUEhOMlp5QWdlRzFzYm5NOUltaDBkSEE2THk5M2QzY3Vkek11YjNKbkx6SXdNREF2YzNabklnb2dJSGRwWkhSb1BTSXlOQ0lLSUNCb1pXbG5hSFE5SWpJMElnb2dJSFpwWlhkQ2IzZzlJakFnTUNBeU5DQXlOQ0lLSUNCbWFXeHNQU0p1YjI1bElnb2dJSE4wY205clpUMGlJekF3TUNJZ2MzUjViR1U5SW1KaFkydG5jbTkxYm1RdFkyOXNiM0k2SUNObVptWTdJR0p2Y21SbGNpMXlZV1JwZFhNNklESndlQ0lLSUNCemRISnZhMlV0ZDJsa2RHZzlJaklpQ2lBZ2MzUnliMnRsTFd4cGJtVmpZWEE5SW5KdmRXNWtJZ29nSUhOMGNtOXJaUzFzYVc1bGFtOXBiajBpY205MWJtUWlDajRLSUNBOGNHRjBhQ0JrUFNKdE9TQXhPQ0EyTFRZdE5pMDJJaUF2UGdvOEwzTjJaejRLKSAtIGh0dHBzOi8vbHVjaWRlLmRldi9pY29ucy9jaGV2cm9uLXJpZ2h0XG4gKiBAc2VlIGh0dHBzOi8vbHVjaWRlLmRldi9ndWlkZS9wYWNrYWdlcy9sdWNpZGUtcmVhY3QgLSBEb2N1bWVudGF0aW9uXG4gKlxuICogQHBhcmFtIHtPYmplY3R9IHByb3BzIC0gTHVjaWRlIGljb25zIHByb3BzIGFuZCBhbnkgdmFsaWQgU1ZHIGF0dHJpYnV0ZVxuICogQHJldHVybnMge0pTWC5FbGVtZW50fSBKU1ggRWxlbWVudFxuICpcbiAqL1xuY29uc3QgQ2hldnJvblJpZ2h0ID0gY3JlYXRlTHVjaWRlSWNvbignY2hldnJvbi1yaWdodCcsIF9faWNvbk5vZGUpO1xuXG5leHBvcnQgZGVmYXVsdCBDaGV2cm9uUmlnaHQ7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/chevron-right.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/file-text.js":
/*!****************************************************************!*\
  !*** ../node_modules/lucide-react/dist/esm/icons/file-text.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ FileText)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z\",\n            key: \"1rqfz7\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M14 2v4a2 2 0 0 0 2 2h4\",\n            key: \"tnqrlb\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M10 9H8\",\n            key: \"b1mrlr\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M16 13H8\",\n            key: \"t4e002\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M16 17H8\",\n            key: \"z1uh3a\"\n        }\n    ]\n];\nconst FileText = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"file-text\", __iconNode);\n //# sourceMappingURL=file-text.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/file-text.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/graduation-cap.js":
/*!*********************************************************************!*\
  !*** ../node_modules/lucide-react/dist/esm/icons/graduation-cap.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ GraduationCap)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M21.42 10.922a1 1 0 0 0-.019-1.838L12.83 5.18a2 2 0 0 0-1.66 0L2.6 9.08a1 1 0 0 0 0 1.832l8.57 3.908a2 2 0 0 0 1.66 0z\",\n            key: \"j76jl0\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M22 10v6\",\n            key: \"1lu8f3\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M6 12.5V16a6 3 0 0 0 12 0v-3.5\",\n            key: \"1r8lef\"\n        }\n    ]\n];\nconst GraduationCap = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"graduation-cap\", __iconNode);\n //# sourceMappingURL=graduation-cap.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/graduation-cap.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/send.js":
/*!***********************************************************!*\
  !*** ../node_modules/lucide-react/dist/esm/icons/send.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Send)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z\",\n            key: \"1ffxy3\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m21.854 2.147-10.94 10.939\",\n            key: \"12cjpa\"\n        }\n    ]\n];\nconst Send = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"send\", __iconNode);\n //# sourceMappingURL=send.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/send.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/user.js":
/*!***********************************************************!*\
  !*** ../node_modules/lucide-react/dist/esm/icons/user.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ User)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2\",\n            key: \"975kel\"\n        }\n    ],\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"7\",\n            r: \"4\",\n            key: \"17ys0d\"\n        }\n    ]\n];\nconst User = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"user\", __iconNode);\n //# sourceMappingURL=user.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2ljb25zL3VzZXIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBR08sTUFBTSxVQUF1QjtJQUNsQztRQUFDLE1BQVE7UUFBQTtZQUFFLEdBQUcsQ0FBNkM7WUFBQSxLQUFLO1FBQUEsQ0FBVTtLQUFBO0lBQzFFO1FBQUMsQ0FBVTtRQUFBO1lBQUUsQ0FBSSxRQUFNO1lBQUEsRUFBSTtZQUFLLENBQUc7WUFBSyxDQUFLO1FBQVU7S0FBQTtDQUN6RDtBQWFNLFdBQU8sa0VBQWlCLFNBQVEsQ0FBVSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxOaWRoYWxcXERvY3VtZW50c1xcc3JjXFxpY29uc1xcdXNlci50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY3JlYXRlTHVjaWRlSWNvbiBmcm9tICcuLi9jcmVhdGVMdWNpZGVJY29uJztcbmltcG9ydCB7IEljb25Ob2RlIH0gZnJvbSAnLi4vdHlwZXMnO1xuXG5leHBvcnQgY29uc3QgX19pY29uTm9kZTogSWNvbk5vZGUgPSBbXG4gIFsncGF0aCcsIHsgZDogJ00xOSAyMXYtMmE0IDQgMCAwIDAtNC00SDlhNCA0IDAgMCAwLTQgNHYyJywga2V5OiAnOTc1a2VsJyB9XSxcbiAgWydjaXJjbGUnLCB7IGN4OiAnMTInLCBjeTogJzcnLCByOiAnNCcsIGtleTogJzE3eXMwZCcgfV0sXG5dO1xuXG4vKipcbiAqIEBjb21wb25lbnQgQG5hbWUgVXNlclxuICogQGRlc2NyaXB0aW9uIEx1Y2lkZSBTVkcgaWNvbiBjb21wb25lbnQsIHJlbmRlcnMgU1ZHIEVsZW1lbnQgd2l0aCBjaGlsZHJlbi5cbiAqXG4gKiBAcHJldmlldyAhW2ltZ10oZGF0YTppbWFnZS9zdmcreG1sO2Jhc2U2NCxQSE4yWnlBZ2VHMXNibk05SW1oMGRIQTZMeTkzZDNjdWR6TXViM0puTHpJd01EQXZjM1puSWdvZ0lIZHBaSFJvUFNJeU5DSUtJQ0JvWldsbmFIUTlJakkwSWdvZ0lIWnBaWGRDYjNnOUlqQWdNQ0F5TkNBeU5DSUtJQ0JtYVd4c1BTSnViMjVsSWdvZ0lITjBjbTlyWlQwaUl6QXdNQ0lnYzNSNWJHVTlJbUpoWTJ0bmNtOTFibVF0WTI5c2IzSTZJQ05tWm1ZN0lHSnZjbVJsY2kxeVlXUnBkWE02SURKd2VDSUtJQ0J6ZEhKdmEyVXRkMmxrZEdnOUlqSWlDaUFnYzNSeWIydGxMV3hwYm1WallYQTlJbkp2ZFc1a0lnb2dJSE4wY205clpTMXNhVzVsYW05cGJqMGljbTkxYm1RaUNqNEtJQ0E4Y0dGMGFDQmtQU0pOTVRrZ01qRjJMVEpoTkNBMElEQWdNQ0F3TFRRdE5FZzVZVFFnTkNBd0lEQWdNQzAwSURSMk1pSWdMejRLSUNBOFkybHlZMnhsSUdONFBTSXhNaUlnWTNrOUlqY2lJSEk5SWpRaUlDOCtDand2YzNablBnbz0pIC0gaHR0cHM6Ly9sdWNpZGUuZGV2L2ljb25zL3VzZXJcbiAqIEBzZWUgaHR0cHM6Ly9sdWNpZGUuZGV2L2d1aWRlL3BhY2thZ2VzL2x1Y2lkZS1yZWFjdCAtIERvY3VtZW50YXRpb25cbiAqXG4gKiBAcGFyYW0ge09iamVjdH0gcHJvcHMgLSBMdWNpZGUgaWNvbnMgcHJvcHMgYW5kIGFueSB2YWxpZCBTVkcgYXR0cmlidXRlXG4gKiBAcmV0dXJucyB7SlNYLkVsZW1lbnR9IEpTWCBFbGVtZW50XG4gKlxuICovXG5jb25zdCBVc2VyID0gY3JlYXRlTHVjaWRlSWNvbigndXNlcicsIF9faWNvbk5vZGUpO1xuXG5leHBvcnQgZGVmYXVsdCBVc2VyO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/user.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5Capplication-form.tsx%22%2C%22ids%22%3A%5B%22ApplicationFormSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5Capplication-hero.tsx%22%2C%22ids%22%3A%5B%22ApplicationHeroSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5Cplaceholder-section.tsx%22%2C%22ids%22%3A%5B%22PlaceholderSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Cwhatsapp-widget.tsx%22%2C%22ids%22%3A%5B%22WhatsAppWidget%22%5D%7D&server=false!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5Capplication-form.tsx%22%2C%22ids%22%3A%5B%22ApplicationFormSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5Capplication-hero.tsx%22%2C%22ids%22%3A%5B%22ApplicationHeroSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5Cplaceholder-section.tsx%22%2C%22ids%22%3A%5B%22PlaceholderSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Cwhatsapp-widget.tsx%22%2C%22ids%22%3A%5B%22WhatsAppWidget%22%5D%7D&server=false! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/sections/application-form.tsx */ \"(app-pages-browser)/./src/components/sections/application-form.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/sections/application-hero.tsx */ \"(app-pages-browser)/./src/components/sections/application-hero.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/sections/placeholder-section.tsx */ \"(app-pages-browser)/./src/components/sections/placeholder-section.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/whatsapp-widget.tsx */ \"(app-pages-browser)/./src/components/ui/whatsapp-widget.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5Capplication-form.tsx%22%2C%22ids%22%3A%5B%22ApplicationFormSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5Capplication-hero.tsx%22%2C%22ids%22%3A%5B%22ApplicationHeroSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5Cplaceholder-section.tsx%22%2C%22ids%22%3A%5B%22PlaceholderSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Cwhatsapp-widget.tsx%22%2C%22ids%22%3A%5B%22WhatsAppWidget%22%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/sections/application-form.tsx":
/*!******************************************************!*\
  !*** ./src/components/sections/application-form.tsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApplicationFormSection: () => (/* binding */ ApplicationFormSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/../node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_intersection_observer__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-intersection-observer */ \"(app-pages-browser)/../node_modules/react-intersection-observer/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_ChevronLeft_ChevronRight_FileText_GraduationCap_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,ChevronLeft,ChevronRight,FileText,GraduationCap,Send,User!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_ChevronLeft_ChevronRight_FileText_GraduationCap_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,ChevronLeft,ChevronRight,FileText,GraduationCap,Send,User!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_ChevronLeft_ChevronRight_FileText_GraduationCap_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,ChevronLeft,ChevronRight,FileText,GraduationCap,Send,User!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_ChevronLeft_ChevronRight_FileText_GraduationCap_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,ChevronLeft,ChevronRight,FileText,GraduationCap,Send,User!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_ChevronLeft_ChevronRight_FileText_GraduationCap_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,ChevronLeft,ChevronRight,FileText,GraduationCap,Send,User!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_ChevronLeft_ChevronRight_FileText_GraduationCap_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,ChevronLeft,ChevronRight,FileText,GraduationCap,Send,User!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_ChevronLeft_ChevronRight_FileText_GraduationCap_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,ChevronLeft,ChevronRight,FileText,GraduationCap,Send,User!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* __next_internal_client_entry_do_not_use__ ApplicationFormSection auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst steps = [\n    {\n        id: 1,\n        title: 'Personal Information',\n        icon: _barrel_optimize_names_CheckCircle_ChevronLeft_ChevronRight_FileText_GraduationCap_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    },\n    {\n        id: 2,\n        title: 'Academic Background',\n        icon: _barrel_optimize_names_CheckCircle_ChevronLeft_ChevronRight_FileText_GraduationCap_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n    },\n    {\n        id: 3,\n        title: 'Program Selection',\n        icon: _barrel_optimize_names_CheckCircle_ChevronLeft_ChevronRight_FileText_GraduationCap_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    },\n    {\n        id: 4,\n        title: 'Review & Submit',\n        icon: _barrel_optimize_names_CheckCircle_ChevronLeft_ChevronRight_FileText_GraduationCap_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    }\n];\nfunction ApplicationFormSection() {\n    _s();\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        // Personal Information\n        firstName: '',\n        lastName: '',\n        email: '',\n        phone: '',\n        dateOfBirth: '',\n        nationality: '',\n        passportNumber: '',\n        // Academic Background\n        highSchoolName: '',\n        graduationYear: '',\n        gpa: '',\n        englishProficiency: '',\n        // Program Selection\n        preferredUniversity: '',\n        firstChoiceProgram: '',\n        secondChoiceProgram: '',\n        intakeYear: '',\n        intakeSemester: ''\n    });\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [submitted, setSubmitted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [ref, inView] = (0,react_intersection_observer__WEBPACK_IMPORTED_MODULE_7__.useInView)({\n        triggerOnce: true,\n        threshold: 0.1\n    });\n    const handleInputChange = (e)=>{\n        const { name, value } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n    };\n    const nextStep = ()=>{\n        if (currentStep < steps.length) {\n            setCurrentStep(currentStep + 1);\n        }\n    };\n    const prevStep = ()=>{\n        if (currentStep > 1) {\n            setCurrentStep(currentStep - 1);\n        }\n    };\n    const handleSubmit = async ()=>{\n        setIsSubmitting(true);\n        // Simulate API call\n        await new Promise((resolve)=>setTimeout(resolve, 2000));\n        setIsSubmitting(false);\n        setSubmitted(true);\n    };\n    if (submitted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n            className: \"section-padding bg-muted/30\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        scale: 0.8\n                    },\n                    animate: {\n                        opacity: 1,\n                        scale: 1\n                    },\n                    className: \"max-w-2xl mx-auto text-center bg-background rounded-xl p-12 shadow-sm border\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-20 h-20 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_ChevronLeft_ChevronRight_FileText_GraduationCap_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"w-10 h-10 text-green-600 dark:text-green-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-3xl font-bold mb-4\",\n                            children: \"Application Submitted Successfully!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground mb-6\",\n                            children: \"Thank you for your application. Our admissions team will review your application and contact you within 24-48 hours with next steps.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-muted/50 rounded-lg p-4 mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Application ID:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                            lineNumber: 93,\n                                            columnNumber: 17\n                                        }, this),\n                                        \" FG-2024-\",\n                                        Math.random().toString(36).substr(2, 9).toUpperCase()\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm mt-1\",\n                                    children: \"Please save this ID for future reference.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            size: \"lg\",\n                            children: \"Track Application Status\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                lineNumber: 77,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n            lineNumber: 76,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        ref: ref,\n        className: \"section-padding bg-muted/30\",\n        id: \"application-form\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 30\n                },\n                animate: inView ? {\n                    opacity: 1,\n                    y: 0\n                } : {},\n                transition: {\n                    duration: 0.8\n                },\n                className: \"max-w-4xl mx-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl md:text-4xl font-bold mb-4\",\n                                children: [\n                                    \"Complete Your\",\n                                    ' ',\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"gradient-text\",\n                                        children: \"University Application\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-muted-foreground\",\n                                children: \"Follow our guided process to submit your application in just a few minutes\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between max-w-2xl mx-auto\",\n                                children: steps.map((step, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center w-12 h-12 rounded-full border-2 transition-all duration-300 \".concat(currentStep >= step.id ? 'bg-primary border-primary text-primary-foreground' : 'border-muted-foreground/30 text-muted-foreground'),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(step.icon, {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                    lineNumber: 138,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 19\n                                            }, this),\n                                            index < steps.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-0.5 mx-2 transition-all duration-300 \".concat(currentStep > step.id ? 'bg-primary' : 'bg-muted-foreground/30')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, step.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between max-w-2xl mx-auto mt-4\",\n                                children: steps.map((step)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center flex-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm font-medium transition-colors \".concat(currentStep >= step.id ? 'text-primary' : 'text-muted-foreground'),\n                                            children: step.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, step.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            x: 50\n                        },\n                        animate: {\n                            opacity: 1,\n                            x: 0\n                        },\n                        exit: {\n                            opacity: 0,\n                            x: -50\n                        },\n                        transition: {\n                            duration: 0.3\n                        },\n                        className: \"bg-background rounded-xl p-8 shadow-sm border\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-2xl font-bold mb-6\",\n                                children: steps[currentStep - 1].title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 13\n                            }, this),\n                            currentStep === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid md:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-2\",\n                                                children: \"First Name *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                name: \"firstName\",\n                                                value: formData.firstName,\n                                                onChange: handleInputChange,\n                                                className: \"w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary\",\n                                                placeholder: \"Enter your first name\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                lineNumber: 177,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-2\",\n                                                children: \"Last Name *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                name: \"lastName\",\n                                                value: formData.lastName,\n                                                onChange: handleInputChange,\n                                                className: \"w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary\",\n                                                placeholder: \"Enter your last name\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-2\",\n                                                children: \"Email Address *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                lineNumber: 198,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"email\",\n                                                name: \"email\",\n                                                value: formData.email,\n                                                onChange: handleInputChange,\n                                                className: \"w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary\",\n                                                placeholder: \"<EMAIL>\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-2\",\n                                                children: \"Phone Number *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                lineNumber: 209,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"tel\",\n                                                name: \"phone\",\n                                                value: formData.phone,\n                                                onChange: handleInputChange,\n                                                className: \"w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary\",\n                                                placeholder: \"+****************\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-2\",\n                                                children: \"Date of Birth *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"date\",\n                                                name: \"dateOfBirth\",\n                                                value: formData.dateOfBirth,\n                                                onChange: handleInputChange,\n                                                className: \"w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-2\",\n                                                children: \"Nationality *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                name: \"nationality\",\n                                                value: formData.nationality,\n                                                onChange: handleInputChange,\n                                                className: \"w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        children: \"Select your nationality\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                        lineNumber: 237,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"US\",\n                                                        children: \"United States\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                        lineNumber: 238,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"UK\",\n                                                        children: \"United Kingdom\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                        lineNumber: 239,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"CA\",\n                                                        children: \"Canada\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                        lineNumber: 240,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"AU\",\n                                                        children: \"Australia\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                        lineNumber: 241,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"DE\",\n                                                        children: \"Germany\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                        lineNumber: 242,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"FR\",\n                                                        children: \"France\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                        lineNumber: 243,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"other\",\n                                                        children: \"Other\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                        lineNumber: 244,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                lineNumber: 231,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 15\n                            }, this),\n                            currentStep === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid md:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-2\",\n                                                children: \"High School Name *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                name: \"highSchoolName\",\n                                                value: formData.highSchoolName,\n                                                onChange: handleInputChange,\n                                                className: \"w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary\",\n                                                placeholder: \"Enter your high school name\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-2\",\n                                                children: \"Graduation Year *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                name: \"graduationYear\",\n                                                value: formData.graduationYear,\n                                                onChange: handleInputChange,\n                                                className: \"w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        children: \"Select graduation year\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                        lineNumber: 272,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    Array.from({\n                                                        length: 10\n                                                    }, (_, i)=>2024 - i).map((year)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: year,\n                                                            children: year\n                                                        }, year, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                            lineNumber: 274,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                        lineNumber: 264,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-2\",\n                                                children: \"GPA / Grade Average *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                name: \"gpa\",\n                                                value: formData.gpa,\n                                                onChange: handleInputChange,\n                                                className: \"w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary\",\n                                                placeholder: \"e.g., 3.5/4.0 or 85%\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-2\",\n                                                children: \"English Proficiency\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                name: \"englishProficiency\",\n                                                value: formData.englishProficiency,\n                                                onChange: handleInputChange,\n                                                className: \"w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        children: \"Select test type\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                        lineNumber: 297,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"IELTS\",\n                                                        children: \"IELTS\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                        lineNumber: 298,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"TOEFL\",\n                                                        children: \"TOEFL\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                        lineNumber: 299,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"Native\",\n                                                        children: \"Native English Speaker\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                        lineNumber: 300,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"None\",\n                                                        children: \"No test taken yet\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                        lineNumber: 301,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                lineNumber: 291,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 15\n                            }, this),\n                            currentStep === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-2\",\n                                                children: \"Preferred University *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                lineNumber: 311,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                name: \"preferredUniversity\",\n                                                value: formData.preferredUniversity,\n                                                onChange: handleInputChange,\n                                                className: \"w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        children: \"Select preferred university\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                        lineNumber: 318,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"EMU\",\n                                                        children: \"Eastern Mediterranean University\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                        lineNumber: 319,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"NEU\",\n                                                        children: \"Near East University\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                        lineNumber: 320,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"CIU\",\n                                                        children: \"Cyprus International University\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                        lineNumber: 321,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"UKU\",\n                                                        children: \"University of Kyrenia\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                        lineNumber: 322,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                lineNumber: 312,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                        lineNumber: 310,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid md:grid-cols-2 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-2\",\n                                                        children: \"First Choice Program *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                        lineNumber: 327,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        name: \"firstChoiceProgram\",\n                                                        value: formData.firstChoiceProgram,\n                                                        onChange: handleInputChange,\n                                                        className: \"w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: \"Select program\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                                lineNumber: 334,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"Computer Engineering\",\n                                                                children: \"Computer Engineering\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                                lineNumber: 335,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"Business Administration\",\n                                                                children: \"Business Administration\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                                lineNumber: 336,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"Medicine\",\n                                                                children: \"Medicine\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                                lineNumber: 337,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"Architecture\",\n                                                                children: \"Architecture\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                                lineNumber: 338,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"Psychology\",\n                                                                children: \"Psychology\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                                lineNumber: 339,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                        lineNumber: 328,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                lineNumber: 326,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-2\",\n                                                        children: \"Second Choice Program\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                        lineNumber: 343,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        name: \"secondChoiceProgram\",\n                                                        value: formData.secondChoiceProgram,\n                                                        onChange: handleInputChange,\n                                                        className: \"w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: \"Select program\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                                lineNumber: 350,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"Computer Engineering\",\n                                                                children: \"Computer Engineering\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                                lineNumber: 351,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"Business Administration\",\n                                                                children: \"Business Administration\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                                lineNumber: 352,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"Medicine\",\n                                                                children: \"Medicine\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                                lineNumber: 353,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"Architecture\",\n                                                                children: \"Architecture\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                                lineNumber: 354,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"Psychology\",\n                                                                children: \"Psychology\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                                lineNumber: 355,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                        lineNumber: 344,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                lineNumber: 342,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid md:grid-cols-2 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-2\",\n                                                        children: \"Intake Year *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                        lineNumber: 361,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        name: \"intakeYear\",\n                                                        value: formData.intakeYear,\n                                                        onChange: handleInputChange,\n                                                        className: \"w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: \"Select year\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                                lineNumber: 368,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"2024\",\n                                                                children: \"2024\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                                lineNumber: 369,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"2025\",\n                                                                children: \"2025\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                                lineNumber: 370,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                        lineNumber: 362,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                lineNumber: 360,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-2\",\n                                                        children: \"Intake Semester *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                        lineNumber: 374,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        name: \"intakeSemester\",\n                                                        value: formData.intakeSemester,\n                                                        onChange: handleInputChange,\n                                                        className: \"w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: \"Select semester\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                                lineNumber: 381,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"Fall\",\n                                                                children: \"Fall (September)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                                lineNumber: 382,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"Spring\",\n                                                                children: \"Spring (February)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                                lineNumber: 383,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"Summer\",\n                                                                children: \"Summer (June)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                                lineNumber: 384,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                        lineNumber: 375,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                lineNumber: 373,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                        lineNumber: 359,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                lineNumber: 309,\n                                columnNumber: 15\n                            }, this),\n                            currentStep === 4 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-muted/50 rounded-lg p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-semibold mb-4\",\n                                                children: \"Application Summary\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                lineNumber: 395,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid md:grid-cols-2 gap-4 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Name:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                                lineNumber: 398,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" \",\n                                                            formData.firstName,\n                                                            \" \",\n                                                            formData.lastName\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                        lineNumber: 397,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Email:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                                lineNumber: 401,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" \",\n                                                            formData.email\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                        lineNumber: 400,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Nationality:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                                lineNumber: 404,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" \",\n                                                            formData.nationality\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                        lineNumber: 403,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"High School:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                                lineNumber: 407,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" \",\n                                                            formData.highSchoolName\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                        lineNumber: 406,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Preferred University:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                                lineNumber: 410,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" \",\n                                                            formData.preferredUniversity\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                        lineNumber: 409,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"First Choice Program:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                                lineNumber: 413,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" \",\n                                                            formData.firstChoiceProgram\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                        lineNumber: 412,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                lineNumber: 396,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                        lineNumber: 394,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-50 dark:bg-blue-950/20 rounded-lg p-6 border border-blue-200 dark:border-blue-800\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-semibold mb-2 text-blue-800 dark:text-blue-200\",\n                                                children: \"Next Steps\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                lineNumber: 419,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"text-sm text-blue-700 dark:text-blue-300 space-y-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• Our admissions team will review your application within 24-48 hours\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                        lineNumber: 421,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• You'll receive an email with document requirements and next steps\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                        lineNumber: 422,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• A dedicated counselor will be assigned to guide you through the process\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                        lineNumber: 423,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• We'll help you with visa applications and accommodation arrangements\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                        lineNumber: 424,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                lineNumber: 420,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                        lineNumber: 418,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                lineNumber: 393,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between mt-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"outline\",\n                                        onClick: prevStep,\n                                        disabled: currentStep === 1,\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_ChevronLeft_ChevronRight_FileText_GraduationCap_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                lineNumber: 438,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Previous\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                        lineNumber: 432,\n                                        columnNumber: 15\n                                    }, this),\n                                    currentStep < steps.length ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        onClick: nextStep,\n                                        className: \"flex items-center\",\n                                        children: [\n                                            \"Next\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_ChevronLeft_ChevronRight_FileText_GraduationCap_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"w-4 h-4 ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                lineNumber: 445,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                        lineNumber: 443,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        onClick: handleSubmit,\n                                        disabled: isSubmitting,\n                                        className: \"flex items-center\",\n                                        size: \"lg\",\n                                        children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                    lineNumber: 456,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \"Submitting...\"\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                \"Submit Application\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_ChevronLeft_ChevronRight_FileText_GraduationCap_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"w-4 h-4 ml-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                                    lineNumber: 462,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                        lineNumber: 448,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                                lineNumber: 431,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, currentStep, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n                lineNumber: 111,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n            lineNumber: 110,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\application-form.tsx\",\n        lineNumber: 109,\n        columnNumber: 5\n    }, this);\n}\n_s(ApplicationFormSection, \"dwX0sGf22eQyB76f9aenJZZq8+c=\", false, function() {\n    return [\n        react_intersection_observer__WEBPACK_IMPORTED_MODULE_7__.useInView\n    ];\n});\n_c = ApplicationFormSection;\nvar _c;\n$RefreshReg$(_c, \"ApplicationFormSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/application-form.tsx\n"));

/***/ })

});