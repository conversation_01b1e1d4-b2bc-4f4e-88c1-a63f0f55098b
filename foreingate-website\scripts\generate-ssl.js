#!/usr/bin/env node

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

// Create certificates directory
const certsDir = path.join(__dirname, '..', 'certificates')
if (!fs.existsSync(certsDir)) {
  fs.mkdirSync(certsDir, { recursive: true })
}

console.log('🔐 Generating SSL certificates for development...')

// Check if OpenSSL is available
try {
  execSync('openssl version', { stdio: 'ignore' })
} catch (error) {
  console.error('❌ OpenSSL is not installed. Please install OpenSSL first.')
  console.log('Windows: Download from https://slproweb.com/products/Win32OpenSSL.html')
  console.log('macOS: brew install openssl')
  console.log('Linux: sudo apt-get install openssl')
  process.exit(1)
}

// Generate private key
console.log('📝 Generating private key...')
try {
  execSync(`openssl genrsa -out "${path.join(certsDir, 'localhost.key')}" 2048`, { stdio: 'inherit' })
} catch (error) {
  console.error('❌ Failed to generate private key')
  process.exit(1)
}

// Create certificate configuration
const configContent = `[req]
distinguished_name = req_distinguished_name
x509_extensions = v3_req
prompt = no

[req_distinguished_name]
C = CY
ST = Northern Cyprus
L = Nicosia
O = Foreingate Group
OU = IT Department
CN = localhost

[v3_req]
keyUsage = keyEncipherment, dataEncipherment
extendedKeyUsage = serverAuth
subjectAltName = @alt_names

[alt_names]
DNS.1 = localhost
DNS.2 = *.localhost
DNS.3 = 127.0.0.1
DNS.4 = ::1
IP.1 = 127.0.0.1
IP.2 = ::1`

const configPath = path.join(certsDir, 'localhost.conf')
fs.writeFileSync(configPath, configContent)

// Generate certificate
console.log('📜 Generating SSL certificate...')
try {
  execSync(`openssl req -new -x509 -key "${path.join(certsDir, 'localhost.key')}" -out "${path.join(certsDir, 'localhost.crt')}" -days 365 -config "${configPath}"`, { stdio: 'inherit' })
} catch (error) {
  console.error('❌ Failed to generate certificate')
  process.exit(1)
}

// Generate PEM format (for some applications)
console.log('🔗 Creating PEM certificate...')
try {
  const keyContent = fs.readFileSync(path.join(certsDir, 'localhost.key'), 'utf8')
  const crtContent = fs.readFileSync(path.join(certsDir, 'localhost.crt'), 'utf8')
  const pemContent = keyContent + '\n' + crtContent
  fs.writeFileSync(path.join(certsDir, 'localhost.pem'), pemContent)
} catch (error) {
  console.error('❌ Failed to create PEM certificate')
  process.exit(1)
}

// Clean up config file
fs.unlinkSync(configPath)

console.log('✅ SSL certificates generated successfully!')
console.log(`📁 Certificates location: ${certsDir}`)
console.log('📋 Generated files:')
console.log('   - localhost.key (Private Key)')
console.log('   - localhost.crt (Certificate)')
console.log('   - localhost.pem (Combined PEM)')
console.log('')
console.log('🔒 To trust the certificate in your browser:')
console.log('   1. Open the certificate file: localhost.crt')
console.log('   2. Install it in your system\'s trusted root certificates')
console.log('   3. Restart your browser')
console.log('')
console.log('🚀 You can now run the HTTPS server with: npm run dev:https')
