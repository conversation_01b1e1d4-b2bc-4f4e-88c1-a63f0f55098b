// RAG (Retrieval-Augmented Generation) System for Smart Chatbot
import { websiteKnowledge } from './chatbot-knowledge'
import { SemanticSearchEngine, QuestionClassifier } from './semantic-search'
import { EnhancedKnowledgeBase } from './document-processor'

// Simple vector embedding using TF-IDF approach
export class VectorEmbedding {
  private vocabulary: Map<string, number> = new Map()
  private idf: Map<string, number> = new Map()
  private documents: string[] = []

  constructor() {
    this.buildVocabulary()
  }

  private buildVocabulary() {
    // Build vocabulary from knowledge base
    const allTexts = this.extractAllTexts()
    this.documents = allTexts
    
    const wordCounts = new Map<string, number>()
    const docWordCounts = new Map<string, Set<string>>()

    allTexts.forEach((text, docIndex) => {
      const words = this.tokenize(text)
      const uniqueWords = new Set(words)
      docWordCounts.set(docIndex.toString(), uniqueWords)

      words.forEach(word => {
        wordCounts.set(word, (wordCounts.get(word) || 0) + 1)
        if (!this.vocabulary.has(word)) {
          this.vocabulary.set(word, this.vocabulary.size)
        }
      })
    })

    // Calculate IDF scores
    const totalDocs = allTexts.length
    this.vocabulary.forEach((_, word) => {
      let docFreq = 0
      docWordCounts.forEach(words => {
        if (words.has(word)) docFreq++
      })
      this.idf.set(word, Math.log(totalDocs / (docFreq + 1)))
    })
  }

  private extractAllTexts(): string[] {
    const texts: string[] = []
    
    // Extract from company info
    texts.push(`${websiteKnowledge.company.name} ${websiteKnowledge.company.description} ${websiteKnowledge.company.mission}`)
    
    // Extract from services
    websiteKnowledge.services.forEach(service => {
      texts.push(`${service.name} ${service.description} ${service.features.join(' ')} ${service.pricing}`)
    })
    
    // Extract from universities
    websiteKnowledge.universities.forEach(uni => {
      texts.push(`${uni.name} ${uni.location} ${uni.programs.join(' ')} ${uni.language} ${uni.accreditation}`)
    })
    
    // Extract from FAQ
    websiteKnowledge.faq.forEach(faq => {
      texts.push(`${faq.question} ${faq.answer}`)
    })
    
    // Extract from testimonials
    websiteKnowledge.testimonials.forEach(testimonial => {
      texts.push(`${testimonial.name} ${testimonial.country} ${testimonial.program} ${testimonial.university} ${testimonial.comment}`)
    })
    
    // Extract from costs
    texts.push(`tuition fees undergraduate graduate medicine living costs accommodation food transportation`)
    
    // Extract from scholarships
    websiteKnowledge.scholarships.forEach(scholarship => {
      texts.push(`${scholarship.name} ${scholarship.amount} ${scholarship.criteria} ${scholarship.eligibility}`)
    })

    return texts
  }

  private tokenize(text: string): string[] {
    return text.toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 2)
  }

  public embed(text: string): number[] {
    const words = this.tokenize(text)
    const vector = new Array(this.vocabulary.size).fill(0)
    
    // Calculate TF scores
    const wordCounts = new Map<string, number>()
    words.forEach(word => {
      wordCounts.set(word, (wordCounts.get(word) || 0) + 1)
    })
    
    // Calculate TF-IDF vector
    wordCounts.forEach((count, word) => {
      const vocabIndex = this.vocabulary.get(word)
      if (vocabIndex !== undefined) {
        const tf = count / words.length
        const idf = this.idf.get(word) || 0
        vector[vocabIndex] = tf * idf
      }
    })
    
    return vector
  }

  public similarity(vec1: number[], vec2: number[]): number {
    let dotProduct = 0
    let norm1 = 0
    let norm2 = 0
    
    for (let i = 0; i < vec1.length; i++) {
      dotProduct += vec1[i] * vec2[i]
      norm1 += vec1[i] * vec1[i]
      norm2 += vec2[i] * vec2[i]
    }
    
    const magnitude = Math.sqrt(norm1) * Math.sqrt(norm2)
    return magnitude === 0 ? 0 : dotProduct / magnitude
  }
}

// Knowledge retrieval system
export class KnowledgeRetriever {
  private embedding: VectorEmbedding
  private knowledgeChunks: Array<{
    content: string
    metadata: any
    vector: number[]
    category: string
  }> = []

  constructor() {
    this.embedding = new VectorEmbedding()
    this.buildKnowledgeBase()
  }

  private buildKnowledgeBase() {
    // University information chunks
    websiteKnowledge.universities.forEach(uni => {
      const content = `${uni.name} is located in ${uni.location}, established in ${uni.established}. 
        It has ${uni.students} students including ${uni.international} international students. 
        Programs offered: ${uni.programs.join(', ')}. 
        Tuition ranges from $${uni.tuition.min} to $${uni.tuition.max} per year. 
        Language of instruction: ${uni.language}. 
        Accreditation: ${uni.accreditation}.`
      
      this.knowledgeChunks.push({
        content,
        metadata: uni,
        vector: this.embedding.embed(content),
        category: 'universities'
      })
    })

    // Service information chunks
    websiteKnowledge.services.forEach(service => {
      const content = `${service.name}: ${service.description}. 
        Features include: ${service.features.join(', ')}. 
        Pricing: ${service.pricing}.`
      
      this.knowledgeChunks.push({
        content,
        metadata: service,
        vector: this.embedding.embed(content),
        category: 'services'
      })
    })

    // FAQ chunks
    websiteKnowledge.faq.forEach(faq => {
      this.knowledgeChunks.push({
        content: `Question: ${faq.question} Answer: ${faq.answer}`,
        metadata: faq,
        vector: this.embedding.embed(`${faq.question} ${faq.answer}`),
        category: 'faq'
      })
    })

    // Cost information chunks
    const costContent = `Undergraduate tuition: $${websiteKnowledge.costs.tuition.undergraduate.min}-${websiteKnowledge.costs.tuition.undergraduate.max} per year.
      Graduate tuition: $${websiteKnowledge.costs.tuition.graduate.min}-${websiteKnowledge.costs.tuition.graduate.max} per year.
      Medicine tuition: $${websiteKnowledge.costs.tuition.medicine.min}-${websiteKnowledge.costs.tuition.medicine.max} per year.
      Living costs: $${websiteKnowledge.costs.living.total.min}-${websiteKnowledge.costs.living.total.max} per month.
      Accommodation: $${websiteKnowledge.costs.living.accommodation.min}-${websiteKnowledge.costs.living.accommodation.max} per month.`
    
    this.knowledgeChunks.push({
      content: costContent,
      metadata: websiteKnowledge.costs,
      vector: this.embedding.embed(costContent),
      category: 'costs'
    })

    // Scholarship chunks
    websiteKnowledge.scholarships.forEach(scholarship => {
      const content = `${scholarship.name}: ${scholarship.amount} reduction. 
        Criteria: ${scholarship.criteria}. 
        Eligibility: ${scholarship.eligibility || 'General eligibility applies'}.`
      
      this.knowledgeChunks.push({
        content,
        metadata: scholarship,
        vector: this.embedding.embed(content),
        category: 'scholarships'
      })
    })

    // Admission process chunk
    const admissionContent = `Admission process steps: ${websiteKnowledge.admissionProcess.steps.join(', ')}. 
      Timeline: ${websiteKnowledge.admissionProcess.timeline}. 
      Undergraduate requirements: ${websiteKnowledge.admissionProcess.requirements.undergraduate.join(', ')}. 
      Graduate requirements: ${websiteKnowledge.admissionProcess.requirements.graduate.join(', ')}.`
    
    this.knowledgeChunks.push({
      content: admissionContent,
      metadata: websiteKnowledge.admissionProcess,
      vector: this.embedding.embed(admissionContent),
      category: 'admissions'
    })

    // Location and lifestyle chunk
    const locationContent = `Northern Cyprus is a safe Mediterranean island with excellent weather, 
      low crime rates, and a welcoming international community. 
      It's located between Turkey and the Middle East, offering rich cultural experiences.`
    
    this.knowledgeChunks.push({
      content: locationContent,
      metadata: { location: 'Northern Cyprus' },
      vector: this.embedding.embed(locationContent),
      category: 'location'
    })
  }

  public retrieve(query: string, topK: number = 5): Array<{
    content: string
    metadata: any
    similarity: number
    category: string
  }> {
    const queryVector = this.embedding.embed(query)
    
    const similarities = this.knowledgeChunks.map(chunk => ({
      ...chunk,
      similarity: this.embedding.similarity(queryVector, chunk.vector)
    }))
    
    return similarities
      .sort((a, b) => b.similarity - a.similarity)
      .slice(0, topK)
      .filter(item => item.similarity > 0.1) // Minimum similarity threshold
  }

  public retrieveByCategory(query: string, category: string, topK: number = 3) {
    const categoryChunks = this.knowledgeChunks.filter(chunk => chunk.category === category)
    const queryVector = this.embedding.embed(query)
    
    const similarities = categoryChunks.map(chunk => ({
      ...chunk,
      similarity: this.embedding.similarity(queryVector, chunk.vector)
    }))
    
    return similarities
      .sort((a, b) => b.similarity - a.similarity)
      .slice(0, topK)
  }
}

// Enhanced response generation with RAG
export class RAGResponseGenerator {
  private retriever: KnowledgeRetriever
  private semanticSearch: SemanticSearchEngine
  private questionClassifier: QuestionClassifier
  private enhancedKB: EnhancedKnowledgeBase

  constructor() {
    this.retriever = new KnowledgeRetriever()
    this.semanticSearch = new SemanticSearchEngine()
    this.questionClassifier = new QuestionClassifier()
    this.enhancedKB = new EnhancedKnowledgeBase()
  }

  public generateResponse(query: string, category?: string): {
    response: string
    sources: any[]
    confidence: number
    retrievedChunks: number
    questionType?: string
    semanticMatches?: any[]
  } {
    // Classify the question type for better response generation
    const questionType = this.questionClassifier.classifyQuestion(query)

    // Use semantic search for better retrieval
    const expandedQuery = this.semanticSearch.expandQuery(query)
    const semanticQuery = expandedQuery.join(' ')

    // Retrieve relevant information using both methods
    const retrievedInfo = category
      ? this.retriever.retrieveByCategory(semanticQuery, category, 3)
      : this.retriever.retrieve(semanticQuery, 5)

    // Enhance with semantic search
    const semanticMatches = this.semanticSearch.findBestMatches(query, retrievedInfo, 3)

    if (retrievedInfo.length === 0 && semanticMatches.length === 0) {
      return {
        response: "I don't have specific information about that topic. Let me connect you with our human advisors who can provide detailed assistance.",
        sources: [],
        confidence: 0,
        retrievedChunks: 0,
        questionType,
        semanticMatches: []
      }
    }

    // Combine and rank results
    const combinedResults = this.combineResults(retrievedInfo, semanticMatches)

    // Calculate enhanced confidence
    const confidence = this.calculateEnhancedConfidence(query, combinedResults, questionType)

    // Generate contextual response using question type
    const response = this.synthesizeEnhancedResponse(query, combinedResults, questionType)

    return {
      response,
      sources: combinedResults.map(item => item.metadata || item),
      confidence,
      retrievedChunks: combinedResults.length,
      questionType,
      semanticMatches
    }
  }

  private combineResults(retrievedInfo: any[], semanticMatches: any[]): any[] {
    const combined = [...retrievedInfo]

    // Add semantic matches that aren't already included
    semanticMatches.forEach(match => {
      const exists = combined.some(item =>
        item.content === match.content ||
        (item.metadata && match.metadata && item.metadata.id === match.metadata.id)
      )
      if (!exists) {
        combined.push({
          content: match.content,
          metadata: match.metadata,
          similarity: match.score,
          category: match.metadata?.category || 'general'
        })
      }
    })

    // Sort by relevance
    return combined.sort((a, b) => (b.similarity || 0) - (a.similarity || 0)).slice(0, 5)
  }

  private calculateEnhancedConfidence(query: string, results: any[], questionType: string): number {
    if (results.length === 0) return 0

    let baseConfidence = results.reduce((sum, item) => sum + (item.similarity || 0), 0) / results.length

    // Boost confidence based on question type match
    const questionTypeBoost = {
      'comparison': 0.1,
      'cost_inquiry': 0.15,
      'process_inquiry': 0.1,
      'factual_inquiry': 0.05,
      'eligibility_inquiry': 0.1,
      'general': 0
    }

    baseConfidence += questionTypeBoost[questionType as keyof typeof questionTypeBoost] || 0

    // Boost for multiple relevant results
    if (results.length >= 3) baseConfidence += 0.1

    // Boost for semantic keyword matches
    const queryLower = query.toLowerCase()
    const hasKeywords = ['university', 'cost', 'admission', 'scholarship', 'visa'].some(keyword =>
      queryLower.includes(keyword)
    )
    if (hasKeywords) baseConfidence += 0.05

    return Math.min(baseConfidence * 100, 95)
  }

  private synthesizeEnhancedResponse(query: string, results: any[], questionType: string): string {
    if (results.length === 0) {
      return "I don't have specific information about that topic. Let me connect you with our human advisors who can provide detailed assistance."
    }

    // Use question classifier to generate appropriate response template
    const contextualResponse = this.questionClassifier.generateResponseTemplate(questionType, results)

    // If template response is generic, fall back to original synthesis
    if (contextualResponse.includes('Based on the information available')) {
      return this.synthesizeResponse(query, results)
    }

    return contextualResponse
  }

  private synthesizeResponse(query: string, retrievedInfo: any[]): string {
    const queryLower = query.toLowerCase()
    
    // Determine response type based on query
    if (queryLower.includes('university') || queryLower.includes('emu') || queryLower.includes('neu') || queryLower.includes('ciu')) {
      return this.generateUniversityResponse(retrievedInfo)
    } else if (queryLower.includes('cost') || queryLower.includes('fee') || queryLower.includes('price') || queryLower.includes('tuition')) {
      return this.generateCostResponse(retrievedInfo)
    } else if (queryLower.includes('scholarship') || queryLower.includes('financial aid')) {
      return this.generateScholarshipResponse(retrievedInfo)
    } else if (queryLower.includes('admission') || queryLower.includes('apply') || queryLower.includes('application')) {
      return this.generateAdmissionResponse(retrievedInfo)
    } else if (queryLower.includes('service') || queryLower.includes('help') || queryLower.includes('support')) {
      return this.generateServiceResponse(retrievedInfo)
    } else {
      return this.generateGeneralResponse(retrievedInfo)
    }
  }

  private generateUniversityResponse(retrievedInfo: any[]): string {
    const universities = retrievedInfo.filter(item => item.category === 'universities')
    
    if (universities.length === 0) {
      return "I can provide information about our partner universities. We work with EMU, NEU, and CIU - all excellent institutions in Northern Cyprus."
    }

    let response = "🏛️ **University Information:**\n\n"
    
    universities.forEach(uni => {
      const metadata = uni.metadata
      response += `**${metadata.name}**\n`
      response += `📍 Location: ${metadata.location}\n`
      response += `👥 Students: ${metadata.students?.toLocaleString()} (${metadata.international?.toLocaleString()} international)\n`
      response += `🎓 Programs: ${metadata.programs?.join(', ')}\n`
      response += `💰 Tuition: $${metadata.tuition?.min?.toLocaleString()}-$${metadata.tuition?.max?.toLocaleString()} per year\n`
      response += `🌍 Language: ${metadata.language}\n\n`
    })

    response += "Would you like more details about any specific university or program?"
    return response
  }

  private generateCostResponse(retrievedInfo: any[]): string {
    const costInfo = retrievedInfo.find(item => item.category === 'costs')
    
    if (!costInfo) {
      return "💰 **Study Costs in Northern Cyprus:**\n\nTuition fees are very affordable compared to other countries. Contact us for detailed cost breakdowns specific to your program of interest."
    }

    return `💰 **Complete Cost Breakdown:**

**📚 Annual Tuition Fees:**
• Undergraduate: $3,000 - $8,000
• Graduate: $4,000 - $12,000  
• Medicine: $8,000 - $15,000

**🏠 Monthly Living Costs:**
• Accommodation: $200 - $600
• Food & Meals: $150 - $300
• Transportation: $50 - $100
• **Total Living: $400 - $1,000**

**💡 Why Choose Northern Cyprus:**
✅ 50-70% cheaper than UK/US/EU
✅ High quality education standards
✅ English-taught programs
✅ Scholarship opportunities available

Need a personalized cost estimate? I can help calculate total expenses for your specific situation!`
  }

  private generateScholarshipResponse(retrievedInfo: any[]): string {
    const scholarships = retrievedInfo.filter(item => item.category === 'scholarships')
    
    let response = "💰 **Scholarship Opportunities:**\n\n"
    
    scholarships.forEach(scholarship => {
      const metadata = scholarship.metadata
      response += `**${metadata.name}** 🏆\n`
      response += `• Amount: ${metadata.amount}\n`
      response += `• Criteria: ${metadata.criteria}\n`
      if (metadata.eligibility) {
        response += `• Eligibility: ${metadata.eligibility}\n`
      }
      response += "\n"
    })

    response += `**🎯 How to Maximize Your Scholarships:**
✅ Apply early (before March 31st for Early Bird)
✅ Maintain high academic performance (3.5+ GPA)
✅ Submit complete application documents
✅ Consider multiple family members (Sibling Discount)

**Total Possible Savings: Up to 65% off tuition!**

Ready to apply for scholarships? I can guide you through the process!`

    return response
  }

  private generateAdmissionResponse(retrievedInfo: any[]): string {
    const admissionInfo = retrievedInfo.find(item => item.category === 'admissions')
    
    return `📋 **University Admission Process:**

**🎯 Complete Steps:**
1. Initial consultation and assessment
2. University and program selection  
3. Document preparation and verification
4. Application submission
5. Interview preparation (if required)
6. Admission decision and acceptance
7. Visa application process
8. Pre-departure orientation
9. Arrival and settlement support

**⏱️ Timeline:** 2-4 months average

**📄 Required Documents:**
**Undergraduate:**
• High school diploma/certificate
• English proficiency test (IELTS 6.0+/TOEFL 79+)
• Passport copy
• Academic transcripts
• Personal statement

**Graduate:**
• Bachelor's degree certificate
• English proficiency test
• Letters of recommendation (2-3)
• Statement of purpose
• GRE/GMAT (some programs)

**🌟 Our Success Rate: 98%**
We guarantee admission or full refund!

Ready to start your application? Let me help you begin!`
  }

  private generateServiceResponse(retrievedInfo: any[]): string {
    const services = retrievedInfo.filter(item => item.category === 'services')
    
    let response = "🎯 **Our Complete Services:**\n\n"
    
    services.forEach(service => {
      const metadata = service.metadata
      response += `**${metadata.name}** ✅\n`
      response += `${metadata.description}\n`
      response += `Features: ${metadata.features?.join(', ')}\n`
      response += `Pricing: ${metadata.pricing}\n\n`
    })

    response += `**🌟 Why Choose Foreingate:**
✅ 98% success rate
✅ 5,000+ students helped
✅ End-to-end support
✅ Multilingual team
✅ Free initial consultation

**📞 Ready to Get Started?**
Contact us for your free consultation and personalized guidance!`

    return response
  }

  private generateGeneralResponse(retrievedInfo: any[]): string {
    const topChunk = retrievedInfo[0]
    
    if (!topChunk) {
      return "I'm here to help with any questions about studying in Northern Cyprus. What specific information would you like to know?"
    }

    return `Based on the information I found, here's what I can tell you:

${topChunk.content}

This information comes from our comprehensive knowledge base with ${Math.round(topChunk.similarity * 100)}% relevance to your question.

Would you like me to provide more specific details about any particular aspect?`
  }
}
