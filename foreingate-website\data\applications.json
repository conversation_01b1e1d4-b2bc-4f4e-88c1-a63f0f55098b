[{"id": "app001", "applicationId": "FG-2024-SAMPLE001", "createdAt": "2025-07-06T13:02:47.400Z", "updatedAt": "2025-07-06T13:02:47.400Z", "status": "PENDING", "firstName": "<PERSON>", "lastName": "<PERSON>", "email": "<EMAIL>", "phone": "+90 ************", "dateOfBirth": "2000-05-15", "nationality": "Egyptian", "passportNumber": "A12345678", "highSchoolName": "Cairo International School", "graduationYear": "2022", "gpa": "3.8/4.0", "englishProficiency": "IELTS 7.0", "preferredUniversity": "Eastern Mediterranean University", "firstChoiceProgram": "Computer Engineering", "secondChoiceProgram": "Software Engineering", "intakeYear": "2024", "intakeSemester": "Fall", "assignedCounselor": null, "notes": null}, {"id": "app002", "applicationId": "FG-2024-SAMPLE002", "createdAt": "2025-07-08T13:02:47.400Z", "updatedAt": "2025-07-09T13:02:47.400Z", "status": "UNDER_REVIEW", "firstName": "<PERSON>", "lastName": "<PERSON>", "email": "maria.rod<PERSON><PERSON><PERSON>@email.com", "phone": "+**************", "dateOfBirth": "1999-12-03", "nationality": "Spanish", "passportNumber": "ESP987654", "highSchoolName": "Instituto Cervantes", "graduationYear": "2021", "gpa": "8.5/10", "englishProficiency": "TOEFL 95", "preferredUniversity": "Near East University", "firstChoiceProgram": "Business Administration", "secondChoiceProgram": "International Relations", "intakeYear": "2024", "intakeSemester": "Spring", "assignedCounselor": "<PERSON>", "notes": "Documents received, processing admission"}, {"id": "app003", "applicationId": "FG-2024-SAMPLE003", "createdAt": "2025-07-10T13:02:47.400Z", "updatedAt": "2025-07-10T13:02:47.400Z", "status": "PENDING", "firstName": "<PERSON>", "lastName": "<PERSON>", "email": "<EMAIL>", "phone": "+86 138 0013 8000", "dateOfBirth": "2001-08-22", "nationality": "Chinese", "passportNumber": "G12345678", "highSchoolName": "Beijing International School", "graduationYear": "2023", "gpa": "3.9/4.0", "englishProficiency": "IELTS 7.5", "preferredUniversity": "Cyprus International University", "firstChoiceProgram": "Medicine", "secondChoiceProgram": "Dentistry", "intakeYear": "2024", "intakeSemester": "Fall", "assignedCounselor": null, "notes": null}, {"id": "app004", "applicationId": "FG-2024-SAMPLE004", "createdAt": "2025-07-04T13:02:47.400Z", "updatedAt": "2025-07-10T13:02:47.400Z", "status": "APPROVED", "firstName": "<PERSON><PERSON>", "lastName": "Al-Zahra", "email": "<EMAIL>", "phone": "+971 50 123 4567", "dateOfBirth": "2000-03-10", "nationality": "UAE", "passportNumber": "UAE123456", "highSchoolName": "Dubai International Academy", "graduationYear": "2022", "gpa": "3.7/4.0", "englishProficiency": "IELTS 6.5", "preferredUniversity": "University of Kyrenia", "firstChoiceProgram": "Architecture", "secondChoiceProgram": "Interior Design", "intakeYear": "2024", "intakeSemester": "Fall", "assignedCounselor": "<PERSON>", "notes": "Approved for admission, visa process started"}, {"id": "app005", "applicationId": "FG-2024-SAMPLE005", "createdAt": "2025-07-09T13:02:47.400Z", "updatedAt": "2025-07-09T13:02:47.400Z", "status": "DOCUMENTS_REQUESTED", "firstName": "<PERSON>", "lastName": "<PERSON>", "email": "<EMAIL>", "phone": "+44 7700 900123", "dateOfBirth": "1998-11-18", "nationality": "British", "passportNumber": "GBR789012", "highSchoolName": "London Grammar School", "graduationYear": "2020", "gpa": "A-levels: AAB", "englishProficiency": "Native", "preferredUniversity": "Eastern Mediterranean University", "firstChoiceProgram": "Psychology", "secondChoiceProgram": "Sociology", "intakeYear": "2024", "intakeSemester": "Spring", "assignedCounselor": "<PERSON>", "notes": "Additional transcripts requested"}]