'use client'

import { ReactNode } from 'react'
import { ThemeProvider } from '@/components/providers/theme-provider'
import { TranslationProvider } from '@/components/providers/translation-provider'
import { ToastProvider } from '@/components/ui/toast'
import { Navigation } from '@/components/layout/navigation'
import { Footer } from '@/components/layout/footer'
import { SmartChatbot } from '@/components/ui/smart-chatbot'

interface PageWrapperProps {
  children: ReactNode
}

export function PageWrapper({ children }: PageWrapperProps) {
  return (
    <ThemeProvider
      attribute="class"
      defaultTheme="system"
      enableSystem
      disableTransitionOnChange
    >
      <TranslationProvider>
        <ToastProvider>
          <Navigation />
          <main className="flex-1">
            {children}
          </main>
          <Footer />
          <SmartChatbot />
        </ToastProvider>
      </TranslationProvider>
    </ThemeProvider>
  )
}
