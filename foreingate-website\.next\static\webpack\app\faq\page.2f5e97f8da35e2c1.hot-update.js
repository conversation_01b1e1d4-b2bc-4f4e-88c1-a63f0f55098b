/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/faq/page",{

/***/ "(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/book-open.js":
/*!****************************************************************!*\
  !*** ../node_modules/lucide-react/dist/esm/icons/book-open.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ BookOpen)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M12 7v14\",\n            key: \"1akyts\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z\",\n            key: \"ruj8y\"\n        }\n    ]\n];\nconst BookOpen = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"book-open\", __iconNode);\n //# sourceMappingURL=book-open.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/book-open.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/chevron-down.js":
/*!*******************************************************************!*\
  !*** ../node_modules/lucide-react/dist/esm/icons/chevron-down.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ ChevronDown)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"m6 9 6 6 6-6\",\n            key: \"qrunsl\"\n        }\n    ]\n];\nconst ChevronDown = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"chevron-down\", __iconNode);\n //# sourceMappingURL=chevron-down.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2ljb25zL2NoZXZyb24tZG93bi5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFHYSxtQkFBdUI7SUFBQztRQUFDLE1BQVE7UUFBQTtZQUFFLEdBQUcsY0FBZ0I7WUFBQSxJQUFLLFNBQVM7UUFBQSxDQUFDO0tBQUM7Q0FBQTtBQWE3RSxrQkFBYyxrRUFBaUIsaUJBQWdCLENBQVUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcTmlkaGFsXFxEb2N1bWVudHNcXHNyY1xcaWNvbnNcXGNoZXZyb24tZG93bi50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY3JlYXRlTHVjaWRlSWNvbiBmcm9tICcuLi9jcmVhdGVMdWNpZGVJY29uJztcbmltcG9ydCB7IEljb25Ob2RlIH0gZnJvbSAnLi4vdHlwZXMnO1xuXG5leHBvcnQgY29uc3QgX19pY29uTm9kZTogSWNvbk5vZGUgPSBbWydwYXRoJywgeyBkOiAnbTYgOSA2IDYgNi02Jywga2V5OiAncXJ1bnNsJyB9XV07XG5cbi8qKlxuICogQGNvbXBvbmVudCBAbmFtZSBDaGV2cm9uRG93blxuICogQGRlc2NyaXB0aW9uIEx1Y2lkZSBTVkcgaWNvbiBjb21wb25lbnQsIHJlbmRlcnMgU1ZHIEVsZW1lbnQgd2l0aCBjaGlsZHJlbi5cbiAqXG4gKiBAcHJldmlldyAhW2ltZ10oZGF0YTppbWFnZS9zdmcreG1sO2Jhc2U2NCxQSE4yWnlBZ2VHMXNibk05SW1oMGRIQTZMeTkzZDNjdWR6TXViM0puTHpJd01EQXZjM1puSWdvZ0lIZHBaSFJvUFNJeU5DSUtJQ0JvWldsbmFIUTlJakkwSWdvZ0lIWnBaWGRDYjNnOUlqQWdNQ0F5TkNBeU5DSUtJQ0JtYVd4c1BTSnViMjVsSWdvZ0lITjBjbTlyWlQwaUl6QXdNQ0lnYzNSNWJHVTlJbUpoWTJ0bmNtOTFibVF0WTI5c2IzSTZJQ05tWm1ZN0lHSnZjbVJsY2kxeVlXUnBkWE02SURKd2VDSUtJQ0J6ZEhKdmEyVXRkMmxrZEdnOUlqSWlDaUFnYzNSeWIydGxMV3hwYm1WallYQTlJbkp2ZFc1a0lnb2dJSE4wY205clpTMXNhVzVsYW05cGJqMGljbTkxYm1RaUNqNEtJQ0E4Y0dGMGFDQmtQU0p0TmlBNUlEWWdOaUEyTFRZaUlDOCtDand2YzNablBnbz0pIC0gaHR0cHM6Ly9sdWNpZGUuZGV2L2ljb25zL2NoZXZyb24tZG93blxuICogQHNlZSBodHRwczovL2x1Y2lkZS5kZXYvZ3VpZGUvcGFja2FnZXMvbHVjaWRlLXJlYWN0IC0gRG9jdW1lbnRhdGlvblxuICpcbiAqIEBwYXJhbSB7T2JqZWN0fSBwcm9wcyAtIEx1Y2lkZSBpY29ucyBwcm9wcyBhbmQgYW55IHZhbGlkIFNWRyBhdHRyaWJ1dGVcbiAqIEByZXR1cm5zIHtKU1guRWxlbWVudH0gSlNYIEVsZW1lbnRcbiAqXG4gKi9cbmNvbnN0IENoZXZyb25Eb3duID0gY3JlYXRlTHVjaWRlSWNvbignY2hldnJvbi1kb3duJywgX19pY29uTm9kZSk7XG5cbmV4cG9ydCBkZWZhdWx0IENoZXZyb25Eb3duO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/chevron-down.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/chevron-up.js":
/*!*****************************************************************!*\
  !*** ../node_modules/lucide-react/dist/esm/icons/chevron-up.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ ChevronUp)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"m18 15-6-6-6 6\",\n            key: \"153udz\"\n        }\n    ]\n];\nconst ChevronUp = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"chevron-up\", __iconNode);\n //# sourceMappingURL=chevron-up.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2ljb25zL2NoZXZyb24tdXAuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBR2EsbUJBQXVCO0lBQUM7UUFBQyxNQUFRO1FBQUE7WUFBRSxHQUFHLGdCQUFrQjtZQUFBLElBQUssU0FBUztRQUFBLENBQUM7S0FBQztDQUFBO0FBYS9FLGdCQUFZLGtFQUFpQixlQUFjLENBQVUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcTmlkaGFsXFxEb2N1bWVudHNcXHNyY1xcaWNvbnNcXGNoZXZyb24tdXAudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGNyZWF0ZUx1Y2lkZUljb24gZnJvbSAnLi4vY3JlYXRlTHVjaWRlSWNvbic7XG5pbXBvcnQgeyBJY29uTm9kZSB9IGZyb20gJy4uL3R5cGVzJztcblxuZXhwb3J0IGNvbnN0IF9faWNvbk5vZGU6IEljb25Ob2RlID0gW1sncGF0aCcsIHsgZDogJ20xOCAxNS02LTYtNiA2Jywga2V5OiAnMTUzdWR6JyB9XV07XG5cbi8qKlxuICogQGNvbXBvbmVudCBAbmFtZSBDaGV2cm9uVXBcbiAqIEBkZXNjcmlwdGlvbiBMdWNpZGUgU1ZHIGljb24gY29tcG9uZW50LCByZW5kZXJzIFNWRyBFbGVtZW50IHdpdGggY2hpbGRyZW4uXG4gKlxuICogQHByZXZpZXcgIVtpbWddKGRhdGE6aW1hZ2Uvc3ZnK3htbDtiYXNlNjQsUEhOMlp5QWdlRzFzYm5NOUltaDBkSEE2THk5M2QzY3Vkek11YjNKbkx6SXdNREF2YzNabklnb2dJSGRwWkhSb1BTSXlOQ0lLSUNCb1pXbG5hSFE5SWpJMElnb2dJSFpwWlhkQ2IzZzlJakFnTUNBeU5DQXlOQ0lLSUNCbWFXeHNQU0p1YjI1bElnb2dJSE4wY205clpUMGlJekF3TUNJZ2MzUjViR1U5SW1KaFkydG5jbTkxYm1RdFkyOXNiM0k2SUNObVptWTdJR0p2Y21SbGNpMXlZV1JwZFhNNklESndlQ0lLSUNCemRISnZhMlV0ZDJsa2RHZzlJaklpQ2lBZ2MzUnliMnRsTFd4cGJtVmpZWEE5SW5KdmRXNWtJZ29nSUhOMGNtOXJaUzFzYVc1bGFtOXBiajBpY205MWJtUWlDajRLSUNBOGNHRjBhQ0JrUFNKdE1UZ2dNVFV0TmkwMkxUWWdOaUlnTHo0S1BDOXpkbWMrQ2c9PSkgLSBodHRwczovL2x1Y2lkZS5kZXYvaWNvbnMvY2hldnJvbi11cFxuICogQHNlZSBodHRwczovL2x1Y2lkZS5kZXYvZ3VpZGUvcGFja2FnZXMvbHVjaWRlLXJlYWN0IC0gRG9jdW1lbnRhdGlvblxuICpcbiAqIEBwYXJhbSB7T2JqZWN0fSBwcm9wcyAtIEx1Y2lkZSBpY29ucyBwcm9wcyBhbmQgYW55IHZhbGlkIFNWRyBhdHRyaWJ1dGVcbiAqIEByZXR1cm5zIHtKU1guRWxlbWVudH0gSlNYIEVsZW1lbnRcbiAqXG4gKi9cbmNvbnN0IENoZXZyb25VcCA9IGNyZWF0ZUx1Y2lkZUljb24oJ2NoZXZyb24tdXAnLCBfX2ljb25Ob2RlKTtcblxuZXhwb3J0IGRlZmF1bHQgQ2hldnJvblVwO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/chevron-up.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/circle-question-mark.js":
/*!***************************************************************************!*\
  !*** ../node_modules/lucide-react/dist/esm/icons/circle-question-mark.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ CircleQuestionMark)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"10\",\n            key: \"1mglay\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3\",\n            key: \"1u773s\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M12 17h.01\",\n            key: \"p32p05\"\n        }\n    ]\n];\nconst CircleQuestionMark = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"circle-question-mark\", __iconNode);\n //# sourceMappingURL=circle-question-mark.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/circle-question-mark.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/dollar-sign.js":
/*!******************************************************************!*\
  !*** ../node_modules/lucide-react/dist/esm/icons/dollar-sign.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ DollarSign)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"line\",\n        {\n            x1: \"12\",\n            x2: \"12\",\n            y1: \"2\",\n            y2: \"22\",\n            key: \"7eqyqh\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6\",\n            key: \"1b0p4s\"\n        }\n    ]\n];\nconst DollarSign = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"dollar-sign\", __iconNode);\n //# sourceMappingURL=dollar-sign.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/dollar-sign.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/file-text.js":
/*!****************************************************************!*\
  !*** ../node_modules/lucide-react/dist/esm/icons/file-text.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ FileText)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z\",\n            key: \"1rqfz7\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M14 2v4a2 2 0 0 0 2 2h4\",\n            key: \"tnqrlb\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M10 9H8\",\n            key: \"b1mrlr\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M16 13H8\",\n            key: \"t4e002\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M16 17H8\",\n            key: \"z1uh3a\"\n        }\n    ]\n];\nconst FileText = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"file-text\", __iconNode);\n //# sourceMappingURL=file-text.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2ljb25zL2ZpbGUtdGV4dC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFHTyxNQUFNLFVBQXVCO0lBQ2xDO1FBQUMsTUFBUTtRQUFBO1lBQUUsR0FBRyxDQUE4RDtZQUFBLEtBQUs7UUFBQSxDQUFVO0tBQUE7SUFDM0Y7UUFBQyxNQUFRO1FBQUE7WUFBRSxHQUFHLENBQTJCO1lBQUEsS0FBSztRQUFBLENBQVU7S0FBQTtJQUN4RDtRQUFDLE1BQVE7UUFBQTtZQUFFLEdBQUcsQ0FBVztZQUFBLEtBQUs7UUFBQSxDQUFVO0tBQUE7SUFDeEM7UUFBQyxNQUFRO1FBQUE7WUFBRSxHQUFHLENBQVk7WUFBQSxLQUFLO1FBQUEsQ0FBVTtLQUFBO0lBQ3pDO1FBQUMsQ0FBUTtRQUFBLENBQUU7WUFBQSxFQUFHLFdBQVk7WUFBQSxJQUFLO1FBQVU7S0FBQTtDQUMzQztBQWFNLGVBQVcsa0VBQWlCLGNBQWEsQ0FBVSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxOaWRoYWxcXERvY3VtZW50c1xcc3JjXFxpY29uc1xcZmlsZS10ZXh0LnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBjcmVhdGVMdWNpZGVJY29uIGZyb20gJy4uL2NyZWF0ZUx1Y2lkZUljb24nO1xuaW1wb3J0IHsgSWNvbk5vZGUgfSBmcm9tICcuLi90eXBlcyc7XG5cbmV4cG9ydCBjb25zdCBfX2ljb25Ob2RlOiBJY29uTm9kZSA9IFtcbiAgWydwYXRoJywgeyBkOiAnTTE1IDJINmEyIDIgMCAwIDAtMiAydjE2YTIgMiAwIDAgMCAyIDJoMTJhMiAyIDAgMCAwIDItMlY3WicsIGtleTogJzFycWZ6NycgfV0sXG4gIFsncGF0aCcsIHsgZDogJ00xNCAydjRhMiAyIDAgMCAwIDIgMmg0Jywga2V5OiAndG5xcmxiJyB9XSxcbiAgWydwYXRoJywgeyBkOiAnTTEwIDlIOCcsIGtleTogJ2IxbXJscicgfV0sXG4gIFsncGF0aCcsIHsgZDogJ00xNiAxM0g4Jywga2V5OiAndDRlMDAyJyB9XSxcbiAgWydwYXRoJywgeyBkOiAnTTE2IDE3SDgnLCBrZXk6ICd6MXVoM2EnIH1dLFxuXTtcblxuLyoqXG4gKiBAY29tcG9uZW50IEBuYW1lIEZpbGVUZXh0XG4gKiBAZGVzY3JpcHRpb24gTHVjaWRlIFNWRyBpY29uIGNvbXBvbmVudCwgcmVuZGVycyBTVkcgRWxlbWVudCB3aXRoIGNoaWxkcmVuLlxuICpcbiAqIEBwcmV2aWV3ICFbaW1nXShkYXRhOmltYWdlL3N2Zyt4bWw7YmFzZTY0LFBITjJaeUFnZUcxc2JuTTlJbWgwZEhBNkx5OTNkM2N1ZHpNdWIzSm5Mekl3TURBdmMzWm5JZ29nSUhkcFpIUm9QU0l5TkNJS0lDQm9aV2xuYUhROUlqSTBJZ29nSUhacFpYZENiM2c5SWpBZ01DQXlOQ0F5TkNJS0lDQm1hV3hzUFNKdWIyNWxJZ29nSUhOMGNtOXJaVDBpSXpBd01DSWdjM1I1YkdVOUltSmhZMnRuY205MWJtUXRZMjlzYjNJNklDTm1abVk3SUdKdmNtUmxjaTF5WVdScGRYTTZJREp3ZUNJS0lDQnpkSEp2YTJVdGQybGtkR2c5SWpJaUNpQWdjM1J5YjJ0bExXeHBibVZqWVhBOUluSnZkVzVrSWdvZ0lITjBjbTlyWlMxc2FXNWxhbTlwYmowaWNtOTFibVFpQ2o0S0lDQThjR0YwYUNCa1BTSk5NVFVnTWtnMllUSWdNaUF3SURBZ01DMHlJREoyTVRaaE1pQXlJREFnTUNBd0lESWdNbWd4TW1FeUlESWdNQ0F3SURBZ01pMHlWamRhSWlBdlBnb2dJRHh3WVhSb0lHUTlJazB4TkNBeWRqUmhNaUF5SURBZ01DQXdJRElnTW1nMElpQXZQZ29nSUR4d1lYUm9JR1E5SWsweE1DQTVTRGdpSUM4K0NpQWdQSEJoZEdnZ1pEMGlUVEUySURFelNEZ2lJQzgrQ2lBZ1BIQmhkR2dnWkQwaVRURTJJREUzU0RnaUlDOCtDand2YzNablBnbz0pIC0gaHR0cHM6Ly9sdWNpZGUuZGV2L2ljb25zL2ZpbGUtdGV4dFxuICogQHNlZSBodHRwczovL2x1Y2lkZS5kZXYvZ3VpZGUvcGFja2FnZXMvbHVjaWRlLXJlYWN0IC0gRG9jdW1lbnRhdGlvblxuICpcbiAqIEBwYXJhbSB7T2JqZWN0fSBwcm9wcyAtIEx1Y2lkZSBpY29ucyBwcm9wcyBhbmQgYW55IHZhbGlkIFNWRyBhdHRyaWJ1dGVcbiAqIEByZXR1cm5zIHtKU1guRWxlbWVudH0gSlNYIEVsZW1lbnRcbiAqXG4gKi9cbmNvbnN0IEZpbGVUZXh0ID0gY3JlYXRlTHVjaWRlSWNvbignZmlsZS10ZXh0JywgX19pY29uTm9kZSk7XG5cbmV4cG9ydCBkZWZhdWx0IEZpbGVUZXh0O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/file-text.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/globe.js":
/*!************************************************************!*\
  !*** ../node_modules/lucide-react/dist/esm/icons/globe.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Globe)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"10\",\n            key: \"1mglay\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20\",\n            key: \"13o1zl\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M2 12h20\",\n            key: \"9i4pu4\"\n        }\n    ]\n];\nconst Globe = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"globe\", __iconNode);\n //# sourceMappingURL=globe.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/globe.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/house.js":
/*!************************************************************!*\
  !*** ../node_modules/lucide-react/dist/esm/icons/house.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ House)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8\",\n            key: \"5wwlr5\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z\",\n            key: \"1d0kgt\"\n        }\n    ]\n];\nconst House = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"house\", __iconNode);\n //# sourceMappingURL=house.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/house.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/search.js":
/*!*************************************************************!*\
  !*** ../node_modules/lucide-react/dist/esm/icons/search.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Search)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"m21 21-4.34-4.34\",\n            key: \"14j7rj\"\n        }\n    ],\n    [\n        \"circle\",\n        {\n            cx: \"11\",\n            cy: \"11\",\n            r: \"8\",\n            key: \"4ej97u\"\n        }\n    ]\n];\nconst Search = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"search\", __iconNode);\n //# sourceMappingURL=search.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/search.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5Cfaq-categories.tsx%22%2C%22ids%22%3A%5B%22FAQCategoriesSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5Cfaq-hero.tsx%22%2C%22ids%22%3A%5B%22FAQHeroSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5Cplaceholder-section.tsx%22%2C%22ids%22%3A%5B%22PlaceholderSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Cwhatsapp-widget.tsx%22%2C%22ids%22%3A%5B%22WhatsAppWidget%22%5D%7D&server=false!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5Cfaq-categories.tsx%22%2C%22ids%22%3A%5B%22FAQCategoriesSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5Cfaq-hero.tsx%22%2C%22ids%22%3A%5B%22FAQHeroSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5Cplaceholder-section.tsx%22%2C%22ids%22%3A%5B%22PlaceholderSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Cwhatsapp-widget.tsx%22%2C%22ids%22%3A%5B%22WhatsAppWidget%22%5D%7D&server=false! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/sections/faq-categories.tsx */ \"(app-pages-browser)/./src/components/sections/faq-categories.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/sections/faq-hero.tsx */ \"(app-pages-browser)/./src/components/sections/faq-hero.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/sections/placeholder-section.tsx */ \"(app-pages-browser)/./src/components/sections/placeholder-section.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/whatsapp-widget.tsx */ \"(app-pages-browser)/./src/components/ui/whatsapp-widget.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5Cfaq-categories.tsx%22%2C%22ids%22%3A%5B%22FAQCategoriesSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5Cfaq-hero.tsx%22%2C%22ids%22%3A%5B%22FAQHeroSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5Cplaceholder-section.tsx%22%2C%22ids%22%3A%5B%22PlaceholderSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Cwhatsapp-widget.tsx%22%2C%22ids%22%3A%5B%22WhatsAppWidget%22%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/sections/faq-categories.tsx":
/*!****************************************************!*\
  !*** ./src/components/sections/faq-categories.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FAQCategoriesSection: () => (/* binding */ FAQCategoriesSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/../node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_intersection_observer__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-intersection-observer */ \"(app-pages-browser)/../node_modules/react-intersection-observer/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronDown_ChevronUp_DollarSign_FileText_Globe_HelpCircle_Home_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronDown,ChevronUp,DollarSign,FileText,Globe,HelpCircle,Home!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronDown_ChevronUp_DollarSign_FileText_Globe_HelpCircle_Home_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronDown,ChevronUp,DollarSign,FileText,Globe,HelpCircle,Home!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronDown_ChevronUp_DollarSign_FileText_Globe_HelpCircle_Home_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronDown,ChevronUp,DollarSign,FileText,Globe,HelpCircle,Home!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronDown_ChevronUp_DollarSign_FileText_Globe_HelpCircle_Home_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronDown,ChevronUp,DollarSign,FileText,Globe,HelpCircle,Home!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronDown_ChevronUp_DollarSign_FileText_Globe_HelpCircle_Home_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronDown,ChevronUp,DollarSign,FileText,Globe,HelpCircle,Home!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/circle-question-mark.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronDown_ChevronUp_DollarSign_FileText_Globe_HelpCircle_Home_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronDown,ChevronUp,DollarSign,FileText,Globe,HelpCircle,Home!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronDown_ChevronUp_DollarSign_FileText_Globe_HelpCircle_Home_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronDown,ChevronUp,DollarSign,FileText,Globe,HelpCircle,Home!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronDown_ChevronUp_DollarSign_FileText_Globe_HelpCircle_Home_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronDown,ChevronUp,DollarSign,FileText,Globe,HelpCircle,Home!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* __next_internal_client_entry_do_not_use__ FAQCategoriesSection auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst faqData = {\n    admissions: {\n        icon: _barrel_optimize_names_BookOpen_ChevronDown_ChevronUp_DollarSign_FileText_Globe_HelpCircle_Home_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        title: 'Admissions & Applications',\n        questions: [\n            {\n                q: 'What are the admission requirements for international students?',\n                a: 'International students need a high school diploma or equivalent, English proficiency test scores (IELTS/TOEFL), passport copy, and academic transcripts. Specific requirements may vary by program and university.'\n            },\n            {\n                q: 'When is the application deadline?',\n                a: 'Most universities have multiple intake periods: Fall (September), Spring (February), and Summer (June). Application deadlines are typically 2-3 months before each intake period.'\n            },\n            {\n                q: 'Can I apply to multiple universities?',\n                a: 'Yes, you can apply to multiple universities through our platform. We recommend applying to 3-5 universities to increase your chances of acceptance.'\n            },\n            {\n                q: 'How long does the application process take?',\n                a: 'The complete application process typically takes 4-6 weeks from submission to final acceptance, including document verification and university review.'\n            }\n        ]\n    },\n    visa: {\n        icon: _barrel_optimize_names_BookOpen_ChevronDown_ChevronUp_DollarSign_FileText_Globe_HelpCircle_Home_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        title: 'Visa & Documentation',\n        questions: [\n            {\n                q: 'Do I need a visa to study in Northern Cyprus?',\n                a: 'Most international students need a student visa. EU citizens may have different requirements. We provide complete visa support and guidance throughout the process.'\n            },\n            {\n                q: 'What documents are required for a student visa?',\n                a: 'Required documents include acceptance letter, passport, financial proof, health insurance, medical certificate, and police clearance certificate.'\n            },\n            {\n                q: 'How long does visa processing take?',\n                a: 'Student visa processing typically takes 2-4 weeks after submitting all required documents. We recommend applying at least 6 weeks before your intended travel date.'\n            }\n        ]\n    },\n    housing: {\n        icon: _barrel_optimize_names_BookOpen_ChevronDown_ChevronUp_DollarSign_FileText_Globe_HelpCircle_Home_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        title: 'Housing & Accommodation',\n        questions: [\n            {\n                q: 'What housing options are available for students?',\n                a: 'Students can choose from university dormitories, private apartments, shared housing, or homestay programs. We help you find the best option based on your budget and preferences.'\n            },\n            {\n                q: 'How much does student accommodation cost?',\n                a: 'Housing costs range from $200-600 per month depending on the type and location. University dormitories are typically the most affordable option.'\n            },\n            {\n                q: 'Is accommodation guaranteed for international students?',\n                a: 'Most universities guarantee accommodation for first-year international students. We recommend applying for housing early to secure your preferred option.'\n            }\n        ]\n    },\n    costs: {\n        icon: _barrel_optimize_names_BookOpen_ChevronDown_ChevronUp_DollarSign_FileText_Globe_HelpCircle_Home_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        title: 'Costs & Financial Aid',\n        questions: [\n            {\n                q: 'What are the tuition fees for international students?',\n                a: 'Tuition fees range from $3,500 to $12,000 per year depending on the program and university. Engineering and medical programs typically have higher fees.'\n            },\n            {\n                q: 'Are scholarships available for international students?',\n                a: 'Yes, many universities offer merit-based scholarships ranging from 25% to 100% tuition coverage. We help you identify and apply for available scholarships.'\n            },\n            {\n                q: 'What are the living costs in Northern Cyprus?',\n                a: 'Monthly living costs range from $400-800 including accommodation, food, transportation, and personal expenses. Northern Cyprus is generally more affordable than many European countries.'\n            }\n        ]\n    },\n    general: {\n        icon: _barrel_optimize_names_BookOpen_ChevronDown_ChevronUp_DollarSign_FileText_Globe_HelpCircle_Home_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        title: 'General Information',\n        questions: [\n            {\n                q: 'What language are courses taught in?',\n                a: 'Most programs are taught in English, with some programs available in Turkish. All universities require English proficiency for English-taught programs.'\n            },\n            {\n                q: 'Is Northern Cyprus safe for international students?',\n                a: 'Yes, Northern Cyprus is considered very safe with low crime rates. Universities provide additional security measures and support for international students.'\n            },\n            {\n                q: 'Can I work while studying?',\n                a: 'International students can work part-time (up to 20 hours per week) with proper permits. Many students find opportunities in tutoring, hospitality, or campus jobs.'\n            }\n        ]\n    },\n    support: {\n        icon: _barrel_optimize_names_BookOpen_ChevronDown_ChevronUp_DollarSign_FileText_Globe_HelpCircle_Home_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        title: 'Student Support Services',\n        questions: [\n            {\n                q: 'What support services do you provide?',\n                a: 'We provide comprehensive support including application assistance, visa guidance, accommodation help, airport pickup, academic support, and ongoing counseling throughout your studies.'\n            },\n            {\n                q: 'Do you provide support after arrival?',\n                a: 'Yes, our support continues after your arrival with orientation programs, academic counseling, career guidance, and assistance with any challenges you may face.'\n            },\n            {\n                q: 'How can I contact support if I need help?',\n                a: 'You can reach our support team 24/7 through phone, email, WhatsApp, or our online portal. We also have local representatives in Northern Cyprus for immediate assistance.'\n            }\n        ]\n    }\n};\nfunction FAQCategoriesSection() {\n    _s();\n    const [activeCategory, setActiveCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('admissions');\n    const [expandedQuestion, setExpandedQuestion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [ref, inView] = (0,react_intersection_observer__WEBPACK_IMPORTED_MODULE_8__.useInView)({\n        triggerOnce: true,\n        threshold: 0.1\n    });\n    const toggleQuestion = (index)=>{\n        setExpandedQuestion(expandedQuestion === index ? null : index);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        ref: ref,\n        className: \"section-padding bg-muted/30\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    animate: inView ? {\n                        opacity: 1,\n                        y: 0\n                    } : {},\n                    transition: {\n                        duration: 0.8\n                    },\n                    className: \"text-center max-w-3xl mx-auto mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold mb-4\",\n                            children: [\n                                \"Browse Questions by\",\n                                ' ',\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"gradient-text\",\n                                    children: \"Category\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-categories.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-categories.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-muted-foreground\",\n                            children: \"Find detailed answers organized by topic to help you make informed decisions\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-categories.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-categories.tsx\",\n                    lineNumber: 140,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl mx-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid lg:grid-cols-4 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    x: -50\n                                },\n                                animate: inView ? {\n                                    opacity: 1,\n                                    x: 0\n                                } : {},\n                                transition: {\n                                    duration: 0.8,\n                                    delay: 0.2\n                                },\n                                className: \"lg:col-span-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-background rounded-xl p-6 shadow-sm border sticky top-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold mb-4\",\n                                            children: \"Categories\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-categories.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                            className: \"space-y-2\",\n                                            children: Object.entries(faqData).map((param)=>{\n                                                let [key, category] = param;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setActiveCategory(key),\n                                                    className: \"w-full flex items-center space-x-3 p-3 rounded-lg text-left transition-all duration-200 \".concat(activeCategory === key ? 'bg-primary text-primary-foreground' : 'hover:bg-muted'),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(category.icon, {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-categories.tsx\",\n                                                            lineNumber: 177,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium text-sm\",\n                                                                    children: category.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-categories.tsx\",\n                                                                    lineNumber: 179,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs \".concat(activeCategory === key ? 'text-primary-foreground/80' : 'text-muted-foreground'),\n                                                                    children: [\n                                                                        category.questions.length,\n                                                                        \" questions\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-categories.tsx\",\n                                                                    lineNumber: 180,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-categories.tsx\",\n                                                            lineNumber: 178,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, key, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-categories.tsx\",\n                                                    lineNumber: 168,\n                                                    columnNumber: 21\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-categories.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-categories.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-categories.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    x: 50\n                                },\n                                animate: inView ? {\n                                    opacity: 1,\n                                    x: 0\n                                } : {},\n                                transition: {\n                                    duration: 0.8,\n                                    delay: 0.4\n                                },\n                                className: \"lg:col-span-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-background rounded-xl p-8 shadow-sm border\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 mb-8\",\n                                            children: [\n                                                React.createElement(faqData[activeCategory].icon, {\n                                                    className: \"w-8 h-8 text-primary\"\n                                                }),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-2xl font-bold\",\n                                                            children: faqData[activeCategory].title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-categories.tsx\",\n                                                            lineNumber: 206,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-muted-foreground\",\n                                                            children: [\n                                                                faqData[activeCategory].questions.length,\n                                                                \" frequently asked questions\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-categories.tsx\",\n                                                            lineNumber: 209,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-categories.tsx\",\n                                                    lineNumber: 205,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-categories.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: faqData[activeCategory].questions.map((faq, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                                    initial: {\n                                                        opacity: 0,\n                                                        y: 20\n                                                    },\n                                                    animate: {\n                                                        opacity: 1,\n                                                        y: 0\n                                                    },\n                                                    transition: {\n                                                        duration: 0.5,\n                                                        delay: index * 0.1\n                                                    },\n                                                    className: \"border rounded-lg overflow-hidden\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>toggleQuestion(index),\n                                                            className: \"w-full flex items-center justify-between p-4 text-left hover:bg-muted/50 transition-colors\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium pr-4\",\n                                                                    children: faq.q\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-categories.tsx\",\n                                                                    lineNumber: 229,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                expandedQuestion === index ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronDown_ChevronUp_DollarSign_FileText_Globe_HelpCircle_Home_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    className: \"w-5 h-5 text-primary flex-shrink-0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-categories.tsx\",\n                                                                    lineNumber: 231,\n                                                                    columnNumber: 27\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronDown_ChevronUp_DollarSign_FileText_Globe_HelpCircle_Home_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                    className: \"w-5 h-5 text-muted-foreground flex-shrink-0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-categories.tsx\",\n                                                                    lineNumber: 233,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-categories.tsx\",\n                                                            lineNumber: 225,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        expandedQuestion === index && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                                            initial: {\n                                                                opacity: 0,\n                                                                height: 0\n                                                            },\n                                                            animate: {\n                                                                opacity: 1,\n                                                                height: 'auto'\n                                                            },\n                                                            exit: {\n                                                                opacity: 0,\n                                                                height: 0\n                                                            },\n                                                            transition: {\n                                                                duration: 0.3\n                                                            },\n                                                            className: \"px-4 pb-4 border-t bg-muted/20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-muted-foreground pt-4\",\n                                                                children: faq.a\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-categories.tsx\",\n                                                                lineNumber: 245,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-categories.tsx\",\n                                                            lineNumber: 238,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-categories.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-categories.tsx\",\n                                            lineNumber: 216,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-categories.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-categories.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-categories.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-categories.tsx\",\n                    lineNumber: 155,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-categories.tsx\",\n            lineNumber: 138,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-categories.tsx\",\n        lineNumber: 137,\n        columnNumber: 5\n    }, this);\n}\n_s(FAQCategoriesSection, \"MI9fDkQyImkL+bS/13vw1qjMqjc=\", false, function() {\n    return [\n        react_intersection_observer__WEBPACK_IMPORTED_MODULE_8__.useInView\n    ];\n});\n_c = FAQCategoriesSection;\nvar _c;\n$RefreshReg$(_c, \"FAQCategoriesSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/faq-categories.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/sections/faq-hero.tsx":
/*!**********************************************!*\
  !*** ./src/components/sections/faq-hero.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FAQHeroSection: () => (/* binding */ FAQHeroSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/../node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_BookOpen_HelpCircle_MessageCircle_Search_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,HelpCircle,MessageCircle,Search!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/circle-question-mark.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_HelpCircle_MessageCircle_Search_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,HelpCircle,MessageCircle,Search!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_HelpCircle_MessageCircle_Search_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,HelpCircle,MessageCircle,Search!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_HelpCircle_MessageCircle_Search_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,HelpCircle,MessageCircle,Search!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* __next_internal_client_entry_do_not_use__ FAQHeroSection auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction FAQHeroSection() {\n    _s();\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const handleSearch = ()=>{\n        // This would typically trigger a search action\n        console.log('Searching for:', searchTerm);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"relative min-h-[50vh] flex items-center justify-center overflow-hidden bg-gradient-to-br from-primary/10 via-background to-secondary/10\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute -top-40 -right-40 w-80 h-80 bg-primary/10 rounded-full blur-3xl animate-pulse-slow\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-hero.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute -bottom-40 -left-40 w-80 h-80 bg-secondary/10 rounded-full blur-3xl animate-pulse-slow\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-hero.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-hero.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container relative z-10 px-4 py-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center max-w-4xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                delay: 0.2,\n                                duration: 0.6\n                            },\n                            className: \"inline-flex items-center space-x-2 bg-primary/10 text-primary px-4 py-2 rounded-full text-sm font-medium mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_HelpCircle_MessageCircle_Search_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-hero.tsx\",\n                                    lineNumber: 33,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Get Instant Answers\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-hero.tsx\",\n                                    lineNumber: 34,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-hero.tsx\",\n                            lineNumber: 27,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                delay: 0.4,\n                                duration: 0.8\n                            },\n                            className: \"space-y-6 mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl md:text-5xl lg:text-6xl font-bold leading-tight\",\n                                    children: [\n                                        \"Frequently Asked\",\n                                        ' ',\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"gradient-text\",\n                                            children: \"Questions\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-hero.tsx\",\n                                            lineNumber: 46,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-hero.tsx\",\n                                    lineNumber: 44,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-muted-foreground max-w-3xl mx-auto\",\n                                    children: \"Find quick answers to common questions about studying in Northern Cyprus, our services, application process, and more. Can't find what you're looking for? Contact our support team.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-hero.tsx\",\n                                    lineNumber: 48,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-hero.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                delay: 0.6,\n                                duration: 0.8\n                            },\n                            className: \"max-w-2xl mx-auto mb-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4 bg-background/80 backdrop-blur-sm rounded-xl p-4 shadow-lg border\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_HelpCircle_MessageCircle_Search_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-hero.tsx\",\n                                                lineNumber: 64,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"Search for answers...\",\n                                                value: searchTerm,\n                                                onChange: (e)=>setSearchTerm(e.target.value),\n                                                className: \"w-full pl-10 pr-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-hero.tsx\",\n                                                lineNumber: 65,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-hero.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        size: \"lg\",\n                                        onClick: handleSearch,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_HelpCircle_MessageCircle_Search_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-hero.tsx\",\n                                                lineNumber: 74,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Search\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-hero.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-hero.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-hero.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                delay: 0.8,\n                                duration: 0.8\n                            },\n                            className: \"grid grid-cols-2 md:grid-cols-4 gap-4 mb-12\",\n                            children: [\n                                {\n                                    icon: _barrel_optimize_names_BookOpen_HelpCircle_MessageCircle_Search_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                                    title: 'Admissions',\n                                    count: '15 questions'\n                                },\n                                {\n                                    icon: _barrel_optimize_names_BookOpen_HelpCircle_MessageCircle_Search_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n                                    title: 'Visa Process',\n                                    count: '12 questions'\n                                },\n                                {\n                                    icon: _barrel_optimize_names_BookOpen_HelpCircle_MessageCircle_Search_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                                    title: 'Student Life',\n                                    count: '18 questions'\n                                },\n                                {\n                                    icon: _barrel_optimize_names_BookOpen_HelpCircle_MessageCircle_Search_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                                    title: 'Costs & Fees',\n                                    count: '10 questions'\n                                }\n                            ].map((category, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.button, {\n                                    initial: {\n                                        opacity: 0,\n                                        scale: 0.8\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        scale: 1\n                                    },\n                                    transition: {\n                                        delay: 1 + index * 0.1,\n                                        duration: 0.5\n                                    },\n                                    className: \"bg-background/50 hover:bg-background/80 rounded-xl p-6 border transition-all duration-300 text-center group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(category.icon, {\n                                            className: \"w-8 h-8 text-primary mx-auto mb-3 group-hover:scale-110 transition-transform\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-hero.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold mb-1\",\n                                            children: category.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-hero.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: category.count\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-hero.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, category.title, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-hero.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-hero.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                delay: 1.2,\n                                duration: 0.8\n                            },\n                            className: \"text-left max-w-3xl mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold mb-4 text-center\",\n                                    children: \"Most Popular Questions\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-hero.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        'What are the admission requirements for international students?',\n                                        'How much does it cost to study in Northern Cyprus?',\n                                        'Do I need a visa to study in Northern Cyprus?',\n                                        'What is the application deadline for universities?'\n                                    ].map((question, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.button, {\n                                            initial: {\n                                                opacity: 0,\n                                                x: -20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                x: 0\n                                            },\n                                            transition: {\n                                                delay: 1.4 + index * 0.1,\n                                                duration: 0.5\n                                            },\n                                            className: \"w-full text-left p-4 bg-background/50 hover:bg-background/80 rounded-lg border transition-all duration-300 group\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm group-hover:text-primary transition-colors\",\n                                                        children: question\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-hero.tsx\",\n                                                        lineNumber: 130,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_HelpCircle_MessageCircle_Search_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        className: \"w-4 h-4 text-muted-foreground group-hover:text-primary transition-colors\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-hero.tsx\",\n                                                        lineNumber: 133,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-hero.tsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, question, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-hero.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-hero.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-hero.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                delay: 1.8,\n                                duration: 0.8\n                            },\n                            className: \"mt-12 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-muted-foreground mb-4\",\n                                    children: \"Still have questions? Our expert team is here to help.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-hero.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    size: \"lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_HelpCircle_MessageCircle_Search_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"w-4 h-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-hero.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Contact Support\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-hero.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-hero.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-hero.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-hero.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\faq-hero.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n_s(FAQHeroSection, \"a1cMJ8t0eYFnsCEdGcHtaGJdbCM=\");\n_c = FAQHeroSection;\nvar _c;\n$RefreshReg$(_c, \"FAQHeroSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/faq-hero.tsx\n"));

/***/ })

});