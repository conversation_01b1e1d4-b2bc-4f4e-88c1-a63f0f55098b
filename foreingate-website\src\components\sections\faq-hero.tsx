'use client'

import { motion } from 'framer-motion'
import { useState } from 'react'
import { Search, HelpCircle, MessageCircle, BookOpen } from 'lucide-react'
import { Button } from '@/components/ui/button'

export function FAQHeroSection() {
  const [searchTerm, setSearchTerm] = useState('')

  const handleSearch = () => {
    // This would typically trigger a search action
    console.log('Searching for:', searchTerm)
  }

  return (
    <section className="relative min-h-[50vh] flex items-center justify-center overflow-hidden bg-gradient-to-br from-primary/10 via-background to-secondary/10">
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-primary/10 rounded-full blur-3xl animate-pulse-slow" />
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-secondary/10 rounded-full blur-3xl animate-pulse-slow" />
      </div>

      <div className="container relative z-10 px-4 py-20">
        <div className="text-center max-w-4xl mx-auto">
          {/* Badge */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2, duration: 0.6 }}
            className="inline-flex items-center space-x-2 bg-primary/10 text-primary px-4 py-2 rounded-full text-sm font-medium mb-6"
          >
            <HelpCircle className="w-4 h-4" />
            <span>Get Instant Answers</span>
          </motion.div>

          {/* Main Heading */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4, duration: 0.8 }}
            className="space-y-6 mb-8"
          >
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight">
              Frequently Asked{' '}
              <span className="gradient-text">Questions</span>
            </h1>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              Find quick answers to common questions about studying in Northern Cyprus,
              our services, application process, and more. Can't find what you're looking for?
              Contact our support team.
            </p>
          </motion.div>

          {/* Search Bar */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6, duration: 0.8 }}
            className="max-w-2xl mx-auto mb-8"
          >
            <div className="flex flex-col sm:flex-row gap-4 bg-background/80 backdrop-blur-sm rounded-xl p-4 shadow-lg border">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search for answers..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                />
              </div>
              <Button size="lg" onClick={handleSearch}>
                <Search className="w-4 h-4 mr-2" />
                Search
              </Button>
            </div>
          </motion.div>

          {/* Quick Categories */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.8, duration: 0.8 }}
            className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-12"
          >
            {[
              { icon: BookOpen, title: 'Admissions', count: '15 questions' },
              { icon: HelpCircle, title: 'Visa Process', count: '12 questions' },
              { icon: MessageCircle, title: 'Student Life', count: '18 questions' },
              { icon: Search, title: 'Costs & Fees', count: '10 questions' }
            ].map((category, index) => (
              <motion.button
                key={category.title}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 1 + index * 0.1, duration: 0.5 }}
                className="bg-background/50 hover:bg-background/80 rounded-xl p-6 border transition-all duration-300 text-center group"
              >
                <category.icon className="w-8 h-8 text-primary mx-auto mb-3 group-hover:scale-110 transition-transform" />
                <h3 className="font-semibold mb-1">{category.title}</h3>
                <p className="text-sm text-muted-foreground">{category.count}</p>
              </motion.button>
            ))}
          </motion.div>

          {/* Popular Questions Preview */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 1.2, duration: 0.8 }}
            className="text-left max-w-3xl mx-auto"
          >
            <h3 className="text-xl font-semibold mb-4 text-center">Most Popular Questions</h3>
            <div className="space-y-2">
              {[
                'What are the admission requirements for international students?',
                'How much does it cost to study in Northern Cyprus?',
                'Do I need a visa to study in Northern Cyprus?',
                'What is the application deadline for universities?'
              ].map((question, index) => (
                <motion.button
                  key={question}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 1.4 + index * 0.1, duration: 0.5 }}
                  className="w-full text-left p-4 bg-background/50 hover:bg-background/80 rounded-lg border transition-all duration-300 group"
                >
                  <div className="flex items-center justify-between">
                    <span className="text-sm group-hover:text-primary transition-colors">
                      {question}
                    </span>
                    <HelpCircle className="w-4 h-4 text-muted-foreground group-hover:text-primary transition-colors" />
                  </div>
                </motion.button>
              ))}
            </div>
          </motion.div>

          {/* Contact CTA */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 1.8, duration: 0.8 }}
            className="mt-12 text-center"
          >
            <p className="text-muted-foreground mb-4">
              Still have questions? Our expert team is here to help.
            </p>
            <Button variant="outline" size="lg">
              <MessageCircle className="w-4 h-4 mr-2" />
              Contact Support
            </Button>
          </motion.div>
        </div>
      </div>
    </section>
  )
}
