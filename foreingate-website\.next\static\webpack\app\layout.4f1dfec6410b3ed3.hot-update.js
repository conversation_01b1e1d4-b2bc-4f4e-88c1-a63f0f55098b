"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"d9bd034b92cf\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXE5pZGhhbFxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxmb3JlaW5nYXRlX2dyb3VwZVxcZm9yZWluZ2F0ZS13ZWJzaXRlXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJkOWJkMDM0YjkyY2ZcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/smart-chatbot.tsx":
/*!*********************************************!*\
  !*** ./src/components/ui/smart-chatbot.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SmartChatbot: () => (/* binding */ SmartChatbot)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/../node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Bot_Copy_MessageCircle_Minimize2_RefreshCw_Send_Sparkles_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Copy,MessageCircle,Minimize2,RefreshCw,Send,Sparkles,ThumbsDown,ThumbsUp,X!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Copy_MessageCircle_Minimize2_RefreshCw_Send_Sparkles_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Copy,MessageCircle,Minimize2,RefreshCw,Send,Sparkles,ThumbsDown,ThumbsUp,X!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Copy_MessageCircle_Minimize2_RefreshCw_Send_Sparkles_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Copy,MessageCircle,Minimize2,RefreshCw,Send,Sparkles,ThumbsDown,ThumbsUp,X!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Copy_MessageCircle_Minimize2_RefreshCw_Send_Sparkles_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Copy,MessageCircle,Minimize2,RefreshCw,Send,Sparkles,ThumbsDown,ThumbsUp,X!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/minimize-2.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Copy_MessageCircle_Minimize2_RefreshCw_Send_Sparkles_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Copy,MessageCircle,Minimize2,RefreshCw,Send,Sparkles,ThumbsDown,ThumbsUp,X!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Copy_MessageCircle_Minimize2_RefreshCw_Send_Sparkles_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Copy,MessageCircle,Minimize2,RefreshCw,Send,Sparkles,ThumbsDown,ThumbsUp,X!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Copy_MessageCircle_Minimize2_RefreshCw_Send_Sparkles_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Copy,MessageCircle,Minimize2,RefreshCw,Send,Sparkles,ThumbsDown,ThumbsUp,X!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/thumbs-up.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Copy_MessageCircle_Minimize2_RefreshCw_Send_Sparkles_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Copy,MessageCircle,Minimize2,RefreshCw,Send,Sparkles,ThumbsDown,ThumbsUp,X!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/thumbs-down.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Copy_MessageCircle_Minimize2_RefreshCw_Send_Sparkles_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Copy,MessageCircle,Minimize2,RefreshCw,Send,Sparkles,ThumbsDown,ThumbsUp,X!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Copy_MessageCircle_Minimize2_RefreshCw_Send_Sparkles_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Copy,MessageCircle,Minimize2,RefreshCw,Send,Sparkles,ThumbsDown,ThumbsUp,X!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* __next_internal_client_entry_do_not_use__ SmartChatbot auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction SmartChatbot(param) {\n    let { className = '' } = param;\n    _s();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMinimized, setIsMinimized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [inputValue, setInputValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [sessionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"SmartChatbot.useState\": ()=>Math.random().toString(36).substr(2, 9)\n    }[\"SmartChatbot.useState\"]);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Auto-scroll to bottom when new messages arrive\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SmartChatbot.useEffect\": ()=>{\n            var _messagesEndRef_current;\n            (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n                behavior: 'smooth'\n            });\n        }\n    }[\"SmartChatbot.useEffect\"], [\n        messages\n    ]);\n    // Focus input when chat opens\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SmartChatbot.useEffect\": ()=>{\n            if (isOpen && !isMinimized) {\n                setTimeout({\n                    \"SmartChatbot.useEffect\": ()=>{\n                        var _inputRef_current;\n                        return (_inputRef_current = inputRef.current) === null || _inputRef_current === void 0 ? void 0 : _inputRef_current.focus();\n                    }\n                }[\"SmartChatbot.useEffect\"], 100);\n            }\n        }\n    }[\"SmartChatbot.useEffect\"], [\n        isOpen,\n        isMinimized\n    ]);\n    // Initialize with welcome message\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SmartChatbot.useEffect\": ()=>{\n            if (isOpen && messages.length === 0) {\n                const welcomeMessage = {\n                    id: 'welcome',\n                    type: 'bot',\n                    content: \"\\uD83C\\uDF1F **Hello! I'm your Foreingate AI Assistant!**\\n\\nI can help you with:\\n• University information and programs\\n• Admission requirements and process\\n• Costs, scholarships, and financial aid\\n• Visa support and documentation\\n• Accommodation options\\n• Life in Northern Cyprus\\n• Any other questions about studying abroad!\\n\\nWhat would you like to know? \\uD83C\\uDF93\",\n                    timestamp: new Date(),\n                    suggestions: [\n                        \"Tell me about universities\",\n                        \"What are the costs?\",\n                        \"How do I apply?\",\n                        \"What about scholarships?\"\n                    ]\n                };\n                setMessages([\n                    welcomeMessage\n                ]);\n            }\n        }\n    }[\"SmartChatbot.useEffect\"], [\n        isOpen,\n        messages.length\n    ]);\n    const sendMessage = async (message)=>{\n        if (!message.trim() || isLoading) return;\n        const userMessage = {\n            id: Date.now().toString(),\n            type: 'user',\n            content: message,\n            timestamp: new Date()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        setInputValue('');\n        setIsLoading(true);\n        try {\n            const response = await fetch('/api/chatbot', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    message,\n                    sessionId\n                })\n            });\n            const data = await response.json();\n            if (data.success) {\n                const botMessage = {\n                    id: (Date.now() + 1).toString(),\n                    type: 'bot',\n                    content: data.response,\n                    timestamp: new Date(),\n                    category: data.category,\n                    confidence: data.confidence,\n                    suggestions: data.suggestions,\n                    data: data.data\n                };\n                setMessages((prev)=>[\n                        ...prev,\n                        botMessage\n                    ]);\n            } else {\n                throw new Error(data.error || 'Failed to get response');\n            }\n        } catch (error) {\n            console.error('Chat error:', error);\n            const errorMessage = {\n                id: (Date.now() + 1).toString(),\n                type: 'bot',\n                content: \"❌ Sorry, I encountered an error. Please try again or contact our support team at:\\n\\n\\uD83D\\uDCE7 **Email:** <EMAIL>\\n\\uD83D\\uDCF1 **WhatsApp:** +90 ************\\n\\uD83D\\uDCDE **Phone:** +90 ************\\n\\nOur human advisors are always ready to help! \\uD83E\\uDD1D\",\n                timestamp: new Date()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    errorMessage\n                ]);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleSuggestionClick = (suggestion)=>{\n        sendMessage(suggestion);\n    };\n    const copyMessage = (content)=>{\n        navigator.clipboard.writeText(content);\n    // Could add a toast notification here\n    };\n    const formatMessage = (content)=>{\n        // Convert markdown-like formatting to HTML\n        return content.replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>').replace(/\\*(.*?)\\*/g, '<em>$1</em>').replace(/•/g, '•').replace(/\\n/g, '<br>');\n    };\n    if (!isOpen) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed bottom-6 right-6 z-[9999] \".concat(className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 rounded-full bg-gradient-to-r from-blue-600 to-purple-600 animate-ping opacity-20 pointer-events-none\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                    lineNumber: 166,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                    initial: {\n                        scale: 0\n                    },\n                    animate: {\n                        scale: 1\n                    },\n                    transition: {\n                        delay: 1,\n                        type: \"spring\",\n                        stiffness: 260,\n                        damping: 20\n                    },\n                    className: \"relative z-10\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        onClick: ()=>{\n                            console.log('Chat button clicked!');\n                            setIsOpen(true);\n                        },\n                        className: \"h-16 w-16 rounded-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 relative cursor-pointer\",\n                        size: \"icon\",\n                        type: \"button\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Copy_MessageCircle_Minimize2_RefreshCw_Send_Sparkles_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-7 w-7\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute -top-1 -right-1 h-4 w-4 bg-green-500 rounded-full animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                    lineNumber: 168,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n            lineNumber: 164,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed bottom-6 right-6 z-50 \".concat(className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n            initial: {\n                opacity: 0,\n                scale: 0.8,\n                y: 20\n            },\n            animate: {\n                opacity: 1,\n                scale: 1,\n                y: 0,\n                height: isMinimized ? 60 : 600,\n                width: isMinimized ? 300 : 400\n            },\n            exit: {\n                opacity: 0,\n                scale: 0.8,\n                y: 20\n            },\n            transition: {\n                duration: 0.3\n            },\n            className: \"bg-white dark:bg-gray-800 rounded-2xl shadow-2xl border border-gray-200 dark:border-gray-700 overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-blue-600 to-purple-600 p-4 text-white\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Copy_MessageCircle_Minimize2_RefreshCw_Send_Sparkles_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"w-8 h-8\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Copy_MessageCircle_Minimize2_RefreshCw_Send_Sparkles_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-3 h-3 absolute -top-1 -right-1 text-yellow-300\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                                lineNumber: 212,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold text-lg\",\n                                                children: \"Foreingate AI\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs opacity-90\",\n                                                children: \"Smart Education Assistant\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                                lineNumber: 216,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: ()=>setIsMinimized(!isMinimized),\n                                        className: \"h-8 w-8 p-0 text-white hover:bg-white/20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Copy_MessageCircle_Minimize2_RefreshCw_Send_Sparkles_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: ()=>setIsOpen(false),\n                                        className: \"h-8 w-8 p-0 text-white hover:bg-white/20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Copy_MessageCircle_Minimize2_RefreshCw_Send_Sparkles_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                            lineNumber: 234,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                    lineNumber: 207,\n                    columnNumber: 9\n                }, this),\n                !isMinimized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-96 overflow-y-auto p-4 space-y-4 bg-gray-50 dark:bg-gray-900\",\n                            children: [\n                                messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex \".concat(message.type === 'user' ? 'justify-end' : 'justify-start'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"max-w-[85%] rounded-2xl px-4 py-3 \".concat(message.type === 'user' ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white' : 'bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 shadow-md border'),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start space-x-2\",\n                                                children: [\n                                                    message.type === 'bot' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Copy_MessageCircle_Minimize2_RefreshCw_Send_Sparkles_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"w-5 h-5 mt-1 text-blue-600 flex-shrink-0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                                        lineNumber: 258,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm leading-relaxed\",\n                                                                dangerouslySetInnerHTML: {\n                                                                    __html: formatMessage(message.content)\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                                                lineNumber: 261,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            message.type === 'bot' && message.confidence && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mt-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                        variant: \"outline\",\n                                                                        className: \"text-xs\",\n                                                                        children: message.category\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                                                        lineNumber: 269,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                        variant: \"outline\",\n                                                                        className: \"text-xs\",\n                                                                        children: [\n                                                                            Math.round(message.confidence),\n                                                                            \"% confident\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                                                        lineNumber: 272,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                                                lineNumber: 268,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            message.suggestions && message.suggestions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-3 space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-600 dark:text-gray-400\",\n                                                                        children: \"Quick questions:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                                                        lineNumber: 281,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex flex-wrap gap-2\",\n                                                                        children: message.suggestions.map((suggestion, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                                variant: \"outline\",\n                                                                                size: \"sm\",\n                                                                                onClick: ()=>handleSuggestionClick(suggestion),\n                                                                                className: \"text-xs h-7 px-2 hover:bg-blue-50 hover:border-blue-300\",\n                                                                                children: suggestion\n                                                                            }, index, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                                                                lineNumber: 284,\n                                                                                columnNumber: 33\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                                                        lineNumber: 282,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                                                lineNumber: 280,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            message.type === 'bot' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mt-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        size: \"sm\",\n                                                                        onClick: ()=>copyMessage(message.content),\n                                                                        className: \"h-6 w-6 p-0 text-gray-500 hover:text-gray-700\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Copy_MessageCircle_Minimize2_RefreshCw_Send_Sparkles_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                            className: \"h-3 w-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                                                            lineNumber: 307,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                                                        lineNumber: 301,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        size: \"sm\",\n                                                                        className: \"h-6 w-6 p-0 text-gray-500 hover:text-green-600\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Copy_MessageCircle_Minimize2_RefreshCw_Send_Sparkles_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                            className: \"h-3 w-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                                                            lineNumber: 314,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                                                        lineNumber: 309,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        size: \"sm\",\n                                                                        className: \"h-6 w-6 p-0 text-gray-500 hover:text-red-600\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Copy_MessageCircle_Minimize2_RefreshCw_Send_Sparkles_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                            className: \"h-3 w-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                                                            lineNumber: 321,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                                                        lineNumber: 316,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                                                lineNumber: 300,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                                        lineNumber: 260,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, message.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 17\n                                    }, this)),\n                                isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-start\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white dark:bg-gray-800 rounded-2xl px-4 py-3 shadow-md border\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Copy_MessageCircle_Minimize2_RefreshCw_Send_Sparkles_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-5 h-5 text-blue-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                                    lineNumber: 335,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-blue-600 rounded-full animate-bounce\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                                            lineNumber: 337,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-blue-600 rounded-full animate-bounce\",\n                                                            style: {\n                                                                animationDelay: '0.1s'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                                            lineNumber: 338,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-blue-600 rounded-full animate-bounce\",\n                                                            style: {\n                                                                animationDelay: '0.2s'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                                            lineNumber: 339,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                                    lineNumber: 336,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                            lineNumber: 334,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                        lineNumber: 333,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                    lineNumber: 332,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    ref: messagesEndRef\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                    lineNumber: 346,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                            lineNumber: 243,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 border-t bg-white dark:bg-gray-800\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                            ref: inputRef,\n                                            value: inputValue,\n                                            onChange: (e)=>setInputValue(e.target.value),\n                                            onKeyPress: (e)=>e.key === 'Enter' && sendMessage(inputValue),\n                                            placeholder: \"Ask me anything about studying abroad...\",\n                                            disabled: isLoading,\n                                            className: \"flex-1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                            lineNumber: 352,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            onClick: ()=>sendMessage(inputValue),\n                                            disabled: isLoading || !inputValue.trim(),\n                                            className: \"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700\",\n                                            size: \"icon\",\n                                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Copy_MessageCircle_Minimize2_RefreshCw_Send_Sparkles_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"h-4 w-4 animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                                lineNumber: 368,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Copy_MessageCircle_Minimize2_RefreshCw_Send_Sparkles_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                                lineNumber: 370,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                            lineNumber: 361,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                    lineNumber: 351,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500 mt-2 text-center\",\n                                    children: \"Powered by Foreingate AI • Always here to help! \\uD83C\\uDF93\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                                    lineNumber: 374,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n                            lineNumber: 350,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n            lineNumber: 193,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\smart-chatbot.tsx\",\n        lineNumber: 192,\n        columnNumber: 5\n    }, this);\n}\n_s(SmartChatbot, \"Eu1ISuiZBhpjVDECGUEX5BB5ejM=\");\n_c = SmartChatbot;\nvar _c;\n$RefreshReg$(_c, \"SmartChatbot\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/smart-chatbot.tsx\n"));

/***/ })

});