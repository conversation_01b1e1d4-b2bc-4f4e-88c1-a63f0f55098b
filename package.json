{"dependencies": {"@hookform/resolvers": "^5.1.1", "@prisma/client": "^6.11.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.2.7", "@types/nodemailer": "^6.4.17", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.23.3", "lucide-react": "^0.525.0", "next-themes": "^0.4.6", "prisma": "^6.11.1", "react-hook-form": "^7.60.0", "react-intersection-observer": "^9.16.0", "resend": "^4.6.0", "tailwind-merge": "^3.3.1", "zod": "^4.0.5"}, "devDependencies": {"@types/node": "^24.0.13", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14"}}