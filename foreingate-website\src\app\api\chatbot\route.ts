import { NextRequest, NextResponse } from 'next/server'
import { websiteKnowledge, questionCategories, responseTemplates } from '@/lib/chatbot-knowledge'
import { RAGResponseGenerator, KnowledgeRetriever } from '@/lib/rag-system'
import { withApiSecurity, secureResponse } from '@/lib/api-security'
import { sanitizeInput } from '@/lib/security'

interface ChatMessage {
  message: string
  timestamp?: string
  sessionId?: string
}

interface ChatResponse {
  response: string
  category: string
  confidence: number
  suggestions?: string[]
  followUp?: string[]
  data?: any
  ragSources?: any[]
  retrievedChunks?: number
  ragEnabled?: boolean
}

class SmartChatbot {
  private knowledge = websiteKnowledge
  private categories = questionCategories
  private ragGenerator: RAGResponseGenerator
  private retriever: KnowledgeRetriever

  constructor() {
    this.ragGenerator = new RAGResponseGenerator()
    this.retriever = new KnowledgeRetriever()
  }

  // Analyze user message and determine intent
  analyzeIntent(message: string): { category: string; confidence: number; keywords: string[] } {
    const cleanMessage = message.toLowerCase().trim()
    const words = cleanMessage.split(/\s+/)
    
    let bestCategory = 'general'
    let maxScore = 0
    let matchedKeywords: string[] = []

    // Check each category for keyword matches
    for (const [category, keywords] of Object.entries(this.categories)) {
      let score = 0
      const categoryMatches: string[] = []

      for (const keyword of keywords) {
        if (cleanMessage.includes(keyword)) {
          score += keyword.length // Longer keywords get higher scores
          categoryMatches.push(keyword)
        }
      }

      if (score > maxScore) {
        maxScore = score
        bestCategory = category
        matchedKeywords = categoryMatches
      }
    }

    const confidence = Math.min(maxScore / cleanMessage.length * 100, 100)
    return { category: bestCategory, confidence, keywords: matchedKeywords }
  }

  // Generate intelligent response using RAG
  generateResponse(message: string): ChatResponse {
    const { category, confidence, keywords } = this.analyzeIntent(message)

    // First try RAG approach for more intelligent responses
    const ragResult = this.ragGenerator.generateResponse(message, category)

    // If RAG provides good results, use it
    if (ragResult.confidence > 30 && ragResult.retrievedChunks > 0) {
      return {
        response: ragResult.response,
        category,
        confidence: ragResult.confidence,
        suggestions: this.getSuggestionsForCategory(category),
        followUp: this.getRandomFollowUp(),
        data: ragResult.sources,
        ragSources: ragResult.sources,
        retrievedChunks: ragResult.retrievedChunks,
        ragEnabled: true
      }
    }

    // Fallback to traditional rule-based responses
    let response = ''
    let data: any = null
    let suggestions: string[] = []

    switch (category) {
      case 'admissions':
        response = this.handleAdmissionsQuery(message, keywords)
        data = this.knowledge.admissionProcess
        suggestions = [
          "What documents do I need?",
          "How long does the process take?",
          "What are the requirements for graduate programs?"
        ]
        break

      case 'universities':
        response = this.handleUniversitiesQuery(message, keywords)
        data = this.knowledge.universities
        suggestions = [
          "Tell me about EMU",
          "What programs does NEU offer?",
          "Which university is best for engineering?"
        ]
        break

      case 'costs':
        response = this.handleCostsQuery(message, keywords)
        data = this.knowledge.costs
        suggestions = [
          "What are the living costs?",
          "Are there scholarships available?",
          "How much is tuition for medicine?"
        ]
        break

      case 'visa':
        response = this.handleVisaQuery(message, keywords)
        suggestions = [
          "What documents do I need for visa?",
          "How long does visa processing take?",
          "Do you help with visa applications?"
        ]
        break

      case 'accommodation':
        response = this.handleAccommodationQuery(message, keywords)
        suggestions = [
          "What housing options are available?",
          "How much does accommodation cost?",
          "Can you help me find housing?"
        ]
        break

      case 'services':
        response = this.handleServicesQuery(message, keywords)
        data = this.knowledge.services
        suggestions = [
          "What services do you offer?",
          "How much do your services cost?",
          "Do you provide ongoing support?"
        ]
        break

      case 'location':
        response = this.handleLocationQuery(message, keywords)
        suggestions = [
          "Is Northern Cyprus safe?",
          "What's the weather like?",
          "How do I get to Northern Cyprus?"
        ]
        break

      case 'contact':
        response = this.handleContactQuery(message, keywords)
        data = this.knowledge.contact
        suggestions = [
          "What are your office hours?",
          "Can I visit your office?",
          "Do you speak my language?"
        ]
        break

      case 'scholarships':
        response = this.handleScholarshipsQuery(message, keywords)
        data = this.knowledge.scholarships
        suggestions = [
          "What scholarships are available?",
          "How do I qualify for merit scholarship?",
          "What is the early bird discount?"
        ]
        break

      case 'language':
        response = this.handleLanguageQuery(message, keywords)
        suggestions = [
          "Do I need to speak Turkish?",
          "What English level is required?",
          "Are there language courses available?"
        ]
        break

      default:
        response = this.handleGeneralQuery(message, keywords)
        suggestions = [
          "Tell me about your services",
          "Which universities do you work with?",
          "How can you help me study abroad?"
        ]
    }

    return {
      response,
      category,
      confidence,
      suggestions,
      followUp: this.getRandomFollowUp(),
      data,
      ragEnabled: false
    }
  }

  private getSuggestionsForCategory(category: string): string[] {
    const categoryMap: { [key: string]: string[] } = {
      universities: [
        "Tell me about EMU programs",
        "What makes NEU special?",
        "Compare universities for engineering"
      ],
      admissions: [
        "What documents do I need?",
        "How long does the process take?",
        "What are the requirements?"
      ],
      costs: [
        "What are the living costs?",
        "Are there scholarships available?",
        "How much is tuition for medicine?"
      ],
      scholarships: [
        "How do I qualify for merit scholarship?",
        "What is the early bird discount?",
        "Can I get multiple scholarships?"
      ],
      visa: [
        "What documents do I need for visa?",
        "How long does visa processing take?",
        "Do you help with visa applications?"
      ],
      accommodation: [
        "What housing options are available?",
        "How much does accommodation cost?",
        "Can you help me find housing?"
      ],
      services: [
        "What services do you offer?",
        "How much do your services cost?",
        "Do you provide ongoing support?"
      ],
      location: [
        "Is Northern Cyprus safe?",
        "What's the weather like?",
        "How do I get to Northern Cyprus?"
      ],
      contact: [
        "What are your office hours?",
        "Can I visit your office?",
        "Do you speak my language?"
      ],
      language: [
        "Do I need to speak Turkish?",
        "What English level is required?",
        "Are there language courses available?"
      ]
    }

    return categoryMap[category] || [
      "Tell me about your services",
      "Which universities do you work with?",
      "How can you help me study abroad?"
    ]
  }

  private handleAdmissionsQuery(message: string, keywords: string[]): string {
    if (keywords.some(k => ['requirements', 'documents'].includes(k))) {
      return `📋 **Admission Requirements:**

**For Undergraduate Programs:**
• High school diploma or equivalent
• English proficiency test (IELTS 6.0+ or TOEFL 79+)
• Passport copy
• Academic transcripts
• Personal statement

**For Graduate Programs:**
• Bachelor's degree
• English proficiency test
• Letters of recommendation (2-3)
• Statement of purpose
• GRE/GMAT (for some programs)

**Timeline:** The complete process typically takes 2-4 months. We handle everything from document preparation to final enrollment!

Would you like me to help you start your application? 🎓`
    }

    if (keywords.some(k => ['deadline', 'when'].includes(k))) {
      return `📅 **Application Deadlines:**

Good news! We accept applications **year-round** for most programs. However, I recommend applying **3-4 months before** your intended start date for:

• Better preparation time
• Higher scholarship chances
• Smoother visa processing
• Better accommodation options

**Intake Periods:**
• Fall Semester: September
• Spring Semester: February
• Summer Programs: June

Ready to start your application? I can connect you with our admissions team! 🚀`
    }

    return `🎓 **University Admissions Made Easy!**

At Foreingate, we make university admissions simple and stress-free. Our comprehensive service includes:

✅ **Free consultation** and university matching
✅ **Complete application** preparation and submission
✅ **Document verification** and translation
✅ **Interview coaching** (when required)
✅ **Admission guarantee** or full refund

We've helped over 5,000 students secure admissions to top universities in Northern Cyprus. Our success rate is 98%!

What specific aspect of admissions would you like to know more about?`
  }

  private handleUniversitiesQuery(message: string, keywords: string[]): string {
    const universities = this.knowledge.universities

    if (message.toLowerCase().includes('emu') || message.toLowerCase().includes('eastern mediterranean')) {
      const emu = universities.find(u => u.name.includes('Eastern Mediterranean'))!
      return `🏛️ **Eastern Mediterranean University (EMU)**

📍 **Location:** Famagusta, Northern Cyprus
📅 **Established:** ${emu.established}
👥 **Students:** ${emu.students.toLocaleString()} (${emu.international.toLocaleString()} international)

**🎓 Popular Programs:**
${emu.programs.map(p => `• ${p}`).join('\n')}

**💰 Tuition:** $${emu.tuition.min.toLocaleString()} - $${emu.tuition.max.toLocaleString()} per year
**🌍 Language:** ${emu.language}
**✅ Accreditation:** ${emu.accreditation}

EMU is known for its strong engineering and business programs, beautiful campus, and vibrant student life!

Would you like more details about specific programs? 🎯`
    }

    if (message.toLowerCase().includes('neu') || message.toLowerCase().includes('near east')) {
      const neu = universities.find(u => u.name.includes('Near East'))!
      return `🏥 **Near East University (NEU)**

📍 **Location:** Nicosia, Northern Cyprus
📅 **Established:** ${neu.established}
👥 **Students:** ${neu.students.toLocaleString()} (${neu.international.toLocaleString()} international)

**🎓 Popular Programs:**
${neu.programs.map(p => `• ${p}`).join('\n')}

**💰 Tuition:** $${neu.tuition.min.toLocaleString()} - $${neu.tuition.max.toLocaleString()} per year
**🌍 Language:** ${neu.language}
**✅ Accreditation:** ${neu.accreditation}

NEU is especially renowned for its **Medicine and Dentistry** programs, with WHO recognition and state-of-the-art facilities!

Interested in medical programs? Let me know! 🩺`
    }

    return `🏛️ **Our Partner Universities in Northern Cyprus:**

We work with the **top 3 universities** that welcome international students:

**1. Eastern Mediterranean University (EMU)** 🌟
   • 20,000+ students • Strong in Engineering & Business
   
**2. Near East University (NEU)** 🏥
   • 25,000+ students • Excellent Medical Programs
   
**3. Cyprus International University (CIU)** 🎨
   • 15,000+ students • Great for Business & Arts

**All universities offer:**
✅ English-taught programs
✅ International accreditation
✅ Modern facilities
✅ Vibrant campus life
✅ Affordable tuition

Which university interests you most? I can provide detailed information! 🎓`
  }

  private handleCostsQuery(message: string, keywords: string[]): string {
    const costs = this.knowledge.costs

    if (keywords.some(k => ['scholarship', 'financial'].includes(k))) {
      return `💰 **Scholarships & Financial Aid:**

**Merit Scholarship** 🏆
• 25-50% tuition reduction
• For students with GPA 3.5+ or equivalent
• Automatic consideration with application

**Early Bird Discount** ⏰
• 10-15% tuition reduction
• Apply before March 31st
• Available for all programs

**Sibling Discount** 👨‍👩‍👧‍👦
• 10% tuition reduction
• For families with multiple students
• Applies to second sibling onwards

**Payment Plans** 💳
• Flexible installment options
• No interest charges
• Customized to your budget

**Total Savings Possible:** Up to 65% off tuition fees!

Ready to apply for scholarships? Let me help you! 🎯`
    }

    return `💰 **Study Costs in Northern Cyprus:**

**📚 Tuition Fees (per year):**
• Undergraduate: $${costs.tuition.undergraduate.min.toLocaleString()} - $${costs.tuition.undergraduate.max.toLocaleString()}
• Graduate: $${costs.tuition.graduate.min.toLocaleString()} - $${costs.tuition.graduate.max.toLocaleString()}
• Medicine: $${costs.tuition.medicine.min.toLocaleString()} - $${costs.tuition.medicine.max.toLocaleString()}

**🏠 Living Costs (per month):**
• Accommodation: $${costs.living.accommodation.min} - $${costs.living.accommodation.max}
• Food: $${costs.living.food.min} - $${costs.living.food.max}
• Transportation: $${costs.living.transportation.min} - $${costs.living.transportation.max}
• **Total Living:** $${costs.living.total.min} - $${costs.living.total.max}

**💡 Why Northern Cyprus is Affordable:**
✅ 50-70% cheaper than UK/US
✅ High quality education
✅ Low cost of living
✅ Scholarship opportunities

Want to know about scholarships to reduce costs even more? 🎓`
  }

  private handleVisaQuery(message: string, keywords: string[]): string {
    return `🛂 **Visa Support Services:**

**We Handle Everything!** ✅
• Complete visa application preparation
• Document collection and verification
• Embassy appointment scheduling
• Application tracking and follow-up

**📋 Typical Documents Needed:**
• Valid passport (6+ months validity)
• University acceptance letter
• Financial proof (bank statements)
• Health insurance
• Accommodation proof
• Passport photos

**⏱️ Processing Time:**
• Most countries: 2-4 weeks
• Some countries: 4-8 weeks
• We expedite when possible

**💰 Our Visa Service:**
• Starting from $200
• Success rate: 95%+
• Full support until visa approval

**🌍 Visa-Free Countries:**
Some nationalities don't need a visa for Northern Cyprus!

Which country are you from? I can give you specific visa information! 🌟`
  }

  private handleAccommodationQuery(message: string, keywords: string[]): string {
    return `🏠 **Student Accommodation Options:**

**🏫 University Dormitories:**
• $200-400/month
• On-campus convenience
• Meal plans available
• Social environment

**🏡 Private Apartments:**
• $300-600/month
• More privacy and space
• Kitchen facilities
• Shared or single options

**👨‍👩‍👧‍👦 Homestay Programs:**
• $250-450/month
• Live with local families
• Cultural immersion
• Meals included

**🚗 Additional Services:**
✅ Airport pickup arrangement
✅ Accommodation booking assistance
✅ Contract negotiation help
✅ Ongoing support

**📍 Popular Areas:**
• Near campus locations
• City center options
• Quiet residential areas

Need help finding the perfect accommodation? We'll match you with the best option for your budget and preferences! 🏡`
  }

  private handleServicesQuery(message: string, keywords: string[]): string {
    return `🎯 **Our Complete Services:**

**🎓 University Admissions**
• Free consultation & university matching
• Application preparation & submission
• Document verification & translation
• Interview coaching

**🛂 Visa Support**
• Complete visa application assistance
• Document preparation
• Embassy appointments
• Application tracking

**🏠 Accommodation Services**
• Housing search & booking
• Contract assistance
• Airport pickup
• Settlement support

**📚 Academic Support**
• Tutoring services
• Study groups
• Academic counseling
• Career guidance

**💰 Pricing:**
• Initial consultation: **FREE**
• University application: Competitive rates
• Visa support: From $200
• Accommodation: Commission-based

**🌟 Why Choose Foreingate:**
✅ 98% success rate
✅ 5,000+ students helped
✅ End-to-end support
✅ Multilingual team

Ready to start your journey? Let's talk! 🚀`
  }

  private handleLocationQuery(message: string, keywords: string[]): string {
    return `🌍 **About Northern Cyprus:**

**📍 Location:**
• Eastern Mediterranean island
• Between Turkey and Middle East
• Beautiful coastline and mountains
• Rich history and culture

**🌤️ Climate:**
• Mediterranean climate
• Warm summers, mild winters
• 300+ sunny days per year
• Perfect for outdoor activities

**🛡️ Safety:**
• Very safe for international students
• Low crime rates
• Welcoming local community
• English widely spoken

**✈️ Getting There:**
• Fly via Turkey (Istanbul/Ankara)
• Direct flights from many countries
• Airport pickup service available
• Easy visa process

**🎭 Student Life:**
• Vibrant international community
• Cultural festivals and events
• Beautiful beaches and nature
• Affordable entertainment

**💡 Why Students Love It:**
✅ Safe and friendly environment
✅ Affordable living costs
✅ Rich cultural experience
✅ Gateway to Europe and Asia

Curious about life in Northern Cyprus? I can share more details! 🏖️`
  }

  private handleContactQuery(message: string, keywords: string[]): string {
    const contact = this.knowledge.contact
    return `📞 **Get in Touch with Foreingate:**

**🏢 Office Contact:**
• 📧 Email: ${contact.email}
• 📱 Phone: ${contact.phone}
• 💬 WhatsApp: ${contact.whatsapp}
• 📍 Address: ${contact.address}

**⏰ Working Hours:**
${contact.workingHours}

**🌍 Languages We Speak:**
${contact.languages.join(' • ')}

**💬 Instant Support:**
• Live chat on our website
• WhatsApp for quick questions
• Email for detailed inquiries
• Phone for urgent matters

**🎯 Best Ways to Reach Us:**
• **Quick questions:** WhatsApp
• **Detailed consultation:** Email or phone
• **Immediate help:** Live chat
• **Document submission:** Email

**📅 Free Consultation:**
Book a free 30-minute consultation to discuss your education plans!

How would you prefer to get in touch? 🤝`
  }

  private handleGeneralQuery(message: string, keywords: string[]): string {
    return `🌟 **Welcome to Foreingate Group!**

We're your **trusted partner** for higher education in Northern Cyprus. Since 2020, we've helped over **5,000 international students** achieve their academic dreams!

**🎯 What We Do:**
✅ University admissions (98% success rate)
✅ Visa support and documentation
✅ Accommodation arrangements
✅ Ongoing academic support

**🏛️ Partner Universities:**
• Eastern Mediterranean University (EMU)
• Near East University (NEU)  
• Cyprus International University (CIU)

**💰 Why Choose Northern Cyprus:**
• High-quality English education
• 50-70% cheaper than UK/US
• Safe and welcoming environment
• EU-recognized degrees

**🚀 Ready to Start?**
• Free consultation available
• Personalized university matching
• Complete application support
• End-to-end service

What aspect of studying abroad interests you most? I'm here to help with any questions! 🎓`
  }

  private handleScholarshipsQuery(message: string, keywords: string[]): string {
    return `💰 **Scholarships & Financial Aid:**

**Merit Scholarship** 🏆
• 25-50% tuition reduction
• For students with GPA 3.5+ or equivalent
• Automatic consideration with application

**Early Bird Discount** ⏰
• 10-15% tuition reduction
• Apply before March 31st
• Available for all programs

**Sibling Discount** 👨‍👩‍👧‍👦
• 10% tuition reduction
• For families with multiple students
• Applies to second sibling onwards

**Payment Plans** 💳
• Flexible installment options
• No interest charges
• Customized to your budget

**Total Savings Possible:** Up to 65% off tuition fees!

**🎯 How to Apply:**
✅ Submit your application early
✅ Maintain high academic performance
✅ Provide all required documents
✅ Meet application deadlines

Ready to apply for scholarships? Let me help you! 🎯`
  }

  private handleLanguageQuery(message: string, keywords: string[]): string {
    return `🌍 **Language Requirements & Support:**

**📚 Program Languages:**
• **English:** All programs taught in English
• **Turkish:** Optional, helpful for daily life
• **No Turkish Required:** For academic success

**📝 English Proficiency Requirements:**
• **IELTS:** 6.0+ overall (5.5+ each band)
• **TOEFL:** 79+ iBT (17+ each section)
• **PTE:** 58+ overall
• **Alternative:** University English test

**🎓 Language Support Services:**
✅ **English Preparatory Program** - If needed
✅ **Academic English Courses** - Writing, presentation skills
✅ **Turkish Language Classes** - Optional cultural integration
✅ **Language Exchange Programs** - Practice with local students

**💡 Language Tips:**
• Most international students succeed with basic English
• Campus environment is very English-friendly
• Local community welcomes international students
• Many locals speak English

**🌟 Don't Worry About Language!**
Our universities have excellent support systems for international students. You'll improve your English naturally in the immersive environment!

Need help with English proficiency tests? We can guide you! 📖`
  }

  private getRandomFollowUp(): string[] {
    return responseTemplates.followUp.sort(() => 0.5 - Math.random()).slice(0, 2)
  }
}

async function handleChatbotRequest(request: NextRequest) {
  try {
    const body = await request.json()
    const { message, sessionId } = body as ChatMessage

    if (!message || message.trim().length === 0) {
      return secureResponse({
        error: 'Message is required'
      }, 400)
    }

    // Sanitize input
    const cleanMessage = sanitizeInput(message.trim())
    
    if (cleanMessage.length > 500) {
      return secureResponse({
        error: 'Message too long. Please keep it under 500 characters.'
      }, 400)
    }

    // Initialize chatbot
    const chatbot = new SmartChatbot()
    
    // Generate response
    const response = chatbot.generateResponse(cleanMessage)

    return secureResponse({
      success: true,
      ...response,
      timestamp: new Date().toISOString(),
      sessionId: sessionId || 'anonymous'
    })

  } catch (error) {
    console.error('Chatbot error:', error)
    return secureResponse({
      error: 'Sorry, I encountered an error. Please try again or contact our support team.',
      fallback: true
    }, 500)
  }
}

// Apply security middleware
export const POST = withApiSecurity(handleChatbotRequest)
