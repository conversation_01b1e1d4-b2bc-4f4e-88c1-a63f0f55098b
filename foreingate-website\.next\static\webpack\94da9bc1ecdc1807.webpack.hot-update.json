{"c": ["app/layout", "webpack"], "r": ["app/language-test/page"], "m": ["(app-pages-browser)/../node_modules/framer-motion/dist/es/components/AnimatePresence/PopChild.mjs", "(app-pages-browser)/../node_modules/framer-motion/dist/es/components/AnimatePresence/PresenceChild.mjs", "(app-pages-browser)/../node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs", "(app-pages-browser)/../node_modules/framer-motion/dist/es/components/AnimatePresence/utils.mjs", "(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/book-open.js", "(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/bot.js", "(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/check.js", "(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/chevron-down.js", "(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/circle-alert.js", "(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/circle-check-big.js", "(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/clock.js", "(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/copy.js", "(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/dollar-sign.js", "(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/facebook.js", "(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/globe.js", "(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/graduation-cap.js", "(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/info.js", "(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/instagram.js", "(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/linkedin.js", "(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/map-pin.js", "(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/message-circle.js", "(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/minimize-2.js", "(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/moon.js", "(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/phone.js", "(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/refresh-cw.js", "(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/search.js", "(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/send.js", "(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/sparkles.js", "(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/sun.js", "(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/thumbs-down.js", "(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/thumbs-up.js", "(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/triangle-alert.js", "(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/twitter.js", "(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/youtube.js", "(app-pages-browser)/../node_modules/next-themes/dist/index.mjs", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Capp%5C%5Clanguage-test%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!", "(app-pages-browser)/./src/app/language-test/page.tsx", "(app-pages-browser)/./src/components/layout/footer.tsx", "(app-pages-browser)/./src/components/layout/navigation.tsx", "(app-pages-browser)/./src/components/layout/page-wrapper.tsx", "(app-pages-browser)/./src/components/providers/theme-provider.tsx", "(app-pages-browser)/./src/components/providers/translation-provider.tsx", "(app-pages-browser)/./src/components/ui/badge.tsx", "(app-pages-browser)/./src/components/ui/card.tsx", "(app-pages-browser)/./src/components/ui/input.tsx", "(app-pages-browser)/./src/components/ui/language-switcher.tsx", "(app-pages-browser)/./src/components/ui/newsletter-signup.tsx", "(app-pages-browser)/./src/components/ui/search.tsx", "(app-pages-browser)/./src/components/ui/smart-chatbot.tsx", "(app-pages-browser)/./src/components/ui/theme-toggle.tsx", "(app-pages-browser)/./src/components/ui/toast.tsx", "(app-pages-browser)/./src/hooks/use-translation.ts", "(app-pages-browser)/./src/lib/i18n.ts", "(app-pages-browser)/./src/locales/ar.ts", "(app-pages-browser)/./src/locales/en.ts", "(app-pages-browser)/./src/locales/es.ts", "(app-pages-browser)/./src/locales/fr.ts", "(app-pages-browser)/./src/locales/tr.ts"]}