'use client'

import { notFound } from 'next/navigation'
import { ProgramDetailHero } from '@/components/sections/program-detail-hero'
import { ProgramDetailContent } from '@/components/sections/program-detail-content'
import { ProgramRequirementsSection } from '@/components/sections/program-requirements'
import { ProgramCareerSection } from '@/components/sections/program-career'
import { WhatsAppWidget } from '@/components/ui/whatsapp-widget'
import { useProgram } from '@/hooks/use-api'
import { useEffect, useState } from 'react'

interface ProgramPageProps {
  params: {
    slug: string
  }
}

export default function ProgramPage({ params }: ProgramPageProps) {
  const { data: program, loading, error } = useProgram(params.slug)

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    )
  }

  if (error || !program) {
    notFound()
  }

  return (
    <>
      <ProgramDetailHero program={program} />
      <ProgramDetailContent program={program} />
      <ProgramRequirementsSection program={program} />
      <ProgramCareerSection program={program} />

      {/* WhatsApp Widget */}
      <WhatsAppWidget
        phoneNumber="905392123456"
        message={`Hello! I'm interested in the ${program.name} program.`}
      />
    </>
  )
}
