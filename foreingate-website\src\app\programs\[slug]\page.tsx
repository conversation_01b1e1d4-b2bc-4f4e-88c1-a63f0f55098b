import { notFound } from 'next/navigation'
import { ProgramDetailHero } from '@/components/sections/program-detail-hero'
import { ProgramDetailContent } from '@/components/sections/program-detail-content'
import { ProgramRequirementsSection } from '@/components/sections/program-requirements'
import { ProgramCareerSection } from '@/components/sections/program-career'
import { WhatsAppWidget } from '@/components/ui/whatsapp-widget'
import { mockPrograms } from '@/lib/mock-data'

interface ProgramPageProps {
  params: {
    slug: string
  }
}

export async function generateMetadata({ params }: ProgramPageProps) {
  const program = mockPrograms.find(p => p.slug === params.slug)
  
  if (!program) {
    return {
      title: 'Program Not Found - Foreingate Group'
    }
  }

  return {
    title: `${program.name} - Foreingate Group`,
    description: program.description,
    keywords: [program.name, program.field, program.degree, 'Northern Cyprus', 'study abroad'],
  }
}

export default function ProgramPage({ params }: ProgramPageProps) {
  const program = mockPrograms.find(p => p.slug === params.slug)

  if (!program) {
    notFound()
  }

  return (
    <>
      <ProgramDetailHero program={program} />
      <ProgramDetailContent program={program} />
      <ProgramRequirementsSection program={program} />
      <ProgramCareerSection program={program} />
      
      {/* WhatsApp Widget */}
      <WhatsAppWidget 
        phoneNumber="905392123456"
        message={`Hello! I'm interested in the ${program.name} program.`}
      />
    </>
  )
}

// Generate static params for all programs
export async function generateStaticParams() {
  return mockPrograms.map((program) => ({
    slug: program.slug,
  }))
}
