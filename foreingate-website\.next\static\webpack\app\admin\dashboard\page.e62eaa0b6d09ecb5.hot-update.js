"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/admin/dashboard/page.tsx":
/*!******************************************!*\
  !*** ./src/app/admin/dashboard/page.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminDashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/../node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_Eye_FileText_Mail_Settings_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowDownRight,ArrowUpRight,BarChart3,Calendar,Eye,FileText,Mail,Settings,TrendingUp!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_Eye_FileText_Mail_Settings_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowDownRight,ArrowUpRight,BarChart3,Calendar,Eye,FileText,Mail,Settings,TrendingUp!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_Eye_FileText_Mail_Settings_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowDownRight,ArrowUpRight,BarChart3,Calendar,Eye,FileText,Mail,Settings,TrendingUp!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_Eye_FileText_Mail_Settings_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowDownRight,ArrowUpRight,BarChart3,Calendar,Eye,FileText,Mail,Settings,TrendingUp!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_Eye_FileText_Mail_Settings_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowDownRight,ArrowUpRight,BarChart3,Calendar,Eye,FileText,Mail,Settings,TrendingUp!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_Eye_FileText_Mail_Settings_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowDownRight,ArrowUpRight,BarChart3,Calendar,Eye,FileText,Mail,Settings,TrendingUp!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/arrow-up-right.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_Eye_FileText_Mail_Settings_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowDownRight,ArrowUpRight,BarChart3,Calendar,Eye,FileText,Mail,Settings,TrendingUp!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/arrow-down-right.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_Eye_FileText_Mail_Settings_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowDownRight,ArrowUpRight,BarChart3,Calendar,Eye,FileText,Mail,Settings,TrendingUp!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_Eye_FileText_Mail_Settings_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowDownRight,ArrowUpRight,BarChart3,Calendar,Eye,FileText,Mail,Settings,TrendingUp!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_Eye_FileText_Mail_Settings_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowDownRight,ArrowUpRight,BarChart3,Calendar,Eye,FileText,Mail,Settings,TrendingUp!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction AdminDashboard() {\n    var _stats_applications, _stats_applications1, _stats_newsletter, _stats_applications2, _stats_newsletter1;\n    _s();\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [recentApplications, setRecentApplications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [recentSubscribers, setRecentSubscribers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [recentContacts, setRecentContacts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminDashboard.useEffect\": ()=>{\n            fetchDashboardData();\n        }\n    }[\"AdminDashboard.useEffect\"], []);\n    const fetchDashboardData = async ()=>{\n        try {\n            setLoading(true);\n            // Fetch applications stats\n            const appsResponse = await fetch('/api/applications?limit=5');\n            const appsData = await appsResponse.json();\n            if (appsData.success) {\n                setRecentApplications(appsData.data);\n                setStats((prev)=>({\n                        ...prev,\n                        applications: appsData.stats\n                    }));\n            }\n            // Fetch newsletter stats\n            const newsletterResponse = await fetch('/api/newsletter?limit=5');\n            const newsletterData = await newsletterResponse.json();\n            if (newsletterData.success) {\n                setRecentSubscribers(newsletterData.data);\n                setStats((prev)=>({\n                        ...prev,\n                        newsletter: newsletterData.stats\n                    }));\n            }\n            // Fetch contact inquiries\n            const contactsResponse = await fetch('/api/contact-inquiries?limit=5');\n            const contactsData = await contactsResponse.json();\n            if (contactsData.success) {\n                setRecentContacts(contactsData.data);\n                setStats((prev)=>({\n                        ...prev,\n                        contacts: contactsData.stats\n                    }));\n            }\n        } catch (error) {\n            console.error('Error fetching dashboard data:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const dashboardStats = [\n        {\n            title: 'Total Applications',\n            value: ((_stats_applications = stats.applications) === null || _stats_applications === void 0 ? void 0 : _stats_applications.total) || 0,\n            change: '+12%',\n            changeType: 'increase',\n            icon: _barrel_optimize_names_Activity_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_Eye_FileText_Mail_Settings_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            color: 'text-blue-600',\n            bgColor: 'bg-blue-100',\n            href: '/admin/applications'\n        },\n        {\n            title: 'Pending Review',\n            value: ((_stats_applications1 = stats.applications) === null || _stats_applications1 === void 0 ? void 0 : _stats_applications1.pending) || 0,\n            change: '+5%',\n            changeType: 'increase',\n            icon: _barrel_optimize_names_Activity_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_Eye_FileText_Mail_Settings_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            color: 'text-yellow-600',\n            bgColor: 'bg-yellow-100',\n            href: '/admin/applications?status=pending'\n        },\n        {\n            title: 'Newsletter Subscribers',\n            value: ((_stats_newsletter = stats.newsletter) === null || _stats_newsletter === void 0 ? void 0 : _stats_newsletter.active) || 0,\n            change: '+8%',\n            changeType: 'increase',\n            icon: _barrel_optimize_names_Activity_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_Eye_FileText_Mail_Settings_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            color: 'text-green-600',\n            bgColor: 'bg-green-100',\n            href: '/admin/newsletter'\n        },\n        {\n            title: 'This Month',\n            value: (((_stats_applications2 = stats.applications) === null || _stats_applications2 === void 0 ? void 0 : _stats_applications2.thisMonth) || 0) + (((_stats_newsletter1 = stats.newsletter) === null || _stats_newsletter1 === void 0 ? void 0 : _stats_newsletter1.thisMonth) || 0),\n            change: '+15%',\n            changeType: 'increase',\n            icon: _barrel_optimize_names_Activity_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_Eye_FileText_Mail_Settings_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            color: 'text-purple-600',\n            bgColor: 'bg-purple-100',\n            href: '/admin/analytics'\n        }\n    ];\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-primary\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                lineNumber: 105,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n            lineNumber: 104,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white shadow-sm border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center py-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-gray-900\",\n                                        children: \"Admin Dashboard\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Welcome back! Here's what's happening with your platform.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: fetchDashboardData,\n                                        children: \"Refresh Data\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/admin/settings\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_Eye_FileText_Mail_Settings_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 126,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Settings\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 114,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                lineNumber: 113,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n                        children: dashboardStats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    delay: index * 0.1\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: stat.href,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow cursor-pointer\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: stat.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 149,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-2xl font-bold \".concat(stat.color),\n                                                            children: stat.value\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 150,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center mt-2\",\n                                                            children: [\n                                                                stat.changeType === 'increase' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_Eye_FileText_Mail_Settings_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    className: \"w-4 h-4 text-green-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 153,\n                                                                    columnNumber: 27\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_Eye_FileText_Mail_Settings_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                    className: \"w-4 h-4 text-red-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 155,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm ml-1 \".concat(stat.changeType === 'increase' ? 'text-green-600' : 'text-red-600'),\n                                                                    children: stat.change\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 157,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-500 ml-1\",\n                                                                    children: \"vs last month\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 162,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 151,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 148,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"\".concat(stat.bgColor, \" p-3 rounded-lg\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(stat.icon, {\n                                                        className: \"w-6 h-6 \".concat(stat.color)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 166,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 165,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 15\n                                }, this)\n                            }, stat.title, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    x: -20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                transition: {\n                                    delay: 0.2\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/admin/applications\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow cursor-pointer\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-gray-900\",\n                                                        children: \"Manage Applications\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 185,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_Eye_FileText_Mail_Settings_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        className: \"w-5 h-5 text-blue-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 186,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 mb-4\",\n                                                children: \"Review, approve, and manage student applications\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center text-blue-600 font-medium\",\n                                                children: [\n                                                    \"View Applications\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_Eye_FileText_Mail_Settings_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"w-4 h-4 ml-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 191,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    x: 0\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                transition: {\n                                    delay: 0.3\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/admin/newsletter\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow cursor-pointer\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-gray-900\",\n                                                        children: \"Newsletter Management\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 205,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_Eye_FileText_Mail_Settings_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"w-5 h-5 text-green-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 206,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 mb-4\",\n                                                children: \"Manage subscribers and send email campaigns\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center text-green-600 font-medium\",\n                                                children: [\n                                                    \"Manage Newsletter\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_Eye_FileText_Mail_Settings_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"w-4 h-4 ml-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 211,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 209,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    x: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                transition: {\n                                    delay: 0.4\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-gray-900\",\n                                                    children: \"Analytics\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 224,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_Eye_FileText_Mail_Settings_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"w-5 h-5 text-purple-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 225,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-4\",\n                                            children: \"View detailed analytics and reports\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-purple-600 font-medium\",\n                                            children: [\n                                                \"Coming Soon\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_Eye_FileText_Mail_Settings_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"w-4 h-4 ml-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    delay: 0.5\n                                },\n                                className: \"bg-white rounded-lg shadow\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6 border-b border-gray-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-gray-900\",\n                                                    children: \"Recent Applications\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 247,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/admin/applications\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_Eye_FileText_Mail_Settings_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"w-4 h-4 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 250,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"View All\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 249,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 248,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                recentApplications.slice(0, 5).map((app)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between p-3 bg-gray-50 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-medium text-gray-900\",\n                                                                        children: [\n                                                                            app.firstName,\n                                                                            \" \",\n                                                                            app.lastName\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 261,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: app.preferredUniversity\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 262,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-500\",\n                                                                        children: new Date(app.createdAt).toLocaleDateString()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 263,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 260,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-1 text-xs rounded-full \".concat(app.status === 'PENDING' ? 'bg-yellow-100 text-yellow-800' : app.status === 'APPROVED' ? 'bg-green-100 text-green-800' : app.status === 'UNDER_REVIEW' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'),\n                                                                children: app.status\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 265,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, app.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 259,\n                                                        columnNumber: 19\n                                                    }, this)),\n                                                recentApplications.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-500 text-center py-4\",\n                                                    children: \"No recent applications\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 276,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    delay: 0.6\n                                },\n                                className: \"bg-white rounded-lg shadow\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6 border-b border-gray-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-gray-900\",\n                                                    children: \"Recent Subscribers\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 291,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/admin/newsletter\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_Eye_FileText_Mail_Settings_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"w-4 h-4 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 294,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"View All\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 293,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 292,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                recentSubscribers.slice(0, 5).map((subscriber)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between p-3 bg-gray-50 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-medium text-gray-900\",\n                                                                        children: subscriber.email\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 305,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: [\n                                                                            \"Source: \",\n                                                                            subscriber.source\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 306,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-500\",\n                                                                        children: new Date(subscriber.subscribedAt).toLocaleDateString()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 307,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 304,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-1 text-xs rounded-full \".concat(subscriber.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'),\n                                                                children: subscriber.isActive ? 'Active' : 'Inactive'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 309,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, subscriber.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 303,\n                                                        columnNumber: 19\n                                                    }, this)),\n                                                recentSubscribers.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-500 text-center py-4\",\n                                                    children: \"No recent subscribers\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 317,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 301,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 300,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 283,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 237,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                lineNumber: 135,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n        lineNumber: 111,\n        columnNumber: 5\n    }, this);\n}\n_s(AdminDashboard, \"0C9CxTReBhGJPdl5JqGXqvuQhOU=\");\n_c = AdminDashboard;\nvar _c;\n$RefreshReg$(_c, \"AdminDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/dashboard/page.tsx\n"));

/***/ })

});