'use client'

import { motion } from 'framer-motion'
import { useInView } from 'react-intersection-observer'
import { MapPin, Phone, Mail, Clock, Globe, MessageCircle } from 'lucide-react'
import { Button } from '@/components/ui/button'

export function ContactInfoSection() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1
  })

  const offices = [
    {
      name: 'Main Office - Nicosia',
      address: 'Foreingate Group Headquarters, Nicosia, Northern Cyprus',
      phone: '+90 ************',
      email: '<EMAIL>',
      hours: 'Mon-Fri: 9AM-6PM, Sat: 10AM-4PM',
      isPrimary: true
    },
    {
      name: 'Branch Office - Famagusta',
      address: 'Near EMU Campus, Famagusta, Northern Cyprus',
      phone: '+90 ************',
      email: '<EMAIL>',
      hours: 'Mon-Fri: 9AM-5PM, Sat: 10AM-2PM',
      isPrimary: false
    },
    {
      name: 'Representative Office - Istanbul',
      address: 'Levent Business District, Istanbul, Turkey',
      phone: '+90 ************',
      email: '<EMAIL>',
      hours: 'Mon-Fri: 9AM-6PM',
      isPrimary: false
    }
  ]

  const contactMethods = [
    {
      icon: Phone,
      title: 'Phone Support',
      description: 'Speak directly with our counselors',
      contact: '+90 ************',
      availability: '24/7 Emergency Line Available',
      action: 'Call Now'
    },
    {
      icon: Mail,
      title: 'Email Support',
      description: 'Send us detailed inquiries',
      contact: '<EMAIL>',
      availability: 'Response within 24 hours',
      action: 'Send Email'
    },
    {
      icon: MessageCircle,
      title: 'WhatsApp',
      description: 'Quick messages and updates',
      contact: '+90 ************',
      availability: 'Instant messaging support',
      action: 'Chat Now'
    },
    {
      icon: Globe,
      title: 'Online Consultation',
      description: 'Video calls with our experts',
      contact: 'Book via website',
      availability: 'Flexible scheduling',
      action: 'Book Session'
    }
  ]

  return (
    <section ref={ref} className="section-padding">
      <div className="container">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
          className="text-center max-w-3xl mx-auto mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Multiple Ways to{' '}
            <span className="gradient-text">Reach Us</span>
          </h2>
          <p className="text-xl text-muted-foreground">
            Choose the most convenient way to connect with our team. We're here to help you succeed.
          </p>
        </motion.div>

        {/* Contact Methods Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
          {contactMethods.map((method, index) => (
            <motion.div
              key={method.title}
              initial={{ opacity: 0, y: 30 }}
              animate={inView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              className="bg-background rounded-xl p-6 shadow-sm border hover:shadow-lg transition-all duration-300 text-center group"
            >
              <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-4 group-hover:bg-primary/20 transition-colors">
                <method.icon className="w-6 h-6 text-primary" />
              </div>
              <h3 className="text-lg font-semibold mb-2">{method.title}</h3>
              <p className="text-muted-foreground text-sm mb-3">{method.description}</p>
              <div className="font-medium text-primary mb-2">{method.contact}</div>
              <div className="text-xs text-muted-foreground mb-4">{method.availability}</div>
              <Button size="sm" variant="outline" className="w-full">
                {method.action}
              </Button>
            </motion.div>
          ))}
        </div>

        {/* Office Locations */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 0.4 }}
          className="mb-16"
        >
          <h3 className="text-2xl font-bold text-center mb-8">Our Office Locations</h3>
          <div className="grid lg:grid-cols-3 gap-6">
            {offices.map((office, index) => (
              <motion.div
                key={office.name}
                initial={{ opacity: 0, y: 20 }}
                animate={inView ? { opacity: 1, y: 0 } : {}}
                transition={{ duration: 0.6, delay: 0.6 + index * 0.1 }}
                className={`bg-background rounded-xl p-6 shadow-sm border ${
                  office.isPrimary ? 'ring-2 ring-primary/20' : ''
                }`}
              >
                {office.isPrimary && (
                  <div className="inline-flex items-center bg-primary text-primary-foreground px-2 py-1 rounded-full text-xs font-medium mb-4">
                    Main Office
                  </div>
                )}
                <h4 className="text-lg font-semibold mb-4">{office.name}</h4>

                <div className="space-y-3">
                  <div className="flex items-start space-x-3">
                    <MapPin className="w-4 h-4 text-primary mt-1 flex-shrink-0" />
                    <span className="text-sm text-muted-foreground">{office.address}</span>
                  </div>

                  <div className="flex items-center space-x-3">
                    <Phone className="w-4 h-4 text-primary flex-shrink-0" />
                    <span className="text-sm">{office.phone}</span>
                  </div>

                  <div className="flex items-center space-x-3">
                    <Mail className="w-4 h-4 text-primary flex-shrink-0" />
                    <span className="text-sm">{office.email}</span>
                  </div>

                  <div className="flex items-center space-x-3">
                    <Clock className="w-4 h-4 text-primary flex-shrink-0" />
                    <span className="text-sm text-muted-foreground">{office.hours}</span>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Map Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 0.8 }}
          className="bg-muted/30 rounded-2xl p-8"
        >
          <h3 className="text-2xl font-bold text-center mb-6">Find Us on the Map</h3>
          <div className="aspect-video bg-muted rounded-xl flex items-center justify-center">
            <div className="text-center">
              <MapPin className="w-12 h-12 text-primary mx-auto mb-4" />
              <p className="text-muted-foreground">
                Interactive map will be integrated here showing all office locations
              </p>
              <Button variant="outline" className="mt-4">
                Open in Google Maps
              </Button>
            </div>
          </div>
        </motion.div>

        {/* Emergency Contact */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 1 }}
          className="mt-16 text-center bg-gradient-to-r from-red-50 to-orange-50 dark:from-red-950/20 dark:to-orange-950/20 rounded-2xl p-8 border border-red-200 dark:border-red-800"
        >
          <h3 className="text-2xl font-bold mb-4 text-red-800 dark:text-red-200">
            Emergency Support
          </h3>
          <p className="text-red-700 dark:text-red-300 mb-6 max-w-2xl mx-auto">
            For urgent matters requiring immediate assistance (current students only),
            our emergency hotline is available 24/7.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button variant="destructive" size="lg">
              <Phone className="w-4 h-4 mr-2" />
              Emergency Hotline: +90 ************
            </Button>
            <Button variant="outline" size="lg">
              <MessageCircle className="w-4 h-4 mr-2" />
              Emergency WhatsApp
            </Button>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
