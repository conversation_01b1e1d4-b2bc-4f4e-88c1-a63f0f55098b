'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { useState } from 'react'
import { useInView } from 'react-intersection-observer'
import { ChevronDown, ChevronUp, BookOpen, FileText, Home, DollarSign, HelpCircle, Globe } from 'lucide-react'

const faqData = {
  admissions: {
    icon: BookOpen,
    title: 'Admissions & Applications',
    questions: [
      {
        q: 'What are the admission requirements for international students?',
        a: 'International students need a high school diploma or equivalent, English proficiency test scores (IELTS/TOEFL), passport copy, and academic transcripts. Specific requirements may vary by program and university.'
      },
      {
        q: 'When is the application deadline?',
        a: 'Most universities have multiple intake periods: Fall (September), Spring (February), and Summer (June). Application deadlines are typically 2-3 months before each intake period.'
      },
      {
        q: 'Can I apply to multiple universities?',
        a: 'Yes, you can apply to multiple universities through our platform. We recommend applying to 3-5 universities to increase your chances of acceptance.'
      },
      {
        q: 'How long does the application process take?',
        a: 'The complete application process typically takes 4-6 weeks from submission to final acceptance, including document verification and university review.'
      }
    ]
  },
  visa: {
    icon: FileText,
    title: 'Visa & Documentation',
    questions: [
      {
        q: 'Do I need a visa to study in Northern Cyprus?',
        a: 'Most international students need a student visa. EU citizens may have different requirements. We provide complete visa support and guidance throughout the process.'
      },
      {
        q: 'What documents are required for a student visa?',
        a: 'Required documents include acceptance letter, passport, financial proof, health insurance, medical certificate, and police clearance certificate.'
      },
      {
        q: 'How long does visa processing take?',
        a: 'Student visa processing typically takes 2-4 weeks after submitting all required documents. We recommend applying at least 6 weeks before your intended travel date.'
      }
    ]
  },
  housing: {
    icon: Home,
    title: 'Housing & Accommodation',
    questions: [
      {
        q: 'What housing options are available for students?',
        a: 'Students can choose from university dormitories, private apartments, shared housing, or homestay programs. We help you find the best option based on your budget and preferences.'
      },
      {
        q: 'How much does student accommodation cost?',
        a: 'Housing costs range from $200-600 per month depending on the type and location. University dormitories are typically the most affordable option.'
      },
      {
        q: 'Is accommodation guaranteed for international students?',
        a: 'Most universities guarantee accommodation for first-year international students. We recommend applying for housing early to secure your preferred option.'
      }
    ]
  },
  costs: {
    icon: DollarSign,
    title: 'Costs & Financial Aid',
    questions: [
      {
        q: 'What are the tuition fees for international students?',
        a: 'Tuition fees range from $3,500 to $12,000 per year depending on the program and university. Engineering and medical programs typically have higher fees.'
      },
      {
        q: 'Are scholarships available for international students?',
        a: 'Yes, many universities offer merit-based scholarships ranging from 25% to 100% tuition coverage. We help you identify and apply for available scholarships.'
      },
      {
        q: 'What are the living costs in Northern Cyprus?',
        a: 'Monthly living costs range from $400-800 including accommodation, food, transportation, and personal expenses. Northern Cyprus is generally more affordable than many European countries.'
      }
    ]
  },
  general: {
    icon: HelpCircle,
    title: 'General Information',
    questions: [
      {
        q: 'What language are courses taught in?',
        a: 'Most programs are taught in English, with some programs available in Turkish. All universities require English proficiency for English-taught programs.'
      },
      {
        q: 'Is Northern Cyprus safe for international students?',
        a: 'Yes, Northern Cyprus is considered very safe with low crime rates. Universities provide additional security measures and support for international students.'
      },
      {
        q: 'Can I work while studying?',
        a: 'International students can work part-time (up to 20 hours per week) with proper permits. Many students find opportunities in tutoring, hospitality, or campus jobs.'
      }
    ]
  },
  support: {
    icon: Globe,
    title: 'Student Support Services',
    questions: [
      {
        q: 'What support services do you provide?',
        a: 'We provide comprehensive support including application assistance, visa guidance, accommodation help, airport pickup, academic support, and ongoing counseling throughout your studies.'
      },
      {
        q: 'Do you provide support after arrival?',
        a: 'Yes, our support continues after your arrival with orientation programs, academic counseling, career guidance, and assistance with any challenges you may face.'
      },
      {
        q: 'How can I contact support if I need help?',
        a: 'You can reach our support team 24/7 through phone, email, WhatsApp, or our online portal. We also have local representatives in Northern Cyprus for immediate assistance.'
      }
    ]
  }
}

export function FAQCategoriesSection() {
  const [activeCategory, setActiveCategory] = useState('admissions')
  const [expandedQuestion, setExpandedQuestion] = useState<number | null>(null)

  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1
  })

  const toggleQuestion = (index: number) => {
    setExpandedQuestion(expandedQuestion === index ? null : index)
  }

  return (
    <section ref={ref} className="section-padding bg-muted/30">
      <div className="container">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
          className="text-center max-w-3xl mx-auto mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Browse Questions by{' '}
            <span className="gradient-text">Category</span>
          </h2>
          <p className="text-xl text-muted-foreground">
            Find detailed answers organized by topic to help you make informed decisions
          </p>
        </motion.div>

        <div className="max-w-6xl mx-auto">
          <div className="grid lg:grid-cols-4 gap-8">
            {/* Category Navigation */}
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              animate={inView ? { opacity: 1, x: 0 } : {}}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="lg:col-span-1"
            >
              <div className="bg-background rounded-xl p-6 shadow-sm border sticky top-8">
                <h3 className="font-semibold mb-4">Categories</h3>
                <nav className="space-y-2">
                  {Object.entries(faqData).map(([key, category]) => (
                    <button
                      key={key}
                      onClick={() => setActiveCategory(key)}
                      className={`w-full flex items-center space-x-3 p-3 rounded-lg text-left transition-all duration-200 ${
                        activeCategory === key
                          ? 'bg-primary text-primary-foreground'
                          : 'hover:bg-muted'
                      }`}
                    >
                      <category.icon className="w-5 h-5" />
                      <div>
                        <div className="font-medium text-sm">{category.title}</div>
                        <div className={`text-xs ${
                          activeCategory === key ? 'text-primary-foreground/80' : 'text-muted-foreground'
                        }`}>
                          {category.questions.length} questions
                        </div>
                      </div>
                    </button>
                  ))}
                </nav>
              </div>
            </motion.div>

            {/* FAQ Content */}
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              animate={inView ? { opacity: 1, x: 0 } : {}}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="lg:col-span-3"
            >
              <div className="bg-background rounded-xl p-8 shadow-sm border">
                {/* Category Header */}
                <div className="flex items-center space-x-3 mb-8">
                  {React.createElement(faqData[activeCategory as keyof typeof faqData].icon, {
                    className: "w-8 h-8 text-primary"
                  })}
                  <div>
                    <h3 className="text-2xl font-bold">
                      {faqData[activeCategory as keyof typeof faqData].title}
                    </h3>
                    <p className="text-muted-foreground">
                      {faqData[activeCategory as keyof typeof faqData].questions.length} frequently asked questions
                    </p>
                  </div>
                </div>

                {/* Questions */}
                <div className="space-y-4">
                  {faqData[activeCategory as keyof typeof faqData].questions.map((faq, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5, delay: index * 0.1 }}
                      className="border rounded-lg overflow-hidden"
                    >
                      <button
                        onClick={() => toggleQuestion(index)}
                        className="w-full flex items-center justify-between p-4 text-left hover:bg-muted/50 transition-colors"
                      >
                        <span className="font-medium pr-4">{faq.q}</span>
                        {expandedQuestion === index ? (
                          <ChevronUp className="w-5 h-5 text-primary flex-shrink-0" />
                        ) : (
                          <ChevronDown className="w-5 h-5 text-muted-foreground flex-shrink-0" />
                        )}
                      </button>

                      {expandedQuestion === index && (
                        <motion.div
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: 'auto' }}
                          exit={{ opacity: 0, height: 0 }}
                          transition={{ duration: 0.3 }}
                          className="px-4 pb-4 border-t bg-muted/20"
                        >
                          <p className="text-muted-foreground pt-4">{faq.a}</p>
                        </motion.div>
                      )}
                    </motion.div>
                  ))}
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </section>
  )
}
