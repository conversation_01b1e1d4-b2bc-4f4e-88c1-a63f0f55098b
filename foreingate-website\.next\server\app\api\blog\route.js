/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/blog/route";
exports.ids = ["app/api/blog/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fblog%2Froute&page=%2Fapi%2Fblog%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fblog%2Froute.ts&appDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fblog%2Froute&page=%2Fapi%2Fblog%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fblog%2Froute.ts&appDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Nidhal_Documents_augment_projects_foreingate_groupe_foreingate_website_src_app_api_blog_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/blog/route.ts */ \"(rsc)/./src/app/api/blog/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/blog/route\",\n        pathname: \"/api/blog\",\n        filename: \"route\",\n        bundlePath: \"app/api/blog/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\api\\\\blog\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Nidhal_Documents_augment_projects_foreingate_groupe_foreingate_website_src_app_api_blog_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fblog%2Froute&page=%2Fapi%2Fblog%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fblog%2Froute.ts&appDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/blog/route.ts":
/*!***********************************!*\
  !*** ./src/app/api/blog/route.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fs/promises */ \"fs/promises\");\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fs_promises__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nconst DATA_DIR = path__WEBPACK_IMPORTED_MODULE_3___default().join(process.cwd(), 'data');\nconst BLOG_FILE = path__WEBPACK_IMPORTED_MODULE_3___default().join(DATA_DIR, 'blog-posts.json');\n// Ensure data directory exists\nasync function ensureDataDir() {\n    if (!(0,fs__WEBPACK_IMPORTED_MODULE_2__.existsSync)(DATA_DIR)) {\n        await (0,fs_promises__WEBPACK_IMPORTED_MODULE_1__.mkdir)(DATA_DIR, {\n            recursive: true\n        });\n    }\n}\n// Read blog posts from file\nasync function readBlogPosts() {\n    try {\n        await ensureDataDir();\n        if (!(0,fs__WEBPACK_IMPORTED_MODULE_2__.existsSync)(BLOG_FILE)) {\n            // Create initial data if file doesn't exist\n            const initialData = [\n                {\n                    id: '1',\n                    title: 'Complete Guide to Studying in Northern Cyprus 2024',\n                    slug: 'complete-guide-studying-northern-cyprus-2024',\n                    excerpt: 'Everything you need to know about universities, programs, costs, and student life in Northern Cyprus. Updated for 2024 admissions.',\n                    content: 'Northern Cyprus has emerged as one of the most attractive destinations for international students seeking quality education at affordable costs...',\n                    author: 'Foreingate Team',\n                    authorImage: '/images/authors/foreingate-team.jpg',\n                    image: '/images/blog/northern-cyprus-guide-2024.jpg',\n                    category: 'Study Guide',\n                    tags: [\n                        'Northern Cyprus',\n                        'Study Abroad',\n                        'Universities',\n                        'Guide'\n                    ],\n                    publishedAt: new Date(Date.now() - 86400000 * 2).toISOString(),\n                    readTime: 8,\n                    featured: true,\n                    status: 'published',\n                    views: 1250,\n                    createdAt: new Date(Date.now() - 86400000 * 5).toISOString(),\n                    updatedAt: new Date(Date.now() - 86400000 * 2).toISOString()\n                },\n                {\n                    id: '2',\n                    title: 'Top 10 Universities in Northern Cyprus for International Students',\n                    slug: 'top-10-universities-northern-cyprus-international-students',\n                    excerpt: 'Discover the best universities in Northern Cyprus offering world-class education, modern facilities, and international recognition.',\n                    content: 'Choosing the right university is crucial for your academic and career success. Here are the top 10 universities in Northern Cyprus...',\n                    author: 'Sarah Johnson',\n                    authorImage: '/images/authors/sarah-johnson.jpg',\n                    image: '/images/blog/top-universities-northern-cyprus.jpg',\n                    category: 'Universities',\n                    tags: [\n                        'Universities',\n                        'Rankings',\n                        'Northern Cyprus',\n                        'Education'\n                    ],\n                    publishedAt: new Date(Date.now() - 86400000 * 5).toISOString(),\n                    readTime: 6,\n                    featured: false,\n                    status: 'published',\n                    views: 890,\n                    createdAt: new Date(Date.now() - 86400000 * 7).toISOString(),\n                    updatedAt: new Date(Date.now() - 86400000 * 5).toISOString()\n                },\n                {\n                    id: '3',\n                    title: 'Student Visa Requirements for Northern Cyprus: Complete Checklist',\n                    slug: 'student-visa-requirements-northern-cyprus-checklist',\n                    excerpt: 'Step-by-step guide to obtaining a student visa for Northern Cyprus, including required documents and application process.',\n                    content: 'Getting a student visa for Northern Cyprus is straightforward if you have all the required documents...',\n                    author: 'Michael Chen',\n                    authorImage: '/images/authors/michael-chen.jpg',\n                    image: '/images/blog/student-visa-northern-cyprus.jpg',\n                    category: 'Visa Guide',\n                    tags: [\n                        'Visa',\n                        'Student Visa',\n                        'Northern Cyprus',\n                        'Documentation'\n                    ],\n                    publishedAt: new Date(Date.now() - 86400000 * 7).toISOString(),\n                    readTime: 5,\n                    featured: false,\n                    status: 'published',\n                    views: 654,\n                    createdAt: new Date(Date.now() - 86400000 * 10).toISOString(),\n                    updatedAt: new Date(Date.now() - 86400000 * 7).toISOString()\n                },\n                {\n                    id: '4',\n                    title: 'Cost of Living for Students in Northern Cyprus',\n                    slug: 'cost-of-living-students-northern-cyprus',\n                    excerpt: 'Detailed breakdown of living costs including accommodation, food, transportation, and entertainment for students in Northern Cyprus.',\n                    content: 'Northern Cyprus offers one of the most affordable study destinations in Europe. Here\\'s a detailed breakdown of costs...',\n                    author: 'Emma Davis',\n                    authorImage: '/images/authors/emma-davis.jpg',\n                    image: '/images/blog/cost-of-living-northern-cyprus.jpg',\n                    category: 'Student Life',\n                    tags: [\n                        'Cost of Living',\n                        'Budget',\n                        'Student Life',\n                        'Northern Cyprus'\n                    ],\n                    publishedAt: new Date(Date.now() - 86400000 * 10).toISOString(),\n                    readTime: 7,\n                    featured: false,\n                    status: 'published',\n                    views: 432,\n                    createdAt: new Date(Date.now() - 86400000 * 12).toISOString(),\n                    updatedAt: new Date(Date.now() - 86400000 * 10).toISOString()\n                },\n                {\n                    id: '5',\n                    title: 'Engineering Programs in Northern Cyprus: Your Gateway to Success',\n                    slug: 'engineering-programs-northern-cyprus-gateway-success',\n                    excerpt: 'Explore the best engineering programs in Northern Cyprus with ABET accreditation and excellent career prospects.',\n                    content: 'Engineering education in Northern Cyprus has gained international recognition for its quality and innovation...',\n                    author: 'Dr. Ahmed Yilmaz',\n                    authorImage: '/images/authors/ahmed-yilmaz.jpg',\n                    image: '/images/blog/engineering-programs-northern-cyprus.jpg',\n                    category: 'Programs',\n                    tags: [\n                        'Engineering',\n                        'Programs',\n                        'ABET',\n                        'Career'\n                    ],\n                    publishedAt: new Date(Date.now() - 86400000 * 14).toISOString(),\n                    readTime: 9,\n                    featured: false,\n                    status: 'published',\n                    views: 567,\n                    createdAt: new Date(Date.now() - 86400000 * 16).toISOString(),\n                    updatedAt: new Date(Date.now() - 86400000 * 14).toISOString()\n                }\n            ];\n            await (0,fs_promises__WEBPACK_IMPORTED_MODULE_1__.writeFile)(BLOG_FILE, JSON.stringify(initialData, null, 2));\n            return initialData;\n        }\n        const data = await (0,fs_promises__WEBPACK_IMPORTED_MODULE_1__.readFile)(BLOG_FILE, 'utf-8');\n        return JSON.parse(data);\n    } catch (error) {\n        console.error('Error reading blog posts:', error);\n        return [];\n    }\n}\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const category = searchParams.get('category');\n        const tags = searchParams.get('tags')?.split(',');\n        const search = searchParams.get('search');\n        const featured = searchParams.get('featured') === 'true';\n        const page = parseInt(searchParams.get('page') || '1');\n        const limit = parseInt(searchParams.get('limit') || '10');\n        let posts = await readBlogPosts();\n        // Apply filters\n        if (category) {\n            posts = posts.filter((post)=>post.category.toLowerCase() === category.toLowerCase());\n        }\n        if (tags && tags.length > 0) {\n            posts = posts.filter((post)=>tags.some((tag)=>post.tags.includes(tag)));\n        }\n        if (featured) {\n            posts = posts.filter((post)=>post.featured === true);\n        }\n        if (search) {\n            posts = posts.filter((post)=>post.title.toLowerCase().includes(search.toLowerCase()) || post.excerpt.toLowerCase().includes(search.toLowerCase()) || post.content.toLowerCase().includes(search.toLowerCase()));\n        }\n        // Sort by published date (newest first)\n        posts.sort((a, b)=>new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime());\n        // Pagination\n        const total = posts.length;\n        const startIndex = (page - 1) * limit;\n        const endIndex = startIndex + limit;\n        const paginatedPosts = posts.slice(startIndex, endIndex);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: paginatedPosts,\n            pagination: {\n                page,\n                limit,\n                total,\n                totalPages: Math.ceil(total / limit),\n                hasNext: endIndex < total,\n                hasPrev: page > 1\n            }\n        });\n    } catch (error) {\n        console.error('Blog API error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Failed to fetch blog posts'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/blog/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "fs/promises":
/*!******************************!*\
  !*** external "fs/promises" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("fs/promises");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fblog%2Froute&page=%2Fapi%2Fblog%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fblog%2Froute.ts&appDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();