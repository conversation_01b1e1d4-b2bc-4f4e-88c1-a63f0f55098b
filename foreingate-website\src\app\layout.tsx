import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Foreingate Group - Study in Northern Cyprus",
  description: "Your trusted partner for studying in Northern Cyprus. We help students achieve their academic dreams with comprehensive support services including university admissions, visa support, and student housing.",
  keywords: ["Northern Cyprus", "University", "Study Abroad", "Student Services", "Education", "Visa Support"],
  authors: [{ name: "Foreingate Group" }],
  creator: "Foreingate Group",
  publisher: "Foreingate Group",
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://foreingate.com",
    title: "Foreingate Group - Study in Northern Cyprus",
    description: "Your trusted partner for studying in Northern Cyprus. We help students achieve their academic dreams with comprehensive support services.",
    siteName: "Foreingate Group",
  },
  twitter: {
    card: "summary_large_image",
    title: "Foreingate Group - Study in Northern Cyprus",
    description: "Your trusted partner for studying in Northern Cyprus. We help students achieve their academic dreams with comprehensive support services.",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html suppressHydrationWarning>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased min-h-screen flex flex-col`}
      >
        {children}
      </body>
    </html>
  );
}
