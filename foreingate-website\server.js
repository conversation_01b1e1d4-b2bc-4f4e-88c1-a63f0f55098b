const https = require('https')
const { parse } = require('url')
const next = require('next')
const fs = require('fs')
const path = require('path')

const dev = process.env.NODE_ENV !== 'production'
const hostname = 'localhost'
const port = process.env.HTTPS_PORT || 3443

// Initialize Next.js app
const app = next({ dev, hostname, port })
const handle = app.getRequestHandler()

// SSL certificate paths
const certsDir = path.join(__dirname, 'certificates')
const keyPath = path.join(certsDir, 'localhost.key')
const certPath = path.join(certsDir, 'localhost.crt')

// Check if certificates exist
if (!fs.existsSync(keyPath) || !fs.existsSync(certPath)) {
  console.error('❌ SSL certificates not found!')
  console.log('🔧 Please run: npm run generate-ssl')
  process.exit(1)
}

// HTTPS options
const httpsOptions = {
  key: fs.readFileSync(keyPath),
  cert: fs.readFileSync(certPath),
}

app.prepare().then(() => {
  https.createServer(httpsOptions, async (req, res) => {
    try {
      // Security headers
      res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload')
      res.setHeader('X-Content-Type-Options', 'nosniff')
      res.setHeader('X-Frame-Options', 'DENY')
      res.setHeader('X-XSS-Protection', '1; mode=block')
      res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin')
      res.setHeader('Permissions-Policy', 'camera=(), microphone=(), geolocation=()')
      res.setHeader('Content-Security-Policy', 
        "default-src 'self'; " +
        "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://vercel.live; " +
        "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; " +
        "font-src 'self' https://fonts.gstatic.com; " +
        "img-src 'self' data: https: blob:; " +
        "connect-src 'self' https: wss: ws:; " +
        "frame-src 'self' https:; " +
        "object-src 'none'; " +
        "base-uri 'self';"
      )

      // Parse the URL
      const parsedUrl = parse(req.url, true)
      await handle(req, res, parsedUrl)
    } catch (err) {
      console.error('Error occurred handling', req.url, err)
      res.statusCode = 500
      res.end('internal server error')
    }
  })
    .once('error', (err) => {
      console.error('HTTPS Server error:', err)
      process.exit(1)
    })
    .listen(port, () => {
      console.log('')
      console.log('🔒 HTTPS Server running securely!')
      console.log(`📍 Local:    https://${hostname}:${port}`)
      console.log(`🌐 Network:  https://[your-ip]:${port}`)
      console.log('')
      console.log('🛡️  Security features enabled:')
      console.log('   ✅ SSL/TLS encryption')
      console.log('   ✅ HSTS (HTTP Strict Transport Security)')
      console.log('   ✅ Content Security Policy')
      console.log('   ✅ XSS Protection')
      console.log('   ✅ MIME type sniffing protection')
      console.log('   ✅ Clickjacking protection')
      console.log('')
      console.log('⚠️  If you see certificate warnings, install the certificate:')
      console.log(`   📁 Certificate: ${certPath}`)
      console.log('')
    })
})
