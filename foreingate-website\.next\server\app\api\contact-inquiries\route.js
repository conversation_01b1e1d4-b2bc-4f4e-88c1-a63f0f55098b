/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/contact-inquiries/route";
exports.ids = ["app/api/contact-inquiries/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcontact-inquiries%2Froute&page=%2Fapi%2Fcontact-inquiries%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcontact-inquiries%2Froute.ts&appDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcontact-inquiries%2Froute&page=%2Fapi%2Fcontact-inquiries%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcontact-inquiries%2Froute.ts&appDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Nidhal_Documents_augment_projects_foreingate_groupe_foreingate_website_src_app_api_contact_inquiries_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/contact-inquiries/route.ts */ \"(rsc)/./src/app/api/contact-inquiries/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/contact-inquiries/route\",\n        pathname: \"/api/contact-inquiries\",\n        filename: \"route\",\n        bundlePath: \"app/api/contact-inquiries/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\api\\\\contact-inquiries\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Nidhal_Documents_augment_projects_foreingate_groupe_foreingate_website_src_app_api_contact_inquiries_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZhcGklMkZjb250YWN0LWlucXVpcmllcyUyRnJvdXRlJnBhZ2U9JTJGYXBpJTJGY29udGFjdC1pbnF1aXJpZXMlMkZyb3V0ZSZhcHBQYXRocz0mcGFnZVBhdGg9cHJpdmF0ZS1uZXh0LWFwcC1kaXIlMkZhcGklMkZjb250YWN0LWlucXVpcmllcyUyRnJvdXRlLnRzJmFwcERpcj1DJTNBJTVDVXNlcnMlNUNOaWRoYWwlNUNEb2N1bWVudHMlNUNhdWdtZW50LXByb2plY3RzJTVDZm9yZWluZ2F0ZV9ncm91cGUlNUNmb3JlaW5nYXRlLXdlYnNpdGUlNUNzcmMlNUNhcHAmcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyZyb290RGlyPUMlM0ElNUNVc2VycyU1Q05pZGhhbCU1Q0RvY3VtZW50cyU1Q2F1Z21lbnQtcHJvamVjdHMlNUNmb3JlaW5nYXRlX2dyb3VwZSU1Q2ZvcmVpbmdhdGUtd2Vic2l0ZSZpc0Rldj10cnVlJnRzY29uZmlnUGF0aD10c2NvbmZpZy5qc29uJmJhc2VQYXRoPSZhc3NldFByZWZpeD0mbmV4dENvbmZpZ091dHB1dD0mcHJlZmVycmVkUmVnaW9uPSZtaWRkbGV3YXJlQ29uZmlnPWUzMCUzRCEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFBK0Y7QUFDdkM7QUFDcUI7QUFDaUY7QUFDOUo7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLHlHQUFtQjtBQUMzQztBQUNBLGNBQWMsa0VBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLFlBQVk7QUFDWixDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0EsUUFBUSxzREFBc0Q7QUFDOUQ7QUFDQSxXQUFXLDRFQUFXO0FBQ3RCO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDMEY7O0FBRTFGIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQXBwUm91dGVSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLW1vZHVsZXMvYXBwLXJvdXRlL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUta2luZFwiO1xuaW1wb3J0IHsgcGF0Y2hGZXRjaCBhcyBfcGF0Y2hGZXRjaCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2xpYi9wYXRjaC1mZXRjaFwiO1xuaW1wb3J0ICogYXMgdXNlcmxhbmQgZnJvbSBcIkM6XFxcXFVzZXJzXFxcXE5pZGhhbFxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxmb3JlaW5nYXRlX2dyb3VwZVxcXFxmb3JlaW5nYXRlLXdlYnNpdGVcXFxcc3JjXFxcXGFwcFxcXFxhcGlcXFxcY29udGFjdC1pbnF1aXJpZXNcXFxccm91dGUudHNcIjtcbi8vIFdlIGluamVjdCB0aGUgbmV4dENvbmZpZ091dHB1dCBoZXJlIHNvIHRoYXQgd2UgY2FuIHVzZSB0aGVtIGluIHRoZSByb3V0ZVxuLy8gbW9kdWxlLlxuY29uc3QgbmV4dENvbmZpZ091dHB1dCA9IFwiXCJcbmNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IEFwcFJvdXRlUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLkFQUF9ST1VURSxcbiAgICAgICAgcGFnZTogXCIvYXBpL2NvbnRhY3QtaW5xdWlyaWVzL3JvdXRlXCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9hcGkvY29udGFjdC1pbnF1aXJpZXNcIixcbiAgICAgICAgZmlsZW5hbWU6IFwicm91dGVcIixcbiAgICAgICAgYnVuZGxlUGF0aDogXCJhcHAvYXBpL2NvbnRhY3QtaW5xdWlyaWVzL3JvdXRlXCJcbiAgICB9LFxuICAgIHJlc29sdmVkUGFnZVBhdGg6IFwiQzpcXFxcVXNlcnNcXFxcTmlkaGFsXFxcXERvY3VtZW50c1xcXFxhdWdtZW50LXByb2plY3RzXFxcXGZvcmVpbmdhdGVfZ3JvdXBlXFxcXGZvcmVpbmdhdGUtd2Vic2l0ZVxcXFxzcmNcXFxcYXBwXFxcXGFwaVxcXFxjb250YWN0LWlucXVpcmllc1xcXFxyb3V0ZS50c1wiLFxuICAgIG5leHRDb25maWdPdXRwdXQsXG4gICAgdXNlcmxhbmRcbn0pO1xuLy8gUHVsbCBvdXQgdGhlIGV4cG9ydHMgdGhhdCB3ZSBuZWVkIHRvIGV4cG9zZSBmcm9tIHRoZSBtb2R1bGUuIFRoaXMgc2hvdWxkXG4vLyBiZSBlbGltaW5hdGVkIHdoZW4gd2UndmUgbW92ZWQgdGhlIG90aGVyIHJvdXRlcyB0byB0aGUgbmV3IGZvcm1hdC4gVGhlc2Vcbi8vIGFyZSB1c2VkIHRvIGhvb2sgaW50byB0aGUgcm91dGUuXG5jb25zdCB7IHdvcmtBc3luY1N0b3JhZ2UsIHdvcmtVbml0QXN5bmNTdG9yYWdlLCBzZXJ2ZXJIb29rcyB9ID0gcm91dGVNb2R1bGU7XG5mdW5jdGlvbiBwYXRjaEZldGNoKCkge1xuICAgIHJldHVybiBfcGF0Y2hGZXRjaCh7XG4gICAgICAgIHdvcmtBc3luY1N0b3JhZ2UsXG4gICAgICAgIHdvcmtVbml0QXN5bmNTdG9yYWdlXG4gICAgfSk7XG59XG5leHBvcnQgeyByb3V0ZU1vZHVsZSwgd29ya0FzeW5jU3RvcmFnZSwgd29ya1VuaXRBc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzLCBwYXRjaEZldGNoLCAgfTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXBwLXJvdXRlLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcontact-inquiries%2Froute&page=%2Fapi%2Fcontact-inquiries%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcontact-inquiries%2Froute.ts&appDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/contact-inquiries/route.ts":
/*!************************************************!*\
  !*** ./src/app/api/contact-inquiries/route.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fs/promises */ \"fs/promises\");\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fs_promises__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nconst DATA_DIR = path__WEBPACK_IMPORTED_MODULE_3___default().join(process.cwd(), 'data');\nconst CONTACT_FILE = path__WEBPACK_IMPORTED_MODULE_3___default().join(DATA_DIR, 'contact-inquiries.json');\n// Ensure data directory exists\nasync function ensureDataDir() {\n    if (!(0,fs__WEBPACK_IMPORTED_MODULE_2__.existsSync)(DATA_DIR)) {\n        await (0,fs_promises__WEBPACK_IMPORTED_MODULE_1__.mkdir)(DATA_DIR, {\n            recursive: true\n        });\n    }\n}\n// Read contact inquiries from file\nasync function readContactInquiries() {\n    try {\n        await ensureDataDir();\n        if (!(0,fs__WEBPACK_IMPORTED_MODULE_2__.existsSync)(CONTACT_FILE)) {\n            return [];\n        }\n        const data = await (0,fs_promises__WEBPACK_IMPORTED_MODULE_1__.readFile)(CONTACT_FILE, 'utf-8');\n        return JSON.parse(data);\n    } catch (error) {\n        console.error('Error reading contact inquiries:', error);\n        return [];\n    }\n}\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const status = searchParams.get('status') // 'new', 'in_progress', 'responded', 'closed'\n        ;\n        const search = searchParams.get('search');\n        const page = parseInt(searchParams.get('page') || '1');\n        const limit = parseInt(searchParams.get('limit') || '10');\n        const sortBy = searchParams.get('sortBy') || 'createdAt';\n        const sortOrder = searchParams.get('sortOrder') || 'desc';\n        let inquiries = await readContactInquiries();\n        // Apply filters\n        if (status && status !== 'all') {\n            inquiries = inquiries.filter((inquiry)=>inquiry.status?.toLowerCase() === status.toLowerCase());\n        }\n        if (search) {\n            inquiries = inquiries.filter((inquiry)=>inquiry.name?.toLowerCase().includes(search.toLowerCase()) || inquiry.email?.toLowerCase().includes(search.toLowerCase()) || inquiry.subject?.toLowerCase().includes(search.toLowerCase()) || inquiry.inquiryId?.toLowerCase().includes(search.toLowerCase()));\n        }\n        // Sort inquiries\n        inquiries.sort((a, b)=>{\n            let aValue = a[sortBy];\n            let bValue = b[sortBy];\n            if (sortBy === 'createdAt' || sortBy === 'updatedAt') {\n                aValue = new Date(aValue).getTime();\n                bValue = new Date(bValue).getTime();\n            }\n            if (sortOrder === 'desc') {\n                return bValue > aValue ? 1 : -1;\n            } else {\n                return aValue > bValue ? 1 : -1;\n            }\n        });\n        // Pagination\n        const total = inquiries.length;\n        const startIndex = (page - 1) * limit;\n        const endIndex = startIndex + limit;\n        const paginatedInquiries = inquiries.slice(startIndex, endIndex);\n        // Calculate statistics\n        const stats = {\n            total: inquiries.length,\n            new: inquiries.filter((inquiry)=>!inquiry.status || inquiry.status === 'NEW').length,\n            inProgress: inquiries.filter((inquiry)=>inquiry.status === 'IN_PROGRESS').length,\n            responded: inquiries.filter((inquiry)=>inquiry.status === 'RESPONDED').length,\n            closed: inquiries.filter((inquiry)=>inquiry.status === 'CLOSED').length,\n            thisWeek: inquiries.filter((inquiry)=>{\n                const inquiryDate = new Date(inquiry.createdAt);\n                const weekAgo = new Date();\n                weekAgo.setDate(weekAgo.getDate() - 7);\n                return inquiryDate > weekAgo;\n            }).length\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: paginatedInquiries,\n            pagination: {\n                page,\n                limit,\n                total,\n                totalPages: Math.ceil(total / limit),\n                hasNext: endIndex < total,\n                hasPrev: page > 1\n            },\n            stats\n        });\n    } catch (error) {\n        console.error('Contact inquiries API error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Failed to fetch contact inquiries'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/contact-inquiries/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "fs/promises":
/*!******************************!*\
  !*** external "fs/promises" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("fs/promises");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcontact-inquiries%2Froute&page=%2Fapi%2Fcontact-inquiries%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcontact-inquiries%2Froute.ts&appDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();