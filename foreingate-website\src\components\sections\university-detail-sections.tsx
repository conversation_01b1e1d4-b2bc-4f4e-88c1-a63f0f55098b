import { PlaceholderSection } from './placeholder-section'
import { University } from '@/types'

interface UniversityDetailProps {
  university: University
}

export function UniversityDetailHero({ university }: UniversityDetailProps) {
  return (
    <PlaceholderSection
      title={university.name}
      description={university.description}
    />
  )
}

export function UniversityDetailContent({ university }: UniversityDetailProps) {
  return (
    <PlaceholderSection
      title="University Information"
      description="Detailed information about facilities, accreditations, and campus life."
      className="bg-muted/30"
    />
  )
}

export function UniversityProgramsSection({ university }: UniversityDetailProps) {
  return (
    <PlaceholderSection
      title="Available Programs"
      description="Browse all academic programs offered by this university."
    />
  )
}

export function UniversityGallerySection({ university }: UniversityDetailProps) {
  return (
    <PlaceholderSection
      title="Campus Gallery"
      description="Explore the campus through our photo gallery."
      className="bg-muted/30"
    />
  )
}
