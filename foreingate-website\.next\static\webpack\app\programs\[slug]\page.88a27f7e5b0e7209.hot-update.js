/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/programs/[slug]/page",{

/***/ "(app-pages-browser)/./node_modules/next/dist/api/navigation.js":
/*!**************************************************!*\
  !*** ./node_modules/next/dist/api/navigation.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../client/components/navigation */ \"(app-pages-browser)/./node_modules/next/dist/client/components/navigation.js\");\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_client_components_navigation__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n//# sourceMappingURL=navigation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYXBpL25hdmlnYXRpb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWdEOztBQUVoRCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxOaWRoYWxcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcZm9yZWluZ2F0ZV9ncm91cGVcXGZvcmVpbmdhdGUtd2Vic2l0ZVxcbm9kZV9tb2R1bGVzXFxuZXh0XFxkaXN0XFxhcGlcXG5hdmlnYXRpb24uanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSAnLi4vY2xpZW50L2NvbXBvbmVudHMvbmF2aWdhdGlvbic7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPW5hdmlnYXRpb24uanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/api/navigation.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Capp%5C%5Cprograms%5C%5C%5Bslug%5D%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Capp%5C%5Cprograms%5C%5C%5Bslug%5D%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/programs/[slug]/page.tsx */ \"(app-pages-browser)/./src/app/programs/[slug]/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDTmlkaGFsJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q2ZvcmVpbmdhdGVfZ3JvdXBlJTVDJTVDZm9yZWluZ2F0ZS13ZWJzaXRlJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDcHJvZ3JhbXMlNUMlNUMlNUJzbHVnJTVEJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPWZhbHNlISIsIm1hcHBpbmdzIjoiQUFBQSw4TEFBZ0siLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXE5pZGhhbFxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxmb3JlaW5nYXRlX2dyb3VwZVxcXFxmb3JlaW5nYXRlLXdlYnNpdGVcXFxcc3JjXFxcXGFwcFxcXFxwcm9ncmFtc1xcXFxbc2x1Z11cXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Capp%5C%5Cprograms%5C%5C%5Bslug%5D%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/programs/[slug]/page.tsx":
/*!******************************************!*\
  !*** ./src/app/programs/[slug]/page.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProgramPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_sections_program_detail_hero__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/sections/program-detail-hero */ \"(app-pages-browser)/./src/components/sections/program-detail-hero.tsx\");\n/* harmony import */ var _components_sections_program_detail_content__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/sections/program-detail-content */ \"(app-pages-browser)/./src/components/sections/program-detail-content.tsx\");\n/* harmony import */ var _components_sections_program_requirements__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/sections/program-requirements */ \"(app-pages-browser)/./src/components/sections/program-requirements.tsx\");\n/* harmony import */ var _components_sections_program_career__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/sections/program-career */ \"(app-pages-browser)/./src/components/sections/program-career.tsx\");\n/* harmony import */ var _components_ui_whatsapp_widget__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/whatsapp-widget */ \"(app-pages-browser)/./src/components/ui/whatsapp-widget.tsx\");\n/* harmony import */ var _hooks_use_api__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/use-api */ \"(app-pages-browser)/./src/hooks/use-api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction ProgramPage(param) {\n    let { params } = param;\n    _s();\n    const { data: program, loading, error } = (0,_hooks_use_api__WEBPACK_IMPORTED_MODULE_7__.useProgram)(params.slug);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-primary\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\programs\\\\[slug]\\\\page.tsx\",\n                lineNumber: 24,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\programs\\\\[slug]\\\\page.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, this);\n    }\n    if (error || !program) {\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.notFound)();\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sections_program_detail_hero__WEBPACK_IMPORTED_MODULE_2__.ProgramDetailHero, {\n                program: program\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\programs\\\\[slug]\\\\page.tsx\",\n                lineNumber: 35,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sections_program_detail_content__WEBPACK_IMPORTED_MODULE_3__.ProgramDetailContent, {\n                program: program\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\programs\\\\[slug]\\\\page.tsx\",\n                lineNumber: 36,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sections_program_requirements__WEBPACK_IMPORTED_MODULE_4__.ProgramRequirementsSection, {\n                program: program\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\programs\\\\[slug]\\\\page.tsx\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sections_program_career__WEBPACK_IMPORTED_MODULE_5__.ProgramCareerSection, {\n                program: program\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\programs\\\\[slug]\\\\page.tsx\",\n                lineNumber: 38,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_whatsapp_widget__WEBPACK_IMPORTED_MODULE_6__.WhatsAppWidget, {\n                phoneNumber: \"905392123456\",\n                message: \"Hello! I'm interested in the \".concat(program.name, \" program.\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\programs\\\\[slug]\\\\page.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(ProgramPage, \"Rza2UMFNQcXB6PfUJ6CYWRn6YqI=\", false, function() {\n    return [\n        _hooks_use_api__WEBPACK_IMPORTED_MODULE_7__.useProgram\n    ];\n});\n_c = ProgramPage;\nvar _c;\n$RefreshReg$(_c, \"ProgramPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/programs/[slug]/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/sections/program-career.tsx":
/*!****************************************************!*\
  !*** ./src/components/sections/program-career.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProgramCareerSection: () => (/* binding */ ProgramCareerSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _placeholder_section__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./placeholder-section */ \"(app-pages-browser)/./src/components/sections/placeholder-section.tsx\");\n\n\nfunction ProgramCareerSection(param) {\n    let { program } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_placeholder_section__WEBPACK_IMPORTED_MODULE_1__.PlaceholderSection, {\n        title: \"Career Opportunities\",\n        description: \"Explore career paths and opportunities after completing this program.\",\n        className: \"bg-muted/30\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\program-career.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, this);\n}\n_c = ProgramCareerSection;\nvar _c;\n$RefreshReg$(_c, \"ProgramCareerSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3NlY3Rpb25zL3Byb2dyYW0tY2FyZWVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQTBEO0FBT25ELFNBQVNDLHFCQUFxQixLQUFzQztRQUF0QyxFQUFFQyxPQUFPLEVBQTZCLEdBQXRDO0lBQ25DLHFCQUNFLDhEQUFDRixvRUFBa0JBO1FBQ2pCRyxPQUFNO1FBQ05DLGFBQVk7UUFDWkMsV0FBVTs7Ozs7O0FBR2hCO0tBUmdCSiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxOaWRoYWxcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcZm9yZWluZ2F0ZV9ncm91cGVcXGZvcmVpbmdhdGUtd2Vic2l0ZVxcc3JjXFxjb21wb25lbnRzXFxzZWN0aW9uc1xccHJvZ3JhbS1jYXJlZXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFBsYWNlaG9sZGVyU2VjdGlvbiB9IGZyb20gJy4vcGxhY2Vob2xkZXItc2VjdGlvbidcbmltcG9ydCB7IFByb2dyYW0gfSBmcm9tICdAL3R5cGVzJ1xuXG5pbnRlcmZhY2UgUHJvZ3JhbUNhcmVlclNlY3Rpb25Qcm9wcyB7XG4gIHByb2dyYW06IFByb2dyYW1cbn1cblxuZXhwb3J0IGZ1bmN0aW9uIFByb2dyYW1DYXJlZXJTZWN0aW9uKHsgcHJvZ3JhbSB9OiBQcm9ncmFtQ2FyZWVyU2VjdGlvblByb3BzKSB7XG4gIHJldHVybiAoXG4gICAgPFBsYWNlaG9sZGVyU2VjdGlvblxuICAgICAgdGl0bGU9XCJDYXJlZXIgT3Bwb3J0dW5pdGllc1wiXG4gICAgICBkZXNjcmlwdGlvbj1cIkV4cGxvcmUgY2FyZWVyIHBhdGhzIGFuZCBvcHBvcnR1bml0aWVzIGFmdGVyIGNvbXBsZXRpbmcgdGhpcyBwcm9ncmFtLlwiXG4gICAgICBjbGFzc05hbWU9XCJiZy1tdXRlZC8zMFwiXG4gICAgLz5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIlBsYWNlaG9sZGVyU2VjdGlvbiIsIlByb2dyYW1DYXJlZXJTZWN0aW9uIiwicHJvZ3JhbSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJjbGFzc05hbWUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/program-career.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/sections/program-detail-content.tsx":
/*!************************************************************!*\
  !*** ./src/components/sections/program-detail-content.tsx ***!
  \************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProgramDetailContent: () => (/* binding */ ProgramDetailContent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _placeholder_section__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./placeholder-section */ \"(app-pages-browser)/./src/components/sections/placeholder-section.tsx\");\n\n\nfunction ProgramDetailContent(param) {\n    let { program } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_placeholder_section__WEBPACK_IMPORTED_MODULE_1__.PlaceholderSection, {\n        title: \"Program Details\",\n        description: \"Comprehensive information about curriculum, duration, and learning outcomes.\",\n        className: \"bg-muted/30\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\program-detail-content.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, this);\n}\n_c = ProgramDetailContent;\nvar _c;\n$RefreshReg$(_c, \"ProgramDetailContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3NlY3Rpb25zL3Byb2dyYW0tZGV0YWlsLWNvbnRlbnQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBMEQ7QUFPbkQsU0FBU0MscUJBQXFCLEtBQXNDO1FBQXRDLEVBQUVDLE9BQU8sRUFBNkIsR0FBdEM7SUFDbkMscUJBQ0UsOERBQUNGLG9FQUFrQkE7UUFDakJHLE9BQU07UUFDTkMsYUFBWTtRQUNaQyxXQUFVOzs7Ozs7QUFHaEI7S0FSZ0JKIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXE5pZGhhbFxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxmb3JlaW5nYXRlX2dyb3VwZVxcZm9yZWluZ2F0ZS13ZWJzaXRlXFxzcmNcXGNvbXBvbmVudHNcXHNlY3Rpb25zXFxwcm9ncmFtLWRldGFpbC1jb250ZW50LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQbGFjZWhvbGRlclNlY3Rpb24gfSBmcm9tICcuL3BsYWNlaG9sZGVyLXNlY3Rpb24nXG5pbXBvcnQgeyBQcm9ncmFtIH0gZnJvbSAnQC90eXBlcydcblxuaW50ZXJmYWNlIFByb2dyYW1EZXRhaWxDb250ZW50UHJvcHMge1xuICBwcm9ncmFtOiBQcm9ncmFtXG59XG5cbmV4cG9ydCBmdW5jdGlvbiBQcm9ncmFtRGV0YWlsQ29udGVudCh7IHByb2dyYW0gfTogUHJvZ3JhbURldGFpbENvbnRlbnRQcm9wcykge1xuICByZXR1cm4gKFxuICAgIDxQbGFjZWhvbGRlclNlY3Rpb25cbiAgICAgIHRpdGxlPVwiUHJvZ3JhbSBEZXRhaWxzXCJcbiAgICAgIGRlc2NyaXB0aW9uPVwiQ29tcHJlaGVuc2l2ZSBpbmZvcm1hdGlvbiBhYm91dCBjdXJyaWN1bHVtLCBkdXJhdGlvbiwgYW5kIGxlYXJuaW5nIG91dGNvbWVzLlwiXG4gICAgICBjbGFzc05hbWU9XCJiZy1tdXRlZC8zMFwiXG4gICAgLz5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIlBsYWNlaG9sZGVyU2VjdGlvbiIsIlByb2dyYW1EZXRhaWxDb250ZW50IiwicHJvZ3JhbSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJjbGFzc05hbWUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/program-detail-content.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/sections/program-detail-hero.tsx":
/*!*********************************************************!*\
  !*** ./src/components/sections/program-detail-hero.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProgramDetailHero: () => (/* binding */ ProgramDetailHero)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _placeholder_section__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./placeholder-section */ \"(app-pages-browser)/./src/components/sections/placeholder-section.tsx\");\n\n\nfunction ProgramDetailHero(param) {\n    let { program } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_placeholder_section__WEBPACK_IMPORTED_MODULE_1__.PlaceholderSection, {\n        title: program.name,\n        description: program.description\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\program-detail-hero.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, this);\n}\n_c = ProgramDetailHero;\nvar _c;\n$RefreshReg$(_c, \"ProgramDetailHero\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3NlY3Rpb25zL3Byb2dyYW0tZGV0YWlsLWhlcm8udHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBMEQ7QUFPbkQsU0FBU0Msa0JBQWtCLEtBQW1DO1FBQW5DLEVBQUVDLE9BQU8sRUFBMEIsR0FBbkM7SUFDaEMscUJBQ0UsOERBQUNGLG9FQUFrQkE7UUFDakJHLE9BQU9ELFFBQVFFLElBQUk7UUFDbkJDLGFBQWFILFFBQVFHLFdBQVc7Ozs7OztBQUd0QztLQVBnQkoiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcTmlkaGFsXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXGZvcmVpbmdhdGVfZ3JvdXBlXFxmb3JlaW5nYXRlLXdlYnNpdGVcXHNyY1xcY29tcG9uZW50c1xcc2VjdGlvbnNcXHByb2dyYW0tZGV0YWlsLWhlcm8udHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFBsYWNlaG9sZGVyU2VjdGlvbiB9IGZyb20gJy4vcGxhY2Vob2xkZXItc2VjdGlvbidcbmltcG9ydCB7IFByb2dyYW0gfSBmcm9tICdAL3R5cGVzJ1xuXG5pbnRlcmZhY2UgUHJvZ3JhbURldGFpbEhlcm9Qcm9wcyB7XG4gIHByb2dyYW06IFByb2dyYW1cbn1cblxuZXhwb3J0IGZ1bmN0aW9uIFByb2dyYW1EZXRhaWxIZXJvKHsgcHJvZ3JhbSB9OiBQcm9ncmFtRGV0YWlsSGVyb1Byb3BzKSB7XG4gIHJldHVybiAoXG4gICAgPFBsYWNlaG9sZGVyU2VjdGlvblxuICAgICAgdGl0bGU9e3Byb2dyYW0ubmFtZX1cbiAgICAgIGRlc2NyaXB0aW9uPXtwcm9ncmFtLmRlc2NyaXB0aW9ufVxuICAgIC8+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJQbGFjZWhvbGRlclNlY3Rpb24iLCJQcm9ncmFtRGV0YWlsSGVybyIsInByb2dyYW0iLCJ0aXRsZSIsIm5hbWUiLCJkZXNjcmlwdGlvbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/program-detail-hero.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/sections/program-requirements.tsx":
/*!**********************************************************!*\
  !*** ./src/components/sections/program-requirements.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProgramRequirementsSection: () => (/* binding */ ProgramRequirementsSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _placeholder_section__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./placeholder-section */ \"(app-pages-browser)/./src/components/sections/placeholder-section.tsx\");\n\n\nfunction ProgramRequirementsSection(param) {\n    let { program } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_placeholder_section__WEBPACK_IMPORTED_MODULE_1__.PlaceholderSection, {\n        title: \"Admission Requirements\",\n        description: \"Learn about the requirements and application process for this program.\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\sections\\\\program-requirements.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, this);\n}\n_c = ProgramRequirementsSection;\nvar _c;\n$RefreshReg$(_c, \"ProgramRequirementsSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3NlY3Rpb25zL3Byb2dyYW0tcmVxdWlyZW1lbnRzLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQTBEO0FBT25ELFNBQVNDLDJCQUEyQixLQUE0QztRQUE1QyxFQUFFQyxPQUFPLEVBQW1DLEdBQTVDO0lBQ3pDLHFCQUNFLDhEQUFDRixvRUFBa0JBO1FBQ2pCRyxPQUFNO1FBQ05DLGFBQVk7Ozs7OztBQUdsQjtLQVBnQkgiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcTmlkaGFsXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXGZvcmVpbmdhdGVfZ3JvdXBlXFxmb3JlaW5nYXRlLXdlYnNpdGVcXHNyY1xcY29tcG9uZW50c1xcc2VjdGlvbnNcXHByb2dyYW0tcmVxdWlyZW1lbnRzLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQbGFjZWhvbGRlclNlY3Rpb24gfSBmcm9tICcuL3BsYWNlaG9sZGVyLXNlY3Rpb24nXG5pbXBvcnQgeyBQcm9ncmFtIH0gZnJvbSAnQC90eXBlcydcblxuaW50ZXJmYWNlIFByb2dyYW1SZXF1aXJlbWVudHNTZWN0aW9uUHJvcHMge1xuICBwcm9ncmFtOiBQcm9ncmFtXG59XG5cbmV4cG9ydCBmdW5jdGlvbiBQcm9ncmFtUmVxdWlyZW1lbnRzU2VjdGlvbih7IHByb2dyYW0gfTogUHJvZ3JhbVJlcXVpcmVtZW50c1NlY3Rpb25Qcm9wcykge1xuICByZXR1cm4gKFxuICAgIDxQbGFjZWhvbGRlclNlY3Rpb25cbiAgICAgIHRpdGxlPVwiQWRtaXNzaW9uIFJlcXVpcmVtZW50c1wiXG4gICAgICBkZXNjcmlwdGlvbj1cIkxlYXJuIGFib3V0IHRoZSByZXF1aXJlbWVudHMgYW5kIGFwcGxpY2F0aW9uIHByb2Nlc3MgZm9yIHRoaXMgcHJvZ3JhbS5cIlxuICAgIC8+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJQbGFjZWhvbGRlclNlY3Rpb24iLCJQcm9ncmFtUmVxdWlyZW1lbnRzU2VjdGlvbiIsInByb2dyYW0iLCJ0aXRsZSIsImRlc2NyaXB0aW9uIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/program-requirements.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/hooks/use-api.ts":
/*!******************************!*\
  !*** ./src/hooks/use-api.ts ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useBlogPost: () => (/* binding */ useBlogPost),\n/* harmony export */   useBlogPosts: () => (/* binding */ useBlogPosts),\n/* harmony export */   useProgram: () => (/* binding */ useProgram),\n/* harmony export */   usePrograms: () => (/* binding */ usePrograms),\n/* harmony export */   useTestimonials: () => (/* binding */ useTestimonials),\n/* harmony export */   useUniversities: () => (/* binding */ useUniversities),\n/* harmony export */   useUniversity: () => (/* binding */ useUniversity)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useUniversities,useUniversity,usePrograms,useProgram,useBlogPosts,useBlogPost,useTestimonials auto */ \n// Universities hooks\nfunction useUniversities(filters) {\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useUniversities.useEffect\": ()=>{\n            const fetchUniversities = {\n                \"useUniversities.useEffect.fetchUniversities\": async ()=>{\n                    try {\n                        setLoading(true);\n                        setError(null);\n                        const params = new URLSearchParams();\n                        if (filters === null || filters === void 0 ? void 0 : filters.location) params.append('location', filters.location);\n                        if (filters === null || filters === void 0 ? void 0 : filters.minTuition) params.append('minTuition', filters.minTuition.toString());\n                        if (filters === null || filters === void 0 ? void 0 : filters.maxTuition) params.append('maxTuition', filters.maxTuition.toString());\n                        if (filters === null || filters === void 0 ? void 0 : filters.search) params.append('search', filters.search);\n                        const response = await fetch(\"/api/universities?\".concat(params.toString()));\n                        const result = await response.json();\n                        if (result.success) {\n                            setData(result.data);\n                        } else {\n                            setError('Failed to fetch universities');\n                        }\n                    } catch (err) {\n                        setError('Network error occurred');\n                        console.error('Universities fetch error:', err);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"useUniversities.useEffect.fetchUniversities\"];\n            fetchUniversities();\n        }\n    }[\"useUniversities.useEffect\"], [\n        filters === null || filters === void 0 ? void 0 : filters.location,\n        filters === null || filters === void 0 ? void 0 : filters.minTuition,\n        filters === null || filters === void 0 ? void 0 : filters.maxTuition,\n        filters === null || filters === void 0 ? void 0 : filters.search\n    ]);\n    return {\n        data,\n        loading,\n        error\n    };\n}\nfunction useUniversity(id) {\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useUniversity.useEffect\": ()=>{\n            const fetchUniversity = {\n                \"useUniversity.useEffect.fetchUniversity\": async ()=>{\n                    try {\n                        setLoading(true);\n                        setError(null);\n                        const response = await fetch(\"/api/universities/\".concat(id));\n                        const result = await response.json();\n                        if (result.success) {\n                            setData(result.data);\n                        } else {\n                            setError('Failed to fetch university');\n                        }\n                    } catch (err) {\n                        setError('Network error occurred');\n                        console.error('University fetch error:', err);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"useUniversity.useEffect.fetchUniversity\"];\n            if (id) {\n                fetchUniversity();\n            }\n        }\n    }[\"useUniversity.useEffect\"], [\n        id\n    ]);\n    return {\n        data,\n        loading,\n        error\n    };\n}\n// Programs hooks\nfunction usePrograms(filters) {\n    var _filters_degrees, _filters_fields, _filters_languages, _filters_universities;\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [pagination, setPagination] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"usePrograms.useEffect\": ()=>{\n            const fetchPrograms = {\n                \"usePrograms.useEffect.fetchPrograms\": async ()=>{\n                    try {\n                        setLoading(true);\n                        setError(null);\n                        const params = new URLSearchParams();\n                        if (filters === null || filters === void 0 ? void 0 : filters.degrees) params.append('degrees', filters.degrees.join(','));\n                        if (filters === null || filters === void 0 ? void 0 : filters.fields) params.append('fields', filters.fields.join(','));\n                        if (filters === null || filters === void 0 ? void 0 : filters.languages) params.append('languages', filters.languages.join(','));\n                        if (filters === null || filters === void 0 ? void 0 : filters.universities) params.append('universities', filters.universities.join(','));\n                        if (filters === null || filters === void 0 ? void 0 : filters.search) params.append('search', filters.search);\n                        if (filters === null || filters === void 0 ? void 0 : filters.page) params.append('page', filters.page.toString());\n                        if (filters === null || filters === void 0 ? void 0 : filters.limit) params.append('limit', filters.limit.toString());\n                        const response = await fetch(\"/api/programs?\".concat(params.toString()));\n                        const result = await response.json();\n                        if (result.success) {\n                            setData(result.data);\n                            setPagination(result.pagination);\n                        } else {\n                            setError('Failed to fetch programs');\n                        }\n                    } catch (err) {\n                        setError('Network error occurred');\n                        console.error('Programs fetch error:', err);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"usePrograms.useEffect.fetchPrograms\"];\n            fetchPrograms();\n        }\n    }[\"usePrograms.useEffect\"], [\n        filters === null || filters === void 0 ? void 0 : (_filters_degrees = filters.degrees) === null || _filters_degrees === void 0 ? void 0 : _filters_degrees.join(','),\n        filters === null || filters === void 0 ? void 0 : (_filters_fields = filters.fields) === null || _filters_fields === void 0 ? void 0 : _filters_fields.join(','),\n        filters === null || filters === void 0 ? void 0 : (_filters_languages = filters.languages) === null || _filters_languages === void 0 ? void 0 : _filters_languages.join(','),\n        filters === null || filters === void 0 ? void 0 : (_filters_universities = filters.universities) === null || _filters_universities === void 0 ? void 0 : _filters_universities.join(','),\n        filters === null || filters === void 0 ? void 0 : filters.search,\n        filters === null || filters === void 0 ? void 0 : filters.page,\n        filters === null || filters === void 0 ? void 0 : filters.limit\n    ]);\n    return {\n        data,\n        loading,\n        error,\n        pagination\n    };\n}\nfunction useProgram(id) {\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useProgram.useEffect\": ()=>{\n            const fetchProgram = {\n                \"useProgram.useEffect.fetchProgram\": async ()=>{\n                    try {\n                        setLoading(true);\n                        setError(null);\n                        const response = await fetch(\"/api/programs/\".concat(id));\n                        const result = await response.json();\n                        if (result.success) {\n                            setData(result.data);\n                        } else {\n                            setError('Failed to fetch program');\n                        }\n                    } catch (err) {\n                        setError('Network error occurred');\n                        console.error('Program fetch error:', err);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"useProgram.useEffect.fetchProgram\"];\n            if (id) {\n                fetchProgram();\n            }\n        }\n    }[\"useProgram.useEffect\"], [\n        id\n    ]);\n    return {\n        data,\n        loading,\n        error\n    };\n}\n// Blog hooks\nfunction useBlogPosts(filters) {\n    var _filters_tags;\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [pagination, setPagination] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useBlogPosts.useEffect\": ()=>{\n            const fetchBlogPosts = {\n                \"useBlogPosts.useEffect.fetchBlogPosts\": async ()=>{\n                    try {\n                        setLoading(true);\n                        setError(null);\n                        const params = new URLSearchParams();\n                        if (filters === null || filters === void 0 ? void 0 : filters.category) params.append('category', filters.category);\n                        if (filters === null || filters === void 0 ? void 0 : filters.tags) params.append('tags', filters.tags.join(','));\n                        if (filters === null || filters === void 0 ? void 0 : filters.search) params.append('search', filters.search);\n                        if (filters === null || filters === void 0 ? void 0 : filters.featured) params.append('featured', 'true');\n                        if (filters === null || filters === void 0 ? void 0 : filters.page) params.append('page', filters.page.toString());\n                        if (filters === null || filters === void 0 ? void 0 : filters.limit) params.append('limit', filters.limit.toString());\n                        const response = await fetch(\"/api/blog?\".concat(params.toString()));\n                        const result = await response.json();\n                        if (result.success) {\n                            setData(result.data);\n                            setPagination(result.pagination);\n                        } else {\n                            setError('Failed to fetch blog posts');\n                        }\n                    } catch (err) {\n                        setError('Network error occurred');\n                        console.error('Blog posts fetch error:', err);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"useBlogPosts.useEffect.fetchBlogPosts\"];\n            fetchBlogPosts();\n        }\n    }[\"useBlogPosts.useEffect\"], [\n        filters === null || filters === void 0 ? void 0 : filters.category,\n        filters === null || filters === void 0 ? void 0 : (_filters_tags = filters.tags) === null || _filters_tags === void 0 ? void 0 : _filters_tags.join(','),\n        filters === null || filters === void 0 ? void 0 : filters.search,\n        filters === null || filters === void 0 ? void 0 : filters.featured,\n        filters === null || filters === void 0 ? void 0 : filters.page,\n        filters === null || filters === void 0 ? void 0 : filters.limit\n    ]);\n    return {\n        data,\n        loading,\n        error,\n        pagination\n    };\n}\nfunction useBlogPost(id) {\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useBlogPost.useEffect\": ()=>{\n            const fetchBlogPost = {\n                \"useBlogPost.useEffect.fetchBlogPost\": async ()=>{\n                    try {\n                        setLoading(true);\n                        setError(null);\n                        const response = await fetch(\"/api/blog/\".concat(id));\n                        const result = await response.json();\n                        if (result.success) {\n                            setData(result.data);\n                        } else {\n                            setError('Failed to fetch blog post');\n                        }\n                    } catch (err) {\n                        setError('Network error occurred');\n                        console.error('Blog post fetch error:', err);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"useBlogPost.useEffect.fetchBlogPost\"];\n            if (id) {\n                fetchBlogPost();\n            }\n        }\n    }[\"useBlogPost.useEffect\"], [\n        id\n    ]);\n    return {\n        data,\n        loading,\n        error\n    };\n}\n// Testimonials hooks\nfunction useTestimonials(filters) {\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [pagination, setPagination] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useTestimonials.useEffect\": ()=>{\n            const fetchTestimonials = {\n                \"useTestimonials.useEffect.fetchTestimonials\": async ()=>{\n                    try {\n                        setLoading(true);\n                        setError(null);\n                        const params = new URLSearchParams();\n                        if (filters === null || filters === void 0 ? void 0 : filters.university) params.append('university', filters.university);\n                        if (filters === null || filters === void 0 ? void 0 : filters.program) params.append('program', filters.program);\n                        if (filters === null || filters === void 0 ? void 0 : filters.country) params.append('country', filters.country);\n                        if (filters === null || filters === void 0 ? void 0 : filters.featured) params.append('featured', 'true');\n                        if (filters === null || filters === void 0 ? void 0 : filters.rating) params.append('rating', filters.rating.toString());\n                        if (filters === null || filters === void 0 ? void 0 : filters.page) params.append('page', filters.page.toString());\n                        if (filters === null || filters === void 0 ? void 0 : filters.limit) params.append('limit', filters.limit.toString());\n                        const response = await fetch(\"/api/testimonials?\".concat(params.toString()));\n                        const result = await response.json();\n                        if (result.success) {\n                            setData(result.data);\n                            setPagination(result.pagination);\n                        } else {\n                            setError('Failed to fetch testimonials');\n                        }\n                    } catch (err) {\n                        setError('Network error occurred');\n                        console.error('Testimonials fetch error:', err);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"useTestimonials.useEffect.fetchTestimonials\"];\n            fetchTestimonials();\n        }\n    }[\"useTestimonials.useEffect\"], [\n        filters === null || filters === void 0 ? void 0 : filters.university,\n        filters === null || filters === void 0 ? void 0 : filters.program,\n        filters === null || filters === void 0 ? void 0 : filters.country,\n        filters === null || filters === void 0 ? void 0 : filters.featured,\n        filters === null || filters === void 0 ? void 0 : filters.rating,\n        filters === null || filters === void 0 ? void 0 : filters.page,\n        filters === null || filters === void 0 ? void 0 : filters.limit\n    ]);\n    return {\n        data,\n        loading,\n        error,\n        pagination\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/use-api.ts\n"));

/***/ })

});