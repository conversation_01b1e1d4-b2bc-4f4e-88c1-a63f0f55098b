import { NextRequest, NextResponse } from 'next/server'
import { readFile, writeFile, mkdir } from 'fs/promises'
import { existsSync } from 'fs'
import path from 'path'

const DATA_DIR = path.join(process.cwd(), 'data')
const TESTIMONIALS_FILE = path.join(DATA_DIR, 'testimonials.json')

// Ensure data directory exists
async function ensureDataDir() {
  if (!existsSync(DATA_DIR)) {
    await mkdir(DATA_DIR, { recursive: true })
  }
}

// Read testimonials from file
async function readTestimonials() {
  try {
    await ensureDataDir()
    if (!existsSync(TESTIMONIALS_FILE)) {
      // Create initial data if file doesn't exist
      const initialData = [
        {
          id: '1',
          name: '<PERSON>',
          program: 'Computer Engineering',
          university: 'Eastern Mediterranean University',
          graduationYear: 2023,
          country: 'Egypt',
          image: '/images/testimonials/ahmed-hassan.jpg',
          rating: 5,
          content: 'Foreingate Group made my dream of studying abroad a reality. Their support throughout the entire process was exceptional - from university selection to visa assistance. I\'m now pursuing my Master\'s in Computer Engineering and couldn\'t be happier!',
          featured: true,
          status: 'approved',
          createdAt: new Date(Date.now() - 86400000 * 30).toISOString(),
          updatedAt: new Date(Date.now() - 86400000 * 30).toISOString()
        },
        {
          id: '2',
          name: 'Maria Rodriguez',
          program: 'Business Administration',
          university: 'Near East University',
          graduationYear: 2022,
          country: 'Spain',
          image: '/images/testimonials/maria-rodriguez.jpg',
          rating: 5,
          content: 'The team at Foreingate was incredibly professional and helpful. They guided me through every step of the application process and helped me secure a scholarship. The university experience has been amazing!',
          featured: true,
          status: 'approved',
          createdAt: new Date(Date.now() - 86400000 * 45).toISOString(),
          updatedAt: new Date(Date.now() - 86400000 * 45).toISOString()
        },
        {
          id: '3',
          name: 'David Chen',
          program: 'Medicine',
          university: 'Near East University',
          graduationYear: 2024,
          country: 'China',
          image: '/images/testimonials/david-chen.jpg',
          rating: 5,
          content: 'Studying medicine in Northern Cyprus has been an incredible journey. Foreingate helped me navigate the complex application process and provided ongoing support. The education quality is world-class!',
          featured: false,
          status: 'approved',
          createdAt: new Date(Date.now() - 86400000 * 60).toISOString(),
          updatedAt: new Date(Date.now() - 86400000 * 60).toISOString()
        },
        {
          id: '4',
          name: 'Fatima Al-Zahra',
          program: 'Architecture',
          university: 'Eastern Mediterranean University',
          graduationYear: 2023,
          country: 'UAE',
          image: '/images/testimonials/fatima-alzahra.jpg',
          rating: 5,
          content: 'The architecture program exceeded my expectations. Foreingate\'s counselors helped me choose the perfect university and program. The facilities and faculty are outstanding!',
          featured: false,
          status: 'approved',
          createdAt: new Date(Date.now() - 86400000 * 75).toISOString(),
          updatedAt: new Date(Date.now() - 86400000 * 75).toISOString()
        },
        {
          id: '5',
          name: 'James Wilson',
          program: 'Psychology',
          university: 'Cyprus International University',
          graduationYear: 2022,
          country: 'UK',
          image: '/images/testimonials/james-wilson.jpg',
          rating: 4,
          content: 'Great experience overall. The support from Foreingate was comprehensive, and the university provided excellent education. Would definitely recommend to other students!',
          featured: false,
          status: 'approved',
          createdAt: new Date(Date.now() - 86400000 * 90).toISOString(),
          updatedAt: new Date(Date.now() - 86400000 * 90).toISOString()
        },
        {
          id: '6',
          name: 'Priya Sharma',
          program: 'International Relations',
          university: 'Near East University',
          graduationYear: 2023,
          country: 'India',
          image: '/images/testimonials/priya-sharma.jpg',
          rating: 5,
          content: 'Foreingate made the entire process seamless. From application to arrival, they were there every step of the way. The international environment at the university is fantastic!',
          featured: false,
          status: 'approved',
          createdAt: new Date(Date.now() - 86400000 * 105).toISOString(),
          updatedAt: new Date(Date.now() - 86400000 * 105).toISOString()
        },
        {
          id: '7',
          name: 'Omar Khalil',
          program: 'Civil Engineering',
          university: 'Eastern Mediterranean University',
          graduationYear: 2024,
          country: 'Jordan',
          image: '/images/testimonials/omar-khalil.jpg',
          rating: 5,
          content: 'The engineering program is top-notch with modern labs and experienced faculty. Foreingate\'s guidance was invaluable in choosing the right program and university.',
          featured: false,
          status: 'approved',
          createdAt: new Date(Date.now() - 86400000 * 120).toISOString(),
          updatedAt: new Date(Date.now() - 86400000 * 120).toISOString()
        },
        {
          id: '8',
          name: 'Elena Popovic',
          program: 'Dentistry',
          university: 'Near East University',
          graduationYear: 2023,
          country: 'Serbia',
          image: '/images/testimonials/elena-popovic.jpg',
          rating: 5,
          content: 'The dentistry program provided excellent hands-on experience. Foreingate\'s support team was always available to help with any questions or concerns.',
          featured: false,
          status: 'approved',
          createdAt: new Date(Date.now() - 86400000 * 135).toISOString(),
          updatedAt: new Date(Date.now() - 86400000 * 135).toISOString()
        }
      ]
      await writeFile(TESTIMONIALS_FILE, JSON.stringify(initialData, null, 2))
      return initialData
    }
    const data = await readFile(TESTIMONIALS_FILE, 'utf-8')
    return JSON.parse(data)
  } catch (error) {
    console.error('Error reading testimonials:', error)
    return []
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const university = searchParams.get('university')
    const program = searchParams.get('program')
    const country = searchParams.get('country')
    const featured = searchParams.get('featured') === 'true'
    const rating = searchParams.get('rating')
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')

    let testimonials = await readTestimonials()

    // Apply filters
    if (university) {
      testimonials = testimonials.filter((testimonial: any) => 
        testimonial.university.toLowerCase().includes(university.toLowerCase())
      )
    }

    if (program) {
      testimonials = testimonials.filter((testimonial: any) => 
        testimonial.program.toLowerCase().includes(program.toLowerCase())
      )
    }

    if (country) {
      testimonials = testimonials.filter((testimonial: any) => 
        testimonial.country.toLowerCase().includes(country.toLowerCase())
      )
    }

    if (featured) {
      testimonials = testimonials.filter((testimonial: any) => 
        testimonial.featured === true
      )
    }

    if (rating) {
      testimonials = testimonials.filter((testimonial: any) => 
        testimonial.rating >= parseInt(rating)
      )
    }

    // Sort by creation date (newest first)
    testimonials.sort((a: any, b: any) => 
      new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    )

    // Pagination
    const total = testimonials.length
    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit
    const paginatedTestimonials = testimonials.slice(startIndex, endIndex)

    return NextResponse.json({
      success: true,
      data: paginatedTestimonials,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: endIndex < total,
        hasPrev: page > 1
      }
    })

  } catch (error) {
    console.error('Testimonials API error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch testimonials' },
      { status: 500 }
    )
  }
}
