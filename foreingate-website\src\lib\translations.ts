import { Locale } from './i18n'

// Translation keys type for type safety
export interface Translations {
  // Navigation
  nav: {
    home: string
    about: string
    services: string
    universities: string
    programs: string
    blog: string
    contact: string
    applyNow: string
    getStarted: string
    language: string
  }

  // Hero Section
  hero: {
    title: string
    subtitle: string
    description: string
    ctaPrimary: string
    ctaSecondary: string
    trustBadge: string
    studentsServed: string
    successRate: string
    yearsExperience: string
  }

  // About Section
  about: {
    title: string
    subtitle: string
    description: string
    mission: string
    vision: string
    values: string
    whyChooseUs: string
    experience: string
    expertise: string
    support: string
    success: string
  }

  // Services
  services: {
    title: string
    subtitle: string
    universitySelection: string
    universitySelectionDesc: string
    admissionGuidance: string
    admissionGuidanceDesc: string
    visaSupport: string
    visaSupportDesc: string
    accommodationHelp: string
    accommodationHelpDesc: string
    scholarshipAssistance: string
    scholarshipAssistanceDesc: string
    ongoingSupport: string
    ongoingSupportDesc: string
  }

  // Universities
  universities: {
    title: string
    subtitle: string
    emu: string
    neu: string
    ciu: string
    programs: string
    students: string
    established: string
    accreditation: string
    tuitionFrom: string
    learnMore: string
    applyNow: string
  }

  // Programs
  programs: {
    title: string
    subtitle: string
    engineering: string
    medicine: string
    business: string
    arts: string
    sciences: string
    law: string
    architecture: string
    education: string
    duration: string
    language: string
    degree: string
    bachelor: string
    master: string
    doctorate: string
  }

  // Testimonials
  testimonials: {
    title: string
    subtitle: string
    readMore: string
    showLess: string
    verified: string
    graduate: string
    currentStudent: string
  }

  // Contact
  contact: {
    title: string
    subtitle: string
    getInTouch: string
    name: string
    email: string
    phone: string
    message: string
    subject: string
    send: string
    sending: string
    sent: string
    error: string
    required: string
    invalidEmail: string
    office: string
    hours: string
    emergency: string
  }

  // Footer
  footer: {
    description: string
    quickLinks: string
    services: string
    contact: string
    followUs: string
    newsletter: string
    newsletterDesc: string
    subscribe: string
    subscribing: string
    subscribed: string
    privacy: string
    terms: string
    cookies: string
    sitemap: string
    allRightsReserved: string
  }

  // Common
  common: {
    loading: string
    error: string
    success: string
    warning: string
    info: string
    close: string
    cancel: string
    confirm: string
    save: string
    edit: string
    delete: string
    search: string
    filter: string
    sort: string
    next: string
    previous: string
    page: string
    of: string
    showing: string
    results: string
    noResults: string
    tryAgain: string
    learnMore: string
    readMore: string
    showMore: string
    showLess: string
    viewAll: string
    backToTop: string
  }

  // Chatbot
  chatbot: {
    title: string
    placeholder: string
    send: string
    thinking: string
    error: string
    retry: string
    clear: string
    minimize: string
    maximize: string
    close: string
    greeting: string
    suggestions: string
    typing: string
    offline: string
    online: string
  }

  // Forms
  forms: {
    firstName: string
    lastName: string
    fullName: string
    email: string
    phone: string
    country: string
    city: string
    address: string
    zipCode: string
    dateOfBirth: string
    gender: string
    male: string
    female: string
    other: string
    preferNotToSay: string
    nationality: string
    passportNumber: string
    education: string
    highSchool: string
    bachelor: string
    master: string
    doctorate: string
    workExperience: string
    englishLevel: string
    beginner: string
    intermediate: string
    advanced: string
    native: string
    interestedProgram: string
    interestedUniversity: string
    startDate: string
    additionalInfo: string
    agreeTerms: string
    agreePrivacy: string
    agreeMarketing: string
    submit: string
    submitting: string
    submitted: string
    required: string
    invalid: string
    tooShort: string
    tooLong: string
    passwordMismatch: string
  }

  // Costs & Scholarships
  costs: {
    title: string
    subtitle: string
    tuitionFees: string
    livingCosts: string
    totalCost: string
    scholarships: string
    financialAid: string
    paymentPlans: string
    currency: string
    perYear: string
    perMonth: string
    accommodation: string
    food: string
    transportation: string
    books: string
    personal: string
    insurance: string
    visa: string
    other: string
    meritScholarship: string
    needBasedAid: string
    earlyBird: string
    siblingDiscount: string
    calculate: string
    getQuote: string
  }
}