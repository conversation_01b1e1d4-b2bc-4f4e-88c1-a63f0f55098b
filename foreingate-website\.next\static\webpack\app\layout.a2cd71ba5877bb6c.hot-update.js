"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"06661533650a\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXE5pZGhhbFxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxmb3JlaW5nYXRlX2dyb3VwZVxcZm9yZWluZ2F0ZS13ZWJzaXRlXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIwNjY2MTUzMzY1MGFcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/hooks/use-translation.ts":
/*!**************************************!*\
  !*** ./src/hooks/use-translation.ts ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TranslationContext: () => (/* binding */ TranslationContext),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   formatRelativeTime: () => (/* binding */ formatRelativeTime),\n/* harmony export */   getNestedTranslation: () => (/* binding */ getNestedTranslation),\n/* harmony export */   getTranslations: () => (/* binding */ getTranslations),\n/* harmony export */   pluralize: () => (/* binding */ pluralize),\n/* harmony export */   translateWithInterpolation: () => (/* binding */ translateWithInterpolation),\n/* harmony export */   useTranslation: () => (/* binding */ useTranslation)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_i18n__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/i18n */ \"(app-pages-browser)/./src/lib/i18n.ts\");\n/* harmony import */ var _locales_en__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/locales/en */ \"(app-pages-browser)/./src/locales/en.ts\");\n/* harmony import */ var _locales_tr__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/locales/tr */ \"(app-pages-browser)/./src/locales/tr.ts\");\n/* harmony import */ var _locales_ar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/locales/ar */ \"(app-pages-browser)/./src/locales/ar.ts\");\n/* harmony import */ var _locales_fr__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/locales/fr */ \"(app-pages-browser)/./src/locales/fr.ts\");\n/* __next_internal_client_entry_do_not_use__ TranslationContext,useTranslation,getTranslations,getNestedTranslation,translateWithInterpolation,pluralize,formatDate,formatNumber,formatCurrency,formatRelativeTime auto */ \n\n// Import all translations\n\n\n\n\n// Translation map\nconst translations = {\n    en: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    tr: _locales_tr__WEBPACK_IMPORTED_MODULE_3__.tr,\n    ar: _locales_ar__WEBPACK_IMPORTED_MODULE_4__.ar,\n    fr: _locales_fr__WEBPACK_IMPORTED_MODULE_5__.fr,\n    // Add more languages as they are created\n    es: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    de: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ru: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    zh: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ja: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ko: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    pt: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    it: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    nl: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    sv: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    no: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    da: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    fi: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    pl: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    cs: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    hu: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ro: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    bg: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    hr: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    sk: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    sl: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    et: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    lv: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    lt: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    mt: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    cy: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ga: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    is: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    mk: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    sq: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    sr: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    bs: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    me: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    uk: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    be: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    kk: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ky: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    uz: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    tg: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    tm: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    mn: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ka: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    hy: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    az: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    fa: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ur: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    hi: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    bn: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ta: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    te: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ml: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    kn: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    gu: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    pa: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    or: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    as: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ne: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    si: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    my: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    th: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    lo: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    km: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    vi: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    id: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ms: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    tl: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    haw: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    mi: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    sm: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    to: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    fj: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    sw: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    am: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ti: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    om: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    so: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    rw: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    rn: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    lg: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ak: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    tw: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    yo: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ig: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ha: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ff: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    wo: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    sn: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    zu: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    xh: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    af: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    st: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    tn: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ss: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ve: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ts: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    nr: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    he: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    yi: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    jv: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    su: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    mad: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ban: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    bug: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    mak: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    min: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ace: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    bjn: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    bbc: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    nij: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    rej: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    sas: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    tet: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en\n};\nconst TranslationContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(undefined);\nfunction useTranslation() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(TranslationContext);\n    if (!context) {\n        throw new Error('useTranslation must be used within a TranslationProvider');\n    }\n    return context;\n}\n// Helper function to get translations for a specific locale\nfunction getTranslations(locale) {\n    return translations[locale] || translations[_lib_i18n__WEBPACK_IMPORTED_MODULE_1__.defaultLocale];\n}\n// Helper function to get nested translation value\nfunction getNestedTranslation(translations, key) {\n    const keys = key.split('.');\n    let value = translations;\n    for (const k of keys){\n        if (value && typeof value === 'object' && k in value) {\n            value = value[k];\n        } else {\n            return key // Return the key if translation not found\n            ;\n        }\n    }\n    return typeof value === 'string' ? value : key;\n}\n// Translation function with interpolation support\nfunction translateWithInterpolation(template) {\n    let values = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    return template.replace(/\\{\\{(\\w+)\\}\\}/g, (match, key)=>{\n        var _values_key;\n        return ((_values_key = values[key]) === null || _values_key === void 0 ? void 0 : _values_key.toString()) || match;\n    });\n}\n// Pluralization helper\nfunction pluralize(count, singular, plural) {\n    if (count === 1) {\n        return singular;\n    }\n    return plural || \"\".concat(singular, \"s\");\n}\n// Date formatting helper\nfunction formatDate(date, locale, options) {\n    try {\n        return new Intl.DateTimeFormat(locale, options).format(date);\n    } catch (error) {\n        // Fallback to English if locale is not supported\n        return new Intl.DateTimeFormat('en', options).format(date);\n    }\n}\n// Number formatting helper\nfunction formatNumber(number, locale, options) {\n    try {\n        return new Intl.NumberFormat(locale, options).format(number);\n    } catch (error) {\n        // Fallback to English if locale is not supported\n        return new Intl.NumberFormat('en', options).format(number);\n    }\n}\n// Currency formatting helper\nfunction formatCurrency(amount, locale) {\n    let currency = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 'USD';\n    try {\n        return new Intl.NumberFormat(locale, {\n            style: 'currency',\n            currency\n        }).format(amount);\n    } catch (error) {\n        // Fallback to English if locale is not supported\n        return new Intl.NumberFormat('en', {\n            style: 'currency',\n            currency\n        }).format(amount);\n    }\n}\n// Relative time formatting helper\nfunction formatRelativeTime(date, locale) {\n    try {\n        const rtf = new Intl.RelativeTimeFormat(locale, {\n            numeric: 'auto'\n        });\n        const now = new Date();\n        const diffInSeconds = Math.floor((date.getTime() - now.getTime()) / 1000);\n        if (Math.abs(diffInSeconds) < 60) {\n            return rtf.format(diffInSeconds, 'second');\n        }\n        const diffInMinutes = Math.floor(diffInSeconds / 60);\n        if (Math.abs(diffInMinutes) < 60) {\n            return rtf.format(diffInMinutes, 'minute');\n        }\n        const diffInHours = Math.floor(diffInMinutes / 60);\n        if (Math.abs(diffInHours) < 24) {\n            return rtf.format(diffInHours, 'hour');\n        }\n        const diffInDays = Math.floor(diffInHours / 24);\n        return rtf.format(diffInDays, 'day');\n    } catch (error) {\n        // Fallback to simple date formatting\n        return formatDate(date, locale);\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9ob29rcy91c2UtdHJhbnNsYXRpb24udHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Mk5BRWlEO0FBQ0M7QUFHbEQsMEJBQTBCO0FBQ087QUFDQTtBQUNBO0FBQ0E7QUFHakMsa0JBQWtCO0FBQ2xCLE1BQU1PLGVBQTZDO0lBQ2pESixFQUFFQSw2Q0FBQUE7SUFDRkMsRUFBRUEsNkNBQUFBO0lBQ0ZDLEVBQUVBLDZDQUFBQTtJQUNGQyxFQUFFQSw2Q0FBQUE7SUFDRix5Q0FBeUM7SUFDekNFLElBQUlMLDJDQUFFQTtJQUNOTSxJQUFJTiwyQ0FBRUE7SUFDTk8sSUFBSVAsMkNBQUVBO0lBQ05RLElBQUlSLDJDQUFFQTtJQUNOUyxJQUFJVCwyQ0FBRUE7SUFDTlUsSUFBSVYsMkNBQUVBO0lBQ05XLElBQUlYLDJDQUFFQTtJQUNOWSxJQUFJWiwyQ0FBRUE7SUFDTmEsSUFBSWIsMkNBQUVBO0lBQ05jLElBQUlkLDJDQUFFQTtJQUNOZSxJQUFJZiwyQ0FBRUE7SUFDTmdCLElBQUloQiwyQ0FBRUE7SUFDTmlCLElBQUlqQiwyQ0FBRUE7SUFDTmtCLElBQUlsQiwyQ0FBRUE7SUFDTm1CLElBQUluQiwyQ0FBRUE7SUFDTm9CLElBQUlwQiwyQ0FBRUE7SUFDTnFCLElBQUlyQiwyQ0FBRUE7SUFDTnNCLElBQUl0QiwyQ0FBRUE7SUFDTnVCLElBQUl2QiwyQ0FBRUE7SUFDTndCLElBQUl4QiwyQ0FBRUE7SUFDTnlCLElBQUl6QiwyQ0FBRUE7SUFDTjBCLElBQUkxQiwyQ0FBRUE7SUFDTjJCLElBQUkzQiwyQ0FBRUE7SUFDTjRCLElBQUk1QiwyQ0FBRUE7SUFDTjZCLElBQUk3QiwyQ0FBRUE7SUFDTjhCLElBQUk5QiwyQ0FBRUE7SUFDTitCLElBQUkvQiwyQ0FBRUE7SUFDTmdDLElBQUloQywyQ0FBRUE7SUFDTmlDLElBQUlqQywyQ0FBRUE7SUFDTmtDLElBQUlsQywyQ0FBRUE7SUFDTm1DLElBQUluQywyQ0FBRUE7SUFDTm9DLElBQUlwQywyQ0FBRUE7SUFDTnFDLElBQUlyQywyQ0FBRUE7SUFDTnNDLElBQUl0QywyQ0FBRUE7SUFDTnVDLElBQUl2QywyQ0FBRUE7SUFDTndDLElBQUl4QywyQ0FBRUE7SUFDTnlDLElBQUl6QywyQ0FBRUE7SUFDTjBDLElBQUkxQywyQ0FBRUE7SUFDTjJDLElBQUkzQywyQ0FBRUE7SUFDTjRDLElBQUk1QywyQ0FBRUE7SUFDTjZDLElBQUk3QywyQ0FBRUE7SUFDTjhDLElBQUk5QywyQ0FBRUE7SUFDTitDLElBQUkvQywyQ0FBRUE7SUFDTmdELElBQUloRCwyQ0FBRUE7SUFDTmlELElBQUlqRCwyQ0FBRUE7SUFDTmtELElBQUlsRCwyQ0FBRUE7SUFDTm1ELElBQUluRCwyQ0FBRUE7SUFDTm9ELElBQUlwRCwyQ0FBRUE7SUFDTnFELElBQUlyRCwyQ0FBRUE7SUFDTnNELElBQUl0RCwyQ0FBRUE7SUFDTnVELElBQUl2RCwyQ0FBRUE7SUFDTndELElBQUl4RCwyQ0FBRUE7SUFDTnlELElBQUl6RCwyQ0FBRUE7SUFDTjBELElBQUkxRCwyQ0FBRUE7SUFDTjJELElBQUkzRCwyQ0FBRUE7SUFDTjRELElBQUk1RCwyQ0FBRUE7SUFDTjZELElBQUk3RCwyQ0FBRUE7SUFDTjhELElBQUk5RCwyQ0FBRUE7SUFDTitELElBQUkvRCwyQ0FBRUE7SUFDTmdFLElBQUloRSwyQ0FBRUE7SUFDTmlFLElBQUlqRSwyQ0FBRUE7SUFDTmtFLElBQUlsRSwyQ0FBRUE7SUFDTm1FLElBQUluRSwyQ0FBRUE7SUFDTm9FLElBQUlwRSwyQ0FBRUE7SUFDTnFFLElBQUlyRSwyQ0FBRUE7SUFDTnNFLElBQUl0RSwyQ0FBRUE7SUFDTnVFLEtBQUt2RSwyQ0FBRUE7SUFDUHdFLElBQUl4RSwyQ0FBRUE7SUFDTnlFLElBQUl6RSwyQ0FBRUE7SUFDTjBFLElBQUkxRSwyQ0FBRUE7SUFDTjJFLElBQUkzRSwyQ0FBRUE7SUFDTjRFLElBQUk1RSwyQ0FBRUE7SUFDTjZFLElBQUk3RSwyQ0FBRUE7SUFDTjhFLElBQUk5RSwyQ0FBRUE7SUFDTitFLElBQUkvRSwyQ0FBRUE7SUFDTmdGLElBQUloRiwyQ0FBRUE7SUFDTmlGLElBQUlqRiwyQ0FBRUE7SUFDTmtGLElBQUlsRiwyQ0FBRUE7SUFDTm1GLElBQUluRiwyQ0FBRUE7SUFDTm9GLElBQUlwRiwyQ0FBRUE7SUFDTnFGLElBQUlyRiwyQ0FBRUE7SUFDTnNGLElBQUl0RiwyQ0FBRUE7SUFDTnVGLElBQUl2RiwyQ0FBRUE7SUFDTndGLElBQUl4RiwyQ0FBRUE7SUFDTnlGLElBQUl6RiwyQ0FBRUE7SUFDTjBGLElBQUkxRiwyQ0FBRUE7SUFDTjJGLElBQUkzRiwyQ0FBRUE7SUFDTjRGLElBQUk1RiwyQ0FBRUE7SUFDTjZGLElBQUk3RiwyQ0FBRUE7SUFDTjhGLElBQUk5RiwyQ0FBRUE7SUFDTitGLElBQUkvRiwyQ0FBRUE7SUFDTmdHLElBQUloRywyQ0FBRUE7SUFDTmlHLElBQUlqRywyQ0FBRUE7SUFDTmtHLElBQUlsRywyQ0FBRUE7SUFDTm1HLElBQUluRywyQ0FBRUE7SUFDTm9HLElBQUlwRywyQ0FBRUE7SUFDTnFHLElBQUlyRywyQ0FBRUE7SUFDTnNHLElBQUl0RywyQ0FBRUE7SUFDTnVHLElBQUl2RywyQ0FBRUE7SUFDTndHLElBQUl4RywyQ0FBRUE7SUFDTnlHLEtBQUt6RywyQ0FBRUE7SUFDUDBHLEtBQUsxRywyQ0FBRUE7SUFDUDJHLEtBQUszRywyQ0FBRUE7SUFDUDRHLEtBQUs1RywyQ0FBRUE7SUFDUDZHLEtBQUs3RywyQ0FBRUE7SUFDUDhHLEtBQUs5RywyQ0FBRUE7SUFDUCtHLEtBQUsvRywyQ0FBRUE7SUFDUGdILEtBQUtoSCwyQ0FBRUE7SUFDUGlILEtBQUtqSCwyQ0FBRUE7SUFDUGtILEtBQUtsSCwyQ0FBRUE7SUFDUG1ILEtBQUtuSCwyQ0FBRUE7SUFDUG9ILEtBQUtwSCwyQ0FBRUE7QUFDVDtBQVNPLE1BQU1xSCxxQkFBcUJ4SCxvREFBYUEsQ0FBcUN5SCxXQUFVO0FBRXZGLFNBQVNDO0lBQ2QsTUFBTUMsVUFBVTFILGlEQUFVQSxDQUFDdUg7SUFDM0IsSUFBSSxDQUFDRyxTQUFTO1FBQ1osTUFBTSxJQUFJQyxNQUFNO0lBQ2xCO0lBQ0EsT0FBT0Q7QUFDVDtBQUVBLDREQUE0RDtBQUNyRCxTQUFTRSxnQkFBZ0JDLE1BQWM7SUFDNUMsT0FBT3ZILFlBQVksQ0FBQ3VILE9BQU8sSUFBSXZILFlBQVksQ0FBQ0wsb0RBQWFBLENBQUM7QUFDNUQ7QUFFQSxrREFBa0Q7QUFDM0MsU0FBUzZILHFCQUNkeEgsWUFBMEIsRUFDMUJ5SCxHQUFXO0lBRVgsTUFBTUMsT0FBT0QsSUFBSUUsS0FBSyxDQUFDO0lBQ3ZCLElBQUlDLFFBQWE1SDtJQUVqQixLQUFLLE1BQU02SCxLQUFLSCxLQUFNO1FBQ3BCLElBQUlFLFNBQVMsT0FBT0EsVUFBVSxZQUFZQyxLQUFLRCxPQUFPO1lBQ3BEQSxRQUFRQSxLQUFLLENBQUNDLEVBQUU7UUFDbEIsT0FBTztZQUNMLE9BQU9KLElBQUksMENBQTBDOztRQUN2RDtJQUNGO0lBRUEsT0FBTyxPQUFPRyxVQUFVLFdBQVdBLFFBQVFIO0FBQzdDO0FBRUEsa0RBQWtEO0FBQzNDLFNBQVNLLDJCQUNkQyxRQUFnQjtRQUNoQkMsU0FBQUEsaUVBQTBDLENBQUM7SUFFM0MsT0FBT0QsU0FBU0UsT0FBTyxDQUFDLGtCQUFrQixDQUFDQyxPQUFPVDtZQUN6Q087UUFBUCxPQUFPQSxFQUFBQSxjQUFBQSxNQUFNLENBQUNQLElBQUksY0FBWE8sa0NBQUFBLFlBQWFHLFFBQVEsT0FBTUQ7SUFDcEM7QUFDRjtBQUVBLHVCQUF1QjtBQUNoQixTQUFTRSxVQUNkQyxLQUFhLEVBQ2JDLFFBQWdCLEVBQ2hCQyxNQUFlO0lBRWYsSUFBSUYsVUFBVSxHQUFHO1FBQ2YsT0FBT0M7SUFDVDtJQUNBLE9BQU9DLFVBQVUsR0FBWSxPQUFURCxVQUFTO0FBQy9CO0FBRUEseUJBQXlCO0FBQ2xCLFNBQVNFLFdBQ2RDLElBQVUsRUFDVmxCLE1BQWMsRUFDZG1CLE9BQW9DO0lBRXBDLElBQUk7UUFDRixPQUFPLElBQUlDLEtBQUtDLGNBQWMsQ0FBQ3JCLFFBQVFtQixTQUFTRyxNQUFNLENBQUNKO0lBQ3pELEVBQUUsT0FBT0ssT0FBTztRQUNkLGlEQUFpRDtRQUNqRCxPQUFPLElBQUlILEtBQUtDLGNBQWMsQ0FBQyxNQUFNRixTQUFTRyxNQUFNLENBQUNKO0lBQ3ZEO0FBQ0Y7QUFFQSwyQkFBMkI7QUFDcEIsU0FBU00sYUFDZEMsTUFBYyxFQUNkekIsTUFBYyxFQUNkbUIsT0FBa0M7SUFFbEMsSUFBSTtRQUNGLE9BQU8sSUFBSUMsS0FBS00sWUFBWSxDQUFDMUIsUUFBUW1CLFNBQVNHLE1BQU0sQ0FBQ0c7SUFDdkQsRUFBRSxPQUFPRixPQUFPO1FBQ2QsaURBQWlEO1FBQ2pELE9BQU8sSUFBSUgsS0FBS00sWUFBWSxDQUFDLE1BQU1QLFNBQVNHLE1BQU0sQ0FBQ0c7SUFDckQ7QUFDRjtBQUVBLDZCQUE2QjtBQUN0QixTQUFTRSxlQUNkQyxNQUFjLEVBQ2Q1QixNQUFjO1FBQ2Q2QixXQUFBQSxpRUFBbUI7SUFFbkIsSUFBSTtRQUNGLE9BQU8sSUFBSVQsS0FBS00sWUFBWSxDQUFDMUIsUUFBUTtZQUNuQzhCLE9BQU87WUFDUEQ7UUFDRixHQUFHUCxNQUFNLENBQUNNO0lBQ1osRUFBRSxPQUFPTCxPQUFPO1FBQ2QsaURBQWlEO1FBQ2pELE9BQU8sSUFBSUgsS0FBS00sWUFBWSxDQUFDLE1BQU07WUFDakNJLE9BQU87WUFDUEQ7UUFDRixHQUFHUCxNQUFNLENBQUNNO0lBQ1o7QUFDRjtBQUVBLGtDQUFrQztBQUMzQixTQUFTRyxtQkFDZGIsSUFBVSxFQUNWbEIsTUFBYztJQUVkLElBQUk7UUFDRixNQUFNZ0MsTUFBTSxJQUFJWixLQUFLYSxrQkFBa0IsQ0FBQ2pDLFFBQVE7WUFBRWtDLFNBQVM7UUFBTztRQUNsRSxNQUFNQyxNQUFNLElBQUlDO1FBQ2hCLE1BQU1DLGdCQUFnQkMsS0FBS0MsS0FBSyxDQUFDLENBQUNyQixLQUFLc0IsT0FBTyxLQUFLTCxJQUFJSyxPQUFPLEVBQUMsSUFBSztRQUVwRSxJQUFJRixLQUFLRyxHQUFHLENBQUNKLGlCQUFpQixJQUFJO1lBQ2hDLE9BQU9MLElBQUlWLE1BQU0sQ0FBQ2UsZUFBZTtRQUNuQztRQUVBLE1BQU1LLGdCQUFnQkosS0FBS0MsS0FBSyxDQUFDRixnQkFBZ0I7UUFDakQsSUFBSUMsS0FBS0csR0FBRyxDQUFDQyxpQkFBaUIsSUFBSTtZQUNoQyxPQUFPVixJQUFJVixNQUFNLENBQUNvQixlQUFlO1FBQ25DO1FBRUEsTUFBTUMsY0FBY0wsS0FBS0MsS0FBSyxDQUFDRyxnQkFBZ0I7UUFDL0MsSUFBSUosS0FBS0csR0FBRyxDQUFDRSxlQUFlLElBQUk7WUFDOUIsT0FBT1gsSUFBSVYsTUFBTSxDQUFDcUIsYUFBYTtRQUNqQztRQUVBLE1BQU1DLGFBQWFOLEtBQUtDLEtBQUssQ0FBQ0ksY0FBYztRQUM1QyxPQUFPWCxJQUFJVixNQUFNLENBQUNzQixZQUFZO0lBQ2hDLEVBQUUsT0FBT3JCLE9BQU87UUFDZCxxQ0FBcUM7UUFDckMsT0FBT04sV0FBV0MsTUFBTWxCO0lBQzFCO0FBQ0YiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcTmlkaGFsXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXGZvcmVpbmdhdGVfZ3JvdXBlXFxmb3JlaW5nYXRlLXdlYnNpdGVcXHNyY1xcaG9va3NcXHVzZS10cmFuc2xhdGlvbi50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgY3JlYXRlQ29udGV4dCwgdXNlQ29udGV4dCB9IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHsgTG9jYWxlLCBkZWZhdWx0TG9jYWxlIH0gZnJvbSAnQC9saWIvaTE4bidcbmltcG9ydCB7IFRyYW5zbGF0aW9ucyB9IGZyb20gJ0AvbGliL3RyYW5zbGF0aW9ucydcblxuLy8gSW1wb3J0IGFsbCB0cmFuc2xhdGlvbnNcbmltcG9ydCB7IGVuIH0gZnJvbSAnQC9sb2NhbGVzL2VuJ1xuaW1wb3J0IHsgdHIgfSBmcm9tICdAL2xvY2FsZXMvdHInXG5pbXBvcnQgeyBhciB9IGZyb20gJ0AvbG9jYWxlcy9hcidcbmltcG9ydCB7IGZyIH0gZnJvbSAnQC9sb2NhbGVzL2ZyJ1xuaW1wb3J0IHsgZXMgfSBmcm9tICdAL2xvY2FsZXMvZXMnXG5cbi8vIFRyYW5zbGF0aW9uIG1hcFxuY29uc3QgdHJhbnNsYXRpb25zOiBSZWNvcmQ8TG9jYWxlLCBUcmFuc2xhdGlvbnM+ID0ge1xuICBlbixcbiAgdHIsXG4gIGFyLFxuICBmcixcbiAgLy8gQWRkIG1vcmUgbGFuZ3VhZ2VzIGFzIHRoZXkgYXJlIGNyZWF0ZWRcbiAgZXM6IGVuLFxuICBkZTogZW4sXG4gIHJ1OiBlbixcbiAgemg6IGVuLFxuICBqYTogZW4sXG4gIGtvOiBlbixcbiAgcHQ6IGVuLFxuICBpdDogZW4sXG4gIG5sOiBlbixcbiAgc3Y6IGVuLFxuICBubzogZW4sXG4gIGRhOiBlbixcbiAgZmk6IGVuLFxuICBwbDogZW4sXG4gIGNzOiBlbixcbiAgaHU6IGVuLFxuICBybzogZW4sXG4gIGJnOiBlbixcbiAgaHI6IGVuLFxuICBzazogZW4sXG4gIHNsOiBlbixcbiAgZXQ6IGVuLFxuICBsdjogZW4sXG4gIGx0OiBlbixcbiAgbXQ6IGVuLFxuICBjeTogZW4sXG4gIGdhOiBlbixcbiAgaXM6IGVuLFxuICBtazogZW4sXG4gIHNxOiBlbixcbiAgc3I6IGVuLFxuICBiczogZW4sXG4gIG1lOiBlbixcbiAgdWs6IGVuLFxuICBiZTogZW4sXG4gIGtrOiBlbixcbiAga3k6IGVuLFxuICB1ejogZW4sXG4gIHRnOiBlbixcbiAgdG06IGVuLFxuICBtbjogZW4sXG4gIGthOiBlbixcbiAgaHk6IGVuLFxuICBhejogZW4sXG4gIGZhOiBlbixcbiAgdXI6IGVuLFxuICBoaTogZW4sXG4gIGJuOiBlbixcbiAgdGE6IGVuLFxuICB0ZTogZW4sXG4gIG1sOiBlbixcbiAga246IGVuLFxuICBndTogZW4sXG4gIHBhOiBlbixcbiAgb3I6IGVuLFxuICBhczogZW4sXG4gIG5lOiBlbixcbiAgc2k6IGVuLFxuICBteTogZW4sXG4gIHRoOiBlbixcbiAgbG86IGVuLFxuICBrbTogZW4sXG4gIHZpOiBlbixcbiAgaWQ6IGVuLFxuICBtczogZW4sXG4gIHRsOiBlbixcbiAgaGF3OiBlbixcbiAgbWk6IGVuLFxuICBzbTogZW4sXG4gIHRvOiBlbixcbiAgZmo6IGVuLFxuICBzdzogZW4sXG4gIGFtOiBlbixcbiAgdGk6IGVuLFxuICBvbTogZW4sXG4gIHNvOiBlbixcbiAgcnc6IGVuLFxuICBybjogZW4sXG4gIGxnOiBlbixcbiAgYWs6IGVuLFxuICB0dzogZW4sXG4gIHlvOiBlbixcbiAgaWc6IGVuLFxuICBoYTogZW4sXG4gIGZmOiBlbixcbiAgd286IGVuLFxuICBzbjogZW4sXG4gIHp1OiBlbixcbiAgeGg6IGVuLFxuICBhZjogZW4sXG4gIHN0OiBlbixcbiAgdG46IGVuLFxuICBzczogZW4sXG4gIHZlOiBlbixcbiAgdHM6IGVuLFxuICBucjogZW4sXG4gIGhlOiBlbixcbiAgeWk6IGVuLFxuICBqdjogZW4sXG4gIHN1OiBlbixcbiAgbWFkOiBlbixcbiAgYmFuOiBlbixcbiAgYnVnOiBlbixcbiAgbWFrOiBlbixcbiAgbWluOiBlbixcbiAgYWNlOiBlbixcbiAgYmpuOiBlbixcbiAgYmJjOiBlbixcbiAgbmlqOiBlbixcbiAgcmVqOiBlbixcbiAgc2FzOiBlbixcbiAgdGV0OiBlbixcbn1cblxuaW50ZXJmYWNlIFRyYW5zbGF0aW9uQ29udGV4dFR5cGUge1xuICBsb2NhbGU6IExvY2FsZVxuICBzZXRMb2NhbGU6IChsb2NhbGU6IExvY2FsZSkgPT4gdm9pZFxuICB0OiBUcmFuc2xhdGlvbnNcbiAgaXNSVEw6IGJvb2xlYW5cbn1cblxuZXhwb3J0IGNvbnN0IFRyYW5zbGF0aW9uQ29udGV4dCA9IGNyZWF0ZUNvbnRleHQ8VHJhbnNsYXRpb25Db250ZXh0VHlwZSB8IHVuZGVmaW5lZD4odW5kZWZpbmVkKVxuXG5leHBvcnQgZnVuY3Rpb24gdXNlVHJhbnNsYXRpb24oKSB7XG4gIGNvbnN0IGNvbnRleHQgPSB1c2VDb250ZXh0KFRyYW5zbGF0aW9uQ29udGV4dClcbiAgaWYgKCFjb250ZXh0KSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCd1c2VUcmFuc2xhdGlvbiBtdXN0IGJlIHVzZWQgd2l0aGluIGEgVHJhbnNsYXRpb25Qcm92aWRlcicpXG4gIH1cbiAgcmV0dXJuIGNvbnRleHRcbn1cblxuLy8gSGVscGVyIGZ1bmN0aW9uIHRvIGdldCB0cmFuc2xhdGlvbnMgZm9yIGEgc3BlY2lmaWMgbG9jYWxlXG5leHBvcnQgZnVuY3Rpb24gZ2V0VHJhbnNsYXRpb25zKGxvY2FsZTogTG9jYWxlKTogVHJhbnNsYXRpb25zIHtcbiAgcmV0dXJuIHRyYW5zbGF0aW9uc1tsb2NhbGVdIHx8IHRyYW5zbGF0aW9uc1tkZWZhdWx0TG9jYWxlXVxufVxuXG4vLyBIZWxwZXIgZnVuY3Rpb24gdG8gZ2V0IG5lc3RlZCB0cmFuc2xhdGlvbiB2YWx1ZVxuZXhwb3J0IGZ1bmN0aW9uIGdldE5lc3RlZFRyYW5zbGF0aW9uKFxuICB0cmFuc2xhdGlvbnM6IFRyYW5zbGF0aW9ucyxcbiAga2V5OiBzdHJpbmdcbik6IHN0cmluZyB7XG4gIGNvbnN0IGtleXMgPSBrZXkuc3BsaXQoJy4nKVxuICBsZXQgdmFsdWU6IGFueSA9IHRyYW5zbGF0aW9uc1xuICBcbiAgZm9yIChjb25zdCBrIG9mIGtleXMpIHtcbiAgICBpZiAodmFsdWUgJiYgdHlwZW9mIHZhbHVlID09PSAnb2JqZWN0JyAmJiBrIGluIHZhbHVlKSB7XG4gICAgICB2YWx1ZSA9IHZhbHVlW2tdXG4gICAgfSBlbHNlIHtcbiAgICAgIHJldHVybiBrZXkgLy8gUmV0dXJuIHRoZSBrZXkgaWYgdHJhbnNsYXRpb24gbm90IGZvdW5kXG4gICAgfVxuICB9XG4gIFxuICByZXR1cm4gdHlwZW9mIHZhbHVlID09PSAnc3RyaW5nJyA/IHZhbHVlIDoga2V5XG59XG5cbi8vIFRyYW5zbGF0aW9uIGZ1bmN0aW9uIHdpdGggaW50ZXJwb2xhdGlvbiBzdXBwb3J0XG5leHBvcnQgZnVuY3Rpb24gdHJhbnNsYXRlV2l0aEludGVycG9sYXRpb24oXG4gIHRlbXBsYXRlOiBzdHJpbmcsXG4gIHZhbHVlczogUmVjb3JkPHN0cmluZywgc3RyaW5nIHwgbnVtYmVyPiA9IHt9XG4pOiBzdHJpbmcge1xuICByZXR1cm4gdGVtcGxhdGUucmVwbGFjZSgvXFx7XFx7KFxcdyspXFx9XFx9L2csIChtYXRjaCwga2V5KSA9PiB7XG4gICAgcmV0dXJuIHZhbHVlc1trZXldPy50b1N0cmluZygpIHx8IG1hdGNoXG4gIH0pXG59XG5cbi8vIFBsdXJhbGl6YXRpb24gaGVscGVyXG5leHBvcnQgZnVuY3Rpb24gcGx1cmFsaXplKFxuICBjb3VudDogbnVtYmVyLFxuICBzaW5ndWxhcjogc3RyaW5nLFxuICBwbHVyYWw/OiBzdHJpbmdcbik6IHN0cmluZyB7XG4gIGlmIChjb3VudCA9PT0gMSkge1xuICAgIHJldHVybiBzaW5ndWxhclxuICB9XG4gIHJldHVybiBwbHVyYWwgfHwgYCR7c2luZ3VsYXJ9c2Bcbn1cblxuLy8gRGF0ZSBmb3JtYXR0aW5nIGhlbHBlclxuZXhwb3J0IGZ1bmN0aW9uIGZvcm1hdERhdGUoXG4gIGRhdGU6IERhdGUsXG4gIGxvY2FsZTogTG9jYWxlLFxuICBvcHRpb25zPzogSW50bC5EYXRlVGltZUZvcm1hdE9wdGlvbnNcbik6IHN0cmluZyB7XG4gIHRyeSB7XG4gICAgcmV0dXJuIG5ldyBJbnRsLkRhdGVUaW1lRm9ybWF0KGxvY2FsZSwgb3B0aW9ucykuZm9ybWF0KGRhdGUpXG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgLy8gRmFsbGJhY2sgdG8gRW5nbGlzaCBpZiBsb2NhbGUgaXMgbm90IHN1cHBvcnRlZFxuICAgIHJldHVybiBuZXcgSW50bC5EYXRlVGltZUZvcm1hdCgnZW4nLCBvcHRpb25zKS5mb3JtYXQoZGF0ZSlcbiAgfVxufVxuXG4vLyBOdW1iZXIgZm9ybWF0dGluZyBoZWxwZXJcbmV4cG9ydCBmdW5jdGlvbiBmb3JtYXROdW1iZXIoXG4gIG51bWJlcjogbnVtYmVyLFxuICBsb2NhbGU6IExvY2FsZSxcbiAgb3B0aW9ucz86IEludGwuTnVtYmVyRm9ybWF0T3B0aW9uc1xuKTogc3RyaW5nIHtcbiAgdHJ5IHtcbiAgICByZXR1cm4gbmV3IEludGwuTnVtYmVyRm9ybWF0KGxvY2FsZSwgb3B0aW9ucykuZm9ybWF0KG51bWJlcilcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAvLyBGYWxsYmFjayB0byBFbmdsaXNoIGlmIGxvY2FsZSBpcyBub3Qgc3VwcG9ydGVkXG4gICAgcmV0dXJuIG5ldyBJbnRsLk51bWJlckZvcm1hdCgnZW4nLCBvcHRpb25zKS5mb3JtYXQobnVtYmVyKVxuICB9XG59XG5cbi8vIEN1cnJlbmN5IGZvcm1hdHRpbmcgaGVscGVyXG5leHBvcnQgZnVuY3Rpb24gZm9ybWF0Q3VycmVuY3koXG4gIGFtb3VudDogbnVtYmVyLFxuICBsb2NhbGU6IExvY2FsZSxcbiAgY3VycmVuY3k6IHN0cmluZyA9ICdVU0QnXG4pOiBzdHJpbmcge1xuICB0cnkge1xuICAgIHJldHVybiBuZXcgSW50bC5OdW1iZXJGb3JtYXQobG9jYWxlLCB7XG4gICAgICBzdHlsZTogJ2N1cnJlbmN5JyxcbiAgICAgIGN1cnJlbmN5XG4gICAgfSkuZm9ybWF0KGFtb3VudClcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAvLyBGYWxsYmFjayB0byBFbmdsaXNoIGlmIGxvY2FsZSBpcyBub3Qgc3VwcG9ydGVkXG4gICAgcmV0dXJuIG5ldyBJbnRsLk51bWJlckZvcm1hdCgnZW4nLCB7XG4gICAgICBzdHlsZTogJ2N1cnJlbmN5JyxcbiAgICAgIGN1cnJlbmN5XG4gICAgfSkuZm9ybWF0KGFtb3VudClcbiAgfVxufVxuXG4vLyBSZWxhdGl2ZSB0aW1lIGZvcm1hdHRpbmcgaGVscGVyXG5leHBvcnQgZnVuY3Rpb24gZm9ybWF0UmVsYXRpdmVUaW1lKFxuICBkYXRlOiBEYXRlLFxuICBsb2NhbGU6IExvY2FsZVxuKTogc3RyaW5nIHtcbiAgdHJ5IHtcbiAgICBjb25zdCBydGYgPSBuZXcgSW50bC5SZWxhdGl2ZVRpbWVGb3JtYXQobG9jYWxlLCB7IG51bWVyaWM6ICdhdXRvJyB9KVxuICAgIGNvbnN0IG5vdyA9IG5ldyBEYXRlKClcbiAgICBjb25zdCBkaWZmSW5TZWNvbmRzID0gTWF0aC5mbG9vcigoZGF0ZS5nZXRUaW1lKCkgLSBub3cuZ2V0VGltZSgpKSAvIDEwMDApXG4gICAgXG4gICAgaWYgKE1hdGguYWJzKGRpZmZJblNlY29uZHMpIDwgNjApIHtcbiAgICAgIHJldHVybiBydGYuZm9ybWF0KGRpZmZJblNlY29uZHMsICdzZWNvbmQnKVxuICAgIH1cbiAgICBcbiAgICBjb25zdCBkaWZmSW5NaW51dGVzID0gTWF0aC5mbG9vcihkaWZmSW5TZWNvbmRzIC8gNjApXG4gICAgaWYgKE1hdGguYWJzKGRpZmZJbk1pbnV0ZXMpIDwgNjApIHtcbiAgICAgIHJldHVybiBydGYuZm9ybWF0KGRpZmZJbk1pbnV0ZXMsICdtaW51dGUnKVxuICAgIH1cbiAgICBcbiAgICBjb25zdCBkaWZmSW5Ib3VycyA9IE1hdGguZmxvb3IoZGlmZkluTWludXRlcyAvIDYwKVxuICAgIGlmIChNYXRoLmFicyhkaWZmSW5Ib3VycykgPCAyNCkge1xuICAgICAgcmV0dXJuIHJ0Zi5mb3JtYXQoZGlmZkluSG91cnMsICdob3VyJylcbiAgICB9XG4gICAgXG4gICAgY29uc3QgZGlmZkluRGF5cyA9IE1hdGguZmxvb3IoZGlmZkluSG91cnMgLyAyNClcbiAgICByZXR1cm4gcnRmLmZvcm1hdChkaWZmSW5EYXlzLCAnZGF5JylcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAvLyBGYWxsYmFjayB0byBzaW1wbGUgZGF0ZSBmb3JtYXR0aW5nXG4gICAgcmV0dXJuIGZvcm1hdERhdGUoZGF0ZSwgbG9jYWxlKVxuICB9XG59XG4iXSwibmFtZXMiOlsiY3JlYXRlQ29udGV4dCIsInVzZUNvbnRleHQiLCJkZWZhdWx0TG9jYWxlIiwiZW4iLCJ0ciIsImFyIiwiZnIiLCJ0cmFuc2xhdGlvbnMiLCJlcyIsImRlIiwicnUiLCJ6aCIsImphIiwia28iLCJwdCIsIml0IiwibmwiLCJzdiIsIm5vIiwiZGEiLCJmaSIsInBsIiwiY3MiLCJodSIsInJvIiwiYmciLCJociIsInNrIiwic2wiLCJldCIsImx2IiwibHQiLCJtdCIsImN5IiwiZ2EiLCJpcyIsIm1rIiwic3EiLCJzciIsImJzIiwibWUiLCJ1ayIsImJlIiwia2siLCJreSIsInV6IiwidGciLCJ0bSIsIm1uIiwia2EiLCJoeSIsImF6IiwiZmEiLCJ1ciIsImhpIiwiYm4iLCJ0YSIsInRlIiwibWwiLCJrbiIsImd1IiwicGEiLCJvciIsImFzIiwibmUiLCJzaSIsIm15IiwidGgiLCJsbyIsImttIiwidmkiLCJpZCIsIm1zIiwidGwiLCJoYXciLCJtaSIsInNtIiwidG8iLCJmaiIsInN3IiwiYW0iLCJ0aSIsIm9tIiwic28iLCJydyIsInJuIiwibGciLCJhayIsInR3IiwieW8iLCJpZyIsImhhIiwiZmYiLCJ3byIsInNuIiwienUiLCJ4aCIsImFmIiwic3QiLCJ0biIsInNzIiwidmUiLCJ0cyIsIm5yIiwiaGUiLCJ5aSIsImp2Iiwic3UiLCJtYWQiLCJiYW4iLCJidWciLCJtYWsiLCJtaW4iLCJhY2UiLCJiam4iLCJiYmMiLCJuaWoiLCJyZWoiLCJzYXMiLCJ0ZXQiLCJUcmFuc2xhdGlvbkNvbnRleHQiLCJ1bmRlZmluZWQiLCJ1c2VUcmFuc2xhdGlvbiIsImNvbnRleHQiLCJFcnJvciIsImdldFRyYW5zbGF0aW9ucyIsImxvY2FsZSIsImdldE5lc3RlZFRyYW5zbGF0aW9uIiwia2V5Iiwia2V5cyIsInNwbGl0IiwidmFsdWUiLCJrIiwidHJhbnNsYXRlV2l0aEludGVycG9sYXRpb24iLCJ0ZW1wbGF0ZSIsInZhbHVlcyIsInJlcGxhY2UiLCJtYXRjaCIsInRvU3RyaW5nIiwicGx1cmFsaXplIiwiY291bnQiLCJzaW5ndWxhciIsInBsdXJhbCIsImZvcm1hdERhdGUiLCJkYXRlIiwib3B0aW9ucyIsIkludGwiLCJEYXRlVGltZUZvcm1hdCIsImZvcm1hdCIsImVycm9yIiwiZm9ybWF0TnVtYmVyIiwibnVtYmVyIiwiTnVtYmVyRm9ybWF0IiwiZm9ybWF0Q3VycmVuY3kiLCJhbW91bnQiLCJjdXJyZW5jeSIsInN0eWxlIiwiZm9ybWF0UmVsYXRpdmVUaW1lIiwicnRmIiwiUmVsYXRpdmVUaW1lRm9ybWF0IiwibnVtZXJpYyIsIm5vdyIsIkRhdGUiLCJkaWZmSW5TZWNvbmRzIiwiTWF0aCIsImZsb29yIiwiZ2V0VGltZSIsImFicyIsImRpZmZJbk1pbnV0ZXMiLCJkaWZmSW5Ib3VycyIsImRpZmZJbkRheXMiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/use-translation.ts\n"));

/***/ })

});