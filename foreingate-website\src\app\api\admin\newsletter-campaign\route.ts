import { NextRequest, NextResponse } from 'next/server'
import { readFile } from 'fs/promises'
import { existsSync } from 'fs'
import path from 'path'

const DATA_DIR = path.join(process.cwd(), 'data')
const NEWSLETTER_FILE = path.join(DATA_DIR, 'newsletter.json')

async function readSubscribers() {
  try {
    if (!existsSync(NEWSLETTER_FILE)) {
      return []
    }
    const data = await readFile(NEWSLETTER_FILE, 'utf-8')
    return JSON.parse(data)
  } catch (error) {
    console.error('Error reading newsletter subscribers:', error)
    return []
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { 
      subject, 
      content, 
      htmlContent, 
      targetAudience = 'all', // 'all', 'active', 'recent'
      testEmail 
    } = body

    if (!subject || !content) {
      return NextResponse.json(
        { success: false, error: 'Subject and content are required' },
        { status: 400 }
      )
    }

    // If test email is provided, send only to test email
    if (testEmail) {
      if (!process.env.RESEND_API_KEY) {
        return NextResponse.json(
          { success: false, error: 'Email service not configured' },
          { status: 500 }
        )
      }

      const { Resend } = require('resend')
      const resend = new Resend(process.env.RESEND_API_KEY)

      const emailHtml = htmlContent || `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center;">
            <h1 style="color: white; margin: 0;">Foreingate Group Newsletter</h1>
          </div>
          <div style="padding: 30px; background: #f8f9fa;">
            <div style="white-space: pre-wrap; line-height: 1.6; color: #333;">
              ${content}
            </div>
          </div>
          <div style="background: #333; padding: 20px; text-align: center;">
            <p style="color: #ccc; margin: 0; font-size: 14px;">
              © 2024 Foreingate Group. All rights reserved.<br>
              This is a test email from the admin panel.
            </p>
          </div>
        </div>
      `

      await resend.emails.send({
        from: 'Foreingate Newsletter <<EMAIL>>',
        to: [testEmail],
        subject: `[TEST] ${subject}`,
        html: emailHtml,
      })

      return NextResponse.json({
        success: true,
        message: 'Test email sent successfully',
        recipients: 1
      })
    }

    // Get subscribers based on target audience
    let subscribers = await readSubscribers()
    
    switch (targetAudience) {
      case 'active':
        subscribers = subscribers.filter((sub: any) => sub.isActive)
        break
      case 'recent':
        const thirtyDaysAgo = new Date()
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
        subscribers = subscribers.filter((sub: any) => 
          sub.isActive && new Date(sub.subscribedAt) > thirtyDaysAgo
        )
        break
      case 'all':
      default:
        subscribers = subscribers.filter((sub: any) => sub.isActive)
        break
    }

    if (subscribers.length === 0) {
      return NextResponse.json(
        { success: false, error: 'No subscribers found for the selected audience' },
        { status: 400 }
      )
    }

    // Send emails in batches to avoid rate limits
    if (!process.env.RESEND_API_KEY) {
      return NextResponse.json(
        { success: false, error: 'Email service not configured' },
        { status: 500 }
      )
    }

    const { Resend } = require('resend')
    const resend = new Resend(process.env.RESEND_API_KEY)

    const emailHtml = htmlContent || `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center;">
          <h1 style="color: white; margin: 0;">Foreingate Group Newsletter</h1>
        </div>
        <div style="padding: 30px; background: #f8f9fa;">
          <div style="white-space: pre-wrap; line-height: 1.6; color: #333;">
            ${content}
          </div>
        </div>
        <div style="background: #333; padding: 20px; text-align: center;">
          <p style="color: #ccc; margin: 0; font-size: 14px;">
            © 2024 Foreingate Group. All rights reserved.<br>
            <a href="#" style="color: #ccc;">Unsubscribe</a>
          </p>
        </div>
      </div>
    `

    let successCount = 0
    let failureCount = 0
    const batchSize = 10 // Send in batches of 10 to avoid rate limits

    for (let i = 0; i < subscribers.length; i += batchSize) {
      const batch = subscribers.slice(i, i + batchSize)
      const emailPromises = batch.map(async (subscriber: any) => {
        try {
          await resend.emails.send({
            from: 'Foreingate Newsletter <<EMAIL>>',
            to: [subscriber.email],
            subject: subject,
            html: emailHtml,
          })
          successCount++
        } catch (error) {
          console.error(`Failed to send email to ${subscriber.email}:`, error)
          failureCount++
        }
      })

      await Promise.all(emailPromises)
      
      // Add delay between batches to respect rate limits
      if (i + batchSize < subscribers.length) {
        await new Promise(resolve => setTimeout(resolve, 1000)) // 1 second delay
      }
    }

    return NextResponse.json({
      success: true,
      message: `Newsletter campaign completed. ${successCount} emails sent successfully, ${failureCount} failed.`,
      summary: {
        totalSubscribers: subscribers.length,
        successful: successCount,
        failed: failureCount,
        targetAudience
      }
    })

  } catch (error) {
    console.error('Newsletter campaign error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to send newsletter campaign' },
      { status: 500 }
    )
  }
}
