"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"0ddcd2529a0a\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXE5pZGhhbFxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxmb3JlaW5nYXRlX2dyb3VwZVxcZm9yZWluZ2F0ZS13ZWJzaXRlXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIwZGRjZDI1MjlhMGFcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/layout/navigation.tsx":
/*!**********************************************!*\
  !*** ./src/components/layout/navigation.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Navigation: () => (/* binding */ Navigation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/../node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/../node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Mail_Menu_Phone_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Mail,Menu,Phone,X!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Mail_Menu_Phone_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Mail,Menu,Phone,X!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Mail_Menu_Phone_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Mail,Menu,Phone,X!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Mail_Menu_Phone_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Mail,Menu,Phone,X!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Mail_Menu_Phone_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Mail,Menu,Phone,X!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_theme_toggle__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/theme-toggle */ \"(app-pages-browser)/./src/components/ui/theme-toggle.tsx\");\n/* harmony import */ var _components_ui_search__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/search */ \"(app-pages-browser)/./src/components/ui/search.tsx\");\n/* harmony import */ var _components_ui_language_switcher__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/language-switcher */ \"(app-pages-browser)/./src/components/ui/language-switcher.tsx\");\n/* harmony import */ var _hooks_use_translation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/use-translation */ \"(app-pages-browser)/./src/hooks/use-translation.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Navigation auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n// Navigation items will be translated\nconst getNavigationItems = (t)=>[\n        {\n            name: t.nav.home,\n            href: '/'\n        },\n        {\n            name: t.nav.about,\n            href: '/about'\n        },\n        {\n            name: t.nav.services,\n            href: '/services',\n            submenu: [\n                {\n                    name: 'University Admissions',\n                    href: '/services/admissions'\n                },\n                {\n                    name: 'Visa Support',\n                    href: '/services/visa'\n                },\n                {\n                    name: 'Student Housing',\n                    href: '/services/housing'\n                },\n                {\n                    name: 'Document Translation',\n                    href: '/services/translation'\n                },\n                {\n                    name: 'Airport Pickup',\n                    href: '/services/pickup'\n                },\n                {\n                    name: 'Academic Support',\n                    href: '/services/academic'\n                }\n            ]\n        },\n        {\n            name: t.nav.universities,\n            href: '/universities'\n        },\n        {\n            name: t.nav.programs,\n            href: '/programs'\n        },\n        {\n            name: t.nav.blog,\n            href: '/blog'\n        },\n        {\n            name: t.nav.contact,\n            href: '/contact'\n        }\n    ];\nfunction Navigation() {\n    _s();\n    const [isOpen, setIsOpen] = react__WEBPACK_IMPORTED_MODULE_1__.useState(false);\n    const [activeSubmenu, setActiveSubmenu] = react__WEBPACK_IMPORTED_MODULE_1__.useState(null);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    // Safe translation hook usage with fallback\n    let t, navigationItems;\n    try {\n        const translation = (0,_hooks_use_translation__WEBPACK_IMPORTED_MODULE_8__.useTranslation)();\n        t = translation.t;\n        navigationItems = getNavigationItems(t);\n    } catch (error) {\n        // Fallback navigation items if translation context is not available\n        navigationItems = [\n            {\n                name: 'Home',\n                href: '/'\n            },\n            {\n                name: 'About',\n                href: '/about'\n            },\n            {\n                name: 'Services',\n                href: '/services',\n                submenu: []\n            },\n            {\n                name: 'Universities',\n                href: '/universities'\n            },\n            {\n                name: 'Programs',\n                href: '/programs'\n            },\n            {\n                name: 'Blog',\n                href: '/blog'\n            },\n            {\n                name: 'Contact',\n                href: '/contact'\n            }\n        ];\n        t = {\n            nav: {\n                applyNow: 'Apply Now',\n                getStarted: 'Get Started'\n            }\n        };\n    }\n    const toggleMenu = ()=>setIsOpen(!isOpen);\n    const closeMenu = ()=>setIsOpen(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden lg:block border-b bg-muted/50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex h-10 items-center justify-between text-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Mail_Menu_Phone_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-3 w-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                                lineNumber: 78,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"+90 ************\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                                lineNumber: 79,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Mail_Menu_Phone_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-3 w-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                                lineNumber: 82,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"<EMAIL>\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                                lineNumber: 83,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_language_switcher__WEBPACK_IMPORTED_MODULE_7__.LanguageSwitcher, {\n                                        variant: \"minimal\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_theme_toggle__WEBPACK_IMPORTED_MODULE_5__.ThemeToggle, {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                    lineNumber: 74,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                lineNumber: 73,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex h-16 items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-8 w-8 rounded-lg bg-primary flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-primary-foreground font-bold text-lg\",\n                                        children: \"F\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-bold text-xl\",\n                                    children: \"Foreingate Group\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"hidden lg:flex items-center space-x-8\",\n                            children: navigationItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    onMouseEnter: ()=>item.submenu && setActiveSubmenu(item.name),\n                                    onMouseLeave: ()=>setActiveSubmenu(null),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: item.href,\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.cn)(\"flex items-center space-x-1 text-sm font-medium transition-colors hover:text-primary\", pathname === item.href ? \"text-primary\" : \"text-muted-foreground\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: item.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                                    lineNumber: 121,\n                                                    columnNumber: 19\n                                                }, this),\n                                                item.submenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Mail_Menu_Phone_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                                    lineNumber: 122,\n                                                    columnNumber: 36\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 17\n                                        }, this),\n                                        item.submenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.AnimatePresence, {\n                                            children: activeSubmenu === item.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                                                initial: {\n                                                    opacity: 0,\n                                                    y: 10\n                                                },\n                                                animate: {\n                                                    opacity: 1,\n                                                    y: 0\n                                                },\n                                                exit: {\n                                                    opacity: 0,\n                                                    y: 10\n                                                },\n                                                transition: {\n                                                    duration: 0.2\n                                                },\n                                                className: \"absolute top-full left-0 mt-2 w-64 rounded-md border bg-popover p-2 shadow-lg\",\n                                                children: item.submenu.map((subItem)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: subItem.href,\n                                                        className: \"block rounded-sm px-3 py-2 text-sm hover:bg-accent hover:text-accent-foreground\",\n                                                        children: subItem.name\n                                                    }, subItem.name, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                                        lineNumber: 137,\n                                                        columnNumber: 27\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, item.name, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden lg:flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_search__WEBPACK_IMPORTED_MODULE_6__.HeaderSearch, {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/apply\",\n                                        children: t.nav.applyNow\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/contact\",\n                                        children: t.nav.getStarted\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            variant: \"ghost\",\n                            size: \"icon\",\n                            className: \"lg:hidden\",\n                            onClick: toggleMenu,\n                            children: isOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Mail_Menu_Phone_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 23\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Mail_Menu_Phone_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                className: \"h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 51\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.AnimatePresence, {\n                children: isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        height: 0\n                    },\n                    animate: {\n                        opacity: 1,\n                        height: 'auto'\n                    },\n                    exit: {\n                        opacity: 0,\n                        height: 0\n                    },\n                    transition: {\n                        duration: 0.3\n                    },\n                    className: \"lg:hidden border-t bg-background\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container mx-auto px-4 py-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"flex flex-col space-y-4\",\n                            children: [\n                                navigationItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: item.href,\n                                                onClick: closeMenu,\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.cn)(\"block py-2 text-sm font-medium transition-colors hover:text-primary\", pathname === item.href ? \"text-primary\" : \"text-muted-foreground\"),\n                                                children: item.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 21\n                                            }, this),\n                                            item.submenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-4 mt-2 space-y-2\",\n                                                children: item.submenu.map((subItem)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: subItem.href,\n                                                        onClick: closeMenu,\n                                                        className: \"block py-1 text-sm text-muted-foreground hover:text-primary\",\n                                                        children: subItem.name\n                                                    }, subItem.name, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                                        lineNumber: 203,\n                                                        columnNumber: 27\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, item.name, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 19\n                                    }, this)),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col space-y-2 pt-4 border-t\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"outline\",\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/apply\",\n                                                onClick: closeMenu,\n                                                children: t.nav.applyNow\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/contact\",\n                                                onClick: closeMenu,\n                                                children: t.nav.getStarted\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                            lineNumber: 220,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between pt-4 border-t\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_language_switcher__WEBPACK_IMPORTED_MODULE_7__.LanguageSwitcher, {\n                                            variant: \"compact\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_theme_toggle__WEBPACK_IMPORTED_MODULE_5__.ThemeToggle, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                    lineNumber: 179,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                lineNumber: 177,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n        lineNumber: 71,\n        columnNumber: 5\n    }, this);\n}\n_s(Navigation, \"XrypzTc58hBAMhMFbMyEWFvxyjc=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname\n    ];\n});\n_c = Navigation;\nvar _c;\n$RefreshReg$(_c, \"Navigation\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layout/navigation.tsx\n"));

/***/ })

});