'use client'

import { ReactNode } from 'react'
import { ThemeProvider } from '@/components/providers/theme-provider'
import { TranslationProvider } from '@/components/providers/translation-provider'
import { ToastProvider } from '@/components/ui/toast'
import { ErrorBoundary } from '@/components/ui/error-boundary'

interface ClientProvidersProps {
  children: ReactNode
}

export function ClientProviders({ children }: ClientProvidersProps) {
  return (
    <ErrorBoundary>
      <ThemeProvider
        attribute="class"
        defaultTheme="system"
        enableSystem
        disableTransitionOnChange
      >
        <TranslationProvider>
          <ToastProvider>
            {children}
          </ToastProvider>
        </TranslationProvider>
      </ThemeProvider>
    </ErrorBoundary>
  )
}
