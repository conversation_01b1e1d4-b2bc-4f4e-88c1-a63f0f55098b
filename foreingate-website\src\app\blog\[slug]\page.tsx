'use client'

import { notFound } from 'next/navigation'
import { BlogPostHero } from '@/components/sections/blog-post-hero'
import { BlogPostContent } from '@/components/sections/blog-post-content'
import { BlogPostRelated } from '@/components/sections/blog-post-related'
import { WhatsAppWidget } from '@/components/ui/whatsapp-widget'
import { useBlogPost } from '@/hooks/use-api'

interface BlogPostPageProps {
  params: {
    slug: string
  }
}

export default function BlogPostPage({ params }: BlogPostPageProps) {
  const { data: post, loading, error } = useBlogPost(params.slug)

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    )
  }

  if (error || !post) {
    notFound()
  }

  return (
    <>
      <BlogPostHero post={post} />
      <BlogPostContent post={post} />
      <BlogPostRelated post={post} />

      {/* WhatsApp Widget */}
      <WhatsAppWidget
        phoneNumber="905392123456"
        message="Hello! I have questions about studying abroad after reading your blog."
      />
    </>
  )
}
