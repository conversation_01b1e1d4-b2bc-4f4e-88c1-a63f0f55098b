# Email Service Configuration
RESEND_API_KEY=your_resend_api_key_here

# Database Configuration (for future use)
DATABASE_URL=your_database_url_here

# Application Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3001
NEXT_PUBLIC_CONTACT_EMAIL=<EMAIL>
NEXT_PUBLIC_PHONE=+90 ************
NEXT_PUBLIC_WHATSAPP=+90 ************

# Security Configuration
NODE_ENV=development
HTTPS_PORT=3443
SSL_CERT_PATH=./certificates/localhost.crt
SSL_KEY_PATH=./certificates/localhost.key

# Security Keys (Generate strong random keys for production)
NEXTAUTH_SECRET=your-super-secret-key-change-in-production-min-32-chars
ENCRYPTION_KEY=your-32-character-encryption-key-here-exactly-32-chars
JWT_SECRET=your-jwt-secret-key-for-tokens-min-32-characters-long

# Rate Limiting
RATE_LIMIT_MAX=100
RATE_LIMIT_WINDOW=60000

# Admin Security
ADMIN_PASSWORD_HASH=your-hashed-admin-password
ADMIN_SESSION_SECRET=your-admin-session-secret-min-32-characters

# CORS Configuration
ALLOWED_ORIGINS=https://localhost:3443,https://foreingate.com

# Analytics (for future use)
GOOGLE_ANALYTICS_ID=your_ga_id_here

# Social Media
NEXT_PUBLIC_FACEBOOK_URL=https://facebook.com/foreingate
NEXT_PUBLIC_INSTAGRAM_URL=https://instagram.com/foreingate
NEXT_PUBLIC_LINKEDIN_URL=https://linkedin.com/company/foreingate
NEXT_PUBLIC_TWITTER_URL=https://twitter.com/foreingate
