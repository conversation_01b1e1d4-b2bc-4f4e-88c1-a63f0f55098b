import { NextRequest, NextResponse } from 'next/server'
import { readFile, writeFile } from 'fs/promises'
import { existsSync } from 'fs'
import path from 'path'

const DATA_DIR = path.join(process.cwd(), 'data')
const APPLICATIONS_FILE = path.join(DATA_DIR, 'applications.json')
const NEWSLETTER_FILE = path.join(DATA_DIR, 'newsletter.json')

async function readApplications() {
  try {
    if (!existsSync(APPLICATIONS_FILE)) {
      return []
    }
    const data = await readFile(APPLICATIONS_FILE, 'utf-8')
    return JSON.parse(data)
  } catch (error) {
    console.error('Error reading applications:', error)
    return []
  }
}

async function writeApplications(applications: any[]) {
  try {
    await writeFile(APPLICATIONS_FILE, JSON.stringify(applications, null, 2))
  } catch (error) {
    console.error('Error writing applications:', error)
    throw error
  }
}

async function readSubscribers() {
  try {
    if (!existsSync(NEWSLETTER_FILE)) {
      return []
    }
    const data = await readFile(NEWSLETTER_FILE, 'utf-8')
    return JSON.parse(data)
  } catch (error) {
    console.error('Error reading newsletter subscribers:', error)
    return []
  }
}

async function writeSubscribers(subscribers: any[]) {
  try {
    await writeFile(NEWSLETTER_FILE, JSON.stringify(subscribers, null, 2))
  } catch (error) {
    console.error('Error writing newsletter subscribers:', error)
    throw error
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { operation, type, ids, data } = body

    if (!operation || !type || !ids || !Array.isArray(ids)) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields: operation, type, ids' },
        { status: 400 }
      )
    }

    let results = []

    if (type === 'applications') {
      const applications = await readApplications()
      
      switch (operation) {
        case 'update_status':
          if (!data?.status) {
            return NextResponse.json(
              { success: false, error: 'Status is required for update_status operation' },
              { status: 400 }
            )
          }
          
          for (const id of ids) {
            const appIndex = applications.findIndex((app: any) => 
              app.id === id || app.applicationId === id
            )
            if (appIndex !== -1) {
              applications[appIndex].status = data.status
              applications[appIndex].updatedAt = new Date().toISOString()
              if (data.notes) {
                applications[appIndex].notes = data.notes
              }
              results.push({ id, success: true })
            } else {
              results.push({ id, success: false, error: 'Application not found' })
            }
          }
          
          await writeApplications(applications)
          break

        case 'assign_counselor':
          if (!data?.counselor) {
            return NextResponse.json(
              { success: false, error: 'Counselor is required for assign_counselor operation' },
              { status: 400 }
            )
          }
          
          for (const id of ids) {
            const appIndex = applications.findIndex((app: any) => 
              app.id === id || app.applicationId === id
            )
            if (appIndex !== -1) {
              applications[appIndex].assignedCounselor = data.counselor
              applications[appIndex].updatedAt = new Date().toISOString()
              results.push({ id, success: true })
            } else {
              results.push({ id, success: false, error: 'Application not found' })
            }
          }
          
          await writeApplications(applications)
          break

        case 'delete':
          for (const id of ids) {
            const appIndex = applications.findIndex((app: any) => 
              app.id === id || app.applicationId === id
            )
            if (appIndex !== -1) {
              applications.splice(appIndex, 1)
              results.push({ id, success: true })
            } else {
              results.push({ id, success: false, error: 'Application not found' })
            }
          }
          
          await writeApplications(applications)
          break

        default:
          return NextResponse.json(
            { success: false, error: 'Invalid operation for applications' },
            { status: 400 }
          )
      }
    } else if (type === 'newsletter') {
      const subscribers = await readSubscribers()
      
      switch (operation) {
        case 'activate':
          for (const id of ids) {
            const subIndex = subscribers.findIndex((sub: any) => 
              sub.id === id || sub.email === id
            )
            if (subIndex !== -1) {
              subscribers[subIndex].isActive = true
              subscribers[subIndex].updatedAt = new Date().toISOString()
              results.push({ id, success: true })
            } else {
              results.push({ id, success: false, error: 'Subscriber not found' })
            }
          }
          
          await writeSubscribers(subscribers)
          break

        case 'deactivate':
          for (const id of ids) {
            const subIndex = subscribers.findIndex((sub: any) => 
              sub.id === id || sub.email === id
            )
            if (subIndex !== -1) {
              subscribers[subIndex].isActive = false
              subscribers[subIndex].updatedAt = new Date().toISOString()
              results.push({ id, success: true })
            } else {
              results.push({ id, success: false, error: 'Subscriber not found' })
            }
          }
          
          await writeSubscribers(subscribers)
          break

        case 'delete':
          for (const id of ids) {
            const subIndex = subscribers.findIndex((sub: any) => 
              sub.id === id || sub.email === id
            )
            if (subIndex !== -1) {
              subscribers.splice(subIndex, 1)
              results.push({ id, success: true })
            } else {
              results.push({ id, success: false, error: 'Subscriber not found' })
            }
          }
          
          await writeSubscribers(subscribers)
          break

        default:
          return NextResponse.json(
            { success: false, error: 'Invalid operation for newsletter' },
            { status: 400 }
          )
      }
    } else {
      return NextResponse.json(
        { success: false, error: 'Invalid type. Must be "applications" or "newsletter"' },
        { status: 400 }
      )
    }

    const successCount = results.filter(r => r.success).length
    const failureCount = results.filter(r => !r.success).length

    return NextResponse.json({
      success: true,
      message: `Bulk operation completed. ${successCount} successful, ${failureCount} failed.`,
      results,
      summary: {
        total: ids.length,
        successful: successCount,
        failed: failureCount
      }
    })

  } catch (error) {
    console.error('Bulk operation error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to perform bulk operation' },
      { status: 500 }
    )
  }
}
