// Advanced Semantic Search for RAG System
export interface SemanticMatch {
  content: string
  score: number
  metadata: any
  context: string
  highlights: string[]
}

export class SemanticSearchEngine {
  private synonyms: Map<string, string[]> = new Map()
  private conceptMap: Map<string, string[]> = new Map()
  private entityRecognition: Map<string, string> = new Map()

  constructor() {
    this.initializeSynonyms()
    this.initializeConceptMap()
    this.initializeEntityRecognition()
  }

  private initializeSynonyms() {
    // Educational synonyms
    this.synonyms.set('university', ['college', 'institution', 'school', 'academy'])
    this.synonyms.set('program', ['course', 'degree', 'major', 'study', 'curriculum'])
    this.synonyms.set('tuition', ['fees', 'cost', 'price', 'charges', 'expenses'])
    this.synonyms.set('scholarship', ['financial aid', 'grant', 'bursary', 'funding', 'discount'])
    this.synonyms.set('admission', ['enrollment', 'application', 'entry', 'acceptance'])
    this.synonyms.set('requirement', ['prerequisite', 'criteria', 'condition', 'qualification'])
    this.synonyms.set('accommodation', ['housing', 'residence', 'dormitory', 'lodging'])
    this.synonyms.set('international', ['foreign', 'overseas', 'global', 'worldwide'])
    this.synonyms.set('graduate', ['postgraduate', 'masters', 'phd', 'doctoral'])
    this.synonyms.set('undergraduate', ['bachelor', 'bachelors', 'first degree'])
    
    // Location synonyms
    this.synonyms.set('cyprus', ['northern cyprus', 'trnc', 'kktc'])
    this.synonyms.set('nicosia', ['lefkosa', 'capital'])
    this.synonyms.set('famagusta', ['gazimagusa'])
    
    // Subject synonyms
    this.synonyms.set('engineering', ['technology', 'technical', 'applied science'])
    this.synonyms.set('medicine', ['medical', 'healthcare', 'health sciences'])
    this.synonyms.set('business', ['management', 'commerce', 'economics', 'finance'])
    this.synonyms.set('computer', ['computing', 'it', 'information technology', 'software'])
  }

  private initializeConceptMap() {
    // Map concepts to related terms
    this.conceptMap.set('cost_related', [
      'tuition', 'fees', 'scholarship', 'financial aid', 'budget', 'affordable',
      'expensive', 'cheap', 'payment', 'installment', 'discount', 'funding'
    ])
    
    this.conceptMap.set('admission_related', [
      'apply', 'application', 'admission', 'enrollment', 'requirements', 'documents',
      'deadline', 'process', 'steps', 'eligibility', 'criteria', 'qualification'
    ])
    
    this.conceptMap.set('academic_related', [
      'program', 'degree', 'course', 'curriculum', 'faculty', 'department',
      'major', 'study', 'education', 'academic', 'learning', 'teaching'
    ])
    
    this.conceptMap.set('location_related', [
      'cyprus', 'northern cyprus', 'nicosia', 'famagusta', 'campus', 'location',
      'address', 'where', 'situated', 'based', 'island', 'mediterranean'
    ])
    
    this.conceptMap.set('support_related', [
      'help', 'support', 'assistance', 'service', 'guidance', 'advice',
      'consultation', 'counseling', 'mentoring', 'coaching'
    ])
    
    this.conceptMap.set('visa_related', [
      'visa', 'permit', 'immigration', 'passport', 'documents', 'embassy',
      'consulate', 'application', 'processing', 'approval', 'entry'
    ])
  }

  private initializeEntityRecognition() {
    // University entities
    this.entityRecognition.set('emu', 'Eastern Mediterranean University')
    this.entityRecognition.set('eastern mediterranean university', 'Eastern Mediterranean University')
    this.entityRecognition.set('neu', 'Near East University')
    this.entityRecognition.set('near east university', 'Near East University')
    this.entityRecognition.set('ciu', 'Cyprus International University')
    this.entityRecognition.set('cyprus international university', 'Cyprus International University')
    
    // Program entities
    this.entityRecognition.set('cs', 'Computer Science')
    this.entityRecognition.set('it', 'Information Technology')
    this.entityRecognition.set('mba', 'Master of Business Administration')
    this.entityRecognition.set('md', 'Medicine')
    this.entityRecognition.set('dds', 'Dentistry')
    
    // Test entities
    this.entityRecognition.set('ielts', 'International English Language Testing System')
    this.entityRecognition.set('toefl', 'Test of English as a Foreign Language')
    this.entityRecognition.set('gre', 'Graduate Record Examination')
    this.entityRecognition.set('gmat', 'Graduate Management Admission Test')
  }

  public expandQuery(query: string): string[] {
    const expandedTerms = new Set<string>()
    const originalTerms = query.toLowerCase().split(/\s+/)
    
    // Add original terms
    originalTerms.forEach(term => expandedTerms.add(term))
    
    // Add synonyms
    originalTerms.forEach(term => {
      const synonymList = this.synonyms.get(term)
      if (synonymList) {
        synonymList.forEach(synonym => expandedTerms.add(synonym))
      }
    })
    
    // Add concept-related terms
    this.conceptMap.forEach((concepts, category) => {
      const hasConceptMatch = originalTerms.some(term => concepts.includes(term))
      if (hasConceptMatch) {
        concepts.forEach(concept => expandedTerms.add(concept))
      }
    })
    
    // Recognize and expand entities
    const queryLower = query.toLowerCase()
    this.entityRecognition.forEach((fullName, abbreviation) => {
      if (queryLower.includes(abbreviation)) {
        expandedTerms.add(fullName.toLowerCase())
      }
    })
    
    return Array.from(expandedTerms)
  }

  public calculateSemanticSimilarity(query: string, content: string): number {
    const expandedQuery = this.expandQuery(query)
    const contentLower = content.toLowerCase()
    
    let score = 0
    let matches = 0
    
    // Direct term matching with weights
    expandedQuery.forEach(term => {
      if (contentLower.includes(term)) {
        matches++
        // Weight longer terms higher
        score += term.length > 5 ? 2 : 1
        
        // Boost for exact phrase matches
        if (query.toLowerCase().includes(term) && contentLower.includes(term)) {
          score += 1
        }
      }
    })
    
    // Concept clustering bonus
    const conceptBonus = this.calculateConceptBonus(query, content)
    score += conceptBonus
    
    // Entity recognition bonus
    const entityBonus = this.calculateEntityBonus(query, content)
    score += entityBonus
    
    // Normalize score
    const maxPossibleScore = expandedQuery.length * 2 + 5 // Max possible with bonuses
    return Math.min(score / maxPossibleScore, 1.0)
  }

  private calculateConceptBonus(query: string, content: string): number {
    let bonus = 0
    const queryLower = query.toLowerCase()
    const contentLower = content.toLowerCase()
    
    this.conceptMap.forEach((concepts, category) => {
      const queryHasConcept = concepts.some(concept => queryLower.includes(concept))
      const contentHasConcept = concepts.some(concept => contentLower.includes(concept))
      
      if (queryHasConcept && contentHasConcept) {
        bonus += 1
      }
    })
    
    return bonus
  }

  private calculateEntityBonus(query: string, content: string): number {
    let bonus = 0
    const queryLower = query.toLowerCase()
    const contentLower = content.toLowerCase()
    
    this.entityRecognition.forEach((fullName, abbreviation) => {
      const queryHasEntity = queryLower.includes(abbreviation) || queryLower.includes(fullName.toLowerCase())
      const contentHasEntity = contentLower.includes(abbreviation) || contentLower.includes(fullName.toLowerCase())
      
      if (queryHasEntity && contentHasEntity) {
        bonus += 2 // Higher bonus for entity matches
      }
    })
    
    return bonus
  }

  public extractKeyPhrases(text: string): string[] {
    const phrases: string[] = []
    const words = text.toLowerCase().split(/\s+/)
    
    // Extract 2-3 word phrases that might be important
    for (let i = 0; i < words.length - 1; i++) {
      const twoWordPhrase = `${words[i]} ${words[i + 1]}`
      if (this.isImportantPhrase(twoWordPhrase)) {
        phrases.push(twoWordPhrase)
      }
      
      if (i < words.length - 2) {
        const threeWordPhrase = `${words[i]} ${words[i + 1]} ${words[i + 2]}`
        if (this.isImportantPhrase(threeWordPhrase)) {
          phrases.push(threeWordPhrase)
        }
      }
    }
    
    return phrases
  }

  private isImportantPhrase(phrase: string): boolean {
    const importantPatterns = [
      /\b(computer|electrical|civil|mechanical|industrial)\s+engineering\b/,
      /\b(business|international)\s+(administration|relations)\b/,
      /\b(tuition|living)\s+(fees|costs)\b/,
      /\b(english|language)\s+(proficiency|requirements)\b/,
      /\b(student|residence)\s+(visa|permit)\b/,
      /\b(northern|eastern)\s+cyprus\b/,
      /\b(near|eastern)\s+(east|mediterranean)\b/,
      /\b(financial|merit)\s+(aid|scholarship)\b/
    ]
    
    return importantPatterns.some(pattern => pattern.test(phrase))
  }

  public highlightMatches(content: string, query: string): string[] {
    const expandedQuery = this.expandQuery(query)
    const highlights: string[] = []
    
    expandedQuery.forEach(term => {
      const regex = new RegExp(`\\b${term}\\b`, 'gi')
      const matches = content.match(regex)
      if (matches) {
        highlights.push(...matches)
      }
    })
    
    return [...new Set(highlights)] // Remove duplicates
  }

  public findBestMatches(query: string, documents: any[], limit: number = 5): SemanticMatch[] {
    const matches: SemanticMatch[] = []
    
    documents.forEach(doc => {
      const similarity = this.calculateSemanticSimilarity(query, doc.content)
      
      if (similarity > 0.1) { // Minimum threshold
        const highlights = this.highlightMatches(doc.content, query)
        const keyPhrases = this.extractKeyPhrases(doc.content)
        
        matches.push({
          content: doc.content,
          score: similarity,
          metadata: doc.metadata || {},
          context: this.extractContext(doc.content, query),
          highlights
        })
      }
    })
    
    return matches
      .sort((a, b) => b.score - a.score)
      .slice(0, limit)
  }

  private extractContext(content: string, query: string, contextLength: number = 200): string {
    const queryTerms = query.toLowerCase().split(/\s+/)
    const contentLower = content.toLowerCase()
    
    // Find the best position to extract context
    let bestPosition = 0
    let maxMatches = 0
    
    for (let i = 0; i <= content.length - contextLength; i += 50) {
      const segment = contentLower.substring(i, i + contextLength)
      const matches = queryTerms.filter(term => segment.includes(term)).length
      
      if (matches > maxMatches) {
        maxMatches = matches
        bestPosition = i
      }
    }
    
    const context = content.substring(bestPosition, bestPosition + contextLength)
    return context.trim() + (bestPosition + contextLength < content.length ? '...' : '')
  }
}

// Question intent classifier for better RAG responses
export class QuestionClassifier {
  private questionPatterns: Map<string, RegExp[]> = new Map()

  constructor() {
    this.initializePatterns()
  }

  private initializePatterns() {
    this.questionPatterns.set('comparison', [
      /\b(compare|difference|better|vs|versus|which is)\b/i,
      /\b(best|top|better|prefer|choose between)\b/i
    ])
    
    this.questionPatterns.set('cost_inquiry', [
      /\b(cost|price|fee|expensive|cheap|afford|budget)\b/i,
      /\b(how much|what does.*cost|tuition)\b/i
    ])
    
    this.questionPatterns.set('process_inquiry', [
      /\b(how to|process|steps|procedure|apply)\b/i,
      /\b(what.*need|requirements|documents)\b/i
    ])
    
    this.questionPatterns.set('factual_inquiry', [
      /\b(what is|tell me about|information about)\b/i,
      /\b(where|when|who|which)\b/i
    ])
    
    this.questionPatterns.set('eligibility_inquiry', [
      /\b(can i|am i eligible|qualify|requirements)\b/i,
      /\b(do i need|must i|should i)\b/i
    ])
  }

  public classifyQuestion(question: string): string {
    const questionLower = question.toLowerCase()
    
    for (const [category, patterns] of this.questionPatterns) {
      if (patterns.some(pattern => pattern.test(questionLower))) {
        return category
      }
    }
    
    return 'general'
  }

  public generateResponseTemplate(questionType: string, context: any[]): string {
    switch (questionType) {
      case 'comparison':
        return this.generateComparisonResponse(context)
      case 'cost_inquiry':
        return this.generateCostResponse(context)
      case 'process_inquiry':
        return this.generateProcessResponse(context)
      case 'factual_inquiry':
        return this.generateFactualResponse(context)
      case 'eligibility_inquiry':
        return this.generateEligibilityResponse(context)
      default:
        return this.generateGeneralResponse(context)
    }
  }

  private generateComparisonResponse(context: any[]): string {
    return `🔍 **Comparison Analysis:**\n\nBased on the information available, here's a detailed comparison:\n\n${context.map(item => `• ${item.content}`).join('\n\n')}\n\nWould you like me to elaborate on any specific aspect of this comparison?`
  }

  private generateCostResponse(context: any[]): string {
    return `💰 **Cost Information:**\n\n${context.map(item => item.content).join('\n\n')}\n\n**💡 Cost-Saving Tips:**\n• Apply early for scholarships\n• Consider shared accommodation\n• Look into payment plan options\n\nNeed a personalized cost estimate? I can help calculate total expenses for your specific situation!`
  }

  private generateProcessResponse(context: any[]): string {
    return `📋 **Step-by-Step Process:**\n\n${context.map((item, index) => `**Step ${index + 1}:** ${item.content}`).join('\n\n')}\n\n**⏱️ Timeline:** Most processes take 2-4 months\n**✅ Success Rate:** 98% with proper guidance\n\nReady to start? I can guide you through each step!`
  }

  private generateFactualResponse(context: any[]): string {
    return `📚 **Information Summary:**\n\n${context.map(item => item.content).join('\n\n')}\n\nThis information is current and verified. Would you like more details about any specific aspect?`
  }

  private generateEligibilityResponse(context: any[]): string {
    return `✅ **Eligibility Assessment:**\n\n${context.map(item => item.content).join('\n\n')}\n\n**📝 Quick Eligibility Check:**\nTo give you a personalized assessment, I'd need to know:\n• Your academic background\n• English proficiency level\n• Intended program of study\n\nShall we do a quick eligibility assessment?`
  }

  private generateGeneralResponse(context: any[]): string {
    return `${context.map(item => item.content).join('\n\n')}\n\nIs there anything specific you'd like me to explain further?`
  }
}
