/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/bulk-operations/route";
exports.ids = ["app/api/admin/bulk-operations/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fbulk-operations%2Froute&page=%2Fapi%2Fadmin%2Fbulk-operations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fbulk-operations%2Froute.ts&appDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fbulk-operations%2Froute&page=%2Fapi%2Fadmin%2Fbulk-operations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fbulk-operations%2Froute.ts&appDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Nidhal_Documents_augment_projects_foreingate_groupe_foreingate_website_src_app_api_admin_bulk_operations_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/admin/bulk-operations/route.ts */ \"(rsc)/./src/app/api/admin/bulk-operations/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/bulk-operations/route\",\n        pathname: \"/api/admin/bulk-operations\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/bulk-operations/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\api\\\\admin\\\\bulk-operations\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Nidhal_Documents_augment_projects_foreingate_groupe_foreingate_website_src_app_api_admin_bulk_operations_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fbulk-operations%2Froute&page=%2Fapi%2Fadmin%2Fbulk-operations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fbulk-operations%2Froute.ts&appDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/admin/bulk-operations/route.ts":
/*!****************************************************!*\
  !*** ./src/app/api/admin/bulk-operations/route.ts ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fs/promises */ \"fs/promises\");\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fs_promises__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nconst DATA_DIR = path__WEBPACK_IMPORTED_MODULE_3___default().join(process.cwd(), 'data');\nconst APPLICATIONS_FILE = path__WEBPACK_IMPORTED_MODULE_3___default().join(DATA_DIR, 'applications.json');\nconst NEWSLETTER_FILE = path__WEBPACK_IMPORTED_MODULE_3___default().join(DATA_DIR, 'newsletter.json');\nasync function readApplications() {\n    try {\n        if (!(0,fs__WEBPACK_IMPORTED_MODULE_2__.existsSync)(APPLICATIONS_FILE)) {\n            return [];\n        }\n        const data = await (0,fs_promises__WEBPACK_IMPORTED_MODULE_1__.readFile)(APPLICATIONS_FILE, 'utf-8');\n        return JSON.parse(data);\n    } catch (error) {\n        console.error('Error reading applications:', error);\n        return [];\n    }\n}\nasync function writeApplications(applications) {\n    try {\n        await (0,fs_promises__WEBPACK_IMPORTED_MODULE_1__.writeFile)(APPLICATIONS_FILE, JSON.stringify(applications, null, 2));\n    } catch (error) {\n        console.error('Error writing applications:', error);\n        throw error;\n    }\n}\nasync function readSubscribers() {\n    try {\n        if (!(0,fs__WEBPACK_IMPORTED_MODULE_2__.existsSync)(NEWSLETTER_FILE)) {\n            return [];\n        }\n        const data = await (0,fs_promises__WEBPACK_IMPORTED_MODULE_1__.readFile)(NEWSLETTER_FILE, 'utf-8');\n        return JSON.parse(data);\n    } catch (error) {\n        console.error('Error reading newsletter subscribers:', error);\n        return [];\n    }\n}\nasync function writeSubscribers(subscribers) {\n    try {\n        await (0,fs_promises__WEBPACK_IMPORTED_MODULE_1__.writeFile)(NEWSLETTER_FILE, JSON.stringify(subscribers, null, 2));\n    } catch (error) {\n        console.error('Error writing newsletter subscribers:', error);\n        throw error;\n    }\n}\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { operation, type, ids, data } = body;\n        if (!operation || !type || !ids || !Array.isArray(ids)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Missing required fields: operation, type, ids'\n            }, {\n                status: 400\n            });\n        }\n        let results = [];\n        if (type === 'applications') {\n            const applications = await readApplications();\n            switch(operation){\n                case 'update_status':\n                    if (!data?.status) {\n                        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                            success: false,\n                            error: 'Status is required for update_status operation'\n                        }, {\n                            status: 400\n                        });\n                    }\n                    for (const id of ids){\n                        const appIndex = applications.findIndex((app)=>app.id === id || app.applicationId === id);\n                        if (appIndex !== -1) {\n                            applications[appIndex].status = data.status;\n                            applications[appIndex].updatedAt = new Date().toISOString();\n                            if (data.notes) {\n                                applications[appIndex].notes = data.notes;\n                            }\n                            results.push({\n                                id,\n                                success: true\n                            });\n                        } else {\n                            results.push({\n                                id,\n                                success: false,\n                                error: 'Application not found'\n                            });\n                        }\n                    }\n                    await writeApplications(applications);\n                    break;\n                case 'assign_counselor':\n                    if (!data?.counselor) {\n                        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                            success: false,\n                            error: 'Counselor is required for assign_counselor operation'\n                        }, {\n                            status: 400\n                        });\n                    }\n                    for (const id of ids){\n                        const appIndex = applications.findIndex((app)=>app.id === id || app.applicationId === id);\n                        if (appIndex !== -1) {\n                            applications[appIndex].assignedCounselor = data.counselor;\n                            applications[appIndex].updatedAt = new Date().toISOString();\n                            results.push({\n                                id,\n                                success: true\n                            });\n                        } else {\n                            results.push({\n                                id,\n                                success: false,\n                                error: 'Application not found'\n                            });\n                        }\n                    }\n                    await writeApplications(applications);\n                    break;\n                case 'delete':\n                    for (const id of ids){\n                        const appIndex = applications.findIndex((app)=>app.id === id || app.applicationId === id);\n                        if (appIndex !== -1) {\n                            applications.splice(appIndex, 1);\n                            results.push({\n                                id,\n                                success: true\n                            });\n                        } else {\n                            results.push({\n                                id,\n                                success: false,\n                                error: 'Application not found'\n                            });\n                        }\n                    }\n                    await writeApplications(applications);\n                    break;\n                default:\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        success: false,\n                        error: 'Invalid operation for applications'\n                    }, {\n                        status: 400\n                    });\n            }\n        } else if (type === 'newsletter') {\n            const subscribers = await readSubscribers();\n            switch(operation){\n                case 'activate':\n                    for (const id of ids){\n                        const subIndex = subscribers.findIndex((sub)=>sub.id === id || sub.email === id);\n                        if (subIndex !== -1) {\n                            subscribers[subIndex].isActive = true;\n                            subscribers[subIndex].updatedAt = new Date().toISOString();\n                            results.push({\n                                id,\n                                success: true\n                            });\n                        } else {\n                            results.push({\n                                id,\n                                success: false,\n                                error: 'Subscriber not found'\n                            });\n                        }\n                    }\n                    await writeSubscribers(subscribers);\n                    break;\n                case 'deactivate':\n                    for (const id of ids){\n                        const subIndex = subscribers.findIndex((sub)=>sub.id === id || sub.email === id);\n                        if (subIndex !== -1) {\n                            subscribers[subIndex].isActive = false;\n                            subscribers[subIndex].updatedAt = new Date().toISOString();\n                            results.push({\n                                id,\n                                success: true\n                            });\n                        } else {\n                            results.push({\n                                id,\n                                success: false,\n                                error: 'Subscriber not found'\n                            });\n                        }\n                    }\n                    await writeSubscribers(subscribers);\n                    break;\n                case 'delete':\n                    for (const id of ids){\n                        const subIndex = subscribers.findIndex((sub)=>sub.id === id || sub.email === id);\n                        if (subIndex !== -1) {\n                            subscribers.splice(subIndex, 1);\n                            results.push({\n                                id,\n                                success: true\n                            });\n                        } else {\n                            results.push({\n                                id,\n                                success: false,\n                                error: 'Subscriber not found'\n                            });\n                        }\n                    }\n                    await writeSubscribers(subscribers);\n                    break;\n                default:\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        success: false,\n                        error: 'Invalid operation for newsletter'\n                    }, {\n                        status: 400\n                    });\n            }\n        } else {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Invalid type. Must be \"applications\" or \"newsletter\"'\n            }, {\n                status: 400\n            });\n        }\n        const successCount = results.filter((r)=>r.success).length;\n        const failureCount = results.filter((r)=>!r.success).length;\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: `Bulk operation completed. ${successCount} successful, ${failureCount} failed.`,\n            results,\n            summary: {\n                total: ids.length,\n                successful: successCount,\n                failed: failureCount\n            }\n        });\n    } catch (error) {\n        console.error('Bulk operation error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Failed to perform bulk operation'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/admin/bulk-operations/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "fs/promises":
/*!******************************!*\
  !*** external "fs/promises" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("fs/promises");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fbulk-operations%2Froute&page=%2Fapi%2Fadmin%2Fbulk-operations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fbulk-operations%2Froute.ts&appDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();