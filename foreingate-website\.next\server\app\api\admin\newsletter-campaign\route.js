/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/newsletter-campaign/route";
exports.ids = ["app/api/admin/newsletter-campaign/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fnewsletter-campaign%2Froute&page=%2Fapi%2Fadmin%2Fnewsletter-campaign%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fnewsletter-campaign%2Froute.ts&appDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fnewsletter-campaign%2Froute&page=%2Fapi%2Fadmin%2Fnewsletter-campaign%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fnewsletter-campaign%2Froute.ts&appDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Nidhal_Documents_augment_projects_foreingate_groupe_foreingate_website_src_app_api_admin_newsletter_campaign_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/admin/newsletter-campaign/route.ts */ \"(rsc)/./src/app/api/admin/newsletter-campaign/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/newsletter-campaign/route\",\n        pathname: \"/api/admin/newsletter-campaign\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/newsletter-campaign/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\api\\\\admin\\\\newsletter-campaign\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Nidhal_Documents_augment_projects_foreingate_groupe_foreingate_website_src_app_api_admin_newsletter_campaign_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fnewsletter-campaign%2Froute&page=%2Fapi%2Fadmin%2Fnewsletter-campaign%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fnewsletter-campaign%2Froute.ts&appDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/admin/newsletter-campaign/route.ts":
/*!********************************************************!*\
  !*** ./src/app/api/admin/newsletter-campaign/route.ts ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fs/promises */ \"fs/promises\");\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fs_promises__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nconst DATA_DIR = path__WEBPACK_IMPORTED_MODULE_3___default().join(process.cwd(), 'data');\nconst NEWSLETTER_FILE = path__WEBPACK_IMPORTED_MODULE_3___default().join(DATA_DIR, 'newsletter.json');\nasync function readSubscribers() {\n    try {\n        if (!(0,fs__WEBPACK_IMPORTED_MODULE_2__.existsSync)(NEWSLETTER_FILE)) {\n            return [];\n        }\n        const data = await (0,fs_promises__WEBPACK_IMPORTED_MODULE_1__.readFile)(NEWSLETTER_FILE, 'utf-8');\n        return JSON.parse(data);\n    } catch (error) {\n        console.error('Error reading newsletter subscribers:', error);\n        return [];\n    }\n}\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { subject, content, htmlContent, targetAudience = 'all', testEmail } = body;\n        if (!subject || !content) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Subject and content are required'\n            }, {\n                status: 400\n            });\n        }\n        // If test email is provided, send only to test email\n        if (testEmail) {\n            if (!process.env.RESEND_API_KEY) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: false,\n                    error: 'Email service not configured'\n                }, {\n                    status: 500\n                });\n            }\n            const { Resend } = __webpack_require__(/*! resend */ \"(rsc)/../node_modules/resend/dist/index.js\");\n            const resend = new Resend(process.env.RESEND_API_KEY);\n            const emailHtml = htmlContent || `\n        <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n          <div style=\"background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center;\">\n            <h1 style=\"color: white; margin: 0;\">Foreingate Group Newsletter</h1>\n          </div>\n          <div style=\"padding: 30px; background: #f8f9fa;\">\n            <div style=\"white-space: pre-wrap; line-height: 1.6; color: #333;\">\n              ${content}\n            </div>\n          </div>\n          <div style=\"background: #333; padding: 20px; text-align: center;\">\n            <p style=\"color: #ccc; margin: 0; font-size: 14px;\">\n              © 2024 Foreingate Group. All rights reserved.<br>\n              This is a test email from the admin panel.\n            </p>\n          </div>\n        </div>\n      `;\n            await resend.emails.send({\n                from: 'Foreingate Newsletter <<EMAIL>>',\n                to: [\n                    testEmail\n                ],\n                subject: `[TEST] ${subject}`,\n                html: emailHtml\n            });\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                message: 'Test email sent successfully',\n                recipients: 1\n            });\n        }\n        // Get subscribers based on target audience\n        let subscribers = await readSubscribers();\n        switch(targetAudience){\n            case 'active':\n                subscribers = subscribers.filter((sub)=>sub.isActive);\n                break;\n            case 'recent':\n                const thirtyDaysAgo = new Date();\n                thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);\n                subscribers = subscribers.filter((sub)=>sub.isActive && new Date(sub.subscribedAt) > thirtyDaysAgo);\n                break;\n            case 'all':\n            default:\n                subscribers = subscribers.filter((sub)=>sub.isActive);\n                break;\n        }\n        if (subscribers.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'No subscribers found for the selected audience'\n            }, {\n                status: 400\n            });\n        }\n        // Send emails in batches to avoid rate limits\n        if (!process.env.RESEND_API_KEY) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Email service not configured'\n            }, {\n                status: 500\n            });\n        }\n        const { Resend } = __webpack_require__(/*! resend */ \"(rsc)/../node_modules/resend/dist/index.js\");\n        const resend = new Resend(process.env.RESEND_API_KEY);\n        const emailHtml = htmlContent || `\n      <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n        <div style=\"background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center;\">\n          <h1 style=\"color: white; margin: 0;\">Foreingate Group Newsletter</h1>\n        </div>\n        <div style=\"padding: 30px; background: #f8f9fa;\">\n          <div style=\"white-space: pre-wrap; line-height: 1.6; color: #333;\">\n            ${content}\n          </div>\n        </div>\n        <div style=\"background: #333; padding: 20px; text-align: center;\">\n          <p style=\"color: #ccc; margin: 0; font-size: 14px;\">\n            © 2024 Foreingate Group. All rights reserved.<br>\n            <a href=\"#\" style=\"color: #ccc;\">Unsubscribe</a>\n          </p>\n        </div>\n      </div>\n    `;\n        let successCount = 0;\n        let failureCount = 0;\n        const batchSize = 10 // Send in batches of 10 to avoid rate limits\n        ;\n        for(let i = 0; i < subscribers.length; i += batchSize){\n            const batch = subscribers.slice(i, i + batchSize);\n            const emailPromises = batch.map(async (subscriber)=>{\n                try {\n                    await resend.emails.send({\n                        from: 'Foreingate Newsletter <<EMAIL>>',\n                        to: [\n                            subscriber.email\n                        ],\n                        subject: subject,\n                        html: emailHtml\n                    });\n                    successCount++;\n                } catch (error) {\n                    console.error(`Failed to send email to ${subscriber.email}:`, error);\n                    failureCount++;\n                }\n            });\n            await Promise.all(emailPromises);\n            // Add delay between batches to respect rate limits\n            if (i + batchSize < subscribers.length) {\n                await new Promise((resolve)=>setTimeout(resolve, 1000)) // 1 second delay\n                ;\n            }\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: `Newsletter campaign completed. ${successCount} emails sent successfully, ${failureCount} failed.`,\n            summary: {\n                totalSubscribers: subscribers.length,\n                successful: successCount,\n                failed: failureCount,\n                targetAudience\n            }\n        });\n    } catch (error) {\n        console.error('Newsletter campaign error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Failed to send newsletter campaign'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/admin/newsletter-campaign/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "async_hooks":
/*!******************************!*\
  !*** external "async_hooks" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("async_hooks");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "fs/promises":
/*!******************************!*\
  !*** external "fs/promises" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("fs/promises");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "prettier/plugins/html":
/*!****************************************!*\
  !*** external "prettier/plugins/html" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = import("prettier/plugins/html");;

/***/ }),

/***/ "prettier/standalone":
/*!**************************************!*\
  !*** external "prettier/standalone" ***!
  \**************************************/
/***/ ((module) => {

"use strict";
module.exports = import("prettier/standalone");;

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/resend"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fnewsletter-campaign%2Froute&page=%2Fapi%2Fadmin%2Fnewsletter-campaign%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fnewsletter-campaign%2Froute.ts&appDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();